import {
  AUDIO_MIME_TYPES,
  VIDEO_MIME_TYPES,
  IMAGES_MIME_TYPES,
  DOCS_MIME_TYPES,
} from '../../../helpers/common';
import { FileTypeFilter } from '../FileTypeFilter';
import { render, fireEvent } from '@testing-library/react';
const Props = {
  fileTypesFilter: {
    video: [],
    audio: [],
    image: [],
    doc: [],
  },
  updateFileFilter: jest.fn(),
};
describe('FileTypeFilter', function () {
  it('renders all checkboxes', function () {
    const childCheckboxes =
      VIDEO_MIME_TYPES.length +
      AUDIO_MIME_TYPES.length +
      IMAGES_MIME_TYPES.length +
      DOCS_MIME_TYPES.length;
    const { getAllByTestId } = render(<FileTypeFilter {...Props} />);
    expect(getAllByTestId('form-control-label')).toHaveLength(
      childCheckboxes + 4
    );
  });
  it('when click the image type', function () {
    const props = {
      fileTypesFilter: {
        video: [],
        audio: [],
        image: [...IMAGES_MIME_TYPES],
        doc: [],
      },
      updateFileFilter: jest.fn(),
    };
    const { getByTestId } = render(<FileTypeFilter {...props} />);
    fireEvent.click(getByTestId('check-box-image'));
    expect(getByTestId('check-box-image')).toHaveProperty('checked', true);
  });

  it('Each father box should have a dropdown button', function () {
    const { getAllByTestId } = render(<FileTypeFilter {...Props} />);
    expect(getAllByTestId('button')).toHaveLength(4);
  });
  it('When the parent box is selected, all children would be selected and vice versa', function () {
    const props = {
      fileTypesFilter: {
        video: [],
        audio: [],
        image: [],
        doc: [...DOCS_MIME_TYPES],
      },
      updateFileFilter: jest.fn(),
    };
    const { getByTestId, getAllByTestId } = render(
      <FileTypeFilter {...props} />
    );
    fireEvent.click(getByTestId('check-box-doc'));
    expect(getByTestId('check-box-doc')).toHaveProperty('checked', true);
    expect(getAllByTestId('check-box-child-doc')).toHaveLength(
      DOCS_MIME_TYPES.length
    );
  });
});
