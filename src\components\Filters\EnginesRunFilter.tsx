import TextField from '@mui/material/TextField';
import ListItemText from '@mui/material/ListItemText';
import List from '@mui/material/List';
import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';

function EnginesRunFilter({
  handleAutocompleteChange,
  onKeyDown,
  searchValue,
  handleInputChange,
  label,
  engineCategories,
  fieldValue,
}: Props) {
  const filteredEngineCategories = engineCategories.filter(
    (engineCategory) =>
      !searchValue.some((searchValue) => searchValue.id === engineCategory.id)
  );
  const filterOptions = createFilterOptions({
    stringify: (option: SearchValue) => JSON.stringify(option),
  });
  return (
    <Autocomplete
      multiple
      limitTags={2}
      id="multiple-limit-tags"
      options={filteredEngineCategories}
      freeSolo
      style={{ width: '100%' }}
      onChange={(_e, newval, _reason) => {
        handleAutocompleteChange(newval);
      }}
      value={searchValue}
      getOptionLabel={(option: any) => option.name}
      filterOptions={filterOptions}
      renderOption={(props, option) => {
        return (
          <List
            sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
            {...(props as any)}
          >
            <ListItemText primary={option.name} secondary={option.id} />
          </List>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          label={label}
          onKeyDown={onKeyDown}
          onChange={handleInputChange}
          value={fieldValue}
        />
      )}
    />
  );
}

interface SearchValue {
  id: string;
  name: string;
}

interface EngineCategory {
  id: string;
  name: string;
  iconClass?: string;
}

interface Props {
  handleAutocompleteChange: (newValue: any) => void;
  onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  searchValue: SearchValue[];
  handleInputChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
  engineCategories: EngineCategory[];
  fieldValue?: string;
}

export default EnginesRunFilter;
