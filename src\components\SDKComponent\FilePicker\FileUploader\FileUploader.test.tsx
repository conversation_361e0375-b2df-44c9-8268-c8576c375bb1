import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import FileUploader from '.';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

class MockFileList implements FileList {
  private _files: File[];

  constructor(files: File[]) {
    this._files = files;

    // Need to attach files to the object with index properties for direct access
    files.forEach((file, index) => {
      this[index] = file;
    });
  }

  get length() {
    return this._files.length;
  }

  item(index: number): File | null {
    return this._files[index] || null;
  }

  [index: number]: File;

  [Symbol.iterator]() {
    return this._files[Symbol.iterator]();
  }
}

export class MockDataTransfer {
  public types: string[];
  public files: FileList;

  constructor(files: File[] = []) {
    this.types = ['Files'];
    this.files = new MockFileList(files);
  }
}

// Function to render component with DnD context
const renderWithDnd = (ui: React.ReactElement) => {
  return render(<DndProvider backend={HTML5Backend}>{ui}</DndProvider>);
};

// Function to create mock files
const createFile = (name: string, size: number, type: string) => {
  const file = new File(['a'.repeat(size)], name, { type });
  return file;
};

beforeAll(() => {
  Object.defineProperty(window, 'DataTransfer', {
    writable: true,
    value: MockDataTransfer,
  });
});

describe('FileUploader', () => {
  const acceptedFileTypes = ['image/png', 'image/jpeg'];
  const onFilesSelected = jest.fn();
  const onFilesRejected = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render', () => {
    renderWithDnd(
      <FileUploader
        acceptedFileTypes={acceptedFileTypes}
        onFilesSelected={onFilesSelected}
      />
    );

    expect(screen.getByTestId('file-uploader')).toBeInTheDocument();
    expect(screen.getByText('Drag & Drop')).toBeInTheDocument();
    expect(screen.getByText('your file(s) here, or')).toBeInTheDocument();
    expect(screen.getByText('browse')).toBeInTheDocument();
    expect(screen.queryByTestId('extension-panel')).not.toBeInTheDocument();
  });

  test('should handle files selection via file dialog', () => {
    renderWithDnd(
      <FileUploader
        acceptedFileTypes={acceptedFileTypes}
        onFilesSelected={onFilesSelected}
        multiple
      />
    );

    const fileInput = screen.getByTestId(
      'file-upload-input'
    ) as HTMLInputElement;
    expect(fileInput).toBeInTheDocument();

    const file1 = createFile('test1.png', 1000, 'image/png');
    const file2 = createFile('test2.jpeg', 2000, 'image/jpeg');

    fireEvent.change(fileInput, { target: { files: [file1, file2] } });

    expect(onFilesSelected).toHaveBeenCalledWith([file1, file2]);
    expect(onFilesRejected).not.toHaveBeenCalled();
  });

  test('should show ExtensionPanel when extension types is clicked', () => {
    renderWithDnd(
      <FileUploader
        acceptedFileTypes={acceptedFileTypes}
        onFilesSelected={onFilesSelected}
      />
    );

    const extensionButton = screen.getByTestId('extension-types');
    fireEvent.click(extensionButton);

    expect(screen.getByTestId('extension-panel')).toBeInTheDocument();
  });

  test('handles drag and drop with accepted files', () => {
    renderWithDnd(
      <FileUploader
        acceptedFileTypes={acceptedFileTypes}
        onFilesSelected={onFilesSelected}
        multiple
      />
    );

    const dropArea = screen.getByTestId('file-uploader');
    const file = createFile('dragged.png', 1500, 'image/png');
    const dataTransfer = new MockDataTransfer([file]);

    // Simulate drop
    fireEvent.dragEnter(dropArea, { dataTransfer });
    fireEvent.dragOver(dropArea, { dataTransfer });
    fireEvent.drop(dropArea, { dataTransfer });

    expect(onFilesSelected).toHaveBeenCalledWith([file]);
    expect(onFilesRejected).not.toHaveBeenCalled();
  });

  test('handles drag and drop with rejected files', () => {
    renderWithDnd(
      <FileUploader
        acceptedFileTypes={acceptedFileTypes}
        onFilesSelected={onFilesSelected}
        onFilesRejected={onFilesRejected}
        multiple
      />
    );

    const dropArea = screen.getByTestId('file-uploader');
    const validFile = createFile('dragged.png', 1500, 'image/png');
    const invalidFile = createFile('dragged.txt', 800, 'text/plain');
    const dataTransfer = new MockDataTransfer([validFile, invalidFile]);

    // Simulate drop
    fireEvent.dragEnter(dropArea, { dataTransfer });
    fireEvent.dragOver(dropArea, { dataTransfer });
    fireEvent.drop(dropArea, { dataTransfer });

    expect(onFilesSelected).toHaveBeenCalledWith([validFile]);
    expect(onFilesRejected).toHaveBeenCalledWith(1);
  });
});
