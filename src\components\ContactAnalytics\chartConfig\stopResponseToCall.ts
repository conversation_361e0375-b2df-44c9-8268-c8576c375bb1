import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    name: "responseToServiceCall"
                    field: "responseToServiceCall"
                    operator: "term"
                    limit: 10000
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'responseToCallAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stop Response To Call',
  filterTextType: 'Response To Call by Type',
  filterType: 'responseToServiceCall',
  filterTerms: {
    'Response to Call': ['true'],
    'Not Response to Call': ['false'],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = data.responseToCallAggregation.map((dateAgg) => {
      return {
        date: dateAgg.key_as_string,
        'Response to Call': 0,
        'Not Response to Call': 0,
        'Total Stops': dateAgg.doc_count,
        ...dateAgg.responseToServiceCall.buckets.reduce(
          (acc: { [key: string]: number }, b) => {
            if (b.key_as_string === 'true') {
              acc['Response to Call'] = b.doc_count;
            }
            if (b.key_as_string === 'false') {
              acc['Not Response to Call'] = b.doc_count;
            }
            return acc;
          },
          {} // this reduce need to return an object to update default values
        ),
      };
    });

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'Response to Call';
    series.dataFields.dateX = 'date';
    series.name = 'Response to Call';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series2 = chart.series.push(new am4charts.LineSeries());
    series2.strokeWidth = config.lineWidth;
    series2.dataFields.valueY = 'Not Response to Call';
    series2.dataFields.dateX = 'date';
    series2.name = 'Not Response to Call';
    series2.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series3 = chart.series.push(new am4charts.LineSeries());
    series3.strokeWidth = config.lineWidth;
    series3.dataFields.valueY = 'Total People';
    series3.dataFields.dateX = 'date';
    series3.name = 'Total People';
    series3.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  responseToCallAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
    responseToServiceCall: {
      buckets: { doc_count: number; key: number; key_as_string: string }[];
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
    };
  }[];
}
