/* cspell:words knexfile */

import knex from 'knex';
import knexfile from './knexfile';

function createDbConnection({
  databaseName,
  host,
  log,
  password,
  port,
  user,
}: {
  databaseName: string;
  host: string;
  log: Logger;
  password: string;
  port: number;
  user: string;
}) {
  log.debug('Creating MSSQL database connection', {
    databaseName,
    host,
    password,
    port: 1433,
    user,
  });

  const mergedConnection = {
    ...knexfile.connection,
    database: databaseName,
    host,
    password,
    port,
    user,
  };

  const mergedKnexfile = {
    ...knexfile,
    connection: mergedConnection,
  };

  const knexInstance = knex(mergedKnexfile);

  return Promise.resolve(knexInstance);
}

export default createDbConnection;
