import { mergedTdos } from './index';

const allTdos = [
  {
    id: '0000000001',
    name: 'testOne.xlsx',
    createdDateTime: '2022-08-29T13:31:40.312Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-ff1d-4673-94ba-fd2131cbe591',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000001_kaodhsQFdm',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000001_31mUnCgW0m',
      signedUri: 'https://testUri.net',
      contentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
  {
    id: '0000000002',
    name: 'testTwo.txt',
    createdDateTime: '2022-08-29T13:31:40.296Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-ff1d-4673-94ba-fd2131cbe591',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000002_Bi1wVmEbyn',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000002_3nygvQ73Yg',
      signedUri: 'https://testUri.net',
      contentType: 'text/plain',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
  {
    id: '0000000003',
    name: 'testThree.txt',
    createdDateTime: '2022-08-29T13:31:39.796Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-72cc-461b-8c78-1606d2b2ea0c',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000003_7VuBgNLoUC',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000003_YLXhMG66rB',
      signedUri: 'https://testUri.net',
      contentType: 'text/plain',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
];

const selectedTdos = [
  {
    id: '0000000001',
    name: 'testOne.xlsx',
    createdDateTime: '2022-08-29T13:31:40.312Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-1111-1111-1111-111111111111',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000001_kaodhsQFdm',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000001_31mUnCgW0m',
      signedUri: 'https://testUri.net',
      contentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
  {
    id: '0000000002',
    name: 'testTwo.txt',
    createdDateTime: '2022-08-29T13:31:40.296Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-1111-1111-1111-111111111111',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000002_Bi1wVmEbyn',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000002_3nygvQ73Yg',
      signedUri: 'https://testUri.net',
      contentType: 'text/plain',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
];

const wantedTdos = [
  {
    id: '0000000001',
    name: 'testOne.xlsx',
    createdDateTime: '2022-08-29T13:31:40.312Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-1111-1111-1111-111111111111',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000001_kaodhsQFdm',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000001_31mUnCgW0m',
      signedUri: 'https://testUri.net',
      contentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
  {
    id: '0000000002',
    name: 'testTwo.txt',
    createdDateTime: '2022-08-29T13:31:40.296Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-1111-1111-1111-111111111111',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000002_Bi1wVmEbyn',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000002_3nygvQ73Yg',
      signedUri: 'https://testUri.net',
      contentType: 'text/plain',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
  {
    id: '0000000003',
    name: 'testThree.txt',
    createdDateTime: '2022-08-29T13:31:39.796Z',
    startDateTime: '2022-08-29T13:31:34.377Z',
    stopDateTime: '2022-08-29T13:31:34.377Z',
    details: {},
    folders: [
      {
        treeObjectId: '12345678-72cc-461b-8c78-1606d2b2ea0c',
      },
    ],
    assets: {
      records: [
        {
          id: '0000000003_7VuBgNLoUC',
          signedUri: 'https://testUri.net',
          assetType: 'vtn-standard',
          contentType: 'application/json',
        },
      ],
    },
    primaryAsset: {
      id: '0000000003_YLXhMG66rB',
      signedUri: 'https://testUri.net',
      contentType: 'text/plain',
    },
    thumbnailUrl: 'https://testUrl.net',
    sourceImageUrl: null,
    previewUrl: 'https://testUrl.net',
    streams: [],
  },
];

describe('mergedTdos', () => {
  test('return empty tdos when both empty', () => {
    expect(mergedTdos(null, null)).toEqual([]);
    expect(mergedTdos([], [])).toEqual([]);
  });

  test('return allTdos when selectedTdos is empty and selectedTdos when allTdos is empty', () => {
    expect(mergedTdos(allTdos, null)).toEqual(allTdos);
    expect(mergedTdos(null, selectedTdos)).toEqual(selectedTdos);
  });

  test('return merged tdos', () => {
    expect(mergedTdos(allTdos, selectedTdos)).toEqual(wantedTdos);
  });
});
