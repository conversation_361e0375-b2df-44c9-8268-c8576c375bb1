import { forEachSeries } from 'p-iteration';
import { NoQuestionDefError } from '../errors';
import { Context } from '../../types';

const findQuestionDefinitions = async (context: Context) => {
  const { req, queries, data } = context;

  data.questionDefinitionIds = [];

  await forEachSeries(
    req.body.data,
    async (
      questionAnswer: {
        recordId: number;
        personNumber: number;
        answer: string;
        questionKey: string;
      },
      index: number
    ) => {
      const def = await queries.findQuestionDefinition({
        orgId: data.authorizedOrgId,
        resultPath: questionAnswer.questionKey,
      });

      if (!def?.id) {
        throw new NoQuestionDefError(
          `Question ${index} has no question definition for orgId: ${data.authorizedOrgId} and questionKey: ${questionAnswer.questionKey}`
        );
      }

      data.questionDefinitionIds.push(def.id);
    }
  );
};

export default findQuestionDefinitions;
