import React from 'react';
import { range } from 'lodash';
import { connect, ConnectedProps } from 'react-redux';
import Grid from '@mui/material/Grid2';
import Dialog from '@mui/material/Dialog';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import Slide from '@mui/material/Slide';
import RefreshIcon from '@mui/icons-material/Refresh';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';

import AppContainer from 'components/AppContainer';
import ContentContainer from 'components/ContentContainer';
import * as styles from './styles.scss';
import { TransitionProps } from '@mui/material/transitions';

function Transition(props: TransitionProps) {
  const { children, ...rest } = props;
  if (!React.isValidElement(children)) {
    return null;
  }
  return (
    <Slide direction="down" {...rest}>
      {children}
    </Slide>
  );
}

class ExampleTakeoverModal extends React.Component<Props> {
  state = {
    currentTab: 0,
  };

  handleChangeTab = (_event: React.ChangeEvent<object>, tabName: string) => {
    this.setState({ currentTab: tabName });
  };

  render() {
    return (
      <Dialog
        fullScreen
        open
        onClose={this.props.onClose}
        TransitionComponent={Transition}
      >
        <AppBar
          position="fixed"
          classes={{
            root: styles.appBar,
          }}
          color="primary"
        >
          <Toolbar
            classes={{
              root: styles.upperToolBar,
            }}
          >
            <Typography
              variant="inherit"
              color="inherit"
              className={styles.title}
            >
              Processed Details: 12345.MOV
            </Typography>
            <IconButton color="inherit" size="large">
              <RefreshIcon />
            </IconButton>
            <IconButton
              color="inherit"
              onClick={this.props.onClose}
              size="large"
            >
              <CloseIcon />
            </IconButton>
          </Toolbar>

          <Toolbar
            classes={{
              root: styles.lowerToolBar,
            }}
          >
            <Tabs
              indicatorColor="secondary"
              value={this.state.currentTab}
              onChange={this.handleChangeTab}
            >
              <Tab
                label="Categories"
                classes={{ root: styles.tab, selected: styles.selectedTab }}
                disableRipple
              />
              <Tab
                label="Tasks"
                classes={{ root: styles.tab, selected: styles.selectedTab }}
                disableRipple
              />
            </Tabs>
          </Toolbar>
        </AppBar>
        <AppContainer appBarOffset topBarOffset>
          <ContentContainer>
            <Grid container>
              <Grid size={{ xs: 12 }}>
                <ImageList
                  rowHeight={200}
                  cols={3}
                  sx={{ overflowY: 'hidden', mt: 0 }}
                >
                  {range(30).map((i) => (
                    <ImageListItem key={i}>
                      <img src={`https://picsum.photos/200?random&seed=${i}`} />
                    </ImageListItem>
                  ))}
                </ImageList>
              </Grid>
            </Grid>
          </ContentContainer>
        </AppContainer>
      </Dialog>
    );
  }
}

type Props = PropsFromRedux & {
  currentRoute: {
    modalOver: string;
  };
};

const mapState = null;

const mapDispatch = (dispatch: any, ownProps: any) => ({
  // navigate to the page this modal has "opened over"
  onClose: () => dispatch({ type: ownProps.currentRoute.modalOver }),
});

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(ExampleTakeoverModal);
