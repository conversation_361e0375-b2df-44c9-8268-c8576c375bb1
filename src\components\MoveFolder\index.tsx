import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { ConnectedProps, connect } from 'react-redux';
import FolderItem from '../SideBar/FolderItem';
import CircularProgress from '@mui/material/CircularProgress';
import * as styles from './styles.scss';
import {
  getMoveFolders,
  getRootFolderId,
  foldersFailedToFetch,
  getFetchingFolderRoot,
  getSelectedMoveFolderId,
} from 'state/modules/folders';
const MoveFolder = ({
  onClose,
  moveFolders,
  rootFolderId,
  onConfirm,
  fetchingFolderRoot,
  selectedMoveFolderId,
  openFolderIdUpload,
  type,
  open,
}: Props) => (
  <Dialog open={open} onClose={onClose} maxWidth="lg">
    <DialogTitle>
      {type === 'uploadFile' ? 'Select Folder' : 'Move Case'}
    </DialogTitle>
    <DialogContent>
      <div
        data-test="move-folder-dialog-content"
        className={styles['content-folder']}
      >
        {fetchingFolderRoot ? (
          <CircularProgress
            size={20}
            className={styles['loading-folder']}
            variant="indeterminate"
          />
        ) : (
          <FolderItem
            // safe due to folders = makeSubfolder with rootFolderId is the Key
            folder={moveFolders[rootFolderId]!}
            allFolders={moveFolders}
            selectedFolderId={
              type === 'uploadFile' ? openFolderIdUpload : selectedMoveFolderId
            }
            type={type}
          />
        )}
      </div>
    </DialogContent>
    <DialogActions>
      <Button
        data-test="move-folder-cancel-button"
        onClick={onClose}
        color="primary"
      >
        Cancel
      </Button>
      <Button
        data-test="move-folder-submit-button"
        onClick={onConfirm}
        color="primary"
        autoFocus
      >
        {type === 'uploadFile' ? 'Select' : 'Move'}
      </Button>
    </DialogActions>
  </Dialog>
);

type Props = PropsFromRedux & {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  openFolderIdUpload?: string;
  type?: string;
};

const mapState = (state: any) => ({
  moveFolders: getMoveFolders(state),
  rootFolderId: getRootFolderId(state),
  fetchingFolderRoot: getFetchingFolderRoot(state),
  error: foldersFailedToFetch(state),
  selectedMoveFolderId: getSelectedMoveFolderId(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(MoveFolder);
