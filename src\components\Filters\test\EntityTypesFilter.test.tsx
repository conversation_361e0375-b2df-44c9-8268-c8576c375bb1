import { EntityTypesFilter } from '../EntityTypesFilter';
import { render, fireEvent } from '@testing-library/react';

describe('EntityTypesFilter', function () {
  const defaultProps = {
    initialEntityNames: ['PERSON', 'ORGANIZATION', 'RELIGION'],
    entityValue: 'PERSON',
  };
  it('renders 4 FromControlLabel', function () {
    const { getAllByTestId } = render(<EntityTypesFilter {...defaultProps} />);
    expect(getAllByTestId('form-control-label')).toHaveLength(4);
  });
  it('onChange should update event', function () {
    const { getByRole } = render(<EntityTypesFilter {...defaultProps} />);
    const input = getByRole('radio', { name: 'PERSON' }) as HTMLInputElement;
    const event = {
      target: { value: 'PERSON' },
    };
    fireEvent.change(input, event);
    expect(input.value).toBe('PERSON');
  });
});
