{"name": "veritone-illuminate-app", "version": "2.0.9", "main": "index.js", "author": "", "license": "UNLICENSED", "now": {"version": 1, "alias": "illuminate-app.now.sh", "files": ["build-local"]}, "scripts": {"start": "webpack-dev-server --mode development", "startssl": "webpack-dev-server --mode development --server-type https --server-options-key local.veritone.com-key.pem --server-options-cert local.veritone.com.pem", "build": "webpack --mode production --output-path build-${ENVIRONMENT:-local}", "test": "jest", "test:watch": "jest --watch", "format": "prettier --write \"./**/*.{js,css,scss,md,json}\"", "lint": "concurrently \"yarn run lint:tsc\" \"yarn run lint:all\" \"yarn run lint:styles\"", "lint:ts": "eslint \"./src/**/*.{ts,tsx}\" --max-warnings 0", "lint:tsc": "tsc -p tsconfig.json --noEmit --checkJs false", "lint:js": "eslint \"./src/**/!(*.test).js\" --max-warnings 0", "lint:all": "eslint \"./src/**/!(*.test).{ts,tsx,js}\" \"./server/src/**/!(*.test).{ts,tsx,js}\"  \"./cypress/**/*.{ts,tsx,js}\" --max-warnings 0", "lint:fix": "eslint \"./src/**/!(*.test).{ts,tsx,js}\" \"./server/src/**/!(*.test).{ts,tsx,js}\"  \"./cypress/**/*.{ts,tsx,js}\" --max-warnings 0 --fix", "lint:styles": "stylelint \"src/**/*.scss\"", "lint:test": "eslint \"./test/**/*.{ts,tsx,js}\" \"./src/**/*.test.{ts,tsx,js}\"", "clean": "rm -rf .awcache/ && rm -rf node_modules/ && rm -f yarn.lock && yarn", "e2e": "cypress run --env ENVIRONMENT=azure-stage", "cy:open": "cypress open", "cy:run": "cypress run", "release": "release-it"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@badeball/cypress-cucumber-preprocessor": "^22.0.1", "@cspell/eslint-plugin": "^9.0.2", "@cypress/webpack-preprocessor": "^6.0.4", "@jambit/eslint-plugin-typed-redux-saga": "^0.4.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@sentry/webpack-plugin": "^3.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dom-to-image": "^2.6.7", "@types/jest": "^29.5.14", "@types/lodash": "4.17.20", "@types/lodash.throttle": "^4.1.9", "@types/luxon": "^3.4.2", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.31", "@types/pluralize": "^0.0.33", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-grid-layout": "^1.3.5", "@types/react-no-ssr": "^1.1.8", "@types/react-redux": "~7.1.34", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "^19.0.0", "@types/react-virtualized": "^9.22.2", "@types/react-virtualized-auto-sizer": "1.0.4", "@types/react-vis": "^1.11.15", "@types/react-window": "^1.8.8", "@types/recompose": "^0.30.15", "@types/redux-api-middleware": "^3.2.7", "@types/redux-first-router": "~2.1.12", "@types/redux-form": "~8.3.11", "@types/redux-logger": "~3.0.13", "@types/redux-mock-store": "^1.0.6", "@types/resize-observer-browser": "^0.1.11", "@types/shortid": "^2.2.0", "@types/uuid": "~10.0.0", "@types/wordcloud": "^1.2.2", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "babel-plugin-macros": "^3.1.0", "canvas": "^2.11.2", "concurrently": "^9.1.2", "copy-webpack-plugin": "^13.0.0", "csp-html-webpack-plugin": "^5.1.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "cypress": "^13.17.0", "cypress-file-upload": "^5.0.8", "cypress-promise": "^1.1.0", "eslint": "~9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-cypress": "^4.3.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "canary", "fork-ts-checker-webpack-plugin": "^9.1.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-module-name-mapper": "^0.1.5", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.5", "postcss-loader": "^8.1.1", "prettier": "^3.5.3", "react-refresh": "^0.17.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "stylelint": "^16.20.0", "stylelint-config-standard-scss": "^14.0.0", "tsconfig-paths-webpack-plugin": "^4.2.0", "typed-redux-saga": "~1.5.0", "typescript": "^5.7.3", "typescript-eslint": "^8.34.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "lint-staged": {"*.{js,css,scss,md,json}": ["prettier --write"]}, "dependencies": {"@amcharts/amcharts4": "^4.10.39", "@amcharts/amcharts4-fonts": "^4.0.1", "@date-io/moment": "^3.0.0", "@devexpress/dx-core": "^4.0.9", "@devexpress/dx-grid-core": "^4.0.9", "@devexpress/dx-react-core": "^4.0.9", "@devexpress/dx-react-grid": "^4.0.9", "@devexpress/dx-react-grid-material-ui": "^4.0.9", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/dosis": "^5.1.0", "@fontsource/nunito": "^5.1.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^6.1.2", "@mui/lab": "6.0.0-beta.31", "@mui/material": "^6.1.2", "@mui/styles": "^6.1.2", "@mui/system": "^6.1.2", "@mui/x-date-pickers": "^7.18.0", "@reduxjs/toolkit": "^1.9.7", "@sentry/react": "^9.0.0", "@veritone/glc-advanced-search-bar": "1.0.8", "@veritone/glc-react": "^1.2.6", "@veritone/glc-redux": "^1.0.15", "@veritone/react-components": "^3.2.20", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "daterangepicker": "^3.1.0", "dom-to-image": "^2.6.0", "fetch-retry": "^6.0.0", "handlebars": "^4.7.8", "html-webpack-template": "^6.2.0", "jss": "^10.10.0", "lodash.throttle": "^4.1.1", "luxon": "^3.5.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mui-datatables": "4.3.0", "path-browserify": "^1.0.1", "powerbi-client": "^2.23.1", "prop-types": "^15.8.1", "query-string": "^9.1.1", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-google-charts": "^5.2.1", "react-grid-layout": "^1.5.0", "react-jss": "^10.10.0", "react-no-ssr": "^1.1.0", "react-pdf": "^9.1.1", "react-redux": "7.2.9", "react-select": "^5.8.3", "react-universal-component": "^4.5.0", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.25", "react-vis": "^1.12.1", "react-window": "^1.8.10", "recharts": "~2.15.1", "redux": "~4.2.1", "redux-api-middleware": "^3.2.1", "redux-first-router": "^2.1.5", "redux-first-router-link": "^2.1.1", "redux-form": "^8.3.10", "redux-logger": "^3.0.6", "redux-mock-store": "^1.5.5", "redux-saga": "1.3.0", "redux-thunk": "^3.1.0", "release-it": "^19.0.3", "screenfull": "^6.0.2", "shortid": "2.2.17", "stream-browserify": "^3.0.0", "veritone-csp-generator": "^4.0.0", "video-react": "^0.16.0", "wordcloud": "timdream/wordcloud2.js"}, "resolutions": {"debug": "4.4.0", "fbjs": "^3.0.4", "d3-color": "3.1.0", "jsonwebtoken": "9.0.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "csp-html-webpack-plugin/cheerio": "1.0.0-rc.10", "@babel/runtime-corejs3": "^7.27.6", "@babel/core": "7.28.0", "@babel/runtime": "^7.27.6", "nanoid": "^3.3.8"}, "npm": {"publish": false}, "packageManager": "yarn@4.9.2", "stepDefinitions": "cypress/e2e/**/[filepath]/*.{js,ts}", "cypress-cucumber-preprocessor": {"json": {"enabled": true, "output": "cypress/reports/cucumber-json.json"}}}