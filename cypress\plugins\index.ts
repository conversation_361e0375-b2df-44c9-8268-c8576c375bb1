// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)
import { envData } from '../fixtures/envData';
import * as appConfig from '../../config.json';

export const addEnvToConfig = (
  _on: Cypress.PluginEvents,
  config: Cypress.PluginConfigOptions
): Cypress.PluginConfigOptions => {
  const env: string = config.env.ENVIRONMENT;
  // if (env === 'ENV') {
  //   if (!config.env.apiRoot) {
  //     console.error(
  //       `fail to find apiRoot in environment. config.env.apiRoot ${config.env.apiRoot}`
  //     );
  //     throw 'fail to find apiRoot in environment';
  //   }
  //   return config;
  // }
  if (env && envData[env]) {
    config.baseUrl = envData[env].baseUrl;
    config.env.apiRoot = envData[env].apiRoot;
  } else {
    // local
    if (!config.env.apiRoot) {
      // use config.json
      config.env.apiRoot = appConfig.apiRoot;
    }
  }
  return config;
};
