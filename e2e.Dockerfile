FROM cypress/base:22.16.0

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
ADD . /app
WORKDIR /app

RUN ls -a && \
    chmod +x /app/*.sh && \
    yarn && \
    yarn install
CMD yarn cypress run --record --key 1fcc04cb-667d-44f9-996c-38991949d0e5 --env ENVIRONMENT=$ENVIRONMENT
