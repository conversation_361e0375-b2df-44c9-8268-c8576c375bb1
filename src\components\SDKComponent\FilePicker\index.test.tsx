import { render } from '@testing-library/react';
import FilePicker from '.';

describe('FilePicker', () => {
  const onClose = jest.fn();
  const onPickFiles = jest.fn();

  it('should have a header', () => {
    const { getByTestId } = render(
      <FilePicker onPickFiles={onPickFiles} onRequestClose={onClose} />
    );
    expect(getByTestId('file-picker-title')).toBeInTheDocument();
  });

  it('should have a footer', () => {
    const { getByTestId } = render(
      <FilePicker onPickFiles={onPickFiles} onRequestClose={onClose} />
    );
    expect(getByTestId('file-picker-footer')).toBeInTheDocument();
  });

  it('should have a body', () => {
    const { getByTestId } = render(
      <FilePicker onPickFiles={onPickFiles} onRequestClose={onClose} />
    );
    expect(getByTestId('file-picker-body')).toBeInTheDocument();
  });
});
