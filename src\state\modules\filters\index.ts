import { get } from 'lodash';
import { ON_SEARCH_BAR_QUERY_CHANGE, ON_DATE_QUERY_CHANGE } from '../search';
import { createAction, createReducer } from '@reduxjs/toolkit';
import {
  Duration,
  EngineRun,
  DateFilter,
  UpdateFileTypes,
} from 'components/Filters';

export const UPDATE_DATE_FILTER = createAction<{
  id: string;
  filteredDate: string;
}>('date filters updated');
export const UPDATE_FILE_TYPES_FILTERS = createAction<{
  fileTypes: UpdateFileTypes;
}>('file type updated ');
export const UPDATE_ENTITY_TYPES_FILTERS = createAction<{
  entityType: string;
}>('entity type updated ');
export const UPDATE_TOPICS_FILTERS = createAction<{
  selectedTopics: string[];
}>('topics updated');
export const APPLY_FILTERS = createAction<{
  date: DateFilter;
  fileNames: string[];
  ids: string[];
  enginesRun: EngineRun[];
  duration: Duration;
}>('apply filters');

export const CLEAR_FILTERS = createAction('clear filters');

export const INITIALIZE_TOPICS = createAction<string[]>('initialize topics');
export const UPDATE_ENTITY_NAMES = createAction<string[]>(
  'update entity names'
);
export const UPDATE_FILE_NAMES_FILTER = createAction<{
  fileNames: string[];
}>('file names updated');
export const UPDATE_IDS_FILTER = createAction<{
  ids: string[];
}>('ids updated');
export const UPDATE_ENGINES_RUN = createAction<{
  enginesRun: { id: string; name: string }[];
}>('engines run updated');
export const UPDATE_DURATION_FILTER = createAction<{
  value: number;
  name: string;
}>('duration updated');
export const CLEAR_DURATION_DATA = createAction<{ type: string }>(
  'duration cleared'
);

export const INPUT_NAME_DURATION = {
  HOURSGT: 'hoursGt',
  MINUTESGT: 'minutesGt',
  SECONDSGT: 'secondsGt',
  HOURSLTE: 'hoursLte',
  MINUTESLTE: 'minutesLte',
  SECONDSLTE: 'secondsLte',
} as const;

export const DURATION_TYPE = {
  GT: 'gt',
  LTE: 'lte',
};

const defaultGt = {
  [INPUT_NAME_DURATION.HOURSGT]: 0,
  [INPUT_NAME_DURATION.MINUTESGT]: 0,
  [INPUT_NAME_DURATION.SECONDSGT]: 0,
};
const defaultLte = {
  [INPUT_NAME_DURATION.HOURSLTE]: 0,
  [INPUT_NAME_DURATION.MINUTESLTE]: 0,
  [INPUT_NAME_DURATION.SECONDSLTE]: 0,
};

export const FILE_TYPES_KEY = {
  VIDEO: 'video',
  AUDIO: 'audio',
  IMAGE: 'image',
  DOC: 'doc',
} as const;

const defaultState = {
  date: {
    startDate: '',
    endDate: '',
  },
  fileTypes: {
    [FILE_TYPES_KEY.VIDEO]: [] as string[],
    [FILE_TYPES_KEY.AUDIO]: [] as string[],
    [FILE_TYPES_KEY.IMAGE]: [] as string[],
    [FILE_TYPES_KEY.DOC]: [] as string[],
  },
  entityNames: [] as string[],
  entityType: '',
  topics: [] as string[],
  selectedTopics: [] as string[],
  fileNames: [] as string[],
  ids: [] as string[],
  enginesRun: [] as { id: string; name: string }[],
  duration: {
    ...defaultGt,
    ...defaultLte,
  },
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(UPDATE_DATE_FILTER, (state, action) => {
      const { id, filteredDate } = action.payload;
      const date = { ...state.date };
      if (id === 'startDate') {
        date.startDate = filteredDate;
      } else {
        date.endDate = filteredDate;
      }
      return {
        ...state,
        date,
      };
    })
    .addCase(UPDATE_FILE_TYPES_FILTERS, (state, action) => {
      const type = get(action, 'payload.fileTypes');
      return {
        ...state,
        fileTypes: {
          ...state.fileTypes,
          ...type,
        },
      };
    })
    .addCase(UPDATE_ENTITY_TYPES_FILTERS, (state, action) => {
      const entityType = get(action, 'payload.entityType');
      return {
        ...state,
        entityType,
      };
    })
    .addCase(INITIALIZE_TOPICS, (state, action) => {
      const topics = get(action, 'payload', []);
      return {
        ...state,
        topics,
      };
    })
    .addCase(UPDATE_TOPICS_FILTERS, (state, action) => {
      const selectedTopics = get(action, 'payload.selectedTopics', []);
      return {
        ...state,
        selectedTopics,
      };
    })
    .addCase(UPDATE_ENTITY_NAMES, (state, action) => {
      const getEntityNames = get(action, 'payload', []);
      const entityNames = [...new Set(getEntityNames)];
      return {
        ...state,
        entityNames,
      };
    })
    .addCase(UPDATE_FILE_NAMES_FILTER, (state, action) => {
      const fileNames = get(action, 'payload.fileNames', []);
      return {
        ...state,
        fileNames,
      };
    })
    .addCase(UPDATE_IDS_FILTER, (state, action) => {
      const ids = get(action, 'payload.ids', []);
      return {
        ...state,
        ids,
      };
    })
    .addCase(UPDATE_ENGINES_RUN, (state, action) => {
      const enginesRun = get(action, 'payload.enginesRun', []);
      return {
        ...state,
        enginesRun,
      };
    })
    .addCase(UPDATE_DURATION_FILTER, (state, action) => {
      const { value, name } = action.payload;
      return {
        ...state,
        duration: {
          ...state.duration,
          [name]: Number(value),
        },
      };
    })
    .addCase(CLEAR_DURATION_DATA, (state, action) => {
      const type = get(action, 'payload.type', '');
      if (type === DURATION_TYPE.GT) {
        return {
          ...state,
          duration: {
            ...state.duration,
            ...defaultGt,
          },
        };
      }
      if (type === DURATION_TYPE.LTE) {
        return {
          ...state,
          duration: {
            ...state.duration,
            ...defaultLte,
          },
        };
      }
      return {
        ...state,
      };
    })
    .addCase(CLEAR_FILTERS, (state) => {
      return {
        ...state,
        fileNames: [],
        ids: [],
        entityType: '',
        enginesRun: [],
        date: {
          startDate: '',
          endDate: '',
        },
        selectedTopics: [],
        fileTypes: {
          [FILE_TYPES_KEY.VIDEO]: [],
          [FILE_TYPES_KEY.AUDIO]: [],
          [FILE_TYPES_KEY.IMAGE]: [],
          [FILE_TYPES_KEY.DOC]: [],
        },
        duration: {
          ...defaultGt,
          ...defaultLte,
        },
      };
    });
});

export const applyFilters = (searchQuery: any) => (dispatch: any) => {
  dispatch(ON_SEARCH_BAR_QUERY_CHANGE({ searchQuery }));
};

export const addDateToSearchQuery =
  (_startDate: string, _endDate: string) => (dispatch: any) => {
    let dateQuery: any = {};
    if (_startDate || _endDate) {
      const startDate = updateDateUnix(_startDate);
      const endDate = updateDateUnix(_endDate) + 86399999; // Add 23h59min59secs because endDate should be at 23.59 PM
      dateQuery = {
        operator: 'range',
        field: 'absoluteStartTimeMs',
        gte: new Date(startDate).toISOString(),
        lte: new Date(endDate).toISOString(),
      };
      if (!startDate) {
        dateQuery.gte = '';
      }
    }
    dispatch(ON_DATE_QUERY_CHANGE(dateQuery));
  };

export const updateDateUnix = (value: string) => {
  const dateTypeObj = new Date(value);
  let dateTypeNum = dateTypeObj.getTime();
  if (!dateTypeNum) {
    dateTypeNum = 0;
  }
  return dateTypeNum;
};

export const updateFileNames = (fileNames: string[]) =>
  UPDATE_FILE_NAMES_FILTER({ fileNames });

export const updateIds = (ids: string[]) => UPDATE_IDS_FILTER({ ids });

export const updateEnginesRun = (enginesRun: { id: string; name: string }[]) =>
  UPDATE_ENGINES_RUN({ enginesRun });

export const updateDuration = (value: number, name: string) =>
  UPDATE_DURATION_FILTER({ value, name });

export const clearDuration = (type: string) => CLEAR_DURATION_DATA({ type });

export const namespace = 'filters';
export const local = (state: any) => state[namespace] as typeof defaultState;
export default reducer;
export const getFilterState = (state: any) => local(state);
export const getFilteredDate = (state: any) => local(state).date;
export const getFilteredFileType = (state: any) => local(state).fileTypes;
export const getFilteredEntityType = (state: any) => local(state).entityType;
export const getEntityNames = (state: any) => local(state).entityNames;
export const getTopics = (state: any) => local(state).topics;
export const getSelectedTopics = (state: any) => local(state).selectedTopics;
export const getFileNames = (state: any) => local(state).fileNames;
export const getIds = (state: any) => local(state).ids;
export const getEnginesRun = (state: any) => local(state).enginesRun;
export const getDuration = (state: any) => local(state).duration;
