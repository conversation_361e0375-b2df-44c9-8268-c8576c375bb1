import { configureStore } from '@reduxjs/toolkit';

import UploadFile, { fixFileMissingType } from '.';
import upLoadFileReducer, {
  namespace as uploadFileNamespace,
} from 'modules/uploadFile';
import { fireEvent, render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';

const mockStore = (initialState: any) => {
  return configureStore({
    reducer: {
      [uploadFileNamespace]: upLoadFileReducer,
    },
    preloadedState: initialState,
  });
};

const initialState = {
  config: {
    simpleCognitiveWorkflowDefaultEngineId: {},
  },
  uploadFile: {
    open: true,
    state: 'overview',
    progressPercentByFileKey: {},
    success: false,
    error: false,
    warning: false,
    uploadResult: [
      {
        key: '1/other/2024/9/4/_/Shoplifter-8-32-224_eaec86a4-87f5-47b8-a3d4-331ce8f3873f.mp4',
        bucket: 'api',
        expiresInSeconds: 86400,
        fileName: 'Shoplifter.mp4',
        size: 1912001,
        type: 'video/mp4',
        error: false,
        unsignedUrl:
          'https://vtstorcorestage.blob.core.usgovcloudapi.net/api/1%2Fother%2F2024%2F9%2F4%2F_%2FShoplifter-8-32-224_eaec86a4-87f5-47b8-a3d4-331ce8f3873f.mp4',
        getUrl:
          'https://vtstorcorestage.blob.core.usgovcloudapi.net/api/1%2Fother%2F2024%2F9%2F4%2F_%2FShoplifter-8-32-224_eaec86a4-87f5-47b8-a3d4-331ce8f3873f.mp4?sv=2019-02-02&se=2024-10-11T08%3A12%3A32Z&sr=c&sp=r&sig=wJ1X1oljLB5lBS8u8xdv2KOL2759vRcqjnvZwyk%2BS8w%3D',
        file: {},
      },
    ],
    checkedFile: [],
    currentEngineCategory: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    uploadResultEdit: {
      getUrlProgramImage: '',
      programLiveImage: '',
      getUrlProgramLiveImage: '',
      uploadResultId: [],
      fileName: '',
      dateTime: '',
      tagsEdit: [],
    },
    isOpenModalUpload: true,
    loadingSaveUpload: false,
    enginesSelected: [],
    contentTemplateSelected: [],
    contentTemplates: [
      {
        id: '1',
        name: 'tracker-match-group',
        description: 'Tracker Match Group v2',
        schemas: {
          records: [
            {
              status: 'published',
              definition: {
                type: 'object',
                title: 'tracker-match-group',
                required: ['id', 'name', 'eventId'],
                properties: {
                  id: {
                    type: 'string',
                  },
                  name: {
                    type: 'string',
                  },
                  eventId: {
                    type: 'string',
                  },
                },
              },
            },
          ],
        },
      },
    ],
    tagsCustomize: [],
    loadingProcessingJobs: false,
    noDataProcessing: false,
    isReprocess: false,
    tdoIdsNotSupported: [],
    tdoIdsSupported: [],
    enginesSelectedSupported: [],
    isShowConfirmSaveTemplate: false,
    templateName: '',
    templateSelected: '',
    loadingRemoveTemplate: false,
    countProcess: 0,
    totalReprocess: 0,
    openModalProcess: false,
    percentageFilesUploaded: 0,
    currentPage: 0,
    pageSize: 10,
    offsetPage: 0,
    statusFilter: [],
    dateTimeRangeFilter: 2,
  },
};

describe('fixFileMissingType', () => {
  it('Should fix missing vob file type', () => {
    const fileName = 'abc.vob';
    const file = new File([new Blob()], fileName);
    const got = fixFileMissingType(file);
    expect(got.name).toBe(fileName);
    expect(got.type).toBe('video/mpeg');
  });

  it('Should handle uppercase vob file', () => {
    const fileName = 'abc.VOB';
    const file = new File([new Blob()], fileName);
    const got = fixFileMissingType(file);
    expect(got.name).toBe(fileName);
    expect(got.type).toBe('video/mpeg');
  });

  it('Should handle dot in filename', () => {
    const fileName = 'abc.efc.vob';
    const file = new File([new Blob()], fileName);
    const got = fixFileMissingType(file);
    expect(got.name).toBe(fileName);
    expect(got.type).toBe('video/mpeg');
  });

  it('Should set file type to application/octet-stream for non vob file missing type', () => {
    const fileName = 'abc.mp3';
    const file = new File([new Blob()], fileName);
    const got = fixFileMissingType(file);
    expect(got.name).toBe(fileName);
    expect(got.type).toBe('application/octet-stream');
  });

  it('Should return non vob file with type', () => {
    const fileName = 'abc.mp3';
    const file = new File([new Blob()], fileName, { type: 'audio/mpeg' });
    const got = fixFileMissingType(file);
    expect(got).toBe(file);
  });

  it('Should handle file no extension', () => {
    const fileName = 'abc';
    const file = new File([new Blob()], fileName, { type: 'audio/mpeg' });
    const got = fixFileMissingType(file);
    expect(got).toBe(file);
  });
});

describe('uploadFile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not allow next step without empty required fields', () => {
    const store = mockStore(initialState);

    render(
      <Provider store={store}>
        <UploadFile />
      </Provider>
    );

    // click next button when have uploaded files
    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i,
      })
    );

    // open advanced cognitive workflow
    expect(
      screen.getByText(/show advanced cognitive workflow/i)
    ).toBeInTheDocument();
    fireEvent.click(screen.getByText(/show advanced cognitive workflow/i));

    // click next button to open content template tab
    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i,
      })
    );
    expect(screen.getByTestId('add-content-template')).toBeInTheDocument();

    // show content template and change required fields value
    fireEvent.click(screen.getByTestId('add-content-template'));
    const requiredFields = screen.getAllByTestId(
      'content-template-field'
    ) as HTMLInputElement[];
    expect(requiredFields.length).toBe(3);

    const idField = requiredFields[0];
    const nameField = requiredFields[1];
    const eventIdField = requiredFields[2];

    if (idField && nameField && eventIdField) {
      fireEvent.change(idField, { target: { value: '1' } });
      fireEvent.change(nameField, { target: { value: 'test name' } });
      fireEvent.change(eventIdField, { target: { value: '3' } });
    }

    // click next button and expect to open customize tab
    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i,
      })
    );
    expect(screen.getByText(/ingestion customization/i)).toBeInTheDocument();

    // click back button to change required field value to empty
    fireEvent.click(
      screen.getByRole('button', {
        name: /back/i,
      })
    );
    const idFieldEmpty = screen.getAllByTestId(
      'content-template-field'
    )[0] as HTMLInputElement;
    fireEvent.change(idFieldEmpty, { target: { value: '' } });

    // click next button and expect to stay in template tab
    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i,
      })
    );
    expect(
      screen.queryByText(/ingestion customization/i)
    ).not.toBeInTheDocument();
  });
});
