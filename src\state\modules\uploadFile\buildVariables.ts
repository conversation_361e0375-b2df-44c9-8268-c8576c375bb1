import { get } from 'lodash';
import { Engine, Library } from './models';
interface InputProps {
  fields: {
    fieldName: string;
    fieldValue: string;
  }[];
  engineId: string;
  uploadUrl?: string;
  targetId?: string;
}
export function buildVariables(
  engine: Engine,
  defaultClusterId: string,
  uploadUrl: string,
  dataLibraries: { [key: string]: Library },
  targetId: string,
  isReprocess: boolean,
  fileName: string,
  isTranslationEngine: boolean,
  hasVtnMode: boolean,
  fileType: string,
  treeObjectId: string,
  hasTextExtractionFolderID: boolean
) {
  const payloadEngine = engine.fields.map((item) => ({
    fieldName: item.name,
    fieldValue: item.defaultValue || '',
  }));
  const fields = [buildClusterId(defaultClusterId), ...payloadEngine];
  if (engine.priority) {
    fields.push({
      fieldName: 'priority',
      fieldValue: engine.priority + '',
    });
  }
  const input: InputProps = {
    fields,
    engineId: engine.id,
  };
  if (!isReprocess || (uploadUrl && isTranslationEngine)) {
    input.uploadUrl = uploadUrl;
    input.fields.push(buildTdoName(fileName));
  }
  if (targetId) {
    input.targetId = targetId;
  }
  const library = buildLibrary(dataLibraries, engine);
  if (library.length) {
    input.fields.push(...library);
  }
  if (isImage(fileType)) {
    input.fields.push(buildEngineImage());
  }
  if (isTranslationEngine) {
    input.fields.push(buildIndexForTranslationEngine());
  }
  if (hasVtnMode) {
    input.fields.push(buildVtnModeForTranslationEngine());
  }
  if (hasTextExtractionFolderID) {
    input.fields.push(buildTextExtractionFolderID(treeObjectId));
  }
  return {
    input,
  };
}
function buildClusterId(defaultClusterId: string) {
  return {
    fieldName: 'clusterId',
    fieldValue: defaultClusterId,
  };
}
function buildLibrary(libraries: { [key: string]: Library }, engine: Engine) {
  const library: any = Object.values(libraries).find(
    (element) => element.name === engine.librariesSelected
  );
  const { records = [] } = get(library, 'engineModels', {});
  const libraryEngineMode: any =
    Array.isArray(records) &&
    records.filter((item) => item.engineId === engine.id);
  if (hasLibrary(engine) && libraryEngineMode.length) {
    return [
      {
        fieldName: 'libraryEngineModelId',
        fieldValue: libraryEngineMode.length && libraryEngineMode[0].id,
      },
      {
        fieldName: 'libraryId',
        fieldValue: library.id,
      },
    ];
  }
  return [];
}
function hasLibrary(engine: Engine) {
  return (
    engine.libraryRequired &&
    engine.librariesSelected &&
    engine.librariesSelected.length
  );
}
function buildEngineImage() {
  return {
    fieldName: 'inputIsImage',
    fieldValue: 'true',
  };
}

function isImage(fileType: string) {
  return fileType.includes('image');
}

function buildTdoName(tdoName: string) {
  return {
    fieldName: 'tdoName',
    fieldValue: tdoName,
  };
}
function buildIndexForTranslationEngine() {
  return {
    fieldName: 'addToIndex',
    fieldValue: 'true',
  };
}
function buildVtnModeForTranslationEngine() {
  return {
    fieldName: 'vtnMode',
    fieldValue: 'true',
  };
}
function buildTextExtractionFolderID(treeObjectId: string) {
  return {
    fieldName: 'textExtractionFolderID',
    fieldValue: treeObjectId,
  };
}
