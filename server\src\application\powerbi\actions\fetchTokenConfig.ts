import { TokenConfigurationRow } from '../definitions';
import { DatabaseError, OrgHasNoTokenRegisteredError } from '../errors';
import { Context } from '../../types';

const fetchTokenConfig = async (context: Context) => {
  const { log, data, queries } = context;

  try {
    const orgId = data.authorizedOrgId ?? data.orgId;
    if (!orgId) {
      log.error(`orgId is not provided to action`);
      throw new Error('`orgId is not provided to action');
    }

    return queries
      .getTokenConfig(orgId)
      .then((tokenConfig: TokenConfigurationRow) => {
        if (!tokenConfig) {
          log.error(`OrgHasNoTokenRegisteredError: Org ${orgId}`);
          throw new OrgHasNoTokenRegisteredError();
        }

        data.workspaceId = tokenConfig.workspaceId;
        data.reportId = tokenConfig.reportId;
        data.datasetId = tokenConfig.datasetId;
        data.profileId = tokenConfig.profileId;
        data.embedUrl = tokenConfig.embedUrl;
        data.lifetimeInMinutes = tokenConfig.lifetimeInMinutes;

        return context;
      });
  } catch (e) {
    log.error('FetchTokenConfig failed', e);
    throw new DatabaseError(e);
  }
};

export default fetchTokenConfig;
