import React, { Fragment, KeyboardEvent } from 'react';
import { debounce, isEmpty, get } from 'lodash';
import { ConnectedProps, connect } from 'react-redux';
import Dialog from '@mui/material/Dialog';
// import {
//   FilePicker as FilePickerComponent,
//   /* @ts-ignore: the type from shared component is wrong */
//   FileProgressDialog,
// } from '@veritone/glc-react';
import FilePickerComponent from '../SDKComponent/FilePicker';
import FileProgressDialog from '../SDKComponent/FilePicker/FileProgressDialog';

import * as filePickerModule from '../../state/modules/uploadFile';
import * as uploadFileAction from '../../state/modules/uploadFile/actions';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import { withStyles, ClassNameMap } from '@mui/styles';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import MuiDialogActions from '@mui/material/DialogActions';
import CloudUpload from '@mui/icons-material/CloudUpload';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import { DateTime } from 'luxon';
import styles from './styles';
import EditFileUpload from './editFile';
import SaveTemplate from './saveTemplate';
import MoveFolder from '../MoveFolder';
import Processing from './processing';
import SimpleProcessing from './simpleProcessing';
import FileUpload from './fileUpload';
import ContentTemplate from './contentTemplate';
import Customize from './customize';
import ProcessLimitExceededAlertDialog from './processLimitExceededAlertDialog';
import {
  Engine,
  isUploadFileSelectionType,
} from '../../state/modules/uploadFile/models';

const DialogTitle = withStyles(styles)((props: DialogTitleProps) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle {...other}>
      <Typography variant="h6" className={classes.title}>
        {children}
      </Typography>
      {onClose ? (
        <IconButton
          aria-label="close upload"
          className={classes.iconClose}
          onClick={onClose}
          size="large"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});
interface DialogTitleProps {
  children: React.ReactNode;
  classes: ClassNameMap;
  onClose: () => void;
  style: object;
}
const DialogContent = withStyles(() => ({
  root: {
    padding: 10,
  },
}))(MuiDialogContent);

const DialogActions = withStyles(() => ({
  root: {
    margin: '0 100px 0 0',
    padding: 10,
  },
}))(MuiDialogActions);

export function fixFileMissingType(file: File): File {
  /* Fixes a bug where macOS and Windows cannot identify certain file types */
  /* Check if file has extension .vob */
  let ext: string | null = null;
  const index = file.name.lastIndexOf('.');
  if (index > 0) {
    ext = file.name.substring(index);
  }
  if (ext && ext.toLowerCase() === '.vob') {
    return new File([file], file.name, { type: 'video/mpeg' });
  } else if (!file.type) {
    return new File([file], file.name, { type: 'application/octet-stream' });
  }
  return file;
}

class UploadFile extends React.Component<Props> {
  static defaultProps = {};
  constructor(props: Props) {
    super(props);
    this.onChangeTemplateName = debounce(this.onChangeTemplateName, 500);
    this.onChangeSearchEngine = debounce(this.onChangeSearchEngine, 500);
    this.onChangeContentTemplate = debounce(this.onChangeContentTemplate, 500);
    this.onChangeFormEngine = debounce(this.onChangeFormEngine, 500);
  }
  state = {
    activeStep: 0,
    skipped: new Set(),
    currentScreen: 'overviewUpload',
    showAdvancedCognitive: this.showAdvancedCognitive,
    engineNameSearch: '',
    checkValidateTemplate: false,
    isOpenFolder: false,
    tagsCustomizeName: '',
    tagsEditFileUpload: '',
    typePick: '',
    checkValidateLibrary: false,
    totalFileSelected: 0,
  };

  handlePick = (event: React.MouseEvent<HTMLElement>) => {
    const type = event.currentTarget.getAttribute('data-type') || '';
    this.props.pick(type);
    this.setState({ currentScreen: 'selectFile', typePick: type });
  };

  cancel = () => {
    this.props.endPick('');
    this.setState({ currentScreen: 'overviewUpload' });
  };

  onFilesSelected = (files: File[]) => {
    const fixedFiles = files.map((f) => fixFileMissingType(f));
    this.setState({ totalFileSelected: fixedFiles.length });
    this.props.uploadRequest({
      files: fixedFiles,
      callback: this.props.onPick,
    });
  };

  renderPickerDialog = () => {
    const { typePick } = this.state;
    return (
      <Dialog open={this.props.open} data-testid="file-picker-dialog">
        <FilePickerComponent
          // {...this.props}
          onRequestClose={this.cancel}
          onPickFiles={this.onFilesSelected}
          multiple
          accept={typePick !== 'uploadFile' ? ['image/*'] : []}
        />
      </Dialog>
    );
  };

  handleRetryDone = () => {
    const { onPick, retryDone } = this.props;
    retryDone && retryDone(onPick);
  };

  handleRetry = () => {
    const { retryRequest, onPick } = this.props;
    retryRequest && retryRequest(onPick);
  };

  handleAbort = (fileKey?: string) => {
    const { abortRequest } = this.props;
    abortRequest && abortRequest(fileKey);
  };

  renderProgressDialog = () => {
    const { classes, success, error, warning, percentByFiles, statusMessage } =
      this.props;
    let totalFileUploaded = 0;
    this.props.percentByFiles.forEach((item) => {
      if (item.value.percent === 100) {
        totalFileUploaded += 1;
      }
    });
    const completeStatus = success
      ? 'success'
      : error
        ? 'failure'
        : warning
          ? 'warning'
          : null;

    return (
      <Dialog open={this.props.open} data-testid="file-progress-dialog">
        <FileProgressDialog
          height={450}
          width={600}
          onClose={this.cancel}
          percentByFiles={percentByFiles}
          progressMessage={statusMessage}
          handleAbort={this.handleAbort}
          retryRequest={this.handleRetry}
          onRetryDone={this.handleRetryDone}
          completeStatus={completeStatus}
          classes={{ container: classes.processDialogContainer }}
        />
        <Typography color="inherit" className={classes.textUploadingProcess}>
          Uploaded {totalFileUploaded}/{this.state.totalFileSelected} files
        </Typography>
      </Dialog>
    );
  };

  handleCloseModalUpload = () => {
    const { closeModalUpload } = this.props;
    closeModalUpload();
    this.setDefaultState();
  };

  setDefaultState = () => {
    this.setState({
      activeStep: 0,
      engineNameSearch: '',
      showAdvancedCognitive: this.showAdvancedCognitive,
      tagsEditFileUpload: '',
      tagsCustomizeName: '',
    });
  };

  getSteps = (showAdvancedCognitive: boolean) => {
    const steps = ['File Upload', 'Processing'];
    if (!showAdvancedCognitive) {
      return steps;
    }
    return steps.concat(['Content Templates', 'Customize']);
  };
  isStepOptional = (step: number) => {
    return step === 1;
  };
  handleNext = () => {
    const { skipped, activeStep } = this.state;
    const { contentTemplateSelected, saveUploadFile, enginesSelected } =
      this.props;
    const newSkipped = skipped;
    let nextStep = false;
    if (activeStep === 3) {
      saveUploadFile('', 'advanced');
      this.setDefaultState();
    } else {
      const contentTemplateValidate = contentTemplateSelected.filter(
        (item) => Array.isArray(item.validate) && item.validate.length
      );
      const engines: Engine[] = [];
      enginesSelected.forEach((element) => {
        element.engineIds.forEach((engine) => {
          engines.push(engine);
        });
      });
      const enginesHasLibrary = engines.filter((item) => item.libraryRequired);
      const validateEngine = [];
      if (enginesHasLibrary.length) {
        enginesHasLibrary.forEach((item) => {
          if (!item.librariesSelected) {
            validateEngine.push(item.id);
          }
        });
      }

      if (activeStep === 1 && validateEngine.length) {
        this.setState({ checkValidateLibrary: true });
      } else {
        if (!contentTemplateValidate.length) {
          nextStep = true;
        }
        if (activeStep !== 2 || nextStep || !contentTemplateSelected.length) {
          this.setState((prevActiveStep) => ({
            ...prevActiveStep,
            activeStep: activeStep + 1,
            skipped: newSkipped,
          }));
        } else {
          this.setState((prevActiveStep) => ({
            ...prevActiveStep,
            checkValidateTemplate: true,
          }));
        }
      }
    }
  };
  handleBack = () => {
    const { activeStep } = this.state;
    this.setState((prevActiveStep) => ({
      ...prevActiveStep,
      activeStep: activeStep - 1,
    }));
  };

  overviewUploadFile = () => {
    const { classes } = this.props;
    return (
      <Fragment>
        <div className={classes.uploadHeader}>
          <div className={classes.iconUploadBody} data-testid="cloud-upload">
            <CloudUpload />
          </div>
          <div
            className={classes.titleUpload}
            data-type={'uploadFile'}
            onClick={this.handlePick}
            data-test="upload-media"
            data-testid="upload-media"
          >
            Upload Media
          </div>
          <div className={classes.titleSelect}>
            Select Video, Audio, Image, or Text files to upload
          </div>
        </div>
        <div className={classes.uploadFoolter}>
          <span>Recommended file formats:</span>
          <span>.mp4, .mp3, .jpg, and .png</span>
        </div>
      </Fragment>
    );
  };
  listFile = () => {
    return <Fragment />;
  };
  handleToggle = (event: React.MouseEvent<HTMLElement>) => {
    // the type can be "all" or "single". "all" is for the checkbox in the header
    // "single" is for the checkbox in each row.
    // when the type is "all", the key is always 0
    // when the type is "single", the key is the index of the file in the list
    const key = event.currentTarget.getAttribute('data-key');
    const type = event.currentTarget.getAttribute('data-type');
    if (!type || !isUploadFileSelectionType(type)) {
      return;
    }
    const { onSelectionChange } = this.props;
    onSelectionChange(Number(key), type);
  };
  handleRemoveFile = () => {
    const { removeFileUpload, checkedFile } = this.props;
    removeFileUpload(checkedFile);
  };
  handleEditFile = () => {
    const { showEditFileUpload } = this.props;
    showEditFileUpload();
  };
  handleCloseEditFileUpload = () => {
    const { hideEditFileUpload } = this.props;
    hideEditFileUpload();
  };
  handleSaveEditFileUpload = () => {
    const { saveEditFileUpload } = this.props;
    saveEditFileUpload();
  };
  onChangeDateTime = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { onChangeDateTimeEdit } = this.props;
    const { value } = event.target;
    onChangeDateTimeEdit(value);
  };
  onChangeFileName = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { onChangeFileNameEdit } = this.props;
    const { value } = event.target;
    onChangeFileNameEdit(value);
  };
  handleShowAdvancedCognitive = () => {
    const { showAdvancedCognitive } = this.state;
    this.setState({ showAdvancedCognitive: !showAdvancedCognitive });
  };
  handleChangeEngine = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { onChangeEngine } = this.props;
    onChangeEngine(event.target.value);
  };
  handleAddEngine = (event: React.MouseEvent<HTMLElement>) => {
    const { addEngine } = this.props;
    const engineId = event.currentTarget.getAttribute('data-id') || '';
    addEngine(engineId);
  };
  handleRemoveEngine = (event: React.MouseEvent<HTMLElement>) => {
    const engineId = event.currentTarget.getAttribute('data-id') || '';
    const { removeEngine } = this.props;
    removeEngine(engineId);
  };
  handleSearchEngine = (event: React.ChangeEvent<HTMLInputElement>) => {
    const engineName = event.target.value;
    this.onChangeSearchEngine(engineName);
  };
  onChangeSearchEngine = (engineNameSearch: string) => {
    this.setState({ engineNameSearch });
  };
  handleShowModalSaveTemplate = () => {
    const { showModalSaveTemplate } = this.props;
    showModalSaveTemplate(true);
  };
  handleHideModalSaveTemplate = () => {
    const { hideModalSaveTemplate } = this.props;
    hideModalSaveTemplate(false);
  };
  handleSaveTemplate = () => {
    const { saveTemplate } = this.props;
    saveTemplate();
  };
  hanldeOnChangeTemPlate = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.onChangeTemplateName(event.target.value);
  };
  onChangeTemplateName = (templateName: string) => {
    const { onChangeTemplateName } = this.props;
    onChangeTemplateName(templateName);
  };
  handleChangeTemplates = (event: React.ChangeEvent<HTMLInputElement>) => {
    const templateId = event.target.value;
    const { onChangeTemplate } = this.props;
    onChangeTemplate(templateId);
  };
  handleAddContentTemplate = (event: React.MouseEvent<HTMLElement>) => {
    const { addContentTemplate } = this.props;
    const contentTemplateId = event.currentTarget.getAttribute('data-id') || '';
    addContentTemplate(contentTemplateId);
  };
  removeContentTemplate = (event: React.MouseEvent<HTMLElement>) => {
    const { removeContentTemplate } = this.props;
    const contentTemplateId = event.currentTarget.getAttribute('data-id') || '';
    removeContentTemplate(contentTemplateId);
  };
  handleChangeContentTemplate = ({
    value,
    contentTemplateId,
    name,
  }: {
    value: DateTime | null | string;
    contentTemplateId: string;
    name: string;
  }) => {
    const { onChangeFormContentTemplate } = this.props;
    onChangeFormContentTemplate(contentTemplateId, name, value);
  };

  onChangeContentTemplate = (
    contentTemplateId: string,
    name: string,
    value: string
  ) => {
    const { onChangeFormContentTemplate } = this.props;
    onChangeFormContentTemplate(contentTemplateId, name, value);
  };

  handleOpenFolder = () => {
    const { fetchFolderUpload } = this.props;
    this.setState((prevState) => ({
      ...prevState,
      isOpenFolder: true,
    }));
    fetchFolderUpload();
  };

  handleCloseFolder = () => {
    this.setState({
      isOpenFolder: false,
    });
  };
  onSelect = () => {
    const { selectFolder } = this.props;
    selectFolder();
    this.handleCloseFolder();
  };
  onKeyPress = (event: KeyboardEvent<HTMLInputElement>) => {
    const { id: type, value } = event.target as HTMLInputElement;
    if (event.charCode === 13) {
      this.addTags(value, type);
    }
  };
  onClickAddTags = (event: React.MouseEvent<HTMLElement>) => {
    const type = event.currentTarget.getAttribute('data-type') || '';
    const { tagsCustomizeName, tagsEditFileUpload } = this.state;
    const value =
      type === 'editFileUpload' ? tagsEditFileUpload : tagsCustomizeName;
    this.addTags(value, type);
  };
  addTags = (value: string, type: string) => {
    const { addTagsCustomize } = this.props;
    if (value) {
      addTagsCustomize(value, type);
      if (type === 'editFileUpload') {
        this.setState({ tagsEditFileUpload: '' });
      } else {
        this.setState({ tagsCustomizeName: '' });
      }
    }
  };
  handleOnChangeTagsCustomize = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { id, value } = event.target;
    if (id === 'editFileUpload') {
      this.setState({ tagsEditFileUpload: value });
    } else {
      this.setState({ tagsCustomizeName: value });
    }
  };
  handleRemoveTagsCustomize = (name: string, type: string) => {
    const { removeTagsCustomize } = this.props;
    removeTagsCustomize(name, type);
  };

  handleExpandClick = (event: React.MouseEvent<HTMLElement>) => {
    const expand = event.currentTarget.getAttribute('data-expand');
    const engineId = event.currentTarget.getAttribute('data-engine-id');
    const { onChangeExpand } = this.props;
    onChangeExpand(engineId, expand === 'true' ? true : false);
  };

  handleChangeFieldsEngine = (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => {
    const { value, name } = event.target;
    this.onChangeFormEngine(engineId, name, value);
  };

  handleChangeJobPriority = (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => {
    const { value } = event.target;
    const priorityInt = parseInt(value);
    const priority = Number.isNaN(priorityInt) ? undefined : priorityInt;
    this.props.onChangeJobPriorityEngineSelected(engineId, priority);
  };
  onChangeFormEngine = (engineId: string, name: string, value: string) => {
    const { onChangeFormEngineSelected } = this.props;
    onChangeFormEngineSelected(engineId, name, value);
  };

  handleChangeLibrariesEngineSelected = (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => {
    event.stopPropagation();
    const { value } = event.target;
    const { onChangeLibrariesEngineSelected } = this.props;
    onChangeLibrariesEngineSelected(engineId, value);
    this.setState({
      checkValidateLibrary: false,
    });
  };

  handleRunProcess = () => {
    const { checkLimitAndSaveReprocessFile, updateDagTemplateIdSelected } =
      this.props;
    updateDagTemplateIdSelected('');
    checkLimitAndSaveReprocessFile();
    this.setState({ engineNameSearch: '' });
  };

  handleCloseProcessLimitExceededAlertDialog = () => {
    const { showModalProcessLimitExceeded } = this.props;
    showModalProcessLimitExceeded(false);
  };

  handleRemoveTemplate = (event: React.MouseEvent<HTMLElement>, id: string) => {
    event.stopPropagation();
    const { removeTemplate } = this.props;
    removeTemplate(id);
  };

  handleClickDagTemplate = (id: string | null, categoryId: string) => {
    const { onClickDagTemplate, dagTemplates } = this.props;
    const dagTemplateSelected = dagTemplates.find(
      (dagTemplate) => dagTemplate.id === id
    );
    if (dagTemplateSelected) {
      onClickDagTemplate(dagTemplateSelected, categoryId);
    }
  };

  handleRunJobTemplate = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const {
      saveUploadFile,
      isReprocess,
      updateDagTemplateIdSelected,
      checkLimitAndSaveReprocessFile,
      dagTemplateByCategorySelected,
    } = this.props;
    const categoryId = event.currentTarget.getAttribute('data-id') || '';
    const selectedDagTemplateId =
      (!isEmpty(dagTemplateByCategorySelected[categoryId]) &&
        dagTemplateByCategorySelected[categoryId]!.id) || // Safe due to isEmpty check
      '';
    this.setState({
      activeStep: 0,
    });
    if (isReprocess) {
      updateDagTemplateIdSelected(selectedDagTemplateId);
      return checkLimitAndSaveReprocessFile();
    }
    return saveUploadFile(selectedDagTemplateId, 'simple');
  };
  get showAdvancedCognitive() {
    const { defaultWorkflowSetting } = this.props;
    return defaultWorkflowSetting === 'advanced' ? true : false;
  }
  render() {
    const pickerComponent = {
      overview: this.overviewUploadFile,
      selecting: this.renderPickerDialog,
      uploading: this.renderProgressDialog,
      complete: this.listFile,
    }[this.props.pickerState]();

    const {
      classes,
      isShowListFile,
      uploadResult,
      checkedFile,
      isShowEditFileUpload,
      engineCategories,
      librariesByCategories,
      engineByCategories,
      currentEngineCategory,
      enginesSelected,
      isShowModalSaveTemplate,
      templates,
      contentTemplates,
      contentTemplateSelected,
      selectedFolder,
      tagsCustomize,
      loadingUpload,
      uploadResultEdit,
      isOpenModalUpload,
      loadingSaveUpload,
      isReprocess,
      templateName,
      templateSelected,
      loadingRemoveTemplate,
      percentageFilesUploaded,
      dagTemplateByCategorySelected,
      dagTemplatesByCategory,
      isShowModalProcessLimitExceeded,
      reprocessLimit,
      showProcessByCategory,
    } = this.props;
    const {
      activeStep,
      showAdvancedCognitive,
      engineNameSearch,
      checkValidateTemplate,
      isOpenFolder,
      tagsCustomizeName,
      tagsEditFileUpload,
      checkValidateLibrary,
    } = this.state;
    const steps = this.getSteps(showAdvancedCognitive);
    const reprocessSimple = isReprocess && !showAdvancedCognitive;
    return (
      <Fragment>
        <Dialog
          fullScreen
          open={isOpenModalUpload}
          onClose={this.handleCloseModalUpload}
          disableEnforceFocus
        >
          <DialogTitle
            onClose={this.handleCloseModalUpload}
            style={{ background: '#1976d2' }}
          >
            {isReprocess ? 'Reprocess' : 'Upload'}
          </DialogTitle>
          <DialogContent dividers className={classes.dialogContent}>
            {!isReprocess && (
              <Stepper
                activeStep={this.state.activeStep}
                data-testid="stepper"
                sx={{ padding: '24px' }}
              >
                {steps.map((label) => {
                  const stepProps = {};
                  const labelProps = {};
                  return (
                    <Step key={label} {...stepProps}>
                      <StepLabel {...labelProps}>{label}</StepLabel>
                    </Step>
                  );
                })}
              </Stepper>
            )}

            {!isReprocess && activeStep === 0 && (
              <FileUpload
                isShowListFile={isShowListFile}
                handlePick={this.handlePick}
                handleEditFile={this.handleEditFile}
                handleRemoveFile={this.handleRemoveFile}
                checkedFile={checkedFile}
                uploadResult={uploadResult}
                handleToggle={this.handleToggle}
                pickerComponent={pickerComponent}
              />
            )}

            {((!isReprocess && activeStep === 1) || isReprocess) && (
              <Fragment>
                {loadingUpload ? (
                  <div className={classes.loadingUpload}>
                    <CircularProgress />
                  </div>
                ) : showAdvancedCognitive ? (
                  <Processing
                    engineCategories={engineCategories}
                    librariesByCategories={librariesByCategories}
                    enginesSelected={enginesSelected}
                    handleShowAdvancedCognitive={
                      this.handleShowAdvancedCognitive
                    }
                    currentEngineCategory={currentEngineCategory}
                    handleChangeEngine={this.handleChangeEngine}
                    handleSearchEngine={this.handleSearchEngine}
                    engineByCategories={engineByCategories}
                    engineNameSearch={engineNameSearch}
                    templateSelected={templateSelected}
                    handleChangeTemplates={this.handleChangeTemplates}
                    templates={templates}
                    handleShowModalSaveTemplate={
                      this.handleShowModalSaveTemplate
                    }
                    checkValidateLibrary={checkValidateLibrary}
                    handleAddEngine={this.handleAddEngine}
                    handleExpandClick={this.handleExpandClick}
                    handleRemoveEngine={this.handleRemoveEngine}
                    handleChangeLibrariesEngineSelected={
                      this.handleChangeLibrariesEngineSelected
                    }
                    handleChangeFieldsEngine={this.handleChangeFieldsEngine}
                    handleChangeJobPriority={this.handleChangeJobPriority}
                    handleRemoveTemplate={this.handleRemoveTemplate}
                    loadingRemoveTemplate={loadingRemoveTemplate}
                    isReprocess={isReprocess}
                  />
                ) : (
                  <SimpleProcessing
                    handleShowAdvancedCognitive={
                      this.handleShowAdvancedCognitive
                    }
                    engineCategories={engineCategories}
                    handleClickDagTemplate={this.handleClickDagTemplate}
                    dagTemplateByCategorySelected={
                      dagTemplateByCategorySelected
                    }
                    dagTemplatesByCategory={dagTemplatesByCategory}
                    handleRunJobTemplate={this.handleRunJobTemplate}
                    handleOpenFolder={this.handleOpenFolder}
                    selectedFolder={selectedFolder}
                    onKeyPress={this.onKeyPress}
                    handleOnChangeTagsCustomize={
                      this.handleOnChangeTagsCustomize
                    }
                    tagsCustomizeName={tagsCustomizeName}
                    tagsCustomize={tagsCustomize}
                    handleRemoveTagsCustomize={this.handleRemoveTagsCustomize}
                    onClickAddTags={this.onClickAddTags}
                    isReprocess={isReprocess}
                    showProcessByCategory={showProcessByCategory}
                    percentageFilesUploaded={percentageFilesUploaded}
                  />
                )}
              </Fragment>
            )}

            {showAdvancedCognitive && !isReprocess && activeStep === 2 && (
              <ContentTemplate
                contentTemplates={contentTemplates}
                contentTemplateSelected={contentTemplateSelected}
                handleAddContentTemplate={this.handleAddContentTemplate}
                handleChangeContentTemplate={this.handleChangeContentTemplate}
                removeContentTemplate={this.removeContentTemplate}
                checkValidateTemplate={checkValidateTemplate}
              />
            )}

            {showAdvancedCognitive && !isReprocess && activeStep === 3 && (
              <Customize
                handleOpenFolder={this.handleOpenFolder}
                selectedFolder={selectedFolder}
                onKeyPress={this.onKeyPress}
                handleOnChangeTagsCustomize={this.handleOnChangeTagsCustomize}
                tagsCustomizeName={tagsCustomizeName}
                tagsCustomize={tagsCustomize}
                handleRemoveTagsCustomize={this.handleRemoveTagsCustomize}
                onClickAddTags={this.onClickAddTags}
              />
            )}
          </DialogContent>
          <DialogActions>
            {!isReprocess ? (
              <Fragment>
                <Button
                  disabled={activeStep === 0}
                  onClick={this.handleBack}
                  className={classes.button}
                  data-testid="back-button"
                >
                  Back
                </Button>
                {((!showAdvancedCognitive && activeStep !== 1) ||
                  showAdvancedCognitive) && (
                  <Button
                    variant="contained"
                    color="primary"
                    data-test="next-step"
                    onClick={this.handleNext}
                    className={classes.button}
                    disabled={
                      !uploadResult.length ||
                      (activeStep === 1 && loadingUpload) ||
                      loadingSaveUpload
                    }
                    data-testid="next-button"
                  >
                    {activeStep === steps.length - 1 ? 'Save' : 'Next'}
                  </Button>
                )}
              </Fragment>
            ) : (
              showAdvancedCognitive && (
                <Button
                  variant="contained"
                  color="primary"
                  data-test="save-reprocess"
                  onClick={this.handleRunProcess}
                  className={classes.button}
                  disabled={loadingSaveUpload}
                  data-testid="save-button"
                >
                  Save
                </Button>
              )
            )}

            {loadingSaveUpload && !reprocessSimple && (
              <Box position="relative" display="inline-flex">
                <CircularProgress
                  variant="determinate"
                  value={percentageFilesUploaded}
                />
                <Box
                  top={0}
                  left={0}
                  bottom={0}
                  right={0}
                  position="absolute"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Typography
                    variant="caption"
                    component="div"
                    color="textSecondary"
                  >{`${Math.round(percentageFilesUploaded)}%`}</Typography>
                </Box>
              </Box>
            )}
          </DialogActions>
        </Dialog>
        <SaveTemplate
          open={isShowModalSaveTemplate}
          handleSave={this.handleSaveTemplate}
          handleClose={this.handleHideModalSaveTemplate}
          onChange={this.hanldeOnChangeTemPlate}
          templateName={templateName}
        />

        <EditFileUpload
          open={isShowEditFileUpload}
          title={'Edit Media'}
          handleClose={this.handleCloseEditFileUpload}
          data={uploadResult.filter((_item, key) => checkedFile.includes(key))}
          onChangeDateTime={this.onChangeDateTime}
          handlePick={this.handlePick}
          onChangeFileName={this.onChangeFileName}
          uploadResultEdit={uploadResultEdit}
          handleSave={this.handleSaveEditFileUpload}
          onKeyPress={this.onKeyPress}
          handleOnChangeTagsCustomize={this.handleOnChangeTagsCustomize}
          tagsEditFileUpload={tagsEditFileUpload}
          handleRemoveTagsCustomize={this.handleRemoveTagsCustomize}
          onClickAddTags={this.onClickAddTags}
        />
        <ProcessLimitExceededAlertDialog
          open={isShowModalProcessLimitExceeded}
          processLimit={reprocessLimit}
          handleProcess={this.props.saveReprocessFile}
          handleClose={this.handleCloseProcessLimitExceededAlertDialog}
        />
        {isOpenFolder && (
          <MoveFolder
            open
            onClose={this.handleCloseFolder}
            onConfirm={this.onSelect}
            type={'uploadFile'}
            openFolderIdUpload={this.props.openFolderIdUpload}
          />
        )}
      </Fragment>
    );
  }
}

type Props = PropsFromRedux;
// {
//   statusMessage: string;
//   onChangeFormContentTemplate: (
//     contentTemplateId: string,
//     name: string,
//     value: string | DateTime | null
//   ) => void;

//   selectedFolder: {
//     name: string;
//     treeObjectId: string;
//   };
//   onSelectionChange: (key: number | null, type: string | null) => void;
//   simpleCognitiveWorkflowDefaultEngineId: string;
//   defaultWorkflowSetting: string;

//   onClickDagTemplate: (
//     dagTemplateSelected: DagTemplate | undefined,
//     categoryId: string
//   ) => void;

//   showProcessByCategory: { [key: string]: boolean };
// };

const mapState = (state: any) => ({
  open: filePickerModule.isOpen(state),
  pickerState: filePickerModule.state(state),
  percentByFiles: filePickerModule.percentByFiles(state),
  success: filePickerModule.didSucceed(state),
  error: filePickerModule.didError(state),
  warning: filePickerModule.didWarn(state),
  statusMessage: filePickerModule.statusMessage(state),
  isShowListFile: filePickerModule.isShowListFile(state),
  uploadResult: filePickerModule.uploadResult(state),
  checkedFile: filePickerModule.checkedFile(state),
  isShowEditFileUpload: filePickerModule.isShowEditFileUpload(state),
  engineCategories: filePickerModule.engineCategories(state),
  librariesByCategories: filePickerModule.librariesByCategories(state),
  engineByCategories: filePickerModule.engineByCategories(state),
  currentEngineCategory: filePickerModule.currentEngineCategory(state),
  enginesSelected: filePickerModule.enginesSelected(state),
  isShowModalSaveTemplate: filePickerModule.isShowModalSaveTemplate(state),
  templates: filePickerModule.templates(state),
  contentTemplates: filePickerModule.contentTemplates(state),
  contentTemplateSelected: filePickerModule.contentTemplateSelected(state),
  selectedFolder: filePickerModule.selectedFolder(state),
  tagsCustomize: filePickerModule.tagsCustomize(state),
  loadingUpload: filePickerModule.loadingUpload(state),
  uploadResultEdit: filePickerModule.uploadResultEdit(state),
  openFolderIdUpload: filePickerModule.openFolderIdUpload(state),
  isOpenModalUpload: filePickerModule.isOpenModalUpload(state),
  loadingSaveUpload: filePickerModule.loadingSaveUpload(state),
  isReprocess: filePickerModule.isReprocess(state),
  simpleCognitiveWorkflowDefaultEngineId: get(
    state,
    'config.simpleCognitiveWorkflowDefaultEngineId',
    ''
  ),
  templateName: filePickerModule.templateName(state),
  defaultWorkflowSetting: filePickerModule.defaultWorkflowSetting(state),
  templateSelected: filePickerModule.templateSelected(state),
  loadingRemoveTemplate: filePickerModule.loadingRemoveTemplate(state),
  percentageFilesUploaded: filePickerModule.percentageFilesUploaded(state),
  dagTemplates: filePickerModule.dagTemplates(state),
  dagTemplateByCategorySelected:
    filePickerModule.dagTemplateByCategorySelected(state),

  dagTemplatesByCategory: filePickerModule.dagTemplatesByCategory(state),
  reprocessLimit: filePickerModule.reprocessLimit(state),
  isShowModalProcessLimitExceeded:
    filePickerModule.isShowModalProcessLimitExceeded(state),
  showProcessByCategory: filePickerModule.showProcessByCategory(state),
});

const mapDispatch = {
  pick: uploadFileAction.pick,
  endPick: uploadFileAction.endPick,
  abortRequest: uploadFileAction.abortRequest,
  uploadRequest: uploadFileAction.uploadRequest,
  retryRequest: uploadFileAction.retryRequest,
  retryDone: uploadFileAction.retryDone,
  onSelectionChange: uploadFileAction.onSelectionChange,
  removeFileUpload: uploadFileAction.removeFileUpload,
  showEditFileUpload: uploadFileAction.showEditFileUpload,
  hideEditFileUpload: uploadFileAction.hideEditFileUpload,
  fetchEngineCategories: uploadFileAction.fetchEngineCategories,
  fetchLibraries: uploadFileAction.fetchLibraries,
  onChangeEngine: uploadFileAction.onChangeEngine,
  addEngine: uploadFileAction.addEngine,
  removeEngine: uploadFileAction.removeEngine,
  showModalSaveTemplate: uploadFileAction.showModalSaveTemplate,
  hideModalSaveTemplate: uploadFileAction.hideModalSaveTemplate,
  saveTemplate: uploadFileAction.saveTemplate,
  onChangeTemplate: uploadFileAction.onChangeTemplate,
  fetchContentTemplates: uploadFileAction.fetchContentTemplates,
  addContentTemplate: uploadFileAction.addContentTemplate,
  removeContentTemplate: uploadFileAction.removeContentTemplate,
  onChangeFormContentTemplate: uploadFileAction.onChangeFormContentTemplate,
  selectFolder: uploadFileAction.selectFolder,
  addTagsCustomize: uploadFileAction.addTagsCustomize,
  removeTagsCustomize: uploadFileAction.removeTagsCustomize,
  saveUploadFile: uploadFileAction.saveUploadFile,
  onChangeFormEngineSelected: uploadFileAction.onChangeFormEngineSelected,
  onChangeJobPriorityEngineSelected:
    uploadFileAction.onChangeJobPriorityEngineSelected,
  onChangeLibrariesEngineSelected:
    uploadFileAction.onChangeLibrariesEngineSelected,
  onChangeExpand: uploadFileAction.onChangeExpand,
  onChangeFileNameEdit: uploadFileAction.onChangeFileNameEdit,
  onChangeDateTimeEdit: uploadFileAction.onChangeDateTimeEdit,
  saveEditFileUpload: uploadFileAction.saveEditFileUpload,
  fetchFolderUpload: uploadFileAction.fetchFolderUpload,
  closeModalUpload: uploadFileAction.closeModalUpload,
  updateDagTemplateIdSelected: uploadFileAction.updateDagTemplateIdSelected,
  showModalProcessLimitExceeded: uploadFileAction.showModalProcessLimitExceeded,
  checkLimitAndSaveReprocessFile:
    uploadFileAction.checkLimitAndSaveReprocessFile,
  saveReprocessFile: uploadFileAction.saveReprocessFile,
  onChangeTemplateName: uploadFileAction.onChangeTemplateName,
  removeTemplate: uploadFileAction.removeTemplate,
  onClickDagTemplate: uploadFileAction.onClickDagTemplate,
};

interface OwnProps {
  renderButton?: () => void;
  onPick?: () => void;
  classes: ClassNameMap;
  categoryId?: boolean;
}

const mergeProps = (
  stateProps: ReturnType<typeof mapState>,
  dispatchProps: typeof mapDispatch,
  ownProps: OwnProps
) => ({
  ...ownProps,
  ...stateProps,
  ...dispatchProps,
  // allow widget version of FilePicker to override uploadRequest
  // uploadRequest: ownProps.uploadRequest || dispatchProps.uploadRequest,
  // retryRequest: ownProps.retryRequest || dispatchProps.retryRequest,
  // retryDone: ownProps.retryDone || dispatchProps.retryDone,
  // abortRequest: ownProps.abortRequest || dispatchProps.abortRequest,
});

const connector = connect(mapState, mapDispatch, mergeProps);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default withStyles(styles, { withTheme: true })(connector(UploadFile));
