import {
  bulkExport,
  createExportJob,
  createExportAsset,
  insertExportHistory,
  DEFAULT_BULK_EXPORT_STATUS,
} from './index';
import { GQLApi } from '../../../helpers/gqlApi';
import config from '../../../../config.json';
import {
  BulkExportOption,
  Notification,
  BulkExportParam,
  BulkExportCustomizedNames,
} from '../../../model';

xdescribe('bulkExport', () => {
  it('bulk export correctly', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const defaultClusterId = 'edge1-5d90f115-2a33-454c-a1cd-933a856d7862';
    const token = '558c1c93-27e0-467c-9cbc-3f0d96c9b6f1';

    const tdoData = ['2170000048'];
    const searchQuery = {};
    const notificationSchemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const exportEngineId = config.engineIdExportDesktop;
    const organizationId = 1;
    const userId = '4a48886f-bbfa-4e64-9b9e-fcb708c1aef7';
    const exportName = 'myExport';
    const password = '';
    const emailAddress = '<EMAIL>';
    const illuminateAppFolder = {
      treeObjectId: '6eba4501-3fd4-4837-b5a7-c694ea5f8c99',
    };
    const exportBatchSize = { hasNative: 5000, noNative: 50000 };
    const exportOption: BulkExportOption = {
      hasTdoId: false,
      hasFilename: false,
      hasBaseFilename: false,
      hasFoldername: false,
      hasTag: false,
      hasPlainText: false,
      hasTtml: true,
      hasWord: true,
      hasObjectNotation: false,
      hasBookmark: false,
      hasTranslation: false,
      hasNativeTranslation: false,
      hasObjectDetection: false,
      hasSentiment: false,
      hasEngineName: false,
      hasNative: false,
      filePathWindows: false,
      hasClosedCaption: false,
    };

    const customizedNames: BulkExportCustomizedNames = {
      tdoId: 'File ID',
      filename: 'Filename',
      baseFilename: 'Base Filename',
      foldername: 'Folder Name',
      tag: 'Tags',
      plainText: 'Plain Text (TXT)',
      plainTextEdited: 'Plain Text (TXT) Edited',
      speakerSeparation: 'Speaker Separation',
      speakerSeparationEdited: 'Speaker Separation Edited',
      ttml: 'Time Text Markup Language (TTML)',
      ttmlEdited: 'Time Text Markup Language (TTML) Edited',
      objectNotation: 'AI Object Notation (JSON)',
      objectNotationEdited: 'AI Object Notation (JSON) Edited',
      speakerSeparationObjectNotation: 'Speaker Separation Object Notation',
      speakerSeparationObjectNotationEdited:
        'Speaker Separation Object Notation Edited',
      bookmark: 'Bookmarks',
      translation: 'Translation',
      objectDetection: 'Object Detection',
      sentiment: 'Sentiment Analysis',
      native: 'Include Original Native Files',
      engineName: 'Engine Name (Language)',
      filePathWindows: 'File Path Delimiter',
      word: 'Word Document',
    };

    const startDateTime = new Date().toISOString();
    const stopDateTime = new Date().toISOString();
    const exportParam: BulkExportParam = {
      tdoData,
      searchQuery,
      exportName,
      password,
      exportOption,
      exportBatchSize,
      exportEngineId,
      clusterId: defaultClusterId,
      notificationSchemaId,
      organizationId,
      userId,
      emailAddress,
      parentFolderId: illuminateAppFolder.treeObjectId,
      startDateTime,
      stopDateTime,
      exportDestination: 'desktop',
      maxConcurrency: 50,
      customizedNames,
    };

    const gql = new GQLApi(endpoint, token, veritoneAppId);

    const result = await bulkExport(exportParam, gql);
    console.log(result);
    expect(result.tdoId).toBeTruthy;
    expect(result.assetId).toBeTruthy;
    expect(result.jobId).toBeTruthy;
    expect(result.sdoId).toBeTruthy;
  }, 60000);
});

xdescribe('createExportAsset', () => {
  it('create export asset correctly', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const defaultClusterId = 'edge1-5d90f115-2a33-454c-a1cd-933a856d7862';
    const token = '558c1c93-27e0-467c-9cbc-3f0d96c9b6f1';

    const tdoData = ['2170000048'];
    const searchQuery = {};
    const notificationSchemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const exportEngineId = config.engineIdExportDesktop;
    const organizationId = 1;
    const userId = '4a48886f-bbfa-4e64-9b9e-fcb708c1aef7';
    const exportName = 'myExport';
    const password = '';
    const emailAddress = '<EMAIL>';
    const illuminateAppFolder = {
      treeObjectId: '6eba4501-3fd4-4837-b5a7-c694ea5f8c99',
    };
    const exportBatchSize = { hasNative: 5000, noNative: 50000 };
    const exportOption: BulkExportOption = {
      hasTdoId: false,
      hasFilename: false,
      hasBaseFilename: false,
      hasFoldername: false,
      hasTag: false,
      hasPlainText: false,
      hasTtml: false,
      hasWord: false,
      hasObjectNotation: false,
      hasBookmark: false,
      hasTranslation: false,
      hasNativeTranslation: false,
      hasObjectDetection: false,
      hasSentiment: false,
      hasEngineName: false,
      hasNative: false,
      filePathWindows: false,
      hasClosedCaption: false,
    };

    const customizedNames: BulkExportCustomizedNames = {
      tdoId: 'File ID',
      filename: 'Filename',
      baseFilename: 'Base Filename',
      foldername: 'Folder Name',
      tag: 'Tags',
      plainText: 'Plain Text (TXT)',
      plainTextEdited: 'Plain Text (TXT) Edited',
      speakerSeparation: 'Speaker Separation',
      speakerSeparationEdited: 'Speaker Separation Edited',
      ttml: 'Time Text Markup Language (TTML)',
      ttmlEdited: 'Time Text Markup Language (TTML) Edited',
      objectNotation: 'AI Object Notation (JSON)',
      objectNotationEdited: 'AI Object Notation (JSON) Edited',
      speakerSeparationObjectNotation: 'Speaker Separation Object Notation',
      speakerSeparationObjectNotationEdited:
        'Speaker Separation Object Notation Edited',
      bookmark: 'Bookmarks',
      translation: 'Translation',
      objectDetection: 'Object Detection',
      sentiment: 'Sentiment Analysis',
      native: 'Include Original Native Files',
      engineName: 'Engine Name (Language)',
      filePathWindows: 'File Path Delimiter',
      word: 'Word Document',
    };

    const startDateTime = new Date().toISOString();
    const stopDateTime = new Date().toISOString();
    const exportParam: BulkExportParam = {
      tdoData,
      searchQuery,
      exportName,
      password,
      exportOption,
      exportBatchSize,
      exportEngineId,
      clusterId: defaultClusterId,
      notificationSchemaId,
      organizationId,
      userId,
      emailAddress,
      parentFolderId: illuminateAppFolder.treeObjectId,
      startDateTime,
      stopDateTime,
      exportDestination: 'desktop',
      maxConcurrency: 50,
      customizedNames,
    };

    const gql = new GQLApi(endpoint, token, veritoneAppId);

    const result = await createExportAsset(exportParam, gql);
    console.log(result);
    expect(result.tdoId).toBeTruthy;
    expect(result.assetId).toBeTruthy;
  }, 60000);
});

xdescribe('createExportJob', () => {
  it('create export Job correctly', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const defaultClusterId = 'edge1-5d90f115-2a33-454c-a1cd-933a856d7862';
    const token = '558c1c93-27e0-467c-9cbc-3f0d96c9b6f1';

    const tdoId = '2180000080';
    const assetId = '2180000080_f2me4EfLHN';
    const password = '';
    const exportEngineId = config.engineIdExportDesktop;
    const clusterId = defaultClusterId;
    const batchSize = 20;

    const gql = new GQLApi(endpoint, token, veritoneAppId);

    const jobId = await createExportJob(
      tdoId,
      exportEngineId,
      clusterId,
      assetId,
      batchSize,
      password,
      gql
    );
    console.log(jobId);
    expect(jobId).toBeTruthy;
  }, 60000);
});

xdescribe('insertExportHistory', () => {
  it('update or insert export history correctly', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '558c1c93-27e0-467c-9cbc-3f0d96c9b6f1';

    const tdoId = 'tdoId1';
    const jobId = 'jobId1';
    const notificationSchemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const exportEngineId = config.engineIdExportDesktop;
    const organizationId = 1;
    const userId = '4a48886f-bbfa-4e64-9b9e-fcb708c1aef7';
    const exportName = 'myExport';

    const gql = new GQLApi(endpoint, token, veritoneAppId);

    const notificationData: Notification = {
      data: {
        tdoId,
        jobId,
        isRead: false,
        status: DEFAULT_BULK_EXPORT_STATUS,
        userId,
        engineId: exportEngineId,
        applicationKey: 'illuminate',
        organizationId,
        exportName,
        hide: false,
        exportType: 'desktop',
      },
    };

    const result = await insertExportHistory(
      notificationSchemaId,
      notificationData,
      gql
    );
    console.log(result);
    expect(result.length).toBeGreaterThan(0);
  }, 60000);
});
