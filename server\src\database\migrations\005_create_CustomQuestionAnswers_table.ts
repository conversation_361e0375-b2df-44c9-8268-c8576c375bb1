import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('CustomQuestionAnswers').then(async (tableExists: boolean) => {
      if (!tableExists) {
        await knex.schema.raw(`CREATE TABLE CustomQuestionAnswers (
          id int IDENTITY(1,1) PRIMARY KEY,
          orgId smallint,
          recordId varchar(64),
          personNumber tinyint,
          isStopQuestion bit,
          questionDefinitionId int,
          answer varchar(MAX),
          question<PERSON>ey varchar(2028),
          FOREIGN KEY (questionDefinitionId) REFERENCES CustomQuestionDefinitions(id)
        )`);
        return true;
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('CustomQuestionAnswers')
  ])
