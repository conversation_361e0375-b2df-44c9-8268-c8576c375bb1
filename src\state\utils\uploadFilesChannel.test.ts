import { standardizeFileType } from './uploadFilesChannel';
describe('test standardizeFileType', () => {
  test.each([
    ['video/x-ms-wma', 'audio/x-ms-wma'],
    ['video/x-ms-wmv', 'video/x-ms-wmv'],
    ['audio/x-ms-wma', 'audio/x-ms-wma'],
    ['video/mp4', 'video/mp4'],
  ])(
    'file type video/x-ms-wma should be converted to audio/x-ms-wma',
    (type, expected) => {
      const received = standardizeFileType(type);
      expect(received).toEqual(expected);
    }
  );
});
