import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            select: [""]
            limit: 1
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "06:00",
                  lt: "19:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'dayTime',
      dataKey: 'totalResults',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            select: [""]
            limit: 1
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "or"
                  conditions: [
                    { field: "timeOfStop", operator: "range", lt: "06:00" }
                    { field: "timeOfStop", operator: "range", gte: "19:00" }
                  ]
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'nightTime',
      dataKey: 'totalResults',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Times',
  filterTextType: 'Day or Night',
  filterType: '',
  filterTerms: {
    'Day Time': {
      queryFilter: `{
          field: "timeOfStop",
          operator: "range",
          gte: "06:00",
          lt: "19:00"
        }`,
      storageKey: 'dayTime',
    },
    'Night Time': {
      queryFilter: `{
          operator: "or"
          conditions: [
            { field: "timeOfStop", operator: "range", lt: "06:00" }
            { field: "timeOfStop", operator: "range", gte: "19:00" }
          ]
        }`,
      storageKey: 'nightTime',
    },
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    // Add data
    chart.data = [];

    if (config.filter === 'Day Time' || config.useFilter === 'all') {
      chart.data.push({
        action: 'People Stopped during Day (6:00-19:00)',
        amount: data.dayTime ?? 0,
      });
    }
    if (config.filter === 'Night Time' || config.useFilter === 'all') {
      chart.data.push({
        action: 'People Stopped during Night (19:00-6:00)',
        amount: data.nightTime ?? 0,
      });
    }

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'amount';
    pieSeries.dataFields.category = 'action';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  dayTime: number;
  nightTime: number;
}
