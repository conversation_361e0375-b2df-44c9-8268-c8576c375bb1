import { isEmpty } from 'lodash';
import { all, takeLatest, put, select, fork } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
import {
  fetchExportTemplateSchemaId,
  watchFetchExportTemplate,
  watchSaveExportTemplate,
  watchDeleteExportTemplate,
} from './exportTemplateSaga';
const {
  user: { selectUserOrganizationKvp, selectUser },
} = modules;
import {
  getTdosForExport,
  getIsExportAll,
  clearExportData,
  isSelectedAll,
  getIlluminateAppFolders,
  getSchemaIdNotification,
  getSelectedTdo,
} from '../tdosTable';
import { getTotalResults } from '../search';
import { REFRESH_EXPORT_HISTORY } from '../history';
import {
  BULK_EXPORT_REQUEST,
  CREATE_BULK_EXPORT_SUCCESS,
  CREATE_BULK_EXPORT_FAILURE,
  createBulkExport,
} from './';
import {
  BulkExportOption,
  BulkExportParam,
  BulkExportCustomizedNames,
} from '../../../model';

import { getMaxAssetCount } from '../../../helpers/getMaxAssetCount';
import getEngineIdExportDesktop from '../../../helpers/getEngineIdExportDesktop';
import getDefaultClusterId from '../../../helpers/getDefaultClusterId';
import getExportBatchSize from '../../../helpers/getExportBatchSize';
import { showNotification } from '../snackbar';

const exportMsg = {
  exportSuccess:
    'Your export job has been submitted and is currently processing. You will receive a notification upon its completion.',
  exportFailure:
    'There was an error submitting your export job. Please try to export again.',
};

function* watchBulkExportRequest() {
  yield* takeLatest(
    BULK_EXPORT_REQUEST,
    function* (action: {
      type: string;
      payload: {
        exportName: string;
        exportOption: BulkExportOption;
        password: string;
        customizedNames: BulkExportCustomizedNames;
      };
    }) {
      const exportOption = action.payload.exportOption;
      const exportBatchSize = yield* select(getExportBatchSize);
      const maxAssetCount = yield* select(getMaxAssetCount);
      const selectedAll = yield* select(isSelectedAll);
      const tdosForExport = yield* select(getTdosForExport);
      const isExportAll = yield* select(getIsExportAll);
      const totalResults = yield* select(getTotalResults);
      const {
        userId,
        organizationId,
      }: { userId: string; organizationId: number } = yield getUser();
      const orgKvp = yield* select(selectUserOrganizationKvp);
      const emailAddress = orgKvp?.features?.illuminate?.emailAddress || '';
      const illuminateAppFolders: { id: string; treeObjectId: string }[] =
        yield* select(getIlluminateAppFolders);
      const defaultClusterId = yield* select(getDefaultClusterId);
      const notificationSchemaId = yield* select(getSchemaIdNotification);
      const exportEngineId = yield* select(getEngineIdExportDesktop);
      const { tdoData, searchQuery } = yield* select(getSelectedTdo);

      const numberOfFiles =
        selectedAll || isExportAll ? totalResults : tdosForExport.length;
      const batchSize = exportOption.hasNative
        ? exportBatchSize.hasNative
        : exportBatchSize.noNative;
      const numberOfBatches = Math.ceil(numberOfFiles / batchSize);
      // Each batch will generate at lease 1 asset. There is limit of 5 GB on asset
      // so, multiply by 2 would give some room for large batch. It is possible to
      // has a batch with more than 2 assets, but it is rare, and
      // the suggestedAssetCount is a suggested number for maxAssetCount,
      // and we can set maxAssetCount to a large number.
      const suggestedAssetCount = numberOfBatches * 2;
      if (maxAssetCount < suggestedAssetCount) {
        yield* put(
          showNotification(
            `Please reach out to veritone support and set maxAssetCount greater than ${suggestedAssetCount}`,
            'warning'
          )
        );
        yield* put(clearExportData());
      } else {
        const startDateTime = new Date().toISOString();
        const stopDateTime = new Date().toISOString();
        const exportParam: BulkExportParam = {
          tdoData,
          searchQuery,
          exportName: action.payload.exportName,
          password: action.payload.password,
          exportOption,
          exportBatchSize,
          exportEngineId,
          clusterId: defaultClusterId,
          notificationSchemaId: notificationSchemaId,
          organizationId,
          userId,
          emailAddress,
          parentFolderId: illuminateAppFolders[0]?.treeObjectId ?? '',
          startDateTime,
          stopDateTime,
          exportDestination: 'desktop',
          maxConcurrency: 50,
        };
        if (!isEmpty(action.payload.customizedNames)) {
          exportParam.customizedNames = action.payload.customizedNames;
        }
        yield* put(createBulkExport(exportParam));
      }
    }
  );
}

function* getUser() {
  const user = yield* select(selectUser);
  const userId = user.userId;
  const organizationId = user?.organization?.organizationId;
  return {
    userId,
    organizationId,
  };
}

function* watchCreateBulkExportSuccess() {
  yield* all([
    takeLatest(CREATE_BULK_EXPORT_SUCCESS, afterCreateBulkExportSuccess),
  ]);
}

function* watchCreateBulkExportFailure() {
  yield* all([
    takeLatest(CREATE_BULK_EXPORT_FAILURE, afterCreateBulkExportFailure),
  ]);
}

function* afterCreateBulkExportSuccess() {
  // show export success message
  yield* put(showNotification(exportMsg.exportSuccess, 'success'));
  // refresh export history
  yield* put(REFRESH_EXPORT_HISTORY());
}

function* afterCreateBulkExportFailure() {
  // show export failure message
  yield* put(showNotification(exportMsg.exportFailure, 'error'));
}

export function* bulkExportSaga() {
  yield* all([
    fork(watchBulkExportRequest),
    fork(watchCreateBulkExportSuccess),
    fork(watchCreateBulkExportFailure),
    fork(fetchExportTemplateSchemaId),
    fork(watchFetchExportTemplate),
    fork(watchSaveExportTemplate),
    fork(watchDeleteExportTemplate),
  ]);
}
