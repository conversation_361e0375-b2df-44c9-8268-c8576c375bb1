import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            select: [""]
            limit: 1
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "disability",
                  operator: "query_string",
                  value: "*"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'disability',
      dataKey: 'totalResults',
    },
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            select: [""]
            limit: 1
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'totalCount',
      dataKey: 'totalResults',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Disabled Distribution',
  filterTextType: 'Disabled Distribution by Type',
  filterType: 'disability',
  filterTerms: {
    Disabled: {
      queryFilter: `{
        field: "disability",
        operator: "query_string",
        value: "*"
      }`,
      storageKey: 'disability',
    },
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const chartData = {
      Disabled: data.disability,
      'Not Disabled': data.totalCount - data.disability,
    };

    // Add data
    chart.data = Object.entries(chartData).map((kvp) => {
      const [disabled, v] = kvp;
      return { isDisabled: disabled, count: v };
    });

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'isDisabled';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );
    return chart;
  },
};

export default config;

interface Data {
  disability: number;
  totalCount: number;
  demographics: DemographicsType;
}
