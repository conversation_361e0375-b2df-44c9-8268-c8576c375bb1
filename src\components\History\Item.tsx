import React, { Fragment, useState } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { DateTime } from 'luxon';
import Paper from '@mui/material/Paper';
import Tooltip from '@mui/material/Tooltip';
import SaveAlt from '@mui/icons-material/SaveAlt';
import TablePagination from '@mui/material/TablePagination';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import {
  PagingState,
  IntegratedPaging,
  CustomPaging,
} from '@devexpress/dx-react-grid';

import {
  Grid,
  Table,
  TableHeaderRow,
  PagingPanel,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui';
import {
  STATUS,
  getTotalCount,
  getCurrentPage,
  HISTORY_TABLE_NEXT_PAGE,
  getLimit,
  getOffsetPage,
  ON_FETCH_PAGE_SIZE,
  ON_FETCH_URI_TO_EXPORT,
  getFetchingHistory,
  getLoadingFetchUriExport,
  getNoData,
} from 'state/modules/history';
import { ExportHistory } from 'state/modules/history/models';
import classNames from 'classnames/bind';
import * as styles from './styles.scss';

const cx = classNames.bind(styles);

function HistoryItem(props: Props) {
  const [columns] = useState([
    { name: '', title: '' },
    { name: 'name', title: 'Name' },
    { name: 'status', title: 'Status' },
    { name: 'date', title: 'Date Initiated' },
    { name: 'actions', title: 'Actions' },
  ]);
  const [tableColumnExtensions] = useState([
    { columnName: '', width: 42 },
    { columnName: 'name', wordWrapEnabled: true },
    { columnName: 'status', wordWrapEnabled: true },
    { columnName: 'date', wordWrapEnabled: true },
    { columnName: 'actions', width: 120 },
  ]);
  const [tdoIdDownload, setTdoIdDownload] = useState('');

  function TableRow({ row, ...restProps }: Table.DataRowProps) {
    return (
      <Table.Row
        row={undefined}
        {...restProps}
        data-testid="export-table-row"
        data-id={row.id}
        data-size={row.size}
        // onClick={this.handleClickDetails}
        className={styles['table-row']}
      />
    );
  }

  function myCustomDataCellComponent(value: NoDataCellProps) {
    const { noData } = props;
    return noData ? <VirtualTable.NoDataCell {...value} /> : '';
  }

  function handleShowSnackBar(event: React.MouseEvent<HTMLButtonElement>) {
    const { onFetchUriToExport } = props;
    const tdoId = event.currentTarget.getAttribute('data-tdo');
    const exportName = event.currentTarget.getAttribute('data-name');
    if (tdoId) {
      setTdoIdDownload(tdoId);
    }
    if (tdoId && exportName) {
      onFetchUriToExport(tdoId, exportName);
    }
  }

  function handlePageChange(
    _event: React.MouseEvent<HTMLButtonElement> | null,
    currentPage: number
  ) {
    const { onNextPage } = props;
    handleFooterChange(null, onNextPage, currentPage);
  }

  function onPageSizeChange(
    event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
  ) {
    const { onChangePageSize } = props;
    handleFooterChange(event, onChangePageSize);
  }

  function handleFooterChange(
    event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement> | null,
    func: ({
      limit,
      offset,
      currentPage,
    }: {
      limit: number;
      offset: number;
      currentPage: number;
    }) => unknown,
    thisPage?: number
  ) {
    const { limit, currentPage } = props;
    const offset =
      thisPage || thisPage === 0 ? limit * thisPage : limit * currentPage;
    const paginationInfo = {
      limit: event ? Number(event.target.value) : limit,
      offset,
      currentPage: thisPage || thisPage === 0 ? thisPage : currentPage,
    };
    func(paginationInfo);
  }

  function myCustomPagination(value: { totalCount: number; pageSize: number }) {
    const { totalCount, pageSize } = value;
    const { currentPage } = props;
    return (
      <TablePagination
        rowsPerPageOptions={[10, 20, 100]}
        component="div"
        count={totalCount}
        rowsPerPage={pageSize}
        page={currentPage}
        slotProps={{
          actions: {
            previousButton: {
              'aria-label': 'Previous Page',
            },
            nextButton: {
              'aria-label': 'Next Page',
            },
          },
        }}
        onPageChange={handlePageChange}
        onRowsPerPageChange={onPageSizeChange}
        style={{
          paddingRight: 68,
        }}
        className={styles['table-footer']}
      />
    );
  }

  const {
    data,
    currentPage,
    totalExport,
    limit,
    fetchingHistory,
    loadingFetchUriExport,
  } = props;

  const dataRow =
    data &&
    data
      .map((item) => {
        if (item.data.status === 'pending') {
          return {
            ...item,
            data: {
              ...item.data,
              status: 'running',
            },
          };
        }
        return item;
      })
      .map((item) => {
        const status = (
          <span className={cx('status', `status-${item.data.status}`)}>
            {item.data.status}
          </span>
        );

        const actions = (
          <div>
            <IconButton
              data-tdo={item.data.tdoId}
              data-name={item.data.exportName}
              onClick={handleShowSnackBar}
              disabled={![STATUS.COMPLETE].includes(item.data.status)}
              size="large"
            >
              {loadingFetchUriExport && tdoIdDownload === item.data.tdoId ? (
                <CircularProgress size={24} variant="indeterminate" />
              ) : (
                <Tooltip title={'Ready to download'}>
                  <SaveAlt />
                </Tooltip>
              )}
            </IconButton>
          </div>
        );
        const date = (
          <>
            {DateTime.fromISO(item.createdDateTime).toFormat(
              'ccc MMM d yyyy HH:mm:ss'
            )}
          </>
        );

        const name = (
          <div>
            <Typography>
              {item.data.exportName ? item.data.exportName : item.data.jobId}
            </Typography>
          </div>
        );
        return {
          name,
          date,
          status,
          actions,
          id: item.id,
        };
      });

  return (
    <Fragment>
      <Paper className={styles['table']}>
        <Grid rows={dataRow} columns={columns}>
          <PagingState currentPage={currentPage} pageSize={limit} />
          <IntegratedPaging />

          <CustomPaging totalCount={totalExport} />
          <VirtualTable
            rowComponent={TableRow}
            columnExtensions={tableColumnExtensions}
            noDataCellComponent={myCustomDataCellComponent}
          />

          <TableHeaderRow />

          <PagingPanel
            containerComponent={myCustomPagination}
            pageSizes={[10, 20, 100]}
          />
        </Grid>
        {fetchingHistory && (
          <div className={styles['loadding-data']}>
            <CircularProgress size={30} variant="indeterminate" />
          </div>
        )}
      </Paper>
    </Fragment>
  );
}
interface Column {
  name: string;
  title?: string;
  showBadge?: boolean;
}
interface TableRow {
  key: string;
  type: symbol;
  rowId?: number | string;
  row?: any;
  height?: number;
}
interface TableColumn {
  key: string;
  type: symbol;
  column?: Column;
  width?: number | string;
  align?: 'left' | 'right' | 'center';
  fixed?: 'left' | 'right';
}
interface NoDataCellProps {
  getMessage: (messageKey: string) => string;
  tableRow: TableRow;
  tableColumn: TableColumn;
  colSpan?: number;
  rowSpan?: number;
}
type Props = PropsFromRedux & {
  data: ExportHistory[];
};

const mapState = (state: any) => ({
  totalExport: getTotalCount(state),
  currentPage: getCurrentPage(state),
  limit: getLimit(state),
  offsetPage: getOffsetPage(state),
  fetchingHistory: getFetchingHistory(state),
  loadingFetchUriExport: getLoadingFetchUriExport(state),
  noData: getNoData(state),
});

const mapDispatch = {
  onNextPage: (paginationInfo: { limit: number; offset: number }) =>
    HISTORY_TABLE_NEXT_PAGE(paginationInfo),
  onChangePageSize: (paginationInfo: { limit: number; offset: number }) =>
    ON_FETCH_PAGE_SIZE(paginationInfo),
  onFetchUriToExport: (tdoId: string, exportName: string) =>
    ON_FETCH_URI_TO_EXPORT({ tdoId, exportName }),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(HistoryItem);
