import {
  all,
  fork,
  takeLatest,
  put,
  select,
  delay,
} from 'typed-redux-saga/macro';
import get from 'lodash/get';
import { showNotification } from '../snackbar';
import { modules } from '@veritone/glc-redux';
const {
  user: { selectUser },
} = modules;
import {
  getSchemaIdNotification,
  FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS,
  fetchGetJob,
  FETCH_REMOVE_NOTIFICATION_SUCCESS,
} from '../tdosTable';
// import { delay } from 'redux-saga';
import {
  fetchExportHistory,
  fetchTotalCount,
  HISTORY_TABLE_NEXT_PAGE,
  ON_FETCH_PAGE_SIZE,
  FETCH_EXPORT_HISTORY_SUCCESS,
  fetchExportUri,
  ON_FETCH_URI_TO_EXPORT,
  OPEN_EXPORT_MODAL,
  getHistory,
  STATUS,
  UPDATE_EXPORT_HISTORY_STATUS_BY_ID,
  FETCH_GET_JOB_EXPORT_HISTORY,
  FETCH_GET_JOB_EXPORT_HISTORY_SUCCESS,
  FETCH_GET_JOB_EXPORT_HISTORY_FAILURE,
  UPDATE_TOTAL_COUNT_EXPORT_SUCCESS,
  REFRESH_EXPORT_HISTORY,
  getLimit,
  getOffsetPage,
  FETCH_URI_SUCCESS,
  updateExportHistorys,
} from '.';
import { Action } from 'state/sagas';
import { arrayHasLength } from '@utils';
import { ExportHistory } from './models';
function* watchRefreshExportHistory() {
  yield* takeLatest(
    [FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS, REFRESH_EXPORT_HISTORY],
    function* () {
      const schemaId = yield* select(getSchemaIdNotification);
      if (!schemaId) {
        console.error(
          'failed to refresh export history. notification schema is empty'
        );
        return;
      }
      const { organizationId, userId } = yield* getUser();
      if (!organizationId) {
        console.error(
          'failed to refresh export history. organizationId is empty'
        );
        return;
      }
      if (!userId) {
        console.error('failed to refresh export history. userId is empty');
        return;
      }
      const firstPage = {
        limit: 10,
        offset: 0,
      };
      yield* put(
        fetchExportHistory({
          schemaId,
          paginationInfo: firstPage,
          organizationId,
          userId,
          type: 'exportHistoryFirstPage',
        })
      );
    }
  );
}

function* watchFetchTotalCount() {
  yield* takeLatest(FETCH_EXPORT_HISTORY_SUCCESS, function* (action) {
    const schemaId = yield* select(getSchemaIdNotification);
    if (!schemaId) {
      console.error('failed to get export count. notification schema is empty');
      return;
    }
    const { organizationId, userId } = yield* getUser();
    if (!organizationId) {
      console.error('failed to get export count. organizationId is empty');
      return;
    }
    if (!userId) {
      console.error('failed to get export count. userId is empty');
      return;
    }
    const { type } = get(action, 'meta.variables', { type: '' });
    if (['exportHistoryFirstPage'].includes(type)) {
      yield* put(fetchTotalCount({ schemaId, organizationId, userId }));
    }
  });
}

function* watchPagination() {
  yield* takeLatest(HISTORY_TABLE_NEXT_PAGE, fetchOffsetAndLimit);
  yield* takeLatest(ON_FETCH_PAGE_SIZE, fetchOffsetAndLimit);
}

function* watchFetchUri() {
  yield* takeLatest(ON_FETCH_URI_TO_EXPORT, function* (action) {
    const tdoId = get(action, 'payload.tdoId', '');
    yield* put(fetchExportUri(tdoId));
  });
}

function* watchFetchUriExportSuccess() {
  yield* takeLatest(FETCH_URI_SUCCESS, function* (action) {
    const records = action.payload.temporalDataObject?.assets?.records || [];
    if (records.length > 1) {
      yield* put(OPEN_EXPORT_MODAL());
      return;
    }
    if (arrayHasLength(records, 1)) {
      window.open(records[0].signedUri);
      return;
    }
    yield* put(showNotification('Asset not found', 'error'));
  });
}
function* fetchOffsetAndLimit(
  action: Action<{ limit: number; offset: number }>
) {
  const schemaId = yield* select(getSchemaIdNotification);
  if (!schemaId) {
    console.error(
      'failed to get export history page. notification schema is empty'
    );
    return;
  }
  const { organizationId, userId } = yield* getUser();
  if (!organizationId) {
    console.error('failed to get export history page. organizationId is empty');
    return;
  }
  if (!userId) {
    console.error('failed to get export history page. userId is empty');
    return;
  }
  const paginationInfo = get(action, 'payload');
  yield* put(
    fetchExportHistory({
      schemaId,
      paginationInfo,
      organizationId,
      userId,
      type: 'OffsetAndLimit',
    })
  );
}

function* getUser() {
  const user = yield* select(selectUser);
  const userId = user?.userId;
  const organizationId = user?.organization?.organizationId;
  return {
    userId,
    organizationId,
  };
}

function* autoRefreshStatusExport() {
  yield* takeLatest(FETCH_GET_JOB_EXPORT_HISTORY_SUCCESS, function* (action) {
    const dataJobs = Object.values(action.payload);
    const schemaId = yield* select(getSchemaIdNotification);
    if (!schemaId) {
      console.error(
        'failed to refresh export status. notification schema is empty'
      );
      return;
    }
    const { organizationId, userId } = yield* getUser();
    if (!organizationId) {
      console.error('failed to refresh export status. organizationId is empty');
      return;
    }
    if (!userId) {
      console.error('failed to refresh export status. userId is empty');
      return;
    }
    const dataExportHistorys = yield* select(getHistory);
    const exportHistoryStatusUpdate: {
      [key: string]: { id: string; status: string };
    } = {};
    const exportHistoryUpdate: ExportHistory[] = [];
    for (const history of dataExportHistorys) {
      for (const job of dataJobs) {
        if (
          history.data.jobId === job.id &&
          history.data.status !== job.status
        ) {
          // if status changes, update status and set isRead to false
          const newData = {
            ...history.data,
            status: job.status,
            isRead: false,
          };
          exportHistoryUpdate.push({
            ...history,
            data: newData,
          });
          exportHistoryStatusUpdate[history.id] = {
            id: history.id,
            status: job.status,
          };
        }
      }
    }
    yield* put(UPDATE_EXPORT_HISTORY_STATUS_BY_ID(exportHistoryStatusUpdate));
    yield* put(updateExportHistorys(schemaId, exportHistoryUpdate));
  });

  while (true) {
    const exportHistorys = yield* select(getHistory);
    if (exportHistorys && exportHistorys.length) {
      const exportHistorysComplete = exportHistorys
        .filter((item) => item.data.status !== STATUS.COMPLETE)
        .map((item) => item.data.jobId);

      if (exportHistorysComplete.length) {
        yield* put(
          fetchGetJob(exportHistorysComplete, [
            FETCH_GET_JOB_EXPORT_HISTORY,
            FETCH_GET_JOB_EXPORT_HISTORY_SUCCESS,
            FETCH_GET_JOB_EXPORT_HISTORY_FAILURE,
          ])
        );
      }
    }
    yield* delay(60000);
  }
}

function* watchUpdateTotalCount() {
  yield* all([
    takeLatest(FETCH_REMOVE_NOTIFICATION_SUCCESS, function* () {
      const schemaId = yield* select(getSchemaIdNotification);
      if (!schemaId) {
        console.error(
          'failed to update export count. notification schema is empty'
        );
        return;
      }
      const { organizationId, userId } = yield* getUser();
      if (!organizationId) {
        console.error('failed to update export count. organizationId is empty');
        return;
      }
      if (!userId) {
        console.error('failed to update export count. userId is empty');
        return;
      }
      const limit = yield* select(getLimit);
      const offset = yield* select(getOffsetPage);
      const page = {
        limit,
        offset,
      };
      yield* put(
        fetchExportHistory({
          schemaId,
          paginationInfo: page,
          organizationId,
          userId,
          type: 'removeSuccess',
        })
      );
    }),
    takeLatest(FETCH_EXPORT_HISTORY_SUCCESS, function* (action) {
      const { type } = get(action, 'meta.variables', { type: '' });
      if (type === 'removeSuccess') {
        yield* put(UPDATE_TOTAL_COUNT_EXPORT_SUCCESS('removeSuccess'));
      }
    }),
  ]);
}
export default function* history() {
  yield* all([
    fork(watchRefreshExportHistory),
    fork(watchPagination),
    fork(watchFetchTotalCount),
    fork(watchFetchUri),
    fork(autoRefreshStatusExport),
    fork(watchUpdateTotalCount),
    fork(watchFetchUriExportSuccess),
  ]);
}
