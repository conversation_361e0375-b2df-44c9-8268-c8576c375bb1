import { modules } from '@veritone/glc-redux';
const {
  user: { hasFeature },
} = modules;

import callGraph<PERSON><PERSON><PERSON>, {
  GQLActions,
  createGQLFailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraphQLApi';
import { getQueryToExportAll } from '../search';
import { prepareSearchQueryForEngine } from '../search/query';
import {
  SEND_TO_REDACT,
  SEND_TO_REDACT_SUCCESS,
  SEND_TO_REDACT_FAILURE,
} from '../sendToRedact';
import {
  BULK_EXPORT_REQUEST,
  CREATE_BULK_EXPORT_SUCCESS,
  CREATE_BULK_EXPORT_FAILURE,
} from '../bulkExport';
import get from 'lodash/get';
import isString from 'lodash/isString';
// import uniq from 'lodash/uniq';

import { Notification } from '../../../model';
import { TDO } from '../universal/TDO';
import { createAction, createReducer } from '@reduxjs/toolkit';
import { FetchCreateFolderResponse } from '../folders';

export const ON_SELECTION_CHANGE = createAction<{
  selectedRows: Array<number | string>;
  isSelectedAll: boolean;
  allTdos?: TDO[];
  indeterminate: boolean;
  currentPage?: number;
}>('request to change table selection');

export const HIDE_CONFIRMATION_DIALOG = createAction<object>(
  'hide confirmation dialog'
);

export const ON_CHANGE_SHOW_DETAIL_TDO = createAction<{
  isShowPreview: boolean;
  initFullScreen: boolean;
}>('on change show detail tdo');
export const UPDATE_TDO_ID_PREVIEW = createAction<string>(
  'update tdo id preview'
);
export const RESET_DATA_PREVIEW = createAction<{ isClosePreview?: boolean }>(
  'reset data preview'
);
export const IS_EXPORT_ALL = createAction<{
  isExportAll: boolean;
}>('export all');

export const FETCH_ENGINE_CATEGORIES = createGQLRequestAction(
  'fetch engine categories'
);
export const FETCH_ENGINE_CATEGORIES_SUCCESS =
  createGQLSuccessAction<FetchEngineCategoriesResponse>(
    'fetch engine categories success'
  );
export const FETCH_ENGINE_CATEGORIES_FAILURE = createGQLFailureAction(
  'fetch engine categories failure'
);

export const FETCH_SCHEMAS_NOTIFICATIONS = createGQLRequestAction(
  'fetch schemas notifications'
);
export const FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS =
  createGQLSuccessAction<FetchSchemasNotificationResponse>(
    'fetch schemas notifications success'
  );
export const FETCH_SCHEMAS_NOTIFICATIONS_FAILURE = createGQLFailureAction(
  'fetch schemas notifications failure'
);

export const FETCH_GET_NOTIFICATION = createGQLRequestAction(
  'fetch get notification'
);
export const FETCH_GET_NOTIFICATION_SUCCESS =
  createGQLSuccessAction<FetchGetNotificationsResponse>(
    'fetch get notification success'
  );
export const FETCH_GET_NOTIFICATION_FAILURE = createGQLFailureAction(
  'fetch get notification failure'
);

export const FETCH_UPDATE_IS_READ_NOTIFICATION = createGQLRequestAction(
  'fetch update is read notification'
);
export const FETCH_UPDATE_IS_READ_NOTIFICATION_SUCCESS =
  createGQLSuccessAction<FetchNotificationsResponse>(
    'fetch update is read notification success'
  );
export const FETCH_UPDATE_IS_READ_NOTIFICATION_FAILURE = createGQLFailureAction(
  'fetch update is read notification failure'
);

export const FETCH_UPDATE_IS_READ_NOTIFICATION_REQUEST =
  'fetch update is read notification request';

export const FETCH_REMOVE_NOTIFICATION_REQUEST = createAction<string>(
  'fetch remove notification request'
);
export const FETCH_REMOVE_NOTIFICATION = createGQLRequestAction(
  'fetch remove notification'
);
export const FETCH_REMOVE_NOTIFICATION_SUCCESS =
  createGQLSuccessAction<FetchRemoveNotificationResponse>(
    'fetch remove notification success'
  );
export const FETCH_REMOVE_NOTIFICATION_FAILURE = createGQLFailureAction(
  'fetch remove notification failure'
);

export const FETCH_SHOW_CONFIRM_REMOVE_NOTIFICATION = createAction<string>(
  'fetch show confirm remove notification'
);
export const FETCH_HIDE_CONFIRM_REMOVE_NOTIFICATION = createAction(
  'fetch hide confirm remove notification'
);

export const SELECTED_ALL = createAction<{
  selected: boolean;
  keySelected: number[];
  tdos: TDO[];
  indeterminate: boolean;
  searchResultTdosAll: TDO[];
  limit: number;
}>('selected all');
export const UNSELECTED_ALL = createAction('unselected all');

export const CREATE_ILLUMINATE_APP_FOLDER = createGQLRequestAction(
  'create illuminate app folder'
);
export const CREATE_ILLUMINATE_APP_FOLDER_SUCCESS =
  createGQLSuccessAction<FetchCreateFolderResponse>(
    'create illuminate app folder success'
  );
export const CREATE_ILLUMINATE_APP_FOLDER_FAILURE = createGQLFailureAction(
  'create illuminate app folder failure'
);

export const UPDATE_ILLUMINATE_APP_FOLDER = createAction<
  {
    id: string;
    treeObjectId: string;
  }[]
>('update illuminate app folder');

export const SHOW_BULK_EXPORT = createAction('show bulk export');
export const HIDE_BULK_EXPORT = createAction('hide bulk export');

export const FETCH_UPDATE_HIDE_NOTIFICATION = createGQLRequestAction(
  'fetch update hide notification'
);
export const FETCH_UPDATE_HIDE_NOTIFICATION_SUCCESS =
  createGQLSuccessAction<FetchNotificationsResponse>(
    'fetch update hide notification success'
  );
export const FETCH_UPDATE_HIDE_NOTIFICATION_FAILURE = createGQLFailureAction(
  'fetch update hide notification failure'
);

export const DELETED_TDOS = createAction<TDO[]>('deleted tdos');
export const MOVED_TDOS = createAction<MoveFolderData[]>('moved tdos');

export const UPDATE_MOVED_TDOS =
  createAction<MoveFolderData[]>('update moved tdos');

export const UPDATE_INDEXES_BY_TDOS = createAction<{
  data: TDO[];
  currentPage: number;
}>('update indexes by tdos');

export const UPDATE_SELECTED_ALL = createAction<boolean>('update selected all');
export const UPDATE_EXPORT_AND_REPROCESS_TDOS = createAction<{
  data: TDO[];
  currentPage: number;
}>('update export and reprocess tdos');

export const CLEAR_EXPORT_DATA = createAction('clear export data');
export const INTERNAL_FOLDER_NAME = 'Illuminate App';

export const EXPORT_TRANSCRIPT_OPTIONS = [
  'hasTtml',
  'hasPlainText',
  'hasSpeakerSeparation',
  'includeEdited',
  'hasVtnStandard',
];

export const EXPORT_OTHER_OPTIONS = [
  'hasBookmark',
  'hasTag',
  'hasTranslation',
  'hasNative',
  'hasSentiment',
  'hasObjectDetection',
];
interface EngineCategory {
  id: string;
  name: string;
  iconClass: string;
}

interface SelectedTdos {
  [key: number]: {
    id: string;
    primaryAsset: {
      id: string;
      signedUri: string;
      contentType: string;
    };
    assets: {
      records: Array<{
        assetType: string;
        signedUri: string;
        details?: {
          audioRedactions: number;
          boundingPolysCount: number;
          selectedPolyGroupsCount: number;
          tdos: Record<
            string,
            {
              auditLogAssetId: string;
              auditLogName: string;
              redactedMediaAssetId: string;
              redactedMediaName: string;
            }
          >;
        };
      }>;
    };
    status?: TDO['status'];
  }[];
}

export type MoveFolderData = TDO & {
  moveFolderId: string;
  oldFolderId: string;
};

const defaultState = {
  selectedRows: [] as Array<number | string>,
  isSelectedAll: false,
  exportingJob: false,
  isExportingSuccess: false,
  sendingToRedact: false,
  tdosForExport: [] as string[],
  sendToRedactErrors: null as any,
  engineCategories: {} as { [key: string]: EngineCategory },
  currentPage: 0,
  indeterminate: false,
  isShowPreview: false,
  initFullScreen: false,
  isClosePreview: false,
  tdoIdPreview: '',
  isExportAll: false,
  schemaIdNotification: '',
  notifications: [] as Notification[],
  confirmNotification: {
    isShowConfirm: false,
    notificationId: '',
  },
  parentFolderId: null as string | null,
  isShowBulkExport: false,
  tdosForReprocess: [] as {
    id: string;
    primaryAsset: TDO['primaryAsset'];
    assets: TDO['assets'];
  }[],
  illuminateAppFolders: [] as { id: string; treeObjectId: string }[],
  totalNotifications: 0,
  exportMsg: undefined as string | undefined,
  deletedTdos: [] as TDO[],
  movedTdos: [] as MoveFolderData[],
  indexesByTdos: {} as { [key: number]: number[] },
  selectedTdos: {} as SelectedTdos,
};

function getIndexesBySelectedTdos(data: TDO[], selectedTdos: SelectedTdos) {
  const indexes = [] as number[];
  const selectedTdosIds = Object.values(selectedTdos).flatMap((tdo) =>
    tdo.map((item: { id: string }) => item.id)
  );
  for (let i = 0; i <= data.length; i++) {
    if (selectedTdosIds.includes(data[i]?.id)) {
      indexes.push(i);
    }
  }
  return indexes;
}

function paginateAndIndexData(
  data: TDO[],
  pageSize: number,
  deletedAndMovedTdoIds: string[]
) {
  const paginatedData = {} as SelectedTdos;
  const indexes: { [key: number]: number[] } = {};
  for (let i = 0; i < data.length; i += pageSize) {
    const page = i / pageSize;
    const filteredTdos = data
      .slice(i, i + pageSize)
      .filter((item) => !deletedAndMovedTdoIds.includes(item.id));

    paginatedData[page] = filteredTdos.map((tdo) => ({
      id: tdo.id,
      primaryAsset: tdo.primaryAsset,
      // use ?? to handle case tdo's status is error
      assets: { ...tdo.assets, records: [...(tdo.assets?.records ?? [])] },
    }));

    indexes[page] = [];

    for (let j = 0; j < pageSize && i + j < data.length; j++) {
      if (!deletedAndMovedTdoIds.includes(data[i + j]?.id ?? '')) {
        indexes[page]?.push(j);
      }
    }
  }
  return { paginatedData, indexes };
}
const clearSelectionValues = {
  selectedRows: [],
  tdosForExport: [],
  isSelectedAll: false,
  indeterminate: false,
  tdosForReprocess: [],
  indexesByTdos: {},
  selectedTdos: {},
};
const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(ON_SELECTION_CHANGE, (state, action) => {
      const selectedRows = action.payload?.selectedRows || [];
      const allTdos = action?.payload?.allTdos || [];
      const { currentPage = 0 } = action.payload;
      const newSelectedTdos = state.selectedTdos;
      const newIndexesBySelectedTdoIds: number[] = [];
      newSelectedTdos[currentPage] = [];

      allTdos.forEach((tdo, tdoIndex) => {
        if (selectedRows.includes(tdoIndex) && isString(tdo.id)) {
          newSelectedTdos[currentPage]?.push({
            id: tdo.id,
            primaryAsset: tdo.primaryAsset,
            // use ?? to handle case tdo's status is error
            assets: {
              ...tdo.assets,
              records: [...(tdo.assets?.records ?? [])],
            },
            status: tdo.status,
          });
          newIndexesBySelectedTdoIds.push(tdoIndex);
        }
      });
      const tdos = Object.values(newSelectedTdos).flat();
      const tdoIds = Object.values(newSelectedTdos).flatMap((item) =>
        item.map((subItem) => subItem.id)
      );

      state.selectedRows = selectedRows;
      state.tdosForExport = tdoIds;
      state.isSelectedAll = action.payload.isSelectedAll;
      state.indeterminate = action.payload.indeterminate;
      state.isShowPreview = false;
      state.tdosForReprocess = tdos;
      state.selectedTdos = newSelectedTdos;
      state.indexesByTdos[currentPage] = newIndexesBySelectedTdoIds;
    })
    .addCase(BULK_EXPORT_REQUEST, (state, _action) => {
      return {
        ...state,
        exportingJob: true,
      };
    })
    .addCase(CREATE_BULK_EXPORT_SUCCESS, (state) => {
      return {
        ...state,
        exportingJob: false,
        isExportingSuccess: true,
        isExportAll: false,
      };
    })
    .addCase(CREATE_BULK_EXPORT_FAILURE, (state) => {
      return {
        ...state,
        exportingJob: false,
        isExportingSuccess: false,
        isExportAll: false,
      };
    })
    .addCase(HIDE_CONFIRMATION_DIALOG, (state) => {
      return {
        ...state,
        exportDialogOpen: false,
      };
    })
    .addCase(SEND_TO_REDACT, (state) => {
      return {
        ...state,
        sendingToRedact: true,
      };
    })
    .addCase(SEND_TO_REDACT_FAILURE, (state, action) => {
      return {
        ...state,
        sendingToRedact: false,
        sendToRedactErrors: get(action, 'payload.errors'),
      };
    })
    .addCase(SEND_TO_REDACT_SUCCESS, (state) => {
      return {
        ...state,
        sendingToRedact: false,
        // selectedRows: [],
        // tdosForExport: [],
        // isSelectedAll: false,
        // indeterminate: false,
        ...clearSelectionValues,
      };
    })
    .addCase(FETCH_ENGINE_CATEGORIES_SUCCESS, (state, action) => {
      return {
        ...state,
        engineCategories: action.payload.engineCategories.records
          .filter((item) => item.iconClass !== null)
          .reduce<{
            [key: string]: (typeof action.payload.engineCategories.records)[0];
          }>((data, item) => {
            return {
              ...data,
              [item.id]: {
                id: item.id,
                iconClass: item.iconClass,
                name: item.name,
              },
            };
          }, {}),
      };
    })
    .addCase(SELECTED_ALL, (state, action) => {
      const {
        selected,
        keySelected,
        indeterminate,
        searchResultTdosAll,
        limit,
      } = action.payload;
      const { deletedTdos, movedTdos } = state;
      const deletedAndMovedTdoIds = [...deletedTdos, ...movedTdos].map(
        (tdo) => tdo.id
      );
      const { paginatedData, indexes } = paginateAndIndexData(
        searchResultTdosAll,
        limit,
        deletedAndMovedTdoIds
      );
      state.isSelectedAll = selected;
      state.selectedRows = keySelected;
      state.tdosForExport = searchResultTdosAll
        .map((tdo) => tdo.id)
        .filter((item) => !deletedAndMovedTdoIds.includes(item));
      state.indeterminate = indeterminate;
      state.tdosForReprocess = searchResultTdosAll
        .map((tdo) => ({
          id: tdo.id,
          primaryAsset: tdo.primaryAsset,
          // use ?? to handle case tdo's status is error
          assets: { ...tdo.assets, records: [...(tdo.assets?.records ?? [])] },
          status: tdo.status,
        }))
        .filter((item) => !deletedAndMovedTdoIds.includes(item.id));
      state.selectedTdos = paginatedData;
      state.indexesByTdos = indexes;
    })
    .addCase(UNSELECTED_ALL, (state) => {
      return {
        ...state,
        ...clearSelectionValues,
      };
    })
    .addCase(ON_CHANGE_SHOW_DETAIL_TDO, (state, action) => {
      return {
        ...state,
        isShowPreview: action.payload.isShowPreview,
        initFullScreen: action.payload.initFullScreen,
      };
    })
    .addCase(UPDATE_TDO_ID_PREVIEW, (state, action) => {
      return {
        ...state,
        tdoIdPreview: action.payload,
      };
    })
    .addCase(RESET_DATA_PREVIEW, (state, action) => {
      const { isClosePreview } = action.payload;
      if (isClosePreview) {
        return {
          ...state,
          tdoIdPreview: '',
          isShowPreview: false,
        };
      }

      return {
        ...state,
        tdoIdPreview: '',
        isShowPreview: false,
        // reset data files
        ...clearSelectionValues,
      };
    })
    .addCase(IS_EXPORT_ALL, (state, action) => {
      return {
        ...state,
        isExportAll: get(action, 'payload.isExportAll', false),
      };
    })
    .addCase(FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS, (state, action) => {
      return {
        ...state,
        schemaIdNotification: get(action, 'payload.schemas.records[0].id', ''),
      };
    })
    .addCase(FETCH_GET_NOTIFICATION_SUCCESS, (state, action) => {
      const data = get(action, 'payload.structuredDataObjects.records', []);
      const notifications = data.filter((item) => item.data.jobId);
      return {
        ...state,
        notifications: notifications,
        totalNotifications: notifications.filter((item) => !item.data.isRead)
          .length,
      };
    })
    .addCase(FETCH_UPDATE_IS_READ_NOTIFICATION_SUCCESS, (state, action) => {
      const dataJobsUpdate: { id: string }[] = Object.values(action.payload);
      const notifications = state.notifications as Notification[];
      const notificationUpdate: Notification[] = [];
      notifications.forEach((notification) => {
        const dataUpdate = dataJobsUpdate.find(
          (item) => item.id === notification.id
        ) as Notification;
        if (dataUpdate) {
          notificationUpdate.push({
            id: dataUpdate.id,
            data: dataUpdate.data,
          });
        } else {
          notificationUpdate.push(notification);
        }
      });
      return {
        ...state,
        notifications: notificationUpdate,
        totalNotifications: 0,
      };
    })
    .addCase(FETCH_REMOVE_NOTIFICATION_SUCCESS, (state, action) => {
      const notificationId = get(action, 'payload.deleteStructuredData.id', '');
      const notifications = state.notifications;
      const notificationFilter = notifications.filter(
        (item) => item.id !== notificationId
      );
      return {
        ...state,
        notifications: notificationFilter,
      };
    })
    .addCase(FETCH_SHOW_CONFIRM_REMOVE_NOTIFICATION, (state, action) => {
      return {
        ...state,
        confirmNotification: {
          isShowConfirm: true,
          notificationId: action.payload,
        },
      };
    })
    .addCase(FETCH_HIDE_CONFIRM_REMOVE_NOTIFICATION, (state) => {
      return {
        ...state,
        confirmNotification: {
          ...state.confirmNotification,
          isShowConfirm: false,
        },
      };
    })
    .addCase(UPDATE_ILLUMINATE_APP_FOLDER, (state, action) => {
      return {
        ...state,
        // parentFolderId: action.payload,
        illuminateAppFolders: [...action.payload],
      };
    })
    .addCase(SHOW_BULK_EXPORT, (state) => {
      return {
        ...state,
        isShowBulkExport: true,
      };
    })
    .addCase(HIDE_BULK_EXPORT, (state, _action) => {
      return {
        ...state,
        isShowBulkExport: false,
      };
    })
    .addCase(FETCH_UPDATE_HIDE_NOTIFICATION_SUCCESS, (state, action) => {
      const dataJobsUpdate: { id: string }[] = Object.values(action.payload);
      const notifications = [...state.notifications];
      const notificationUpdate = notifications.map((notification) => {
        const dataUpdate = dataJobsUpdate.find(
          (item) => item.id === notification.id
        ) as Notification;
        if (dataUpdate) {
          return {
            ...notification,
            data: dataUpdate.data,
          };
        }
        return notification;
      });
      return {
        ...state,
        notifications: notificationUpdate,
      };
    })
    .addCase(CLEAR_EXPORT_DATA, (state: any) => {
      return {
        ...state,
        ...clearSelectionValues,
        exportingJob: false,
        isExportAll: false,
      };
    })
    .addCase(DELETED_TDOS, (state, action) => {
      return {
        ...state,
        ...clearSelectionValues,
        deletedTdos: [...state.deletedTdos, ...action.payload],
      };
    })
    .addCase(MOVED_TDOS, (state, action) => {
      return {
        ...state,
        ...clearSelectionValues,
        movedTdos: [...state.movedTdos, ...action.payload],
      };
    })
    .addCase(UPDATE_INDEXES_BY_TDOS, (state, action) => {
      const { data, currentPage } = action.payload;
      state.indexesByTdos[currentPage] = getIndexesBySelectedTdos(
        data,
        state.selectedTdos
      );
    })
    .addCase(UPDATE_EXPORT_AND_REPROCESS_TDOS, (state, action) => {
      const { data, currentPage } = action.payload;
      const newSelectedTdos = state.selectedTdos;

      const newIndexesBySelectedTdoIds: { [key: number]: number[] } =
        state.indexesByTdos;
      newSelectedTdos[currentPage] = [];
      newIndexesBySelectedTdoIds[currentPage] = [];
      data.forEach((tdo, tdoIndex) => {
        newSelectedTdos[currentPage]?.push({
          id: tdo.id,
          primaryAsset: tdo.primaryAsset,
          // use ?? to handle case tdo's status is error
          assets: { ...tdo.assets, records: [...(tdo.assets?.records ?? [])] },
        });
        newIndexesBySelectedTdoIds[currentPage]?.push(tdoIndex);
      });
      const tdos = Object.values(newSelectedTdos).flat();
      const tdoIds = Object.values(newSelectedTdos).flatMap((item) =>
        item.map((subItem) => subItem.id)
      );
      state.tdosForExport = tdoIds;
      state.tdosForReprocess = tdos;
      state.selectedTdos = newSelectedTdos;
      state.indexesByTdos = newIndexesBySelectedTdoIds;
    })
    .addCase(UPDATE_SELECTED_ALL, (state, action) => {
      return {
        ...state,
        isSelectedAll: action.payload,
      };
    })
    .addCase(UPDATE_MOVED_TDOS, (state, action) => {
      return {
        ...state,
        movedTdos: action.payload,
      };
    });
});

export const setSelection = (
  selectedRows: Array<string | number>,
  isSelectedAll: boolean,
  allTdos?: TDO[],
  currentPage?: number,
  indeterminate?: boolean
) =>
  ON_SELECTION_CHANGE({
    selectedRows,
    isSelectedAll,
    allTdos,
    currentPage,
    indeterminate: indeterminate ?? false,
  });
export const unselectRows = () => UNSELECTED_ALL();

// export const createExportJobOld =
//   (
//     engineIdCreateJob: string,
//     tdoId: string,
//     assetId: string,
//     defaultClusterId: string,
//     batchSize: any
//   ) =>
//   (dispatch: any, getState: any) => {
//     const query = `
//   mutation createJob($input: CreateJob){
//     createJob(input: $input) {
//       id
//     }
//   }`;

//     const variables = {
//       input: {
//         targetId: tdoId,
//         tasks: [
//           {
//             engineId: engineIdCreateJob,
//             payload: {
//               payloadAssetId: assetId,
//               batchSize: batchSize,
//             },
//           },
//         ],
//         routes: [],
//         clusterId: '',
//       },
//     };

//     if (defaultClusterId) {
//       variables.input.clusterId = defaultClusterId;
//     }
//     return callGraphQLApi({
//       actionTypes: [
//         FETCH_CREATE_JOB,
//         FETCH_CREATE_JOB_SUCCESS,
//         FETCH_CREATE_JOB_FAILURE,
//       ],
//       query,
//       variables,
//       dispatch,
//       getState,
//     });
//   };

interface FetchEngineCategoriesResponse {
  engineCategories: {
    count: number;
    records: {
      id: string;
      name: string;
      iconClass: string;
    }[];
  };
}
export const fetchEngineCategories = () => (dispatch: any, getState: any) => {
  const query = `query engineCategories{
    engineCategories(limit:200){
      count
      records{
        id
        name
        iconClass
      }

    }
   }
  `;
  return callGraphQLApi<FetchEngineCategoriesResponse>({
    actionTypes: [
      FETCH_ENGINE_CATEGORIES,
      FETCH_ENGINE_CATEGORIES_SUCCESS,
      FETCH_ENGINE_CATEGORIES_FAILURE,
    ],
    query,
    variables: {},
    dispatch,
    getState,
  });
};

interface FetchSchemasNotificationResponse {
  schemas: {
    records: {
      id: string;
      status: string;
      definition: string;
      validActions: string[];
    }[];
  };
}
export const fetchSchemasNotification =
  (_dataRegistryId?: string) => (dispatch: any, getState: any) => {
    const query = `query getSchemas {
    schemas(name: "Notifications Schema") {
      records {
        id
        status
        definition
        validActions
      }
    }
  }`;
    return callGraphQLApi<FetchSchemasNotificationResponse>({
      actionTypes: [
        FETCH_SCHEMAS_NOTIFICATIONS,
        FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS,
        FETCH_SCHEMAS_NOTIFICATIONS_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

interface FetchGetNotificationsResponse {
  structuredDataObjects: {
    count: number;
    records: {
      id: string;
      data: {
        jobId: string;
        isRead: boolean;
        status: string;
        userId: string;
        engineId: string;
        applicationKey: string;
        organizationId: number;
        hide: any;
        exportName: string;
        tdoId: string;
        exportType: string;
      };
    }[];
  };
}
export const fetchGetNotification =
  (schemaId: string, userId: string, organizationId: string) =>
  (dispatch: any, getState: any) => {
    const query = `query getNotification{
    structuredDataObjects(
      schemaId: "${schemaId}",
      orderBy: {
        field: modifiedDateTime,
        direction: desc
      },
      owned:true,
      limit:10,
      filter: {
        userId: "${userId}",
        applicationKey: "illuminate",
        organizationId: "${organizationId}",
        hide: "false"
      }
    ){
      count
      records{
        id
        data
      }
    }
  }`;
    return callGraphQLApi<FetchGetNotificationsResponse>({
      actionTypes: [
        FETCH_GET_NOTIFICATION,
        FETCH_GET_NOTIFICATION_SUCCESS,
        FETCH_GET_NOTIFICATION_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export type FetchNotificationsResponse = Record<
  string,
  {
    id: string;
    data: any;
    createdDateTime: string;
    modifiedDateTime: string;
  }
>;

export const fetchNotifications =
  (data: Notification[], actionTypes: GQLActions<FetchNotificationsResponse>) =>
  (dispatch: any, getState: any) => {
    const sdo = [] as string[];
    let variables;

    data.forEach((item, key) => {
      sdo.push(`
      structuredData_${key}: createStructuredData(input: {
          id:  ${item.id ? `"${item.id}"` : null},
          schemaId: "${item.schemaId}",
          data: {
            tdoId: "${item.data.tdoId}",
            exportType: "${item.data.exportType}",
            jobId: "${item.data.jobId}",
            isRead: ${item.data.isRead},
            status: "${item.data.status}",
            userId: "${item.data.userId}",
            engineId: "${item.data.engineId}",
            applicationKey: "${item.data.applicationKey}",
            organizationId: ${item.data.organizationId},
            exportName: "${item.data.exportName}",
            hide: ${item.data.hide}
          }
        }) {
          id
          data
          createdDateTime
          modifiedDateTime
        }
      `);
    });
    const query = `
      mutation structuredDataNotification {
          ${sdo.join('\n')}
      }
    `;

    return callGraphQLApi<FetchNotificationsResponse>({
      actionTypes,
      query,
      variables: variables ? variables : {},
      dispatch,
      getState,
    });
  };

export type FetchGetJobResponse = Record<
  string,
  {
    id: string;
    status: string;
  }
>;

export const fetchGetJob =
  (data: string[], actionTypes: GQLActions<FetchGetJobResponse>) =>
  (dispatch: any, getState: any) => {
    const jobs = [] as string[];
    data.forEach((item: string, key: number) => {
      jobs.push(`
        getJob_${key}: job(id: "${item}") {
        id
          status
        }
      `);
    });

    const query = `
    query getJob {
      ${jobs.join('\n')}
  }`;

    return callGraphQLApi<FetchGetJobResponse>({
      actionTypes,
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export const fetchUpdateStatusReadNotification = () => ({
  type: FETCH_UPDATE_IS_READ_NOTIFICATION_REQUEST,
});
export const fetchRemoveNotificationAction = (payload: string) =>
  FETCH_REMOVE_NOTIFICATION_REQUEST(payload);
interface FetchRemoveNotificationResponse {
  deleteStructuredData: {
    id: string;
    message: string;
  };
}
export const fetchRemoveNotification =
  (notificationId: string, schemaId: string) =>
  (dispatch: any, getState: any) => {
    const query = `
    mutation deleteSDO{
      deleteStructuredData(input:{
        id: "${notificationId}",
        schemaId: "${schemaId}"
      }){
        id
        message
      }
    }`;

    return callGraphQLApi<FetchRemoveNotificationResponse>({
      actionTypes: [
        FETCH_REMOVE_NOTIFICATION,
        FETCH_REMOVE_NOTIFICATION_SUCCESS,
        FETCH_REMOVE_NOTIFICATION_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export const fetchConfirmRemoveNotification = (payload: string) =>
  FETCH_SHOW_CONFIRM_REMOVE_NOTIFICATION(payload);

export const getSelectedTdo = (
  state: any
): { tdoData: Array<string>; searchQuery: any } => {
  let tdoData = [...state.tdosTable.tdosForExport];
  let queryForSelectedAll = {};
  if (isSelectedAll(state) || state.tdosTable.isExportAll) {
    queryForSelectedAll = prepareSearchQueryForEngine(
      getQueryToExportAll(state)
    );
    tdoData = [];
  }
  return { tdoData: tdoData, searchQuery: queryForSelectedAll };
};

export const updateIlluminateAppFolder = (
  folders: {
    id: string;
    treeObjectId: string;
  }[]
) => UPDATE_ILLUMINATE_APP_FOLDER(folders);

export const updateTdoIdPreview = (tdoId: string) =>
  UPDATE_TDO_ID_PREVIEW(tdoId);

export const openTdoPreview = ({
  isShowPreview,
  initFullScreen,
}: {
  isShowPreview: boolean;
  initFullScreen: boolean;
}) =>
  ON_CHANGE_SHOW_DETAIL_TDO({
    isShowPreview,
    initFullScreen,
  });

export const clearExportData = () => CLEAR_EXPORT_DATA();

export const getCountFromSelectedTdos = (data: SelectedTdos) => {
  let count = 0;
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const items = data[key];
      if (Array.isArray(items)) {
        count += items.length;
      }
    }
  }
  return count;
};

export default reducer;
export const namespace = 'tdosTable';
export const local = (state: any) => state[namespace] as typeof defaultState;

export const isSelectedAll = (state: any) => local(state).isSelectedAll;
export const getSelectedRows = (state: any) => local(state).selectedRows;
export const getExportMsg = (state: any) => local(state).exportMsg;
export const getEngineCategories = (state: any) =>
  local(state).engineCategories;
export const getTdosForExport = (state: any) => local(state).tdosForExport;
export const getIndeterminate = (state: any) =>
  local(state).indeterminate || false;
export const getIsShowPreview = (state: any) => local(state).isShowPreview;
export const getInitFullScreen = (state: any) => local(state).initFullScreen;
export const getTdoIdPreview = (state: any) => local(state).tdoIdPreview;
export const getSchemaIdNotification = (state: any) =>
  local(state).schemaIdNotification;
export const getNotifications = (state: any) => local(state).notifications;
export const getTotalNotifications = (state: any) =>
  local(state).totalNotifications;
export const getAllState = (state: any) => state;
export const getConfirmNotificaions = (state: any) =>
  local(state).confirmNotification;
export const getIlluminateAppFolders = (state: any) =>
  local(state).illuminateAppFolders;
export const getIsShowBulkExport = (state: any) =>
  local(state).isShowBulkExport;
export const getDisableAnalytics = (state: any) =>
  hasFeature(state, 'illuminate.disableAnalytics');
export const getContactAnalyticsStatus = (state: any) =>
  hasFeature(state, 'illuminate.contactAnalytics');
export const getContactAnalyticsPbiStatus = (state: any) =>
  hasFeature(state, 'illuminate.contactAnalyticsPbi');
export const getTdosForReprocess = (state: any) =>
  local(state).tdosForReprocess;
export const getIsExportAll = (state: any) => local(state).isExportAll;
export const getEnableRedactNavigation = (state: any) =>
  hasFeature(state, 'illuminate.enableRedactNavigation');
export const getDeletedTdos = (state: any) => local(state).deletedTdos;
export const getMovedTdos = (state: any) => local(state).movedTdos;
export const getSelectedTdos = (state: any) => local(state).selectedTdos;
export const getIndexesByTdos = (state: any) => local(state).indexesByTdos;
export const getSelectedCount = (state: any) =>
  getCountFromSelectedTdos(local(state).selectedTdos);
