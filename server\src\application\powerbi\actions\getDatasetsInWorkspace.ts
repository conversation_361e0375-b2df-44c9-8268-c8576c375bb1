import axios from 'axios';
import { ApiError, NoDatasetsError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';
import sleep from '../../../util/sleep';

export interface DatasetsResponse {
  value: {
    '@odata.context': string;
    id: string;
    name: string;
  }[];
}

const getDatasetsInWorkspace = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    let poll = true;
    let pollAttempts = 20;

    while (poll && pollAttempts > 0) {
      const { data: resp } = await axios.get<DatasetsResponse>(
        `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/datasets`,
        {
          headers: {
            Authorization: data.pbiBearerToken,
            'X-PowerBI-Profile-Id': data.profileId,
          },
        }
      );

      const newDataset = resp.value.find(
        (v) => v.name === data.datasetDisplayName
      );

      if (newDataset) {
        poll = false;
        data.datasetId = newDataset.id;
        data.datasetName = newDataset.name;
      } else {
        log.error('No datasets available! Retrying...');
        await sleep(1000);
        pollAttempts--;
      }
    }

    if (!data.datasetId) {
      log.error(
        'No datasets available! Dataset was not created following PBIX upload...'
      );
      throw new NoDatasetsError();
    }

    return context;
  } catch (e) {
    log.error('GetDatasetsInWorkspace failed', e);
    throw new ApiError(e);
  }
};

export default getDatasetsInWorkspace;
