import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { Config } from '../chartDefinitions';

export default {
  dataQueries: [
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound,
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
            select: [""]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'search',
      dataKey: 'totalResults',
    },
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
                {
                  operator: "term"
                  field: "actionsTakenDuringStop"
                  value: "Property was seized"
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
            select: [""]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'searchAndSeizure',
      dataKey: 'totalResults',
    },
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            select: [""]
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'totalRecords',
      dataKey: 'totalResults',
    },
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    // Add data
    chart.data = [
      {
        action: 'Search',
        numberOfPeople: data.search,
      },
      {
        action: 'Search and Seizure',
        numberOfPeople: data.searchAndSeizure,
      },
      {
        action: 'Total People',
        numberOfPeople: data.totalRecords,
      },
    ];

    // Create axes
    const categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = 'action';
    categoryAxis.title.text = 'Action';

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Number of People';
    valueAxis.min = 0;
    valueAxis.max = Math.max(...chart.data.map((d) => d.numberOfPeople));

    // Create series
    const series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = 'numberOfPeople';
    series.dataFields.categoryX = 'action';
    series.columns.template.tooltipText =
      'Series: {name}\nCategory: {categoryX}\nValue: {valueY}';
    series.columns.template.fill = am4core.color('#104547');

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

interface Data {
  search: number;
  searchAndSeizure: number;
  totalRecords: number;
}
