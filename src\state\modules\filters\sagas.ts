import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import get from 'lodash/get';
import {
  addDateToSearchQuery,
  getFilteredFileType,
  getSelectedTopics,
  APPLY_FILTERS,
  INITIALIZE_TOPICS,
  UPDATE_ENTITY_NAMES,
  updateFileNames,
  updateIds,
  CLEAR_FILTERS,
} from '.';
import {
  updateFileTypeQuery,
  updateTopicsQuery,
  updateFileNamesQuery,
  updateIdsQuery,
  updateEnginesRunQuery,
  updateDurationQuery,
  dispatchSearchMediaAction,
  QUERY_TYPE,
  CLEAR_FILTERS_QUERY,
} from '../search';
import {
  getColouredWords,
  SEARCH_PATH_TOPIC,
  FETCH_WORDCLOUD_AGGREGATIONS_SUCCESS,
} from '../wordcloud';
import {
  getAnalyticsData,
  FETCH_SUNBURST_AGGREGATIONS_SUCCESS,
  FETCH_SUNBURST_AGGREGATIONS,
} from '../sunburst';
import { FETCH_MEDIA_AGGREGATIONS } from '../dashboard';

function* watchApplyFilters() {
  yield* all([
    takeLatest(APPLY_FILTERS, function* (action) {
      const date = get(action, 'payload.date', {
        startDate: '',
        endDate: '',
      });
      const fileNames = action?.payload?.fileNames || [];
      const ids = action?.payload?.ids || [];
      const enginesRun = action?.payload?.enginesRun || [];
      const duration = get(action, 'payload.duration', {});
      const newEnginesRun = enginesRun.map((item) => item.id);
      yield* put(addDateToSearchQuery(date.startDate, date.endDate));
      const fileTypes = yield* select(getFilteredFileType);
      const { video, audio, image, doc } = fileTypes;
      const files = [...video, ...audio, ...image, ...doc];
      yield* put(updateFileTypeQuery(files, null));
      const selectedTopics = yield* select(getSelectedTopics);
      yield* put(updateFileNames(fileNames));
      yield* put(updateFileNamesQuery(fileNames));
      yield* put(updateIds(ids));
      yield* put(updateIdsQuery(ids));
      yield* put(updateEnginesRunQuery(newEnginesRun));
      yield* put(updateDurationQuery(duration as any));
      yield* put(
        updateTopicsQuery(
          SEARCH_PATH_TOPIC,
          selectedTopics,
          QUERY_TYPE.APPLY_FILTER
        ) as any
      );
      yield* put(dispatchSearchMediaAction(0));
      yield* put(FETCH_SUNBURST_AGGREGATIONS()); // update sunburst
      yield* put(FETCH_MEDIA_AGGREGATIONS()); // update cards
    }),
  ]);
}
function* watchSunburstAggregationsSuccess() {
  yield* all([
    takeLatest(FETCH_SUNBURST_AGGREGATIONS_SUCCESS, function* () {
      const analyticsData = yield* select(getAnalyticsData);
      const entityArray = get(analyticsData, 'children', []);
      const entityNames: string[] = entityArray.map(
        (el: { name: string }) => el.name
      );
      yield* put(UPDATE_ENTITY_NAMES(entityNames));
    }),
  ]);
}

function* watchTopicsAggregationsSuccess() {
  yield* all([
    takeLatest(FETCH_WORDCLOUD_AGGREGATIONS_SUCCESS, function* () {
      const coloredWords = yield* select(getColouredWords);
      const topics: string[] = [];
      for (const element of Object.values(coloredWords)) {
        topics.push(...element);
      }
      yield* put(INITIALIZE_TOPICS(topics));
    }),
  ]);
}

function* watchClearFilters() {
  yield* takeLatest(CLEAR_FILTERS, function* () {
    yield* put(CLEAR_FILTERS_QUERY());
    yield* put(dispatchSearchMediaAction(0));
    yield* put(FETCH_SUNBURST_AGGREGATIONS()); // update sunburst
    yield* put(FETCH_MEDIA_AGGREGATIONS()); // update cards
  });
}

export default function* filters() {
  yield* all([
    fork(watchApplyFilters),
    fork(watchSunburstAggregationsSuccess),
    fork(watchTopicsAggregationsSuccess),
    fork(watchClearFilters),
  ]);
}
