import React from 'react';
import Processing from '../processing';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('Processing', () => {
  const props = {
    handleShowAdvancedCognitive: jest.fn(),
    engineCategories: [
      {
        id: '581dbb32-ea5b-4458-bd15-8094942345e3',
        name: 'Transcode',
        iconClass: 'icon-engine-transcode',
        description: 'Converts incoming data formats to another.',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        description: 'Converts speech audio to text.',
      },
    ],
    librariesByCategories: {
      '6faad6b7-0837-45f9-b161-2f6bf31b7a07': {
        '4e48925b-2cfc-43b3-8794-53e6e683651f': {
          coverImageUrl: '',
          createdDateTime: '2021-01-12T09:40:23.000Z',
          id: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          libraryId: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          name: 'Quan_Test3',
          organizationId: '1',
          summary: { entityCount: 1, unpublishedEntityCount: 0 },
          version: 1,
        },
      },
    },
    enginesSelected: [
      {
        categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        categoryName: 'Transcription',
        engineIds: [
          {
            id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
            description: 'This engine converts US English speech to text.',
            edgeVersion: 3,
            mode: 'Chunk',
            name: 'Speechmatics Transcription - English (Global) V3',
            runtimeType: 'edge',
            standaloneJobTemplates: [],
            fields: [
              {
                defaultValue: 'true',
                info: 'diarise',
                label: 'diarise',
                max: null,
                min: null,
                name: 'diarise',
                step: null,
                type: 'Picklist',
                options: [
                  {
                    keys: 'false',
                    value: 'false',
                  },
                  {
                    keys: 'true',
                    value: 'true',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    currentEngineCategory: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    handleChangeEngine: jest.fn(),
    handleSearchEngine: jest.fn(),
    engineByCategories: {
      '67cd4dd0-2f75-445d-a6f0-2f297d6cd182': [
        {
          id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
          isPublic: true,
          isSelected: false,
          name: 'Speechmatics Transcription - English (Global) V3',
          description: 'This engine converts US English speech to text.',
        },
      ],
    },
    engineNameSearch: '',
    templateSelected: '',
    handleChangeTemplates: jest.fn(),
    templates: [],
    handleShowModalSaveTemplate: jest.fn(),
    checkValidateLibrary: false,
    handleAddEngine: jest.fn(),
    handleExpandClick: jest.fn(),
    handleRemoveEngine: jest.fn(),
    handleChangeLibrariesEngineSelected: jest.fn(),
    handleChangeFieldsEngine: jest.fn(),
    handleChangeJobPriority: jest.fn(),
    handleRemoveTemplate: jest.fn(),
    loadingRemoveTemplate: false,
    isReprocess: false,
  };

  it('renders a title with content Advanced Cognitive Workflow', () => {
    const { getByText } = render(<Processing {...props} />);
    expect(getByText('Advanced Cognitive Workflow')).toBeInTheDocument();
  });
  it('renders a title with content Show Simple Cognitive Workflow', () => {
    const { getByText } = render(<Processing {...props} />);
    expect(getByText('Show Simple Cognitive Workflow')).toBeInTheDocument();
  });
  it('click Show Simple Cognitive Workflow', () => {
    const { getByTestId } = render(<Processing {...props} />);
    fireEvent.click(getByTestId('show-simple-cognitive-workflow'));
    expect(props.handleShowAdvancedCognitive).toHaveBeenCalled();
  });
  it('render available engine Grid', () => {
    const { getByTestId } = render(<Processing {...props} />);
    expect(getByTestId('available-engine-grid')).toBeInTheDocument();
  });
  it('render forward Grid', () => {
    const { getByTestId } = render(<Processing {...props} />);
    expect(getByTestId('forward-grid')).toBeInTheDocument();
  });
  it('render engine selected Grid', () => {
    const { getByTestId } = render(<Processing {...props} />);
    expect(getByTestId('engine-selected-grid')).toBeInTheDocument();
  });
});
