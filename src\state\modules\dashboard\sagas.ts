import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import { returnSearchQuery } from '../../utils/queryConstructor';
import { get } from 'lodash';
import {
  fetchMediaAggregations,
  emitFetchMediaAggregationsAction,
  fetchProcessedTime,
  constructQueryProcessedTime,
  FILE_TYPE_AGGREGATION,
  QUERY_RANGE,
  FETCH_MEDIA_AGGREGATIONS,
  FETCH_MEDIA_AGGREGATIONS_SUCCESS,
  FETCH_MEDIA_AGGREGATIONS_FAILURE,
  FETCH_PROCESSED_TIME_FOR_ONE_ENGINE,
  FETCH_TOTAL_MEDIA_PROCESSED_TIME,
} from '.';
import {
  ON_FOLDER_QUERY_CHANGE,
  getFullSearchQuery,
  getSearchBarQuery,
} from '../search';
import { getEngineCategories, getDisableAnalytics } from '../tdosTable';
import config from '../../../../config.json';

const { displayedEngineNames, excludedEngineNames } = config;
export interface DurPerEngineClass {
  id: string;
  iconClass: string;
  name: string;
  duration: string;
}

function* watchFetchMediaAggregations() {
  yield* all([takeLatest(FETCH_MEDIA_AGGREGATIONS, doFetchMediaAggregations)]);
}

function* doFetchMediaAggregations() {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    const fullSearchQuery = yield* select(getFullSearchQuery);

    const queryAggregate = returnSearchQuery(
      fullSearchQuery,
      QUERY_RANGE,
      FILE_TYPE_AGGREGATION
    );
    yield* put(
      fetchMediaAggregations(
        queryAggregate,
        FETCH_MEDIA_AGGREGATIONS_SUCCESS,
        FETCH_MEDIA_AGGREGATIONS_FAILURE
      )
    );
  }
}

function* watchActionsToCauseAggregationsFetch() {
  // here everytime one of these emitted - we will refetch aggregations
  yield* all([takeLatest(ON_FOLDER_QUERY_CHANGE, doEmitFetchAggregations)]);
}

function* doEmitFetchAggregations() {
  yield* put(emitFetchMediaAggregationsAction() as any);
}

function* watchToDisplayEngineCategories() {
  yield* all([
    takeLatest(FETCH_MEDIA_AGGREGATIONS_SUCCESS, doFetchOneEngineProcessedTime),
    takeLatest(FETCH_MEDIA_AGGREGATIONS, doFetchTotalMediaProcessedTime),
  ]);
}

function* doFetchOneEngineProcessedTime(): any {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    // fetch processed hours for one type of engine
    const engineCategories = yield* select(getEngineCategories);
    const engineCategoriesArr = Object.values(engineCategories);
    const displayedEngines = displayedEngineNames.map((name) => {
      return engineCategoriesArr.filter((engine) => {
        if (name === engine.name) {
          return engine;
        }
      })[0];
    });

    const searchBarQuery = yield* select(getSearchBarQuery);
    const fullSearchQuery = yield* select(getFullSearchQuery);
    const engineQuery = constructQueryProcessedTime(
      fullSearchQuery,
      searchBarQuery
    );

    for (let i = 0; i < displayedEngines.length; i++) {
      let durPerEngineClass: DurPerEngineClass;
      const engine = displayedEngines[i];
      if (engine) {
        engineQuery.query.conditions[0].values[0] = engine.id;
        const response = yield* put(fetchProcessedTime(engineQuery));
        const data = yield response;
        const durationMilisec = get(data, 'aggregations.count.value', 0);
        let duration = Math.round(durationMilisec / 3600).toLocaleString();
        if (durationMilisec < 3600) {
          duration = Math.ceil(durationMilisec / 3600).toLocaleString();
        }
        durPerEngineClass = {
          id: engine.id,
          iconClass: engine.iconClass,
          name: engine.name,
          duration,
        };
        // change iconClass for Content Classification because it's not right
        if (!engine.iconClass.includes('icon')) {
          durPerEngineClass.iconClass = 'icon-content-copy';
        }
      } else {
        durPerEngineClass = {
          id: 'N/A',
          iconClass: 'N/A',
          name: 'N/A',
          duration: 'N/A',
        };
      }
      yield* put(FETCH_PROCESSED_TIME_FOR_ONE_ENGINE(durPerEngineClass));
    }
  }
}

function* doFetchTotalMediaProcessedTime(): any {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    // fetch TOTAL MEDIA HOURS PROCESSED

    const engineCategories = yield* select(getEngineCategories);
    const engineCategoriesArr = Object.values(engineCategories);
    const usingEngines = engineCategoriesArr.filter(
      (engine) => !excludedEngineNames.includes(engine.name)
    );
    const usingEngineIds = usingEngines.map((engine) => engine.id);
    const searchBarQuery = yield* select(getSearchBarQuery);
    const fullSearchQuery = yield* select(getFullSearchQuery);
    const engineQuery = constructQueryProcessedTime(
      fullSearchQuery,
      searchBarQuery,
      usingEngineIds
    );
    const response = yield* put(fetchProcessedTime(engineQuery));
    const data = yield response;
    yield* put(FETCH_TOTAL_MEDIA_PROCESSED_TIME(data));
  }
}

export default function* dashboard() {
  yield* all([
    fork(watchFetchMediaAggregations),
    fork(watchActionsToCauseAggregationsFetch),
    fork(watchToDisplayEngineCategories),
  ]);
}
