import { createReducer } from '@reduxjs/toolkit';
import { get, cloneDeep } from 'lodash';
import * as actions from './actions';
import { ProcessingJob, Job } from './models';
import { GQLApi } from '../../../helpers/gqlApi';
import { callAsyncFunc } from '../../../helpers/apiHelper';
import { DateTime } from 'luxon';
// import { MaterialUiPickersDate } from '@material-ui/pickers/typings/date';

const defaultState = {
  currentPage: 0,
  pageSize: 10,
  statusFilter: [
    {
      name: actions.STATUS_PROCESSING_JOBS.inQueue.name,
      checked: false,
      value: actions.STATUS_PROCESSING_JOBS.inQueue.value,
    },
    {
      name: actions.STATUS_PROCESSING_JOBS.inProgress.name,
      checked: false,
      value: actions.STATUS_PROCESSING_JOBS.inProgress.value,
    },
    {
      name: actions.STATUS_PROCESSING_JOBS.complete.name,
      checked: false,
      value: actions.STATUS_PROCESSING_JOBS.complete.value,
    },
    {
      name: actions.STATUS_PROCESSING_JOBS.errors.name,
      checked: false,
      value: actions.STATUS_PROCESSING_JOBS.errors.value,
    },
  ] as { name: string; checked: boolean; value: readonly string[] }[],
  loadingProcessingJobs: false,
  noDataProcessing: false,
  dateTimeRangeFilter: 2,
  loadingRetryJobs: false,
  percentageFailedJobsUploaded: 0,
  selectedRows: {} as { [key: string]: (string | number)[] },
  isSelectedAllJobsFailed: false,
  indeterminate: false,
  processingJobs: [] as ProcessingJob[],
  dataJobsFailed: [] as Job[],
  totalProcessingJobs: 0,
  isFilterJobsFailed: false,
  showCustomRange: false as boolean | null,
  customStartDate: null as DateTime | null,
  customEndDate: null as DateTime | null,
  lastOffset: 0,
  remainingProcessingJobs: [] as ProcessingJob[],
  loadingChangePage: false,
  exportingFailedJobs: false,
  exportFailedJobsPercentage: 0,
};
const cleanUpSearchResults = {
  processingJobs: [] as ProcessingJob[],
  totalProcessingJobs: 0,
  currentPage: 0,
  selectedRows: {} as { [key: string]: (string | number)[] },
  indeterminate: false,
  lastOffset: 0,
  remainingProcessingJobs: [] as ProcessingJob[],
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(
      actions.FETCH_PROCESSING_JOBS_SUCCESS,
      (state, { payload: { results, remainingProcessingJobs, offset } }) => {
        return {
          ...state,
          processingJobs: state.processingJobs.concat(results),
          noDataProcessing: results.length ? false : true,
          lastOffset: offset,
          remainingProcessingJobs,
          loadingChangePage: false,
        };
      }
    )

    .addCase(
      actions.UPDATE_TOTAL_PROCESSING_JOBS_SUCCESS,
      (state, { payload: { total } }) => {
        return {
          ...state,
          totalProcessingJobs: total,
          loadingProcessingJobs: false,
        };
      }
    )

    .addCase(actions.REFRESH_PROCESSING_JOBS, (state) => {
      return {
        ...state,
        loadingProcessingJobs: true,
        noDataProcessing: false,
        loadingChangePage: true,
        ...cleanUpSearchResults,
      };
    })

    .addCase(
      actions.ON_CHANGE_PAGE_PROCESSING_JOBS,
      (state, { payload: { pageSize, currentPage } }) => {
        return {
          ...state,
          pageSize,
          currentPage,
          loadingProcessingJobs: true,
          noDataProcessing: false,
          loadingChangePage: true,
        };
      }
    )
    .addCase(
      actions.ON_CHANGE_PAGE_SIZE_PROCESSING_JOBS,
      (state, { payload: { pageSize } }) => {
        return {
          ...state,
          pageSize,
          loadingProcessingJobs: true,
          noDataProcessing: false,
          loadingChangePage: true,
          ...cleanUpSearchResults,
        };
      }
    )

    .addCase(
      actions.ON_CHANGE_STATUS_FILTER,
      (state, { payload: { value } }) => {
        const { statusFilter } = state;
        const updateStatusFilter = statusFilter.map((status) => {
          if (status.name === value.name) {
            return {
              ...status,
              checked: value.checked,
            };
          }
          return status;
        });
        return {
          ...state,
          statusFilter: updateStatusFilter,
        };
      }
    )
    .addCase(
      actions.FILTER_JOB_BY_DATE_TIME_RANGE,
      (state, { payload: { value } }) => {
        return {
          ...state,
          loadingProcessingJobs: true,
          dateTimeRangeFilter: value,
          noDataProcessing: false,
          loadingChangePage: true,
          ...cleanUpSearchResults,
        };
      }
    )
    .addCase(actions.FILTER_JOB_BY_STATUS, (state) => {
      const filterFailed = state.statusFilter.filter(
        (item) =>
          item.name === actions.STATUS_PROCESSING_JOBS.errors.name &&
          item.checked
      );
      return {
        ...state,
        loadingProcessingJobs: true,
        noDataProcessing: false,
        isFilterJobsFailed: filterFailed.length > 0,
        loadingChangePage: true,
        ...cleanUpSearchResults,
      };
    })

    .addCase(actions.RETRY_JOBS, (state) => {
      return {
        ...state,
        loadingRetryJobs: true,
      };
    })
    .addCase(
      actions.UPDATE_PERCENTAGE_FAILED_JOBS_UPLOADED,
      (state, { payload: { percentage } }) => {
        return {
          ...state,
          percentageFailedJobsUploaded: percentage,
        };
      }
    )
    .addCase(actions.RETRY_JOBS_SUCCESS, (state) => {
      return {
        ...state,
        loadingRetryJobs: false,
        percentageFailedJobsUploaded: 0,
      };
    })
    .addCase(actions.RETRY_JOBS_FAILED, (state) => {
      return {
        ...state,
        loadingRetryJobs: false,
        percentageFailedJobsUploaded: 0,
      };
    })
    //
    .addCase(actions.ON_SELECTION_JOBS_CHANGE, (state, action) => {
      const { selectedRows, isSelectedAll, indeterminate } = get(
        action,
        'payload'
      );
      const processingJobs = state.processingJobs || [];
      const dataJobsFailed: Job[] = [];

      processingJobs.forEach((processingJob) => {
        const keys = selectedRows[processingJob.targetId];
        Array.isArray(keys) &&
          keys.forEach((key) => {
            const job = processingJob.jobs[Number(key)];
            job && dataJobsFailed.push(job);
          });
      });
      return {
        ...state,
        selectedRows,
        isSelectedAllJobsFailed: isSelectedAll,
        indeterminate,
        dataJobsFailed,
      };
    })
    .addCase(actions.UPDATE_CURRENT_PAGE_PROCESSING_JOBS, (state, action) => {
      const currentPage = get(action, 'payload', 0);
      return {
        ...state,
        currentPage,
      };
    })
    .addCase(actions.SET_SHOW_CUSTOM_RANGE, (state, action) => {
      const showCustomRange = !!action.payload;
      return {
        ...state,
        showCustomRange,
      };
    })
    .addCase(actions.SET_CUSTOM_START_DATE, (state, action) => {
      const customStartDate = get(action, 'payload', null);
      return {
        ...state,
        customStartDate: customStartDate,
      };
    })
    .addCase(actions.SET_CUSTOM_END_DATE, (state, action) => {
      const customEndDate = get(action, 'payload', null);
      return {
        ...state,
        customEndDate: customEndDate,
      };
    })
    .addCase(actions.FILTER_JOB_BY_CUSTOM_DATE_RANGE, (state) => {
      return {
        ...state,
        loadingProcessingJobs: true,
        noDataProcessing: false,
        ...cleanUpSearchResults,
      };
    })
    .addCase(actions.CLEAR_CUSTOM_DATE_RANGE, (state) => {
      return {
        ...state,
        customStartDate: null,
        customEndDate: null,
        showCustomRange: null,
      };
    })
    .addCase(actions.EXPORT_FAILED_JOBS, (state) => {
      return {
        ...state,
        exportingFailedJobs: true,
      };
    })
    .addCase(actions.EXPORT_FAILED_JOBS_SUCCESS, (state) => {
      return {
        ...state,
        exportingFailedJobs: false,
        exportFailedJobsPercentage: 0,
      };
    })
    .addCase(actions.EXPORT_FAILED_JOBS_FAILED, (state) => {
      return {
        ...state,
        exportingFailedJobs: false,
        exportFailedJobsPercentage: 0,
      };
    })
    .addCase(
      actions.EXPORT_FAILED_JOBS_PERCENTAGE,
      (state, { payload: { percentage } }) => {
        return {
          ...state,
          exportFailedJobsPercentage: percentage,
        };
      }
    );
});

export const createFetchProcessingJobsRequest =
  (
    limit: number,
    offset: number,
    status: string[],
    dateTimeRangeFilter: number,
    remainingProcessingJobs: ProcessingJob[],
    customStartDate: DateTime | null,
    customEndDate: DateTime | null,
    isShowCustomRange: boolean
  ) =>
  (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await fetchProcessingJobGroupedByTdoId(
        gql,
        limit,
        offset,
        status,
        dateTimeRangeFilter,
        remainingProcessingJobs,
        customStartDate,
        customEndDate,
        isShowCustomRange
      );
      return {
        data: {
          results: data.results,
          remainingProcessingJobs: data.remainingProcessingJobs,
          offset: data.offset,
        },
      };
    };

    return callAsyncFunc({
      actionTypes: [
        actions.FETCH_PROCESSING_JOBS,
        actions.FETCH_PROCESSING_JOBS_SUCCESS,
        actions.FETCH_PROCESSING_JOBS_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function fetchProcessingJobGroupedByTdoId(
  gql: GQLApi,
  limit: number,
  offset: number,
  status: string[],
  dateTimeRangeFilter: number,
  remainingProcessingJobs: ProcessingJob[],
  customStartDate: DateTime | null,
  customEndDate: DateTime | null,
  isShowCustomRange: boolean
): Promise<{
  results: ProcessingJob[];
  remainingProcessingJobs: ProcessingJob[];
  offset: number;
}> {
  let results = [...remainingProcessingJobs];
  let newOffset = offset;
  const fromDateTime = new Date();
  fromDateTime.setHours(fromDateTime.getHours() - dateTimeRangeFilter);
  let fromDateTimeIso = fromDateTime.toISOString();
  let toDateTimeIso = new Date().toISOString();

  // Update from and to dateTime values if a custom date range is selected
  if (isShowCustomRange && customEndDate !== null && customStartDate !== null) {
    fromDateTimeIso = customStartDate?.toJSDate().toISOString();
    toDateTimeIso = customEndDate?.toJSDate().toISOString();
  }

  do {
    const response = await gql.getProcessingJobsByOffset({
      fromDateTimeIso,
      toDateTimeIso,
      status,
      offset: newOffset,
      limit,
    });

    if (response.errors) {
      const errors = [];
      for (const error of response.errors) {
        if (error?.message.includes('The requested TDO was not found')) {
          continue;
        }
        errors.push(error);
      }
      if (errors.length) {
        console.error('failed to fetchProcessingJobGroupedByTdoId', errors);
      }
    }

    const records = response?.data?.jobs?.records ?? [];

    // TODO: this is a workarond for https://veritone.atlassian.net/browse/AWT-11120
    // When querying for jobs with status queued, the jobs with
    // running status are returned, though all the tasks are queued.
    // The job status should be queued instead of “running“ for job with
    // all tasks queued.
    for (const job of records) {
      const isQueued = job.tasks.records.every(
        (task) => task.status === 'queued'
      );
      if (isQueued) {
        job.status = 'queued';
      }
    }

    const recordsWithTdo = [];
    const recordsWithoutTdo = [];
    for (const record of records) {
      if (record?.target?.id) {
        recordsWithTdo.push(record);
      } else {
        recordsWithoutTdo.push(record);
      }
    }
    if (recordsWithoutTdo.length) {
      const errMsg = recordsWithoutTdo.map((r) => r.id).join(', ');
      console.warn(`processing status: jobs without tdo: ${errMsg}`);
    }
    // merges data in results and recordsWithTdo, and groups the data by tdo id,
    // and returns a new array of data.
    const newData = groupsJobsByTdoId(results, recordsWithTdo);
    results = newData;
    newOffset += limit;
    if (records.length < limit) {
      break;
    }
    // Sometimes, the jobs of same tdo are across the page when paginating.
    // so, an extra page of jobs are queried for grouping job by tdo id.
  } while (results.length < limit * 2);
  const resultsByLimit = results.slice(0, limit);
  const newRemainingProcessingJobs = results.slice(limit, results.length);
  return {
    results: resultsByLimit,
    remainingProcessingJobs: newRemainingProcessingJobs,
    offset: newOffset,
  };
}

export const createFetchProcessingJobCountRequest =
  (
    timeFilter: number,
    status: string[],
    customStartDate: any,
    customEndDate: any,
    isShowCustomRange: boolean
  ) =>
  (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await fetchProcessingJobCount(
        gql,
        timeFilter,
        status,
        customStartDate,
        customEndDate,
        isShowCustomRange
      );
      return {
        data: {
          total: data.total,
        },
      };
    };

    return callAsyncFunc({
      actionTypes: [
        actions.UPDATE_TOTAL_PROCESSING_JOBS,
        actions.UPDATE_TOTAL_PROCESSING_JOBS_SUCCESS,
        actions.UPDATE_TOTAL_PROCESSING_JOBS_FAILED,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };
export async function fetchProcessingJobCount(
  gql: GQLApi,
  timeFilter: number,
  status: string[],
  customStartDate: any,
  customEndDate: any,
  isShowCustomRange: boolean
) {
  const fromDateTime = new Date();
  fromDateTime.setHours(fromDateTime.getHours() - timeFilter);
  let fromDateTimeIso = fromDateTime.toISOString();
  let toDateTimeIso = new Date().toISOString();

  // Update from and to dateTime values if a custom date range is selected
  if (isShowCustomRange && customEndDate !== null && customStartDate !== null) {
    fromDateTimeIso = customStartDate?.toJSDate().toISOString();
    toDateTimeIso = customEndDate?.toJSDate().toISOString();
  }
  const response = await gql.getJobCount({
    status,
    fromDateTimeIso,
    toDateTimeIso,
  });
  if (response.errors) {
    console.error('failed to fetchProcessingJobCount', response.errors);
  }
  return {
    total: response.count,
  };
}

function groupsJobsByTdoId(processingJob: ProcessingJob[], jobs: Job[]) {
  const newDataProcessingJobs: ProcessingJob[] = cloneDeep(processingJob);
  if (jobs.length === 0) {
    return newDataProcessingJobs;
  }
  for (const job of jobs) {
    const existingProcessingJob = newDataProcessingJobs.find(
      (processingJob) => processingJob.targetId === job.target?.id
    );
    if (existingProcessingJob) {
      existingProcessingJob.jobs.push(job);
    } else {
      newDataProcessingJobs.push({
        targetId: job.target?.id ?? '',
        name:
          job.target?.details?.veritoneFile?.filename ??
          job.target?.details?.veritoneFile?.fileName ??
          '',
        modifiedDateTime: job.modifiedDateTime,
        jobs: [{ ...job }],
      });
    }
  }
  return newDataProcessingJobs;
}

export default reducer;
export const namespace = 'processingJobs';
const local = (state: any) => state[namespace] as typeof defaultState;
export const processingJobs = (state: any) => local(state).processingJobs || [];
export const loadingProcessingJobs = (state: any) =>
  get(local(state), 'loadingProcessingJobs', false);
export const noDataProcessing = (state: any) =>
  get(local(state), 'noDataProcessing', false);
export const currentPage = (state: any) => get(local(state), 'currentPage', 0);
export const pageSize = (state: any) => get(local(state), 'pageSize', 10);
export const statusFilter = (state: any) => local(state).statusFilter || [];
export const dateTimeRangeFilter = (state: any) =>
  get(local(state), 'dateTimeRangeFilter', 2);
export const getLoadingRetryJobs = (state: any) =>
  get(local(state), 'loadingRetryJobs', false);
export const getPercentageFailedJobsUploaded = (state: any) =>
  get(local(state), 'percentageFailedJobsUploaded', 0);
export const getSelectedRows = (state: any) => local(state).selectedRows || [];
export const getIsSelectedAllJobsFailed = (state: any) =>
  get(local(state), 'isSelectedAllJobsFailed', false);
export const getIndeterminate = (state: any) =>
  get(local(state), 'indeterminate', false);
export const getDataJobsFailed = (state: any) =>
  get(local(state), 'dataJobsFailed', []);
export const getTotalProcessingJobs = (state: any) =>
  local(state).totalProcessingJobs || 0;
export const getIsFilterJobsFailed = (state: any) =>
  get(local(state), 'isFilterJobsFailed', false);
export const getShowCustomRange = (state: any) =>
  local(state).showCustomRange || false;
export const getCustomStartDate = (state: any) => local(state).customStartDate;
export const getCustomEndDate = (state: any) => local(state).customEndDate;
export const lastOffset = (state: any) => get(local(state), 'lastOffset', 0);
export const getRemainingProcessingJobs = (state: any) =>
  local(state).remainingProcessingJobs || [];
export const loadingChangePage = (state: any) =>
  local(state).loadingChangePage || false;
export const getExportingFailedJobs = (state: any) =>
  get(local(state), 'exportingFailedJobs', false);
export const getExportFailedJobsPercentage = (state: any) =>
  get(local(state), 'exportFailedJobsPercentage', 0);
