{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "target": "es5",
    "module": "ESNext",
    "lib": ["es2019", "dom", "dom.iterable", "scripthost", "webworker"],
    "noEmit": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "noImplicitAny": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "checkJs": true,
    "baseUrl": "./",
    "typeRoots": [
      "../node_modules",
      "../node_modules/@types",
      "../node_modules/veritone-types/@types",
      "../types"
    ],
    "types": ["cypress", "node"],
    "paths": {
      "resources/*": ["../resources/*"],
      "components/*": ["../src/components/*"],
      "pages/*": ["../src/pages/*"],
      "state/*": ["../src/state/*"],
      "modules/*": ["../src/state/modules/*"],
      "sagas/*": ["../src/state/sagas/*"],
      "~helpers/*": ["../src/helpers/*"],
      "@utils": ["../src/utils/index"]
    }
  },
  "include": [
    "**/*.js",
    "**/*.ts",
    "**/*.tsx",
  ],
  "exclude": [
    "node_modules"
  ]
}