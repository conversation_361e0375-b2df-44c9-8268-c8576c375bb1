{"compileOnSave": false, "compilerOptions": {"target": "es5", "module": "ESNext", "lib": ["es2019", "dom", "dom.iterable", "scripthost", "webworker"], "skipLibCheck": true, "allowJs": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "outDir": "./build", "removeComments": true, "importHelpers": true, "downlevelIteration": true, "resolveJsonModule": true, "strict": true, "noImplicitAny": true, "noUncheckedIndexedAccess": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./", "rootDir": "./", "typeRoots": ["node_modules/@types", "node_modules/veritone-types/@types", "./types"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"resources/*": ["resources/*"], "components/*": ["src/components/*"], "pages/*": ["src/pages/*"], "state/*": ["src/state/*"], "modules/*": ["src/state/modules/*"], "sagas/*": ["src/state/sagas/*"], "~helpers/*": ["src/helpers/*"], "@utils": ["src/utils/index"]}}, "include": ["src/**/*", "test/**/*"], "files": ["./src/index.tsx", "./src/types.d.ts"], "exclude": ["node_modules", "build", "src/**/*.js", "dist"]}