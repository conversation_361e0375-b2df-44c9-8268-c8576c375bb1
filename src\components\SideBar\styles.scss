@import 'src/variables';

$sidebarwidth: 220;
$color: rgba(0, 0, 0, 0.54);

:export {
  // stylelint-disable
  sidebarwidth: $sidebarwidth;
  // stylelint-enable
}

.list-container {
  margin-left: -20px;
  margin-top: 10px;
}

.list {
  padding-top: 1px;
}

.list-item-icon-folder svg {
  color: $color;
}

.list-item-icon-work svg {
  color: #f7ac2a;
}

.file-actions {
  color: #555f7c;
}

.icon-menu {
  display: none;
  padding: 2px;
  font-size: 16px;
}
.active-icon {
  display: block !important;
}
.none-icon {
  display: none;
}
.icon-menu svg {
  font-size: 18px;
}

.menu {
  width: 200px;
}

.list-item,
.list-item-selected {
  &:hover .icon-menu {
    display: block;
  }

  .arrow-right {
    color: $color;
  }

  &:hover .arrow-right {
    display: none;
  }
}

.list-item {
  padding: 11px 12px 11px 18px;

  > div {
    margin-right: 3px;
  }

  &:focus {
    background-color: #fff;
  }
}

.list-item-selected {
  background-color: #e0e0e0;
  padding: 11px 12px 11px 18px;

  > div {
    margin-right: 3px;
  }
}

.list-item-text {
  width: 100%;
  line-height: 15px;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: none;
  font-size: 13px;
  font-family: Roboto, sans-serif;
  padding-left: 5px;
  color: $color;
}

.folder {
  padding: 0;
}

.list-item-text-work {
  width: 100%;
  line-height: 14px;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: none;
  font-size: 12px;
  font-family: Roboto, sans-serif;
  padding-left: 5px;
  color: $color;
  font-weight: 500;

  > div {
    margin-right: 10px;
  }
}

.list-item-work-selected {
  background-color: #e0e0e0;
  padding: 11px 12px 11px 23px;

  > div {
    margin-right: 10px;
  }
}
