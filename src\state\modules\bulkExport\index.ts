import { isEmpty } from 'lodash';
import { G<PERSON><PERSON><PERSON> } from '../../../helpers/gqlApi';
import {
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '../../../helpers/apiHelper';
import {
  Notification,
  BulkExportParam,
  BulkExportAssetContent,
  BulkExportOption,
  BulkExportCustomizedNames,
} from '../../../model';
import { createAction } from '@reduxjs/toolkit';

export const DEFAULT_BULK_EXPORT_STATUS = 'running';
export const BULK_EXPORT_REQUEST = createAction<{
  exportName: string;
  exportOption: BulkExportOption;
  password?: string;
  customizedNames?: BulkExportCustomizedNames;
}>('bulk export request');
export const CREATE_BULK_EXPORT_REQUEST = createAsyncFuncRequestAction(
  'create bulk export request'
);
export const CREATE_BULK_EXPORT_SUCCESS = createAsyncFuncSuccessAction(
  'create bulk export success'
);
export const CREATE_BULK_EXPORT_FAILURE = createAsyncFuncFailureAction(
  'create bulk export failure'
);

export const createBulkExport =
  (exportParam: BulkExportParam) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await bulkExport(exportParam, gql);
      return { data };
    };
    return callAsyncFunc({
      actionTypes: [
        CREATE_BULK_EXPORT_REQUEST,
        CREATE_BULK_EXPORT_SUCCESS,
        CREATE_BULK_EXPORT_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function bulkExport(exportParam: BulkExportParam, gql: GQLApi) {
  const { tdoId, assetId } = await createExportAsset(exportParam, gql);
  if (!assetId) {
    throw new Error('bulk export assetId is empty');
  }
  const batchSize = exportParam.exportOption.hasNative
    ? exportParam.exportBatchSize.hasNative
    : exportParam.exportBatchSize.noNative;
  const jobId = await createExportJob(
    tdoId,
    exportParam.exportEngineId,
    exportParam.clusterId,
    assetId,
    batchSize,
    exportParam.password,
    gql
  );
  const notificationData: Notification = {
    data: {
      tdoId,
      jobId: jobId,
      isRead: false,
      status: DEFAULT_BULK_EXPORT_STATUS,
      userId: exportParam.userId,
      engineId: exportParam.exportEngineId,
      applicationKey: 'illuminate',
      organizationId: exportParam.organizationId,
      exportName: exportParam.exportName,
      exportType: exportParam.exportDestination,
      hide: false,
    },
  };
  const historyResult = await insertExportHistory(
    exportParam.notificationSchemaId,
    notificationData,
    gql
  );
  return { tdoId, assetId, jobId, sdoId: historyResult[0]?.id };
}

export async function createExportAsset(
  exportParam: BulkExportParam,
  gql: GQLApi
) {
  const data: BulkExportAssetContent = {
    exportDestination: exportParam.exportDestination,
    emailAddress: exportParam.emailAddress,
    exportName: exportParam.exportName,
    maxConcurrency: exportParam.maxConcurrency,
    organizationId: exportParam.organizationId,
    hasTdoId: exportParam.exportOption.hasTdoId,
    hasFilename: exportParam.exportOption.hasFilename,
    hasBaseFilename: exportParam.exportOption.hasBaseFilename,
    hasFoldername: exportParam.exportOption.hasFoldername,
    hasTag: exportParam.exportOption.hasTag,
    hasPlainText: exportParam.exportOption.hasPlainText,
    hasTtml: exportParam.exportOption.hasTtml,
    hasWord: exportParam.exportOption.hasWord,
    hasObjectNotation: exportParam.exportOption.hasObjectNotation,
    hasBookmark: exportParam.exportOption.hasBookmark,
    hasTranslation: exportParam.exportOption.hasTranslation,
    hasNativeTranslation: exportParam.exportOption.hasNativeTranslation,
    hasObjectDetection: exportParam.exportOption.hasObjectDetection,
    hasSentiment: exportParam.exportOption.hasSentiment,
    hasNative: exportParam.exportOption.hasNative,
    hasEngineName: exportParam.exportOption.hasEngineName,
    filePathWindows: exportParam.exportOption.filePathWindows,
    tdoData: exportParam.tdoData,
    searchQuery: exportParam.searchQuery,
    customizedNames: exportParam.customizedNames,
    hasClosedCaption: exportParam.exportOption.hasClosedCaption,
  };

  const urls = await gql.upload({ data });
  const addToIndex = false;
  const contentType = 'application/json';
  const assetType = 'illuminate-payload';
  const uri = urls.getUrl;
  const tdoAsset = await gql.createTDOWithAsset({
    startDateTime: exportParam.startDateTime,
    stopDateTime: exportParam.stopDateTime,
    parentFolderId: exportParam.parentFolderId,
    name: exportParam.exportName,
    addToIndex,
    contentType,
    assetType,
    uri,
  });
  return {
    tdoId: tdoAsset.id,
    assetId: tdoAsset?.assets?.records[0]?.id,
  };
}

export async function createExportJob(
  tdoId: string,
  engineId: string,
  clusterId: string,
  assetId: string,
  batchSize: number,
  password: string,
  gql: GQLApi
) {
  const isEncrypted = !isEmpty(password);
  const tasks = [
    {
      engineId,
      payload: {
        payloadAssetId: assetId,
        batchSize: batchSize,
        isEncrypted: isEncrypted,
        password: password,
      },
    },
  ];
  return await gql.createJobByTask({ targetId: tdoId, tasks, clusterId });
}

export async function insertExportHistory(
  schemaId: string,
  notificationData: Notification,
  gql: GQLApi
) {
  const resp = await gql.createStructuredData(schemaId, [
    { data: notificationData.data },
  ]);
  if (resp.errors && resp.errors.length) {
    resp.errors.forEach((error) => {
      console.error(`failed to insertExportHistory - ${error.message}`);
    });
  }
  return resp.data;
}
