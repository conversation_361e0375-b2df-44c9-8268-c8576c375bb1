import {
  all,
  fork,
  takeLatest,
  put,
  select,
  call,
} from 'typed-redux-saga/macro';

import {
  fetchFolders,
  FETCH_FOLDERS_SUCCESS,
  FETCH_SUB_FOLDERS_REQUEST,
  getSelectedFolderId,
  getRootFolderId,
  setSelectedFolderId,
  ON_FETCH_FOLDERS,
  FETCH_CREATE_FOLDER_REQUEST,
  fetch<PERSON>reateFolder,
  FETCH_CREATE_FOLDER_SUCCESS,
  FETCH_UPDATE_FOLDER_REQUEST,
  fetchUpdateFolder,
  FETCH_UPDATE_FOLDER_SUCCESS,
  FETCH_MOVE_SUB_FOLDERS_REQUEST,
  FETCH_MOVE_SUB_FOLDERS,
  FETCH_MOVE_SUB_FOLDERS_SUCCESS,
  FETCH_MOVE_SUB_FOLDERS_FAILURE,
  setSelectedMoveFolderId,
  getSelectedMoveFolderId,
  FETCH_DELETE_FOLDER_REQUEST,
  fetchDeleteFolder,
  getFolders,
  FETCH_DELETE_FOLDER_SUCCESS,
  FETCH_DELETE_FOLDER_FAILURE,
  FETCH_CREATE_FOLDER_FAILURE,
  FETCH_UPDATE_FOLDER_FAILURE,
  FETCH_UPDATE_MOVE_FOLDER_REQUEST,
  FETCH_UPDATE_MOVE_FOLDER_SUCCESS,
  FETCH_UPDATE_MOVE_FOLDER_FAILURE,
  getMoveFolders,
  fetchMoveFolder,
  FETCH_MOVE_FOLDER_REQUEST,
  FETCH_MOVE_FOLDER,
  FETCH_MOVE_FOLDER_SUCCESS,
  FETCH_MOVE_FOLDER_FAILURE,
  createMoveFilesRequest,
  FETCH_UPDATE_MOVE_FILES_REQUEST,
  FETCH_UPDATE_MOVE_FILES_SUCCESS,
  FETCH_UPDATE_MOVE_FILES_FAILURE,
  createDeleteFilesRequest,
  DELETE_FILES_REQUEST,
  DELETE_FILES_SUCCESS,
  DELETE_FILES_FAILURE,
  getActionFolderId,
  getOpenFolderId,
  getCaseOptions,
  SET_SELECTED_FOLDER_ID,
  FETCH_SUB_FOLDERS_SUCCESS,
  HIDE_FOLDER,
  getDispatcherComponent,
  FETCH_FOLDERS,
  FETCH_FOLDERS_FAILURE,
  FETCH_SUB_FOLDERS,
  FETCH_SUB_FOLDERS_FAILURE,
  onFetchFolders,
  FETCH_CHILD_FOLDER_SUCCESS,
  createFetchChildFoldersRequest,
  FETCH_CHILD_FOLDER_REQUEST,
  createFetchFolderFromRootRequest,
} from '.';
import {
  getTdosForExport,
  unselectRows,
  DELETED_TDOS,
  MOVED_TDOS,
  MoveFolderData,
} from 'state/modules/tdosTable';
import {
  getSearchResultTdosAll,
  updateSearchResultTdos,
  updateSearchByFolderQuery,
} from 'state/modules/search';
import { showNotification } from '../snackbar';
import { get, isEmpty, isString } from 'lodash';
import { FETCH_FOLDER_UPLOAD_REQUEST } from '../uploadFile/actions';
import { handleRequest } from '../../utils/util';
import { Action } from 'state/sagas';
import { AsyncFuncActions } from '~helpers/apiHelper';
import { TDO } from '../universal/TDO';
import { IN_REDACTION } from 'state/modules/sendToRedact';
const ROOT_FOLDER_TYPE = 'cms';

function* doFetchFolders() {
  yield* fetchMoreFolders(
    'root',
    [FETCH_FOLDERS, FETCH_FOLDERS_SUCCESS, FETCH_FOLDERS_FAILURE],
    null,
    {}
  );
}

function* watchFetchFolders() {
  yield* all([takeLatest(ON_FETCH_FOLDERS, doFetchFolders)]);
}

function* watchFetchFoldersSuccess() {
  yield* all([takeLatest(FETCH_FOLDERS_SUCCESS, selectInitialFolder)]);
}

function* selectInitialFolder() {
  const selectedFolderId = yield* select(getSelectedFolderId);
  const rootFolderId = yield* select(getRootFolderId);
  const selectedMoveFolderId = yield* select(getSelectedMoveFolderId);

  if (!selectedFolderId) {
    yield* put(setSelectedFolderId(rootFolderId, 'initialPageLoad'));
  }
  if (!selectedMoveFolderId) {
    yield* put(setSelectedMoveFolderId(rootFolderId));
  }
}

export function* handleFetchSelectSubfolder() {
  yield* takeLatest(FETCH_SUB_FOLDERS_REQUEST, function* (action) {
    const rootFolderId = yield* select(getRootFolderId);
    if (rootFolderId === action.payload.folderId) {
      yield* fetchMoreFolders(
        'root',
        [FETCH_FOLDERS, FETCH_FOLDERS_SUCCESS, FETCH_FOLDERS_FAILURE],
        null,
        {}
      );
    } else {
      const variables = {
        folderId: action.payload.folderId,
        expanded: action.payload.expanded,
        type: null,
      };
      yield* fetchMoreFolders(
        'subFolder',
        [
          FETCH_SUB_FOLDERS,
          FETCH_SUB_FOLDERS_SUCCESS,
          FETCH_SUB_FOLDERS_FAILURE,
        ],
        action.payload.folderId,
        variables
      );
    }
  });
}
function* handleFetchCreateFolder() {
  yield* all([takeLatest(FETCH_CREATE_FOLDER_REQUEST, createFolder)]);
  yield* takeLatest(
    [FETCH_CREATE_FOLDER_SUCCESS, FETCH_CREATE_FOLDER_FAILURE],
    function* (action) {
      if (FETCH_CREATE_FOLDER_SUCCESS.match(action)) {
        const rootFolderId = yield* select(getRootFolderId);
        const parentFolderId = action.meta.variables.parentFolderId;
        if (rootFolderId === parentFolderId) {
          yield* fetchMoreFolders(
            'root',
            [FETCH_FOLDERS, FETCH_FOLDERS_SUCCESS, FETCH_FOLDERS_FAILURE],
            null,
            {}
          );
        } else {
          const variables = {
            folderId: parentFolderId,
            expanded: true,
            type: null,
          };
          yield* fetchMoreFolders(
            'subFolder',
            [
              FETCH_SUB_FOLDERS,
              FETCH_SUB_FOLDERS_SUCCESS,
              FETCH_SUB_FOLDERS_FAILURE,
            ],
            parentFolderId,
            variables
          );
        }
        yield* put(
          showNotification(
            `New Folder: ${action.payload.createFolder.name} has been created`,
            'success'
          )
        );
      } else if (FETCH_CREATE_FOLDER_FAILURE.match(action)) {
        let message = 'Cannot create new folder ';
        if (action.payload?.[0]?.data?.maxAllowedDepth) {
          message +=
            '(Adding the new folder would exceed maximum folder depth limitations)';
        }
        yield* put(showNotification(message, 'error'));
      }
    }
  );
}
function* createFolder(action: Action<string>) {
  const openFolderId = yield* select(getOpenFolderId);
  yield* put(fetchCreateFolder(action.payload, openFolderId));
}
function* handleFetchUpdateFolder() {
  yield* all([takeLatest(FETCH_UPDATE_FOLDER_REQUEST, updateFolder)]);
  yield* takeLatest(
    [FETCH_UPDATE_FOLDER_SUCCESS, FETCH_UPDATE_FOLDER_FAILURE],
    function* (action) {
      if (FETCH_UPDATE_FOLDER_SUCCESS.match(action)) {
        yield* put(
          showNotification(
            `Folder has been renamed to: ${action.payload.updateFolder.name}`,
            'success'
          )
        );
      } else if (FETCH_UPDATE_FOLDER_FAILURE.match(action)) {
        yield* put(
          showNotification(
            `Cannot rename folder ${action.meta.variables.name}`,
            'error'
          )
        );
      }
    }
  );
}
function* updateFolder(action: Action<{ id: string; name: string }>) {
  yield* put(fetchUpdateFolder(action.payload.id, action.payload.name));
}

export function* handleFetchMoveFolders() {
  yield* takeLatest(FETCH_MOVE_FOLDER_REQUEST, function* () {
    yield* fetchMoreFolders(
      'root',
      [FETCH_MOVE_FOLDER, FETCH_MOVE_FOLDER_SUCCESS, FETCH_MOVE_FOLDER_FAILURE],
      null,
      {}
    );
  });
}
export function* handleFetchSelectMoveSubfolder() {
  yield* takeLatest(FETCH_MOVE_SUB_FOLDERS_REQUEST, function* (action) {
    const rootFolderId = yield* select(getRootFolderId);
    if (rootFolderId === action.payload.folderId) {
      yield* fetchMoreFolders(
        'root',
        [
          FETCH_MOVE_FOLDER,
          FETCH_MOVE_FOLDER_SUCCESS,
          FETCH_MOVE_FOLDER_FAILURE,
        ],
        null,
        {}
      );
    } else {
      const variables = {
        folderId: action.payload.folderId,
        expanded: action.payload.expanded,
        type: null,
      };
      yield* fetchMoreFolders(
        'subFolder',
        [
          FETCH_MOVE_SUB_FOLDERS,
          FETCH_MOVE_SUB_FOLDERS_SUCCESS,
          FETCH_MOVE_SUB_FOLDERS_FAILURE,
        ],
        action.payload.folderId,
        variables
      );
    }
  });
}
function* handleFetchDeleteFolder() {
  yield* all([takeLatest(FETCH_DELETE_FOLDER_REQUEST, deleteFolder)]);
}
function* deleteFolder() {
  const selectedFolderId = yield* select(getSelectedFolderId);
  const folders = yield* select(getFolders);
  const selectedFolder = folders[selectedFolderId];
  if (!selectedFolder) {
    yield* put(showNotification(`Unable to find the selected folder`, 'error'));
    return;
  }

  const treeObjectId = selectedFolder.treeObjectId;
  const parentFolderId = selectedFolder.parentId;
  if (!parentFolderId) {
    yield* put(showNotification(`Cannot delete root folder`, 'error'));
    return;
  }

  const subfolders = folders[parentFolderId]?.subfolders || [selectedFolderId];
  const rootFolderId = yield* select(getRootFolderId);
  yield* put(fetchDeleteFolder(treeObjectId, selectedFolderId));
  yield* takeLatest(
    [FETCH_DELETE_FOLDER_SUCCESS, FETCH_DELETE_FOLDER_FAILURE],
    function* (action) {
      if (FETCH_DELETE_FOLDER_SUCCESS.match(action)) {
        if (rootFolderId === parentFolderId) {
          yield* fetchMoreFolders(
            'root',
            [FETCH_FOLDERS, FETCH_FOLDERS_SUCCESS, FETCH_FOLDERS_FAILURE],
            null,
            {}
          );
        } else {
          const variables = {
            folderId: parentFolderId,
            expanded: subfolders.length > 1 ? true : false,
            type: null,
          };
          yield* fetchMoreFolders(
            'subFolder',
            [
              FETCH_SUB_FOLDERS,
              FETCH_SUB_FOLDERS_SUCCESS,
              FETCH_SUB_FOLDERS_FAILURE,
            ],
            parentFolderId,
            variables
          );
        }
        yield* put(setSelectedFolderId(parentFolderId));
        yield* put(
          showNotification(
            `Folder: ${folders[selectedFolderId]?.name} has been deleted`,
            'success'
          )
        );
      } else if (FETCH_DELETE_FOLDER_FAILURE.match(action)) {
        yield* put(
          showNotification(`Cannot delete ${selectedFolder.name}`, 'error')
        );
      }
    }
  );
}

export function* hanldeFetchMoveSubFolder() {
  yield* all([takeLatest(FETCH_UPDATE_MOVE_FOLDER_REQUEST, moveFolder)]);
}
function* moveFolder() {
  const caseOptions = yield* select(getCaseOptions);
  const actionFolderId =
    caseOptions === 'tab'
      ? yield* select(getSelectedFolderId)
      : yield* select(getActionFolderId);
  const folders = yield* select(getFolders);
  const treeObjectId = folders[actionFolderId]?.treeObjectId ?? '';
  const selectedMoveFolderId = yield* select(getSelectedMoveFolderId);
  const moveFolder = yield* select(getMoveFolders);
  const treeObjectNewParentId =
    moveFolder[selectedMoveFolderId]?.treeObjectId ?? '';
  const parentFolderId = folders[actionFolderId]?.parentId ?? '';
  const treeObjectPrevParent = folders[parentFolderId]?.treeObjectId ?? '';
  const rootFolderId = yield* select(getRootFolderId);
  const treeObjectIdRoot = folders[rootFolderId]?.treeObjectId;
  yield* put(
    fetchMoveFolder(treeObjectId, treeObjectNewParentId, treeObjectPrevParent)
  );
  yield* takeLatest(
    [FETCH_UPDATE_MOVE_FOLDER_SUCCESS, FETCH_UPDATE_MOVE_FOLDER_FAILURE],
    function* (action) {
      if (FETCH_UPDATE_MOVE_FOLDER_SUCCESS.match(action)) {
        if (treeObjectIdRoot === treeObjectNewParentId) {
          yield* fetchMoreFolders(
            'root',
            [FETCH_FOLDERS, FETCH_FOLDERS_SUCCESS, FETCH_FOLDERS_FAILURE],
            null,
            { type: 'moveFolder' }
          );
        } else {
          const variables = {
            folderId: selectedMoveFolderId,
            expanded: true,
            type: 'moveFolder',
          };
          yield* fetchMoreFolders(
            'subFolder',
            [
              FETCH_SUB_FOLDERS,
              FETCH_SUB_FOLDERS_SUCCESS,
              FETCH_SUB_FOLDERS_FAILURE,
            ],
            selectedMoveFolderId,
            variables
          );
        }

        yield* put(
          showNotification(
            `Folder ${action.payload.moveFolder.name} has been moved`,
            'success'
          )
        );

        const selectedFolderId = yield* select(getSelectedFolderId);
        yield* put(
          SET_SELECTED_FOLDER_ID({
            selectedFolderId,
            type: 'moveFolder',
          })
        );
      } else if (FETCH_UPDATE_MOVE_FOLDER_FAILURE.match(action)) {
        yield* put(
          showNotification(
            `Cannot move ${folders[actionFolderId]?.name}`,
            'error'
          )
        );
      }
    }
  );
}
export function* handleFetchMoveFiles() {
  yield* all([takeLatest(FETCH_UPDATE_MOVE_FILES_REQUEST, moveFiles)]);
}
function* moveFiles() {
  const selectedMoveFolderId = yield* select(getSelectedMoveFolderId);
  const selectedFolderId = yield* select(getSelectedFolderId);

  const moveFolder = yield* select(getMoveFolders);
  const treeObjectNewId = moveFolder[selectedMoveFolderId]?.treeObjectId ?? '';
  const selectedTdoIds = yield* select(getTdosForExport);
  const allTdos = yield* select(getSearchResultTdosAll);
  const errorTdoIds = allTdos
    .filter((tdo) => tdo.status === 'error')
    .map((tdo) => tdo.id);
  const selectedValidTdoIds = selectedTdoIds.filter(
    (id) => !errorTdoIds.includes(id)
  );

  const { tdosInRedaction, tdosNotInRedaction } = groupTdosByRedaction(
    allTdos,
    selectedValidTdoIds,
    selectedMoveFolderId,
    selectedFolderId
  );

  if (!tdosNotInRedaction.length) {
    yield* put(
      showNotification(
        `File${tdosInRedaction.length > 1 ? 's' : ''} currently in redaction cannot be moved`,
        'error'
      )
    );
    return;
  }
  yield* put(createMoveFilesRequest(tdosNotInRedaction, treeObjectNewId));
  yield* takeLatest(
    [FETCH_UPDATE_MOVE_FILES_SUCCESS, FETCH_UPDATE_MOVE_FILES_FAILURE],
    function* (action) {
      if (FETCH_UPDATE_MOVE_FILES_SUCCESS.match(action)) {
        const fileCountInRedaction = `${tdosInRedaction.length} file${tdosInRedaction.length > 1 ? 's have' : ' has'}`;
        const fileCountNotInRedaction = `${tdosNotInRedaction.length} file${tdosNotInRedaction.length > 1 ? 's have' : ' has'}`;
        const successNotification = `${fileCountNotInRedaction} been moved to ${moveFolder[selectedMoveFolderId]?.name} folder${tdosInRedaction.length ? `, ${fileCountInRedaction} currently in redaction cannot be moved.` : '.'} All changes take up to 20 seconds to complete.`;

        yield* put(showNotification(successNotification, 'success'));
        // Remove selected tdo
        yield* put(
          updateSearchResultTdos(
            tdosNotInRedaction.map((item) => item.id),
            'moving'
          )
        );
        // Used to change the pageSize
        yield* put(MOVED_TDOS(tdosNotInRedaction));
        yield* put(unselectRows());
      } else if (FETCH_UPDATE_MOVE_FILES_FAILURE.match(action)) {
        yield* put(showNotification(`Cannot Move Files`, 'error'));
      }
    }
  );
}

export function* handleDeleteFiles() {
  yield* all([takeLatest(DELETE_FILES_REQUEST, deleteFiles)]);
}
function* deleteFiles() {
  const selectedTdoIds = yield* select(getTdosForExport);
  const allTdos = yield* select(getSearchResultTdosAll);
  const selectedTdos = allTdos.filter(
    (tdo) => selectedTdoIds.includes(tdo.id) && tdo.status !== 'error'
  );
  const selectedTdoIdsValid = selectedTdos.map((tdo) => tdo.id);
  yield* put(createDeleteFilesRequest(selectedTdoIdsValid));
  yield* takeLatest(
    [DELETE_FILES_SUCCESS, DELETE_FILES_FAILURE],
    function* (action) {
      if (DELETE_FILES_SUCCESS.match(action)) {
        let successNotification;
        if (selectedTdoIdsValid.length > 1) {
          successNotification = `Files have been deleted. All changes take up to 20 seconds to complete.`;
        } else {
          successNotification = `File has been deleted. All changes take up to 20 seconds to complete.`;
        }
        yield* put(showNotification(successNotification, 'success'));
        // Unselect rows after deletion is successful
        yield* put(updateSearchResultTdos(selectedTdoIds, 'deleting'));
        // Used to change the pageSize
        yield* put(DELETED_TDOS(selectedTdos));
        yield* put(unselectRows());
      } else if (DELETE_FILES_FAILURE.match(action)) {
        yield* put(
          showNotification(`Errors have occurred while deleting files`, 'error')
        );
      }
    }
  );
}

function* watchFetchSubFolders() {
  yield* takeLatest(FETCH_SUB_FOLDERS_SUCCESS, breadcrumbsFetchData);
  yield* takeLatest(FETCH_FOLDERS_SUCCESS, breadcrumbsFetchData);
}

function* breadcrumbsFetchData() {
  const fromComponent = yield* select(getDispatcherComponent);
  if (fromComponent === 'breadcrumbs') {
    yield* put(SET_SELECTED_FOLDER_ID());
    yield* put(HIDE_FOLDER());
  }
}

function* fetchMoreFolders(
  type: string,
  actionTypes: AsyncFuncActions<any>,
  folderId: string | null,
  variablesFolder: object
) {
  if (type === 'root') {
    return yield* put(
      createFetchFolderFromRootRequest(actionTypes, variablesFolder)
    );
  }
  return yield* fetchFolderFromNonRoot(actionTypes, folderId, variablesFolder);
}

function* fetchFolderFromNonRoot(
  actionTypes: AsyncFuncActions<any>,
  folderId: string | null,
  variablesFolder: object
): any {
  let count = 0;
  let offset = 0;
  const pageSize = 1000;
  const query = `query folder($id:ID!, $offset: Int, $limit: Int){
    folder(id: $id) {
      id
      name
      ownerId
      treeObjectId
      description
      childFolders(offset: $offset, limit: $limit){
        count
        records {
          id,
          name
          treeObjectId
          description
          parent{
            id
          }
          childFolders{
            count
          }
          folderPath{
            id
            name
          }
        }
      }
      parent{
        id
      }
      folderPath{
        id
        name
      }
    }
  }`;

  const results = [];
  do {
    const variables = {
      id: folderId,
      limit: pageSize,
      offset: offset,
      ...variablesFolder,
    };
    const data = yield* put(fetchFolders(query, variables, actionTypes));
    const response = yield data;
    count = response?.folder?.childFolders?.count ?? 0;
    results.push(response?.folder);
    offset += pageSize;
  } while (count === pageSize);

  const subFolders = results.flatMap(
    (item) => item?.childFolders?.records ?? []
  );
  const dataFolder: Record<string, any> = {};
  dataFolder.folder = { ...results[0] };
  dataFolder.folder.childFolders.records = subFolders;
  dataFolder.folder.childFolders.count = subFolders.length;

  if (isString(actionTypes[1])) {
    yield* put({
      type: actionTypes[1],
      payload: dataFolder,
      meta: {
        variables: variablesFolder,
      },
    });
  } else {
    yield* put(actionTypes[1](dataFolder, { variables: variablesFolder }));
  }

  return dataFolder;
}

export function* checkAndCreateRootFolder() {
  const queryRootFolders = `query folder {
    rootFolders(type: ${ROOT_FOLDER_TYPE}) {
      id
      name
    }
  }`;
  const rootFoldersResp = yield* call(handleRequest, {
    query: queryRootFolders,
  });
  if (rootFoldersResp.error) {
    throw rootFoldersResp.error;
  }

  const rootFolders = get(rootFoldersResp, 'response.data.rootFolders', []);
  if (isEmpty(rootFolders)) {
    console.warn('missing root folder. creating...');
    const queryCreateRootFolders = `mutation {
        createRootFolders(rootFolderType: ${ROOT_FOLDER_TYPE}) {
          id,
          name
        }
      }`;
    const queryCreateRootFoldersResp = yield* call(handleRequest, {
      query: queryCreateRootFolders,
    });
    if (queryCreateRootFoldersResp.error) {
      console.error(
        'Unable to create root folder. Make sure the user has CMS Editor role. ',
        queryCreateRootFoldersResp.error
      );
      throw rootFoldersResp.error;
    }
  }

  return yield* put(onFetchFolders());
}

function* watchFetchFolderUpload() {
  yield* takeLatest(FETCH_FOLDER_UPLOAD_REQUEST, function* () {
    yield* fetchMoreFolders(
      'root',
      [FETCH_MOVE_FOLDER, FETCH_MOVE_FOLDER_SUCCESS, FETCH_MOVE_FOLDER_FAILURE],
      null,
      {}
    );
  });
}

function* watchFetchChildFolders() {
  yield* takeLatest(FETCH_CHILD_FOLDER_REQUEST, function* (action) {
    const folderId = get(action, 'payload.folderId', '');
    if (folderId) {
      yield* put(createFetchChildFoldersRequest(folderId));
    }
  });
}

function* watchFetchChildFoldersSuccess() {
  yield* takeLatest(FETCH_CHILD_FOLDER_SUCCESS, function* () {
    const folders = yield* select(getFolders);
    const rootFolderId = yield* select(getRootFolderId);
    const selectedFolderId = yield* select(getSelectedFolderId);
    const folderTreeObjectId: any = folders[selectedFolderId]?.treeObjectId;
    const isRoot = selectedFolderId === rootFolderId;
    yield* put(updateSearchByFolderQuery(folderTreeObjectId, isRoot));
  });
}

function groupTdosByRedaction(
  allTdos: TDO[],
  selectedTdoIds: string[],
  selectedMoveFolderId: string,
  selectedFolderId: string
) {
  const tdosInRedaction = [] as MoveFolderData[];
  const tdosNotInRedaction = [] as MoveFolderData[];

  for (const tdo of allTdos) {
    if (selectedTdoIds.includes(tdo.id)) {
      const selectedTdo = {
        ...tdo,
        moveFolderId: selectedMoveFolderId,
        oldFolderId: selectedFolderId,
      };
      const hasRedactionTag = selectedTdo.details?.tags?.some(
        (tag) => tag.value === IN_REDACTION
      );

      if (hasRedactionTag) {
        tdosInRedaction.push(selectedTdo);
      } else {
        tdosNotInRedaction.push(selectedTdo);
      }
    }
  }

  return { tdosInRedaction, tdosNotInRedaction };
}
export default function* folders() {
  yield* all([
    fork(watchFetchSubFolders),
    fork(watchFetchFolders),
    fork(watchFetchFoldersSuccess),
    fork(handleFetchSelectSubfolder),
    fork(handleFetchCreateFolder),
    fork(handleFetchUpdateFolder),
    fork(handleFetchSelectMoveSubfolder),
    fork(handleFetchDeleteFolder),
    fork(handleDeleteFiles),
    fork(hanldeFetchMoveSubFolder),
    fork(handleFetchMoveFiles),
    fork(handleFetchMoveFolders),
    fork(watchFetchFolderUpload),
    fork(watchFetchChildFolders),
    fork(watchFetchChildFoldersSuccess),
  ]);
}
