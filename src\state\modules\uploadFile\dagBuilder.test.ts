import { buildDAG } from './dagBuilder';
import * as config from '../../../../config.json';

test('playback engine video or audio DAG is created correctly', () => {
  const fileUrl = 'a test url';
  const fileType = 'video/mp4';
  const tdoId = '12345';
  const defaultClusterId = 'edge1-5d90f115-2a33-454c-a1cd-933a856d7862';
  const defaultEngineId = config.defaultEngineId;
  const { tasks, routes } = buildGLCIngestionTask(fileUrl);
  const input = {
    targetId: tdoId,
    clusterId: defaultClusterId,
    tasks,
    routes,
    jobConfig: {
      illuminate: {
        workflow: 'simple',
      },
    },
  };
  const want = {
    input,
  };

  const { variables } = buildDAG(
    fileUrl,
    fileType,
    tdoId,
    defaultClusterId,
    defaultEngineId,
    'simple'
  );

  expect(variables).toEqual(want);
});

test('create assets for non video DAG is created correctly', () => {
  const defaultClusterId = 'edge1-5d90f115-2a33-454c-a1cd-933a856d7862';
  const fileUrl = 'a test url';
  const fileType = 'image/jpeg';
  const tdoId = '12345';
  const defaultEngineId = config.defaultEngineId;
  const { tasks, routes } = buildCreateAssetTasks(fileUrl, tdoId);
  const input = {
    targetId: tdoId,
    clusterId: defaultClusterId,
    tasks,
    routes,
    jobConfig: {
      illuminate: {
        workflow: 'advanced',
      },
    },
  };
  const want = {
    input,
  };

  const { variables } = buildDAG(
    fileUrl,
    fileType,
    tdoId,
    defaultClusterId,
    defaultEngineId,
    'advanced'
  );

  expect(variables).toEqual(want);
});

function buildGLCIngestionTask(fileUrl: string) {
  const tasks = [
    {
      engineId: 'da093aca-2a6b-4577-8bfe-2b19a2f2faea',
      payload: {
        url: fileUrl,
      },
    },
  ];
  const routes: object[] = [];
  return { tasks, routes };
}

function buildCreateAssetTasks(url: string, tdoId: string) {
  const tasks = [
    {
      engineId: '9e611ad7-2d3b-48f6-a51b-0a1ba40fe255',
      payload: {
        url,
        tdoId,
      },
      ioFolders: [
        {
          referenceId: 'wsaOut',
          mode: 'stream',
          type: 'output',
        },
      ],
    },
    {
      engineId: '75fc943b-b5b0-4fe1-bcb6-9a7e1884257a',
      payload: {
        assetType: 'media',
        setAsPrimary: true,
      },
      ioFolders: [
        {
          referenceId: 'assetCreatorIn',
          mode: 'stream',
          type: 'input',
        },
      ],
      executionPreferences: {
        parentCompleteBeforeStarting: true,
      },
    },
  ];
  const routes = [
    {
      parentIoFolderReferenceId: 'wsaOut',
      childIoFolderReferenceId: 'assetCreatorIn',
      options: {},
    },
  ];

  return { tasks, routes };
}
