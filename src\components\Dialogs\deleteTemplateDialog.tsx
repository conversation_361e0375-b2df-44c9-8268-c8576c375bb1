import React, { ComponentType } from 'react';
import {
  ThemeProvider,
  StyledEngineProvider,
  createTheme,
  Theme,
} from '@mui/material/styles';
import withStyles from '@mui/styles/withStyles';
import { ClassNameMap } from '@mui/styles';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide, { SlideProps } from '@mui/material/Slide';

const Transition = React.forwardRef((props: SlideProps, ref) => (
  <Slide direction="up" {...props} ref={ref} />
));

Transition.displayName = 'Transition';

const buttonTheme = createTheme({
  palette: {
    primary: {
      main: '#1871E8',
    },
    secondary: {
      main: '#2A323C',
    },
  },
});

const style = (_theme: Theme) => ({
  paper: {
    overflow: 'hidden',
    width: '462px',
  },
  dialogTitle: {
    color: '#5C6269',
  },
  padding: {
    paddingLeft: '30px',
    paddingRight: '30px',
    paddingBottom: '30px',
  },
});

class AlertDialogSlide extends React.Component<Props> {
  state = {
    menuIsOpen: false,
  };

  render() {
    const { classes, handleClose, onConfirm, open } = this.props;
    return (
      <div>
        <Dialog
          open={open}
          TransitionComponent={Transition as ComponentType<SlideProps>}
          keepMounted
          onClose={handleClose}
          aria-labelledby="alert-dialog-slide-title"
          aria-describedby="alert-dialog-slide-description"
          classes={{ paper: classes.paper }}
        >
          <div>
            <DialogTitle
              id="alert-dialog-slide-title"
              className={classes.dialogTitle}
            >
              Delete Bulk Export Template
            </DialogTitle>
            <DialogContent className={classes.paper}>
              <DialogContentText id="alert-dialog-slide-description">
                {
                  'Are you sure you want to delete this bulk export template? This action cannot be undone.'
                }
              </DialogContentText>
            </DialogContent>
            <DialogActions className={classes.padding}>
              <StyledEngineProvider injectFirst>
                <ThemeProvider theme={buttonTheme}>
                  <Button onClick={handleClose} color="secondary">
                    Cancel
                  </Button>
                  <Button
                    data-test="delete-dialog-save-button"
                    onClick={onConfirm}
                    className={classes.buttonColor}
                    color="primary"
                    variant="contained"
                  >
                    Delete
                  </Button>
                </ThemeProvider>
              </StyledEngineProvider>
            </DialogActions>
          </div>
        </Dialog>
      </div>
    );
  }
}

interface Props {
  classes: ClassNameMap;
  handleClose: () => void;
  onConfirm: () => void;
  open: boolean;
}

export default withStyles(style, { withTheme: true })(AlertDialogSlide);
