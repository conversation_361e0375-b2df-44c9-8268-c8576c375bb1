import React from 'react';
import '@testing-library/jest-dom';
import WorkerMock from './__mocks__/workerMock';
import { TextEncoder, TextDecoder } from 'util';

jest.mock('uuid', () => ({ v4: jest.fn().mockReturnValue(0.5) }));
jest.mock(
  '../src/components/ContactAnalytics/worker/workerConnect.tsx',
  () => ({
    workerConnect: () => (Component: React.FC) => {
      const WrappedComponent: React.FC = (props: any) => (
        <Component {...props} />
      );
      return WrappedComponent;
    },
  })
);

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder as any;

window.Worker = WorkerMock;

// Enzyme.configure({ adapter: new Adapter() });

window.HTMLMediaElement.prototype.load = () => {};
window.HTMLMediaElement.prototype.play = () => Promise.resolve();
window.HTMLMediaElement.prototype.pause = () => {};
window.HTMLMediaElement.prototype.addTextTrack = () => new TextTrack();
