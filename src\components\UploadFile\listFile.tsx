import React, { Fragment } from 'react';
import makeStyles from '@mui/styles/makeStyles';
import ListComponent from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Checkbox from '@mui/material/Checkbox';
import { AutoSizer, List } from 'react-virtualized';
import styles from './styles';
import { ListItemButton, Typography } from '@mui/material';
const useStyles = makeStyles(styles);
function ListFileUpload({
  data,
  checked,
  handleToggle,
  indeterminate,
  checkedAll,
}: Props) {
  const classes = useStyles();
  function rowRenderer({ index, style }: { index: number; style: object }) {
    const file = data[index];
    return (
      <ListComponent key={file && file.key} style={style}>
        <ListItem
          role={undefined}
          dense
          onClick={handleToggle}
          data-key={index}
          data-type={'single'}
          secondaryAction={
            <Typography className={classes.listFileAction}>
              <span>{file && file.type}</span>
            </Typography>
          }
          disablePadding
        >
          <ListItemButton>
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={checked.includes(index)}
                tabIndex={-1}
                disableRipple
                data-testid="checkbox-file"
              />
            </ListItemIcon>
            <ListItemText
              className={classes.listFileAction}
              primary={file && file.fileName}
            />
          </ListItemButton>
        </ListItem>
      </ListComponent>
    );
  }

  if (!data.length) {
    return <Fragment />;
  }
  return (
    <ListComponent>
      <ListItem
        role={undefined}
        dense
        onClick={handleToggle}
        data-type={'all'}
        data-testid="list-file"
        secondaryAction={
          <Typography className={classes.listFileAction}>
            {checked.length ? (
              <span>{`${checked.length} file currently selected`}</span>
            ) : (
              <span>{`${data.length} files`}</span>
            )}
          </Typography>
        }
        disablePadding
      >
        <ListItemButton>
          <ListItemIcon>
            <Checkbox
              edge="start"
              checked={checkedAll}
              indeterminate={indeterminate}
              data-test="checked-all-file"
              data-testid="check-box-all"
            />
          </ListItemIcon>
        </ListItemButton>
      </ListItem>

      <AutoSizer disableHeight>
        {({ width }: { width: number }) => (
          <List
            ref={`List`}
            height={600}
            overscanRowCount={10}
            rowCount={data.length}
            rowHeight={50}
            rowRenderer={rowRenderer}
            width={width}
          />
        )}
      </AutoSizer>
    </ListComponent>
  );
}
interface Props {
  data: {
    fileName: string;
    type: string;
    key: string;
  }[];
  checked: number[];
  handleToggle: (event: React.MouseEvent<HTMLElement>) => void;
  indeterminate: boolean;
  checkedAll: boolean;
}
export default ListFileUpload;
