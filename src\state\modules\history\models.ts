interface Data {
  hide: boolean;
  jobId: string;
  tdoId: string;
  isRead: boolean;
  status: string;
  userId: string;
  engineId: string;
  exportName: string;
  exportType: string;
  applicationKey: string;
  organizationId: number;
}
export interface ExportHistory {
  id: string;
  data: Data;
  createdDateTime: string;
  modifiedDateTime: string;
}
export interface FetchTotalCountError {
  message: string;
  name: string;
  path: string[];
  time_thrown: string;
  location: {
    column: number;
    line: number;
  }[];
}
