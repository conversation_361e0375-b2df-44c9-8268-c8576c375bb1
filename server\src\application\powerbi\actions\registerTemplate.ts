import { DateTime } from 'luxon';
import { DatabaseError } from '../errors';
import { Context } from '../../types';

const registerTemplate = async (context: Context) => {
  const { req, log, queries, data } = context;

  try {
    const date = DateTime.now().toUTC().toSQL({ includeOffset: false });
    const row = await queries.insertTemplate({
      name: req.get('x-name'),
      description: req.get('x-description'),
      pbixFileName: req.params.fileName,
      modified: date,
      created: date,
    });

    data.templateId = row.id;
  } catch (e) {
    log.error('registerTemplate failed', e);
    throw new DatabaseError(e);
  }
};

export default registerTemplate;
