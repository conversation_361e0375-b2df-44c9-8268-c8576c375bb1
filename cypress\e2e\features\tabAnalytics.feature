Feature: Analytics Tab

  Background:
    Given the user is logged in and on the Analytics page

  @e2e @tabAnalytics
  Scenario: Verify Analytics Dashboard Metrics
    Then the user sees the summary cards with the following values:
      | Card Title                  | Value |
      | TOTAL NUMBER OF FILES       | 3     |
      | TOTAL MEDIA HOURS           | 1     |
      | TOTAL MEDIA HOURS PROCESSED | 1     |
    And the user sees the engine metrics with the following values:
      | Engine Name            | Value |
      | Transcription          | 1     |
      | Translate              | 0     |
      | Facial Detection       | 0     |
      | Object Detection       | 0     |
      | Entity Extraction      | 0     |
      | Content Classification | 0     |
      | Text Recognition       | 1     |
      | Speaker Detection      | 1     |
    # TODO: Can't generate word
    # And the user sees the following widgets are displayed:
    #   | widget            |
    #   | Word Cloud        |
    #   | Entity Clusters   |
