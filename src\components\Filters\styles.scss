@import 'src/variables';

.start-date {
  width: 145px;
}

.end-date {
  width: 145px;
}

.separator {
  margin: 0 8px;
  font-size: 12px;
}

.form-container {
  display: flex;
  align-items: center;
}

.file-sub-type {
  margin-left: 20px;
}

.overwrite-form-label {
  display: flex;

  > span:nth-child(1) {
    padding: 12px;
  }

  > span:nth-child(2) {
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
    letter-spacing: 0.0107em;
  }
}

.form-label-entity {
  > span:nth-child(2) {
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
  }
}

.file-types {
  display: flex;
  align-items: center;
  justify-content: space-between;

  label > span:nth-child(1) {
    padding: 12px;
  }

  label > span:nth-child(2) {
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
  }
}

.file-type-container {
  width: 100%;
}

.file-type-form-control {
  width: 100%;
}

.expand-button {
  color: $font;
  min-width: 36px;
  border-radius: 50%;
}

.error-mess {
  color: $red-2;
  font-size: 13px;
  margin: 0 auto;
  margin-top: 5px;
  width: 85%;
  min-height: 13px;
}

.durationMain {
  width: 100%;
  text-align: center;
}

.duration {
  background-color: white;
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 4px;
  align-items: center;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
