import { Context } from '../../types';
import fetchTokenConfig from './fetchTokenConfig';
import getPbixTemplate from './getPbixTemplate';
import getDatasourceGateway from './getDatasourceGateway';
import uploadPbixToWorkspace from './uploadPbixToWorkspace';
import getDatasetsInWorkspace from './getDatasetsInWorkspace';
import getReportsInWorkspace from './getReportsInWorkspace';
import setDatasourceCredentials from './setDatasourceCredentials';
import registerTokenConfig from './registerTokenConfig';

const bulkAssociateTemplate = async (context: Context) => {
  const { log, data } = context;
  const { orgIds } = data;

  let unassociatedOrgs = orgIds;
  try {
    for (const orgId of orgIds) {
      context.data.orgId = orgId;

      await fetchTokenConfig(context)
        .then((cxt) => getPbixTemplate(cxt))
        .then((cxt) => uploadPbixToWorkspace(cxt))
        .then((cxt) => getDatasetsInWorkspace(cxt))
        .then((cxt) => getDatasourceGateway(cxt))
        .then((cxt) => getReportsInWorkspace(cxt))
        .then((cxt) => setDatasourceCredentials(cxt))
        .then((cxt) => registerTokenConfig(cxt));
      unassociatedOrgs = unassociatedOrgs.filter((id: number) => id !== orgId);
      context.data.unassociatedOrgs = unassociatedOrgs;
    }

    return context;
  } catch (e) {
    log.error('bulkAssociateTemplate failed', e);
    log.error('unassociatedOrgs:', unassociatedOrgs);
    throw e;
  }
};
export default bulkAssociateTemplate;
