import * as Sentry from '@sentry/react';
import { all, call, put, takeEvery, select } from 'typed-redux-saga/macro';
import {
  get,
  uniq,
  concat,
  pickBy,
  identity,
  compact,
  has,
  isEmpty,
} from 'lodash';
import {
  handleRequest,
  fetchSearchMedia,
  fetchUpload,
  getConfigModule,
} from '../../utils/util';
import {
  enginesSelected,
  uploadResult,
  tagsCustomize,
  contentTemplateSelected,
  selectedFolder,
  libraries,
  dagTemplateIdSelected,
  reprocessLimit,
  dagTemplates,
} from './';
import {
  getDataJobsFailed,
  getIsSelectedAllJobsFailed,
} from '../processingJobs';
import { refreshProcessingJobs } from '../processingJobs/actions';
import { showNotification } from '../snackbar';
import * as actions from './actions';
import { buildVariables } from './buildVariables';
import getDefaultClusterId from '../../../helpers/getDefaultClusterId';
import getDefaultEngineId from '../../../helpers/getDefaultEngineId';
import {
  getIsShowPreview,
  getTdoIdPreview,
  isSelectedAll,
  getTdosForReprocess,
  getIlluminateAppFolders,
} from '../tdosTable';
import { getFullSearchQuery } from '../search';
import { buildDAG } from './dagBuilder';
import {
  Engine,
  Library,
  DefaultEngineId,
  UploadResult,
  TagsCustomize,
  Records,
  EnginesResult,
  ProcessTDO,
} from './models';
import { Folder } from '../../../model';
import { fetchAllJobsFailed } from '../processingJobs/sagas';
import { Action } from 'state/sagas';
import { TDO } from 'state/modules/universal/TDO';
import { DOCS_MIME_TYPES } from '~helpers/common';
// TODO: Should this be fixed?
// eslint-disable-next-line @jambit/typed-redux-saga/use-typed-effects
import { AllEffect } from 'redux-saga/effects';
const ENGINE_AMAZON_TRANSLATE = '1fc4d3d4-54ab-42d1-882c-cfc9df42f386';
const TRANSCRIPT_TRANSLATION_SUPPORTED = [ENGINE_AMAZON_TRANSLATE];

export function* watchSaveUploadFile() {
  yield* takeEvery(actions.SAVE_UPLOAD_FILE, saveUploadFile);
}

export function* watchcheckLimitAndSaveReprocessFile() {
  yield* takeEvery(
    actions.CHECK_LIMIT_AND_SAVE_REPROCESS_FILE,
    checkLimitAndSaveReprocessFile
  );
}

export function* watchSaveReprocessFile() {
  yield* takeEvery(actions.SAVE_REPROCESS_FILE, saveReprocessFile);
}
const queryCreateLaunchSingleEngineJob = `mutation createLaunchSingleEngineJob($input: SingleEngineJobInput!){
  launchSingleEngineJob(input:$input){
    targetId
  }
}
`;
interface CreateTdoResponse {
  createTDO: {
    id: string;
    primaryAsset: {
      signedUri: string;
    };
  };
}
const queryCreateTdo = `mutation createTDO($input: CreateTDO){
  createTDO(input:$input){
    id
    primaryAsset(assetType: "media"){
      signedUri
    }
  }
}`;
const queryCreateJobFromDagTemplate = `mutation createJobTemplate($input: CreateJob){
  createJob(input: $input){
    id
  }
}`;

const batchSize = 10;

function* saveUploadFile(
  action: Action<{
    dagTemplateId: Parameters<typeof actions.saveUploadFile>[0];
    workflow: Parameters<typeof actions.saveUploadFile>[1];
  }>
) {
  const { dagTemplateId, workflow } = action.payload;
  const dataUploadResult = yield* select(uploadResult);
  const dataTagsCustomize = yield* select(tagsCustomize);
  const dataContentTemplateSelected = yield* select(contentTemplateSelected);
  const defaultClusterId = yield* select(getDefaultClusterId);
  const dataLibraries = yield* select(libraries);
  const defaultEngineId = yield* select(getDefaultEngineId);
  const dataSelectedFolder = yield* select(selectedFolder);
  const enginesSelectedResults = yield* select(enginesSelected);
  const selectedEngines = enginesSelectedResults.flatMap(
    (item) => item.engineIds
  );
  const contentTemplates = dataContentTemplateSelected.map((item) => {
    return {
      schemaId: get(item, 'schemas.records[0].id', null),
      data: pickBy(item.data, identity),
    };
  });
  const date = new Date().toISOString();
  const dataUploadSuccess: Array<UploadResult & { engines: Engine[] }> = [];
  const dataUploadFailed = [];
  const { hasStandaloneJobTemplates, noStandaloneJobTemplates } =
    checkStandaloneJobTemplates(selectedEngines);
  for (const engineMissingTemplates of noStandaloneJobTemplates) {
    console.error(
      `missing standaloneJobTemplates for engine ${engineMissingTemplates.description}: ${engineMissingTemplates.id}`
    );
  }

  dataUploadResult.forEach((item) => {
    if (isEmpty(selectedEngines)) {
      dataUploadSuccess.push({
        ...item,
        engines: [],
      });
      return;
    }
    const supportedEngines = getSupportedEngine(
      hasStandaloneJobTemplates,
      item.type,
      'upload'
    );
    if (supportedEngines.length > 0) {
      dataUploadSuccess.push({
        ...item,
        engines: supportedEngines,
      });
    } else {
      dataUploadFailed.push(item);
    }
  });
  const numOfSuccess = yield* createJobsForBatchUpload({
    uploadItems: dataUploadSuccess,
    dataTagsCustomize,
    date,
    contentTemplates,
    dataSelectedFolder,
    defaultClusterId,
    defaultEngineId,
    dataLibraries,
    dagTemplateId,
    workflow,
  });

  yield* displayMessage(
    dataUploadSuccess.length,
    numOfSuccess,
    dataUploadResult.length - numOfSuccess
  );
}

function* checkLimitAndSaveReprocessFile() {
  const isShowPreview = yield* select(getIsShowPreview);
  const isSelectedAllTdo = yield* select(isSelectedAll);
  const selectedTdos = yield* select(getTdosForReprocess);
  const reprocessFileLimit = yield* select(reprocessLimit);
  const dataJobsFailed = yield* select(getDataJobsFailed);
  const isSelectedAllJobsFailed = yield* select(getIsSelectedAllJobsFailed);
  const { apiRoot, token, veritoneAppId } = yield* call(getConfigModule);

  let numOfTdo = 0;
  if (isShowPreview) {
    // media detail page - single tdo
    numOfTdo = 1;
  } else if (isSelectedAllTdo) {
    // selected all tdo
    let searchQuery = yield* select(getFullSearchQuery);
    searchQuery = {
      ...searchQuery,
    };
    const { response }: any = yield* call(fetchSearchMedia, {
      searchQuery,
      apiRoot,
      token: token!, // TODO: Can't justify !, what should happen if token is null?
      veritoneAppId,
    });
    numOfTdo = response.totalRecords;
  } else if (selectedTdos.length > 0) {
    // selected individuals
    numOfTdo = selectedTdos.length;
  } else if (isSelectedAllJobsFailed) {
    const dataJobsFailed = yield* call(fetchAllJobsFailed);
    const reprocessTdoIds: string[] = [];
    for (const job of dataJobsFailed) {
      if (job?.target?.id && !reprocessTdoIds.includes(job.target.id)) {
        reprocessTdoIds.push(job.target.id);
      }
    }
    numOfTdo = reprocessTdoIds.length;
  } else if (dataJobsFailed.length > 0) {
    // selected individuals
    const reprocessTdoIds: string[] = [];
    for (const job of dataJobsFailed) {
      if (job?.target?.id && !reprocessTdoIds.includes(job.target.id)) {
        reprocessTdoIds.push(job.target.id);
      }
    }
    numOfTdo = reprocessTdoIds.length;
  }
  if (numOfTdo > reprocessFileLimit) {
    yield* put(actions.showModalProcessLimitExceeded(true));
  } else {
    yield* saveReprocessFile();
  }
}

function* saveReprocessFile() {
  const isShowPreview = yield* select(getIsShowPreview);
  const tdoIdPreview = yield* select(getTdoIdPreview);
  const defaultClusterId = yield* select(getDefaultClusterId);
  const dataLibraries = yield* select(libraries);
  const isSelectedAllTdo = yield* select(isSelectedAll);
  const enginesSelectedResults = yield* select(enginesSelected);
  const selectedTdos = yield* select(getTdosForReprocess);
  const dagTemplateId = yield* select(dagTemplateIdSelected);
  const reprocessFileLimit = yield* select(reprocessLimit);
  const dataJobsFailed = yield* select(getDataJobsFailed);
  const isSelectedAllJobsFailed = yield* select(getIsSelectedAllJobsFailed);
  const dagTemplatesData = yield* select(dagTemplates);
  const { apiRoot, token, veritoneAppId } = yield* call(getConfigModule);

  const categorySelected = dagTemplatesData?.find(
    (item) => item.id === dagTemplateId
  );
  const categoryId = categorySelected?.cognitiveCategoryId ?? '';
  yield* put(actions.showProcessByCategory(categoryId, true));

  const selectedEngines = enginesSelectedResults.flatMap(
    (item) => item.engineIds
  );

  const { hasStandaloneJobTemplates, noStandaloneJobTemplates } =
    checkStandaloneJobTemplates(selectedEngines);
  for (const engineMissingTemplates of noStandaloneJobTemplates) {
    console.error(
      `missing standaloneJobTemplates for engine ${engineMissingTemplates.description}: ${engineMissingTemplates.id}`
    );
  }
  let reprocessTdoIds: Array<string> = [];
  let reprocessTdos = [] as ProcessTDO[];
  if (isShowPreview) {
    // media detail page - single tdo
    reprocessTdoIds = [tdoIdPreview];
  } else if (isSelectedAllTdo) {
    // selected all tdo
    let searchQuery = yield* select(getFullSearchQuery);
    searchQuery = {
      ...searchQuery,
      limit: reprocessFileLimit,
    };
    const { response }: any = yield* call(fetchSearchMedia, {
      searchQuery,
      apiRoot,
      token: token!, // TODO: Can't justify !, what should happen if token is null?
      veritoneAppId,
    });
    if (response.error) {
      for (const error of response.error.errors) {
        yield* put(
          showNotification(
            `${error.message} was encountered while getting files. Please contact Veritone Support for assistance`,
            'error'
          )
        );
      }
      return;
    }
    const { records = [] } = response;
    reprocessTdoIds = records.map((item: { mediaId: string }) => item.mediaId);
  } else if (selectedTdos.length > 0) {
    // selected individuals
    reprocessTdos = selectedTdos.slice(0, reprocessFileLimit);
  } else if (isSelectedAllJobsFailed) {
    const dataJobsFailed = yield* call(fetchAllJobsFailed);
    for (const job of dataJobsFailed) {
      if (job?.target?.id && !reprocessTdoIds.includes(job.target.id)) {
        reprocessTdoIds.push(job.target.id);
      }
    }
  } else if (dataJobsFailed.length > 0) {
    // selected individuals
    for (const job of dataJobsFailed) {
      if (job?.target?.id && !reprocessTdoIds.includes(job.target.id)) {
        reprocessTdoIds.push(job.target.id);
      }
    }
  }

  const { numProcessTdos, numOfSuccess } = yield* createJobsForBatchReprocess({
    reprocessTdoIds,
    reprocessTdos,
    selectedEngines: hasStandaloneJobTemplates,
    defaultClusterId,
    dataLibraries,
    dagTemplateId,
    workflow: dagTemplateId ? 'simple' : 'advanced',
  });
  const totalFailure =
    reprocessTdoIds.length + reprocessTdos.length - numOfSuccess;
  yield* displayMessage(
    numProcessTdos,
    numOfSuccess,
    totalFailure - numOfSuccess
  );
}

function* displayMessage(
  numProcessTdos: number,
  numOfSuccess: number,
  numOfFailure: number
) {
  // nothing to process
  if (numProcessTdos === 0) {
    yield* put(actions.updateLoadingSaveUpload());
    yield* put(
      showNotification(
        'Unknown error was encountered while getting files. Please contact Veritone Support for assistance',
        'error'
      )
    );
    return;
  }

  if (numOfSuccess < 1) {
    // show message if all failed
    yield* showNotificationFailed();
    return;
  }

  yield* showNotificationUploadAndReprocess(numOfSuccess, numOfFailure);
}

function* createJobsForBatchReprocess({
  reprocessTdoIds,
  reprocessTdos,
  selectedEngines,
  defaultClusterId,
  dataLibraries,
  dagTemplateId,
  workflow,
}: {
  reprocessTdoIds: string[];
  reprocessTdos: ProcessTDO[];
  selectedEngines: Engine[];
  defaultClusterId: string;
  dataLibraries: { [key: string]: Library };
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}) {
  let reprocessItems: any = [];
  let isTdoId = true;
  if (reprocessTdoIds !== null && reprocessTdoIds.length > 0) {
    reprocessItems = reprocessTdoIds;
  } else {
    reprocessItems = reprocessTdos;
    isTdoId = false;
  }

  let numOfSuccess = 0;
  let numProcessTdos = 0;
  if (reprocessItems === null || reprocessItems.length === 0) {
    return { numProcessTdos, numOfSuccess };
  }
  for (let i = 0; i < reprocessItems.length; i += batchSize) {
    // Keep session alive via SDK call while reprocessing files
    window.aiware?.auth?.reportAppActivity?.();
    const batch = reprocessItems.slice(i, i + batchSize);
    const batchTdos = isTdoId ? yield* fetchDataTdos(batch) : batch;
    const processTdos = yield* buildReprocessTdo(
      batchTdos,
      selectedEngines,
      dagTemplateId
    );

    const jobResults = yield* call(createJobForReprocess, {
      tdos: processTdos,
      defaultClusterId,
      dataLibraries,
      dagTemplateId,
      isReprocess: true,
      workflow,
    });
    numProcessTdos += processTdos.length;
    numOfSuccess +=
      jobResults?.response?.data && !jobResults.error
        ? Object.keys(jobResults.response.data).length
        : 0;
    const percentage = (i / reprocessItems.length) * 100;
    yield* put(actions.updatePercentageFilesUploaded(percentage));
  }
  return { numProcessTdos, numOfSuccess };
}

function* createJobsForBatchUpload({
  uploadItems,
  dataTagsCustomize,
  date,
  contentTemplates,
  dataSelectedFolder,
  defaultClusterId,
  defaultEngineId,
  dataLibraries,
  dagTemplateId,
  workflow,
}: {
  uploadItems: UploadResult[];
  dataTagsCustomize: TagsCustomize[];
  date: string;
  contentTemplates: { schemaId: null; data: any }[];
  dataSelectedFolder: Folder | undefined;
  defaultClusterId: string;
  defaultEngineId: string;
  dataLibraries: { [key: string]: Library };
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}) {
  let numOfSuccess = 0;
  if (uploadItems.length === 0) {
    return numOfSuccess;
  }
  for (let i = 0; i < uploadItems.length; i += batchSize) {
    // Keep session alive via SDK call while uploading files
    window.aiware?.auth?.reportAppActivity?.();
    const batch = uploadItems.slice(i, i + batchSize);
    const jobResults: any = yield* all(
      batch.map((uploadItem) => {
        return call(createJobsForSingleUpload, {
          uploadItem,
          dataTagsCustomize,
          date,
          contentTemplates,
          dataSelectedFolder,
          defaultClusterId,
          defaultEngineId,
          dataLibraries,
          dagTemplateId,
          workflow,
        });
      })
    );
    let errMsg = '';
    for (const jobResult of jobResults) {
      if (jobResult.error) {
        errMsg += jobResult.error + ' ';
      } else {
        numOfSuccess++;
      }
    }
    if (errMsg) {
      Sentry.captureException(`failed to created job - ${errMsg}`);
      console.error(`failed to created job - ${errMsg}`);
    }
    const percentage = (i / uploadItems.length) * 100;
    yield* put(actions.updatePercentageFilesUploaded(percentage));
  }
  return numOfSuccess;
}

// upload file would do:
// 1. create a tdo
// 2. create job for default engine. such as primary asset or playback assets creation
// 3. create job for selected engine.
// if no engine selected, the tdo and default job (1, 2) are still created.
function* createJobsForSingleUpload({
  uploadItem,
  dataTagsCustomize,
  date,
  contentTemplates,
  dataSelectedFolder,
  defaultClusterId,
  defaultEngineId,
  dataLibraries,
  dagTemplateId,
  workflow,
}: {
  uploadItem: UploadResult;
  dataTagsCustomize: TagsCustomize[];
  date: string;
  contentTemplates: { schemaId: null; data: any }[];
  dataSelectedFolder: Folder | undefined;
  defaultClusterId: string;
  defaultEngineId: string;
  dataLibraries: { [key: string]: Library };
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}) {
  const tdoDetail = buildTdoDetail(uploadItem, dataTagsCustomize, date);
  const input = {
    startDateTime: date,
    stopDateTime: date,
    addToIndex: true,
    details: tdoDetail,
    name: uploadItem.fileName,
    contentTemplates,
    parentFolderId: dataSelectedFolder?.treeObjectId ?? '',
    sourceData: {
      sourceId: -1,
    },
  };
  const variables = {
    input,
  };
  const tdoResults = yield* call(handleRequest<CreateTdoResponse>, {
    query: queryCreateTdo,
    variables,
  });
  if (tdoResults.error) {
    return { file: uploadItem.fileName, error: tdoResults.error };
  }
  if (!has(tdoResults, 'response.data.createTDO.id')) {
    return {
      file: uploadItem.fileName,
      error: new Error(`no tdo id is created for file ${uploadItem.fileName}`),
    };
  }
  const tdoId = tdoResults.response!.data.createTDO.id; // ! valid because of `has` check above

  const callCreateJobResults = yield* call(callCreateJob, {
    defaultClusterId,
    defaultEngineId,
    tdoId: tdoId,
    fileUrl: uploadItem.getUrl,
    fileType: uploadItem.type,
    engines: uploadItem.engines,
    dagTemplateId,
    workflow,
  } as any);
  if (callCreateJobResults.error) {
    return {
      tdoId,
      file: uploadItem.fileName,
      error: callCreateJobResults.error,
    };
  }

  // if no engine selected, then no job specific engines.
  if (isEmpty(uploadItem.engines) && !dagTemplateId) {
    return { tdoId, file: uploadItem.fileName };
  }

  if (uploadItem.engines && uploadItem.getUrl) {
    const callLaunchMultipleEnginesJobResults: any = yield* call(
      callLaunchMultipleEnginesJob,
      {
        engines: uploadItem.engines,
        targetId: tdoId,
        isReprocess: false,
        uploadUrl: uploadItem.getUrl,
        defaultClusterId,
        dataLibraries,
        fileName: '',
        fileType: uploadItem.type,
        dagTemplateId,
        workflow,
      }
    );
    if (callLaunchMultipleEnginesJobResults.error) {
      return {
        tdoId,
        file: uploadItem.fileName,
        error: callLaunchMultipleEnginesJobResults.error,
      };
    }
    return { tdoId, file: uploadItem.fileName };
  }
}

function* callCreateJob({
  defaultClusterId,
  defaultEngineId,
  tdoId,
  fileUrl,
  fileType,
  engines,
  dagTemplateId,
  workflow,
}: {
  defaultClusterId: string;
  defaultEngineId: DefaultEngineId;
  tdoId: string;
  fileUrl: string;
  fileType: string;
  engines: Engine[];
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}) {
  const { query, variables } = buildDAG(
    fileUrl,
    fileType,
    tdoId,
    defaultClusterId,
    defaultEngineId,
    workflow
  );

  const isTextFile = DOCS_MIME_TYPES.includes(fileType);
  // run engine open pdf if the uploaded file is text file and
  // user choose at least an engine or a engine template. Currently
  // no job other than ingestion job created if no engine or dag template
  // is selected.
  if ((!isEmpty(engines) || dagTemplateId) && isTextFile) {
    const variables = buildVariablesEngineOpenPdf(
      tdoId,
      actions.ENGINE_OPEN_PDF,
      fileUrl,
      defaultClusterId
    );
    yield* call(handleRequest, {
      query: queryCreateLaunchSingleEngineJob,
      variables,
    });
  }
  return yield* call(handleRequest, { query, variables });
}

function* createJobForReprocess({
  tdos,
  defaultClusterId,
  dataLibraries,
  dagTemplateId,
  isReprocess,
  workflow,
}: {
  tdos: ProcessTDO[];
  defaultClusterId: string;
  dataLibraries: { [key: string]: Library };
  dagTemplateId: string;
  isReprocess: boolean;
  workflow: 'simple' | 'advanced';
}) {
  if (dagTemplateId) {
    const queryWithVariables = buildCreateJobQueryForDagTemplate({
      tdos,
      defaultClusterId,
      dagTemplateId,
      workflow,
    });
    return yield* call(
      handleRequest<BuildCreateJobQueryForDagTemplateResponse>,
      queryWithVariables
    );
  }
  const illuminateAppFolders = yield* select(getIlluminateAppFolders);
  const queryWithVariables = buildCreateJobQueryForLaunchSingleEngineJob({
    isReprocess,
    tdos,
    defaultClusterId,
    dataLibraries,
    illuminateAppFolderTreeObjectId:
      illuminateAppFolders[0]?.treeObjectId ?? '',
  });

  return yield* call(
    handleRequest<BuildCreateJobQueryForLaunchSingleEngineJobResponse>,
    queryWithVariables
  );
}

type BuildCreateJobQueryForLaunchSingleEngineJobResponse = Record<
  string,
  { targetId: string }
>;
function buildCreateJobQueryForLaunchSingleEngineJob({
  isReprocess,
  tdos,
  defaultClusterId,
  dataLibraries,
  illuminateAppFolderTreeObjectId,
}: {
  tdos: ProcessTDO[];
  defaultClusterId: string;
  dataLibraries: { [key: string]: Library };
  isReprocess: boolean;
  illuminateAppFolderTreeObjectId: string;
}) {
  const statements: string[] = [];
  const inputs: string[] = [];
  const variables: {
    [key: string]: {
      fields: {
        fieldName: string;
        fieldValue: string;
      }[];
      engineId: string;
      targetId?: string;
    };
  } = {};

  for (let i = 0; i < tdos.length; i++) {
    const tdoKey = i;
    const tdo = tdos[i]!; // Safe due to loop condition
    const targetId = tdo.id;
    const fileType = get(tdo, 'primaryAsset.contentType', '');
    const uploadUrl = get(tdo, 'uploadUrl', '');
    const fileName = '';

    if (Array.isArray(tdo.engines)) {
      for (let j = 0; j < tdo.engines.length; j++) {
        const engineKey = j;
        const engine = tdo.engines[j]!; // Safe due to loop condition
        const isTranslationEngine =
          engine.category.id === actions.CATEGORY_ID_TRANSLATE;
        const hasVtnMode = isReprocess
          ? isTranslationEngine &&
            [
              ...actions.DOCS_MIMES_TYPE,
              ...actions.VIDEO_MIMES_TYPE,
              ...actions.AUDIO_MIME_TYPES,
              ...actions.MESSAGE_MIME_TYPE,
            ].includes(fileType)
          : isTranslationEngine;
        const hasTextExtractionFolderID =
          engine.id === actions.ENGINE_FILE_TRANSLATOR;
        const createJobVariables = buildVariables(
          engine,
          defaultClusterId,
          uploadUrl,
          dataLibraries,
          targetId,
          isReprocess,
          fileName,
          isTranslationEngine,
          hasVtnMode,
          fileType,
          illuminateAppFolderTreeObjectId,
          hasTextExtractionFolderID
        );
        inputs.push(`$input_${tdoKey}_${engineKey}: SingleEngineJobInput!`);
        variables[`input_${tdoKey}_${engineKey}`] = createJobVariables.input;
        statements.push(`
        launchSingleEngineJob_${tdoKey}_${engineKey}: launchSingleEngineJob(input: $input_${tdoKey}_${engineKey}){
          targetId
        }
      `);
      }
    }
  }

  const query = `mutation createLaunchSingleEngineJob(${inputs.join()}){
   ${statements.join('\n')}
  }`;

  return {
    query,
    variables,
  };
}

type BuildCreateJobQueryForDagTemplateResponse = Record<string, { id: string }>;

function buildCreateJobQueryForDagTemplate({
  tdos,
  defaultClusterId,
  dagTemplateId,
  workflow,
}: {
  tdos: ProcessTDO[];
  defaultClusterId: string;
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}) {
  const statements: string[] = [];
  const inputs: string[] = [];
  const variables: {
    [key: string]: {
      targetId: string;
      dagTemplateId: string;
      uploadUrl: string;
      clusterId: string;
      jobConfig: {
        illuminate: {
          workflow: 'simple' | 'advanced';
        };
      };
    };
  } = {};

  for (let i = 0; i < tdos.length; i++) {
    const tdoKey = i;
    const tdo = tdos[i]!; // Safe due to loop
    const targetId = tdo.id;
    const uploadUrl = get(tdo, 'uploadUrl', '');
    const dagTemplateJobVariables = buildDagTemplateJobVariables({
      targetId,
      dagTemplateId,
      uploadUrl,
      defaultClusterId,
      workflow,
    });
    inputs.push(`$input_${tdoKey}: CreateJob`);
    variables[`input_${tdoKey}`] = dagTemplateJobVariables.input;
    statements.push(`
          createJob_${tdoKey}: createJob(input: $input_${tdoKey}){
            id
          }
        `);
  }
  const query = `mutation createJobTemplate(${inputs.join()}){
      ${statements.join('\n')}
    }`;

  return {
    query,
    variables,
  };
}

function* callLaunchMultipleEnginesJob({
  engines,
  targetId,
  isReprocess,
  uploadUrl,
  defaultClusterId,
  dataLibraries,
  fileName,
  fileType,
  dagTemplateId,
  workflow,
}: {
  engines: Engine[];
  targetId: string;
  defaultClusterId: string;
  dataLibraries: { [key: string]: Library };
  uploadUrl: string;
  fileType: string;
  dagTemplateId: string;
  isReprocess: boolean;
  fileName: string;
  workflow: 'simple' | 'advanced';
}) {
  if (dagTemplateId) {
    const dagTemplateJobVariables = buildDagTemplateJobVariables({
      targetId: targetId,
      dagTemplateId,
      uploadUrl,
      defaultClusterId,
      workflow,
    });
    return yield* call(handleRequest, {
      query: queryCreateJobFromDagTemplate,
      variables: dagTemplateJobVariables,
    });
  }

  const illuminateAppFolders = yield* select(getIlluminateAppFolders);
  return yield* all(
    engines.map((engine) => {
      const isTranslationEngine =
        engine.category.id === actions.CATEGORY_ID_TRANSLATE;
      const hasVtnMode = isReprocess
        ? isTranslationEngine &&
          [
            ...actions.DOCS_MIMES_TYPE,
            ...actions.VIDEO_MIMES_TYPE,
            ...actions.AUDIO_MIME_TYPES,
            ...actions.MESSAGE_MIME_TYPE,
          ].includes(fileType)
        : isTranslationEngine;
      const hasTextExtractionFolderID =
        engine.id === actions.ENGINE_FILE_TRANSLATOR;
      const variables = buildVariables(
        engine,
        defaultClusterId,
        uploadUrl,
        dataLibraries,
        targetId,
        isReprocess,
        fileName,
        isTranslationEngine,
        hasVtnMode,
        fileType,
        illuminateAppFolders?.[0]?.treeObjectId ?? '',
        hasTextExtractionFolderID
      );
      return call(handleRequest, {
        query: queryCreateLaunchSingleEngineJob,
        variables,
      });
    })
  );
}

function* buildReprocessTdo(
  tdoArray: TDO[],
  selectedEngines: Engine[],
  dagTemplateId: string
) {
  // reprocess tdo with engine translate
  const regularTdos = [];
  const extractedTextToPlainTextTdos = [];
  const transcriptTranslationTdos = [];
  const transcriptToPlainTextTdos = [];
  const docFileTypes = [
    ...actions.DOCS_MIMES_TYPE,
    ...actions.MESSAGE_MIME_TYPE,
  ];
  const videoAudioFileTypes = [
    ...actions.VIDEO_MIMES_TYPE,
    ...actions.AUDIO_MIME_TYPES,
  ];

  for (const dataTdo of tdoArray) {
    const fileType = get(dataTdo, 'primaryAsset.contentType', '');
    // can not translate video or audio, so translate transcript
    if (
      isTranslationSelected(selectedEngines) &&
      videoAudioFileTypes.includes(fileType)
    ) {
      // if the engine dose not support transcript translation,
      // then convert transcript to to plain text for translation
      if (isTranscriptTranslationSupported(selectedEngines)) {
        transcriptTranslationTdos.push(dataTdo);
      } else {
        transcriptToPlainTextTdos.push(dataTdo);
      }
      continue;
    }
    // can not translate native doc(e.g. pdf,) so translate extracted text
    if (
      isTranslationSelected(selectedEngines) &&
      docFileTypes.includes(fileType)
    ) {
      extractedTextToPlainTextTdos.push(dataTdo);
      continue;
    }
    regularTdos.push(dataTdo);
  }

  const extractedTextTextConvertedTdos = yield* buildPlainTextTranslationTdos(
    extractedTextToPlainTextTdos,
    'textExtraction'
  );
  const transcriptTextConvertedTdos = yield* buildPlainTextTranslationTdos(
    transcriptToPlainTextTdos,
    'transcription'
  );
  const transcriptTranslationUpdatedTdos = buildTranscriptTranslationTdos(
    transcriptTranslationTdos
  );
  const reprocessTdos = [
    ...regularTdos,
    ...extractedTextTextConvertedTdos,
    ...transcriptTextConvertedTdos,
    ...transcriptTranslationUpdatedTdos,
  ];

  const dataTdosSuccess: any = [];
  const dataTdosFailed = [];
  reprocessTdos.forEach((item) => {
    const supportedEngines = getSupportedEngine(
      selectedEngines,
      get(item, 'primaryAsset.contentType', ''),
      'reprocess'
    );
    if (dagTemplateId) {
      dataTdosSuccess.push({
        ...item,
        engines: [],
      });
      return;
    }
    if (supportedEngines.length > 0) {
      dataTdosSuccess.push({
        ...item,
        engines: supportedEngines,
      });
    } else {
      dataTdosFailed.push(item);
    }
  });
  return dataTdosSuccess;
}

function* showNotificationUploadAndReprocess(success: number, failed: number) {
  yield* put(actions.fetchCreateJobSuccess());
  yield* put(showNotification(renderMessage(success, failed), 'success'));
  yield* put(refreshProcessingJobs());
}

function renderMessage(success: number, failed: number) {
  let message = `${success} jobs are created successfully `;
  if (failed > 0) {
    message += `${failed} jobs are failed`;
  }
  return message;
}

function getSupportedFormats(engine: Engine, type: string) {
  const supportedFormats = concat(
    get(engine, 'supportedInputFormats', []),
    get(engine, 'manifest.supportedInputTypes', []),
    get(engine, 'builds.records[0].supportedInputFormats', [])
  );
  if (
    type === 'reprocess' &&
    engine.category.id === actions.CATEGORY_ID_TRANSLATE
  ) {
    supportedFormats.push(
      ...actions.VIDEO_MIMES_TYPE,
      ...actions.AUDIO_MIME_TYPES,
      ...actions.DOCS_MIMES_TYPE,
      ...actions.MESSAGE_MIME_TYPE
    );
  }
  return uniq(supportedFormats);
}

function getSupportedEngine(engines: Engine[], fileType: string, type: string) {
  return engines.filter((engine) =>
    getSupportedFormats(engine, type).includes(fileType)
  );
}

function checkStandaloneJobTemplates(engines: Engine[]) {
  const hasStandaloneJobTemplates: any = [];
  const noStandaloneJobTemplates: any = [];

  engines.forEach((engine) => {
    if (engine?.standaloneJobTemplates?.length ?? 0 > 0) {
      hasStandaloneJobTemplates.push(engine);
    } else {
      noStandaloneJobTemplates.push(engine);
    }
  });
  return { hasStandaloneJobTemplates, noStandaloneJobTemplates };
}

function isTranslationSelected(selectedEngines: Engine[]) {
  return selectedEngines.some(
    (engine) => engine.category.id === actions.CATEGORY_ID_TRANSLATE
  );
}

function isTranscriptTranslationSupported(selectedEngines: Engine[]) {
  return selectedEngines.some((engine) =>
    TRANSCRIPT_TRANSLATION_SUPPORTED.includes(engine.id)
  );
}

function buildTdoDetail(
  item: UploadResult,
  dataTagsCustomize: TagsCustomize[],
  date: string
) {
  return {
    'veritone-program': {
      programId: '-1',
      programImage: get(item, 'programImage', ''),
      programLiveImage: get(item, 'programLiveImage', ''),
      programName: '',
    },
    'veritone-permissions': {
      acls: [
        {
          groupId: '57941654-0f5f-4234-ba31-a232b3df75f7',
          permission: 'owner',
        },
      ],
      isPublic: false,
    },
    tags:
      item.tagsEdit && item.tagsEdit.length
        ? dataTagsCustomize.concat(item.tagsEdit)
        : dataTagsCustomize,
    date: item.dateTime ? item.dateTime : date,
    'veritone-file': {
      filename: item.fileName,
      size: item.size,
      mimetype: item.type,
    },
    'veritone-media-source': {
      mediaSourceTypeId: -5,
      mediaSourceId: -1,
    },
  };
}

function* showNotificationFailed() {
  yield* put(actions.setDefaultStateUploadFile());
  yield* put(
    showNotification(
      'An error occurred while creating the job. Please try again later',
      'error'
    )
  );
}

function buildVariablesEngineOpenPdf(
  targetId: string,
  engineId: string,
  uploadUrl: string,
  defaultClusterId: string
) {
  return {
    input: {
      targetId,
      engineId,
      uploadUrl,
      fields: [
        {
          fieldName: 'clusterId',
          fieldValue: defaultClusterId,
        },
      ],
    },
  };
}

function* buildPlainTextTranslationTdos(translateTdos: TDO[], type: string) {
  const updateTranslateDocsTdos: any = [];
  if (translateTdos.length < 1) {
    return [];
  }
  const { signedUrls, enginesResult } = yield* fetchSignedUrlAndEngineResults(
    translateTdos,
    type === 'textExtraction'
      ? actions.CATEGORY_ID_TEXT_EXTRACTION
      : actions.CATEGORY_ID_TRANSCRIPTION
  );
  yield* all(
    enginesResult
      .filter((item: EnginesResult) => item.records?.[0])
      .map((item: EnginesResult, key: number) => {
        const body = convertTextFromVtnStandard(item.records[0]!, type);
        updateTranslateDocsTdos.push({
          ...translateTdos.find(
            (tdo) => tdo.id === get(item, 'records[0].tdoId', '')
          ),
          uploadUrl: signedUrls?.[key]?.getUrl ?? '',
        });
        return call(fetchUpload, signedUrls?.[key]?.url ?? '', body);
      })
  );
  return updateTranslateDocsTdos;
}

function buildTranscriptTranslationTdos(translateTdos: TDO[]) {
  return translateTdos.map((item) => ({
    ...item,
    uploadUrl: get(item, 'assets.records[0].signedUri', ''),
  }));
}

function convertTextFromVtnStandard(item: Records, type: string) {
  let text = '';
  if (type === 'textExtraction') {
    const { object = [] } = get(item, 'jsondata', []);
    object.length &&
      object.forEach((item: { text: string }) => {
        text += get(item, 'text', '') + '.';
      });
    return text;
  }
  const { series = [] } = get(item, 'jsondata', []);
  series.length &&
    series.forEach((item: { words: { word: string }[] }) => {
      text += get(item, 'words[0].word', '') + ' ';
    });
  return text;
}

function* fetchSignedUrlAndEngineResults(
  translateTdos: { id: string }[],
  engineCategoryId: string
): Generator<
  AllEffect<any>,
  {
    signedUrls: {
      url: string;
      unsignedUrl: string;
      getUrl: string;
      key: string;
    }[];
    enginesResult: {
      records: {
        jsondata: Record<string, any>;
        engineId: string;
        assetId: string;
        tdoId: string;
      }[];
    }[];
  }
> {
  const signedUrl: string[] = [];
  const engResult: string[] = [];
  translateTdos.forEach((item, key) => {
    signedUrl.push(`
         getSignedWritableUrl_${key}: getSignedWritableUrl(expiresInSeconds:86400) {
          url
          unsignedUrl
          getUrl
          key
        }
      `);
    engResult.push(`
        getEngineResult_${key}: engineResults(tdoId: "${item.id}", engineCategoryIds: ["${engineCategoryId}"]){
          records {
            jsondata
            engineId
            assetId
            tdoId
          }
        }
      `);
  });
  const querySignedUrl = `
      query getSignedWritableUrl {
        ${signedUrl.join('\n')}
    }`;
  const queryEngineResult = `
      query getEngineResults {
        ${engResult.join('\n')}
    }`;
  const [dataSignedUrl, dataEnginesResult] = yield* all([
    call(
      handleRequest<
        Record<
          string,
          {
            url: string;
            unsignedUrl: string;
            getUrl: string;
            key: string;
          }
        >
      >,
      { query: querySignedUrl }
    ),
    call(
      handleRequest<
        Record<
          string,
          {
            records: {
              jsondata: Record<string, any>;
              engineId: string;
              assetId: string;
              tdoId: string;
            }[];
          }
        >
      >,
      { query: queryEngineResult }
    ),
  ]);
  // const signedUrls = Object.values(get(dataSignedUrl, 'response.data', {}));
  const signedUrls = Object.values(dataSignedUrl?.response?.data || {});
  const enginesResult = Object.values(
    // get(dataEnginesResult, 'response.data', {})
    dataEnginesResult?.response?.data || {}
  );
  return { signedUrls, enginesResult };
}

type GetTdosResponse = Record<
  string,
  {
    id: string;
    primaryAsset: {
      assetType: string;
      signedUri: string;
      contentType: string;
    };
    assets: {
      records: {
        id: string;
        signedUri: string;
        assetType: string;
        contentType: string;
      }[];
    };
  }
>;
function* fetchDataTdos(tdoIds: string[]) {
  const tdos: string[] = [];
  tdoIds.forEach((item, key) => {
    tdos.push(`
      getTdo_${key}: temporalDataObject(id:"${item}"){
        id,
        primaryAsset(assetType:"media"){
          assetType,
          signedUri,
          contentType
        }
        assets(assetType: "vtn-standard"){
          records{
            id
            signedUri
            assetType
            contentType
          }
        }
      }
    `);
  });
  const query = `
        query getTdos {
          ${tdos.join('\n')}
      }`;
  const { response } = yield* call(handleRequest<GetTdosResponse>, {
    query,
    ignoredError: true,
  });
  if (!response) {
    return [];
  }
  delete response.data.errors;
  const dataTdos = compact(Object.values(get(response, 'data', [])));
  return dataTdos;
}

function buildDagTemplateJobVariables({
  targetId,
  dagTemplateId,
  uploadUrl,
  defaultClusterId,
  workflow,
}: {
  targetId: string;
  dagTemplateId: string;
  uploadUrl: string;
  defaultClusterId: string;
  workflow: 'simple' | 'advanced';
}) {
  const input = {
    targetId,
    dagTemplateId,
    uploadUrl,
    clusterId: defaultClusterId,
    jobConfig: {
      illuminate: {
        workflow,
      },
    },
  };
  return {
    input,
  };
}
