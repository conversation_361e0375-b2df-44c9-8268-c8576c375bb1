{"info": {"_postman_id": "e5543746-af75-40d7-95ad-a423a30ed94e", "name": "illuminate Backend (local-azure-dev)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "23719937"}, "item": [{"name": "Healthcheck", "request": {"method": "GET", "header": [], "url": {"raw": "https://localhost:3002/api/health", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "health"]}}, "response": []}, {"name": "Provision Org", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer ee7cbff9-1ec7-4309-aff3-0ab54e8e3dce", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"orgName\": \"AzureDev32\",\n    \"templatePbixFileName\": \"ContactAnalyticsDemo.pbix\",\n    \"lifetimeInMinutes\": 100,\n    \"orgId\": 32\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/provisionOrg", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "provisionOrg"]}}, "response": []}, {"name": "Generate Embed <PERSON>ken", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer 32bc1e44-66d5-478f-865c-8cc67224bfa3", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/generateEmbedToken", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "generateEmbedToken"]}}, "response": []}, {"name": "<PERSON><PERSON> Data", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb250ZW50QXBwbGljYXRpb25JZCI6IjI3ZGRiNzcxLWQ2NDctNDNiMS05NDE4LTAyOTc2OWJlZWUyNCIsImNvbnRlbnRPcmdhbml6YXRpb25JZCI6MTQwLCJ0b2tlbkFwcGxpY2F0aW9uSWQiOiI4YjNlYWMxYy01MTUwLTQ0OGUtOGQ5OS1mYjdiODYwZTdlNDEiLCJ1c2VySWQiOiI5YzVlNGI1My0yNWI4LTQ0YzYtOTFhNS0xZTQ2NDk3Yzk5MDMiLCJzY29wZSI6W3siYWN0aW9ucyI6WyJjbXMuYWNjZXNzIiwiY21zLm1lZGlhLmNyZWF0ZSIsImNtcy5tZWRpYS5yZWFkIiwiY21zLm1lZGlhLnVwZGF0ZSIsImNtcy5tZWRpYS5kZWxldGUiLCJjbXMud29ya2Zsb3dzLmNyZWF0ZSIsImNtcy53b3JrZmxvd3MucmVhZCIsImNtcy53b3JrZmxvd3MudXBkYXRlIiwiY21zLndvcmtmbG93cy5kZWxldGUiLCJjbXMuY29udGVudHRlbXBsYXRlLmNyZWF0ZSIsImNtcy5jb250ZW50dGVtcGxhdGUucmVhZCIsImNtcy5jb250ZW50dGVtcGxhdGUudXBkYXRlIiwiY21zLmNvbnRlbnR0ZW1wbGF0ZS5kZWxldGUiLCJqb2IuY3JlYXRlIiwiam9iLnJlYWQiLCJqb2IudXBkYXRlIiwiam9iLmRlbGV0ZSIsInRhc2suY3JlYXRlIiwidGFzay5yZWFkIiwidGFzay51cGRhdGUiLCJ0YXNrLmRlbGV0ZSIsInJlY29yZGluZy5jcmVhdGUiLCJyZWNvcmRpbmcucmVhZCIsInJlY29yZGluZy51cGRhdGUiLCJyZWNvcmRpbmcuZGVsZXRlIiwiY21zLnJlcG9ydC5jcmVhdGUiLCJjbXMuYW5hbHl0aWNzLnJlYWQiXSwicmVzb3VyY2VzIjp7ImFwcGxpY2F0aW9uSWQiOiI4YjNlYWMxYy01MTUwLTQ0OGUtOGQ5OS1mYjdiODYwZTdlNDEifX1dLCJpYXQiOjE2OTk5ODczMTksImV4cCI6MTcwMDU5MjExOSwic3ViIjoiZW5naW5lLXJ1biIsImp0aSI6IjdlYmI3YTEwLTgzMWQtMTFlZS05YWViLWViNDY4Y2JlMzVlMyJ9.CNLsdt1_O_re9jXHx2sLCzACkyh8bMpzDNXlDU9zN98", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"stops\": [\n        {\n            \"data\": [\n                {\n                    \"recordId\": \"{{$guid}}\",\n                    \"age\": 85,\n                    \"city\": \"ANAHEIM\",\n                    \"lgbt\": false,\n                    \"gender\": \"Male\",\n                    \"school\": \"\",\n                    \"location\": \"BROOKHURST / crescent \",\n                    \"ethnicity\": [\n                        \"White\"\n                    ],\n                    \"disability\": [],\n                    \"k12_school\": false,\n                    \"timeOfStop\": \"09:05\",\n                    \"personNumber\": 1,\n                    \"resultOfStop\": [\n                        \"Warning (verbal or written)\"\n                    ],\n                    \"ecSubdivision\": \"\",\n                    \"basisForSearch\": [],\n                    \"datetimeOfStop\": \"2022-11-07T17:05:00Z\",\n                    \"durationOfStop\": 5,\n                    \"limitedEnglish\": false,\n                    \"reasonsForStop\": \"Traffic Violation\",\n                    \"warningOffCode\": [\n                        \"21461(A) - DRIVER, FAIL \\\"OBEY\\\" SIGN/ETC\",\n                        \"test\",\n                        \"234556(A) - DRIVER, FAIL \\\"OBEY\\\" 2/ETC\"\n                    ],\n                    \"citationOffCode\": [],\n                    \"stopForAStudent\": false,\n                    \"trafficViolation\": \"Moving\",\n                    \"disciplineUnderEc\": \"\",\n                    \"ethnicityExclusive\": \"White\",\n                    \"genderNonconforming\": false,\n                    \"reasonableSuspicion\": [],\n                    \"contrabandOrEvidence\": [],\n                    \"suspicionOffenseCode\": \"\",\n                    \"typeOfPropertySeized\": [],\n                    \"responseToServiceCall\": false,\n                    \"actionsTakenDuringStop\": [],\n                    \"custodialArrestOffCode\": [],\n                    \"reasonForStopNarrative\": \"Regulatory Sign \",\n                    \"basisForPropertySeizure\": [],\n                    \"basisForSearchNarrative\": \"\",\n                    \"officerTypeOfAssignment\": \"Patrol, traffic, field operations\",\n                    \"officerYearsOfExperience\": 1,\n                    \"inFieldCiteAndReleaseCode\": [\n                        \"test\",\n                        \"test,test,test\",\n                        \"12345678 \\\" , \\\"\"\n                    ],\n                    \"officerOtherAssignmentType\": \"\",\n                    \"trafficViolationOffenseCode\": \"21461(A) - DRIVER FAIL OBEY SIGN/ETC\",\n                    \"raceOfOfficer\": [\n                        \"Asian\",\n                        \"Black/African American\",\n                        \"Hispanic/Latine(x)\",\n                        \"White\"\n                    ],\n                    \"sexualOrientation\": \"Straight/Heterosexual\",\n                    \"typeOfStop\": \"Vehicular\",\n                    \"officerWorksWithNonPrimaryAgency\": false,\n                    \"reasonGivenStoppedPerson\": [\n                        \"Matched suspect description\",\n                        \"Matched description of suspect’s vehicle or vehicle observed at the scene of a crime\",\n                        \"Witness or victim identified stopped person as a suspect of a crime\"\n                    ],\n                    \"stopDuringWellnessCheck\": false,\n                    \"typeOfAssignmentOfficer\": \"Off Duty - Working Private Event\",\n                    \"stoppedPassenger\": false,\n                    \"stoppedInsideResidence\": false,\n                    \"nonConforming\": false,\n                    \"unhoused\": false,\n                    \"genderOfOfficer\": \"Cisgender man/boy\",\n                    \"nonBinaryOfficer\": false\n                }\n            ]\n        },\n        {\n            \"customQuestionData\": [\n                {\n                    \"recordId\": \"contact-stop-id-1\", // stop record id\n                    \"personNumber\": 1, // person number = 0 is for stop-wide answers,\n                    \"isStopQuestion\": false, // the answer is for a stop-wide question (personNumber should be 0 if true)\n                    \"answer\": [\n                        \"Option, 1\",\n                        \"Option ,\\\",2\\\"\"\n                    ], // string or string[]\n                    \"questionKey\": \"somethingRandom<k9ds22bjK>\" // the result path the question is saved to,\n                },\n                {\n                    \"recordId\": \"contact-stop-id-1\", // stop record id\n                    \"personNumber\": 0, // person number = 0 is for stop-wide answers,\n                    \"isStopQuestion\": true, // the answer is for a stop-wide question (personNumber should be 0 if true)\n                    \"answer\": \"Some text answer\", // string or string[]\n                    \"questionKey\": \"enterSomeText<123567890>\" // the result path the question is saved to,\n                }\n            ],\n            \"data\": [\n                {\n                    \"recordId\": \"{{$guid}}\",\n                    \"age\": 85,\n                    \"city\": \"ANAHEIM\",\n                    \"lgbt\": false,\n                    \"gender\": \"Male\",\n                    \"school\": \"\",\n                    \"location\": \"BROOKHURST / crescent \",\n                    \"ethnicity\": [\n                        \"White\"\n                    ],\n                    \"disability\": [],\n                    \"k12_school\": false,\n                    \"timeOfStop\": \"09:05\",\n                    \"personNumber\": 1,\n                    \"resultOfStop\": [\n                        \"Warning (verbal or written)\"\n                    ],\n                    \"ecSubdivision\": \"\",\n                    \"basisForSearch\": [],\n                    \"datetimeOfStop\": \"2022-11-07T17:05:00Z\",\n                    \"durationOfStop\": 5,\n                    \"limitedEnglish\": false,\n                    \"reasonsForStop\": \"Traffic Violation\",\n                    \"warningOffCode\": [\n                        \"21461(A) - DRIVER, FAIL \\\"OBEY\\\" SIGN/ETC\",\n                        \"test\",\n                        \"234556(A) - DRIVER, FAIL \\\"OBEY\\\" 2/ETC\"\n                    ],\n                    \"citationOffCode\": [],\n                    \"stopForAStudent\": false,\n                    \"trafficViolation\": \"Moving\",\n                    \"disciplineUnderEc\": \"\",\n                    \"ethnicityExclusive\": \"White\",\n                    \"genderNonconforming\": false,\n                    \"reasonableSuspicion\": [],\n                    \"contrabandOrEvidence\": [],\n                    \"suspicionOffenseCode\": \"\",\n                    \"typeOfPropertySeized\": [],\n                    \"responseToServiceCall\": false,\n                    \"actionsTakenDuringStop\": [],\n                    \"custodialArrestOffCode\": [],\n                    \"reasonForStopNarrative\": \"Regulatory Sign \",\n                    \"basisForPropertySeizure\": [],\n                    \"basisForSearchNarrative\": \"\",\n                    \"officerTypeOfAssignment\": \"Patrol, traffic, field operations\",\n                    \"officerYearsOfExperience\": 1,\n                    \"inFieldCiteAndReleaseCode\": [\n                        \"test\",\n                        \"test,test,test\",\n                        \"12345678 \\\" , \\\"\"\n                    ],\n                    \"officerOtherAssignmentType\": \"\",\n                    \"trafficViolationOffenseCode\": \"21461(A) - DRIVER FAIL OBEY SIGN/ETC\",\n                    \"raceOfOfficer\": [\n                        \"Asian\",\n                        \"Black/African American\",\n                        \"Hispanic/Latine(x)\",\n                        \"White\"\n                    ],\n                    \"sexualOrientation\": \"Straight/Heterosexual\",\n                    \"typeOfStop\": \"Vehicular\",\n                    \"officerWorksWithNonPrimaryAgency\": false,\n                    \"reasonGivenStoppedPerson\": [\n                        \"Matched suspect description\",\n                        \"Matched description of suspect’s vehicle or vehicle observed at the scene of a crime\",\n                        \"Witness or victim identified stopped person as a suspect of a crime\"\n                    ],\n                    \"stopDuringWellnessCheck\": false,\n                    \"typeOfAssignmentOfficer\": \"Off Duty - Working Private Event\",\n                    \"stoppedPassenger\": false,\n                    \"stoppedInsideResidence\": false,\n                    \"nonConforming\": false,\n                    \"unhoused\": false\n                },\n                {\n                    \"recordId\": \"{{$guid}}\",\n                    \"age\": 85,\n                    \"city\": \"ANAHEIM\",\n                    \"lgbt\": false,\n                    \"gender\": \"Male\",\n                    \"school\": \"\",\n                    \"location\": \"BROOKHURST / crescent \",\n                    \"ethnicity\": [\n                        \"White\"\n                    ],\n                    \"disability\": [],\n                    \"k12_school\": false,\n                    \"timeOfStop\": \"09:05\",\n                    \"personNumber\": 1,\n                    \"resultOfStop\": [\n                        \"Warning (verbal or written)\"\n                    ],\n                    \"ecSubdivision\": \"\",\n                    \"basisForSearch\": [],\n                    \"datetimeOfStop\": \"2022-11-07T17:05:00Z\",\n                    \"durationOfStop\": 5,\n                    \"limitedEnglish\": false,\n                    \"reasonsForStop\": \"Traffic Violation\",\n                    \"warningOffCode\": [\n                        \"21461(A) - DRIVER, FAIL \\\"OBEY\\\" SIGN/ETC\",\n                        \"test\",\n                        \"234556(A) - DRIVER, FAIL \\\"OBEY\\\" 2/ETC\"\n                    ],\n                    \"citationOffCode\": [],\n                    \"stopForAStudent\": false,\n                    \"trafficViolation\": \"Moving\",\n                    \"disciplineUnderEc\": \"\",\n                    \"ethnicityExclusive\": \"White\",\n                    \"genderNonconforming\": false,\n                    \"reasonableSuspicion\": [],\n                    \"contrabandOrEvidence\": [],\n                    \"suspicionOffenseCode\": \"\",\n                    \"typeOfPropertySeized\": [],\n                    \"responseToServiceCall\": false,\n                    \"actionsTakenDuringStop\": [],\n                    \"custodialArrestOffCode\": [],\n                    \"reasonForStopNarrative\": \"Regulatory Sign \",\n                    \"basisForPropertySeizure\": [],\n                    \"basisForSearchNarrative\": \"\",\n                    \"officerTypeOfAssignment\": \"Patrol, traffic, field operations\",\n                    \"officerYearsOfExperience\": 1,\n                    \"inFieldCiteAndReleaseCode\": [\n                        \"test\",\n                        \"test,test,test\",\n                        \"12345678 \\\" , \\\"\"\n                    ],\n                    \"officerOtherAssignmentType\": \"\",\n                    \"trafficViolationOffenseCode\": \"21461(A) - DRIVER FAIL OBEY SIGN/ETC\",\n                    \"raceOfOfficer\": [\n                        \"Asian\",\n                        \"Black/African American\",\n                        \"Hispanic/Latine(x)\",\n                        \"White\"\n                    ],\n                    \"sexualOrientation\": \"Straight/Heterosexual\",\n                    \"typeOfStop\": \"Vehicular\",\n                    \"officerWorksWithNonPrimaryAgency\": false,\n                    \"reasonGivenStoppedPerson\": [\n                        \"Matched suspect description\",\n                        \"Matched description of suspect’s vehicle or vehicle observed at the scene of a crime\",\n                        \"Witness or victim identified stopped person as a suspect of a crime\"\n                    ],\n                    \"stopDuringWellnessCheck\": false,\n                    \"typeOfAssignmentOfficer\": \"Off Duty - Working Private Event\",\n                    \"stoppedPassenger\": false,\n                    \"stoppedInsideResidence\": false,\n                    \"nonConforming\": false,\n                    \"unhoused\": false,\n                    \"genderOfOfficer\": \"Cisgender man/boy\",\n                    \"nonBinaryOfficer\": false\n                }\n            ]\n        }\n    ]\n}\n// Validation schema:  \n// joi.object().keys({\n//       stops: joi.array().items(\n//         joi.object().keys({\n//           data: joi.array().items(\n//             joi.object({\n//               recordId: joi.string().required(),\n//               basisForPropertySeizure: joi.array().items(joi.string()).min(0).required(),\n//               officerYearsOfExperience: joi.number().required(),\n//               basisForSearchNarrative: joi.string().allow('').required(),\n//               ethnicity: joi.array().items(joi.string()).required(),\n//               city: joi.string().required(),\n//               disability: joi.array().items(joi.string()).required(),\n//               timeOfStop: joi.string().required(),\n//               reasonsForStop: joi.string().required(),\n//               personNumber: joi.number().required(),\n//               k12_school: joi.boolean().required(),\n//               basisForSearch: joi.array().items(joi.string()).min(0),\n//               custodialArrestOffCode: joi.array().items(joi.string()).min(0).required(),\n//               citationOffCode: joi.array().items(joi.string()).min(0),\n//               school: joi.string().allow('').required(),\n//               warningOffCode: joi.array().items(joi.string().min(0)),\n//               responseToServiceCall: joi.boolean().required(),\n//               officerTypeOfAssignment: joi.string().required(),\n//               inFieldCiteAndReleaseCode: joi.array().items(joi.string()),\n//               trafficViolationOffenseCode: joi.string().allow('').required(),\n//               trafficViolation: joi.string().allow('').required(),\n//               ecSubdivision: joi.string().allow('').required(),\n//               durationOfStop: joi.number().required(),\n//               reasonableSuspicion: joi.array().items(joi.string()).min(0).required(),\n//               suspicionOffenseCode: joi.string().allow('').required(),\n//               stopForAStudent: joi.boolean().required(),\n//               typeOfPropertySeized: joi.array().items(joi.string()).min(0).required(),\n//               location: joi.string().required(),\n//               genderNonconforming: joi.boolean().required(),\n//               reasonForStopNarrative: joi.string().required(),\n//               age: joi.number().required(),\n//               ethnicityExclusive: joi.string().required(),\n//               lgbt: joi.boolean().required(),\n//               raceOfOfficer: joi.array().items(joi.string()).min(0).required(),\n//               genderOfOfficer: joi.string().allow('').required(),\n//               nonBinaryOfficer: joi.boolean(),\n//               gender: joi.string().allow('').required(),\n//               nonConforming: joi.boolean().required(),\n//               sexualOrientation: joi.string().required(),\n//               typeOfStop: joi.string().required(),\n//               unhoused:  joi.boolean().required(),\n//               officerWorksWithNonPrimaryAgency:  joi.boolean().required(),\n//               reasonGivenStoppedPerson:  joi.array().items(joi.string()).min(0).required(),\n//               stopDuringWellnessCheck:  joi.boolean().required(),\n//               typeOfAssignmentOfficer: joi.string().required(),\n//               stoppedPassenger:  joi.boolean().required(),\n//               stoppedInsideResidence:  joi.boolean().required(),\n//               limitedEnglish: joi.boolean().required(),\n//               disciplineUnderEc: joi.string().allow('').required(),\n//               contrabandOrEvidence: joi.array().items(joi.string()).min(0).required(),\n//               officerOtherAssignmentType: joi.string().allow('').required(),\n//               resultOfStop: joi.array().items(joi.string()).min(0).required(),\n//               actionsTakenDuringStop: joi.array().items(joi.string()).min(0).required(),\n//               datetimeOfStop: joi.string().required(),\n//             })\n//           ).min(1).required(),\n//           customQuestionData: joi.array().items(\n//             joi.object({\n//               recordId: joi.string().required(),\n//               personNumber: joi.number().required(),\n//               isStopQuestion: joi.boolean().required(),\n//               answer: joi.alternatives().try(joi.array().items(joi.string()).min(0), joi.string().allow('')),\n//               questionKey: joi.string().required(),\n//             })\n//           ).min(0)\n//         })\n//       ).min(1).required()\n//     }),  ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/pushData", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "pushData"]}}, "response": []}, {"name": "Get templates", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer de10f9fd-2a2d-4b6c-8ad4-02d96d88a8e1", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/template", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "template"]}}, "response": []}, {"name": "Update template", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer de10f9fd-2a2d-4b6c-8ad4-02d96d88a8e1", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{      \n    \"name\": \"Template Name\",\n    \"description\": \"This is a template description.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/template", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "template"]}}, "response": []}, {"name": "Upload template", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "bearer da7e763a-167d-465f-b6ff-fdd3f3cfeae4", "type": "text"}, {"key": "x-description", "value": "This is an awesome pbix", "type": "text"}, {"key": "x-name", "value": "Contact demo", "type": "text"}], "body": {"mode": "file", "file": {"src": "/Users/<USER>/Documents/TemplateReportDesktop - Phillip.pbix"}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/template/TemplateReportDesktopPhillip.pbix", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "template", "TemplateReportDesktopPhillip.pbix"]}}, "response": []}, {"name": "Associate Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer da7e763a-167d-465f-b6ff-fdd3f3cfeae4", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{      \n    \"orgId\": 29,\n    \"templatePbixFileName\": \"ContactAnalyticsDemo.pbix\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/associateTemplate", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "associateTemplate"]}}, "response": []}, {"name": "Bulk Associate Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer da7e763a-167d-465f-b6ff-fdd3f3cfeae4", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{      \n    \"orgIds\": [32, 141, 142, 143, 76],\n    \"templatePbixFileName\": \"Template-01-21-2025-v35.pbix\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/bulkAssociateTemplate", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "bulkAssociateTemplate"]}}, "response": []}, {"name": "Push Custom Question Definitions", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer 8c3921aa-a91b-4883-babc-1e007bde55f7", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"data\": [\n        {\n            \"contactId\": \"0UelaAsJdI\",\n            \"component\": \"DynamicSelectForm\",\n            \"title\": \"Select an option\",\n            \"options\": [\n                \"Opt\\\"ion\\\" 1\",\n                \"Option 2\"\n            ],\n            \"isMultiSelect\": true,\n            \"required\": true,\n            \"disabled\": false,\n            \"disabledDate\": \"\",\n            \"resultPath\": \"somethingRandom<k9ds22bjK>\"\n        },\n        {\n            \"contactId\": \"aAsJdI0Uel\",\n            \"component\": \"DynamicTextForm\",\n            \"title\": \"Select an option\",\n            \"isMultiSelect\": \"false\",\n            \"required\": true,\n            \"disabled\": false,\n            \"disabledDate\": \"\",\n            \"resultPath\": \"enterSomeText<123567890>\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/pushCustomQuestionDefinitions", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "pushCustomQuestionDefinitions"]}}, "response": []}]}