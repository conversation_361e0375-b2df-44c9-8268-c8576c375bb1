import React from 'react';
import TextField from '@mui/material/TextField';
import TimelapseIcon from '@mui/icons-material/Schedule';
import ClearIcon from '@mui/icons-material/Clear';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
const min = 0;
const max = 60;

function formatValue(value: number) {
  return value < 10 ? `0${value}` : `${value}`;
}

function MUITextField({
  name,
  value,
  handleInputChange,
  width,
  showClearTimeIcon,
  clearDuration,
  type,
  totalTime,
}: Props) {
  const [tempValue, setTempValue] = React.useState<number>(0);
  React.useEffect(() => {
    setTempValue(value);
  }, [value]);
  function onChange(event: React.ChangeEvent<HTMLInputElement>) {
    let tempValue = parseInt(event.target.value, 10);
    const name = event.target.name;

    if (tempValue > max) {
      tempValue = max;
    }
    if (tempValue < min) {
      tempValue = min;
    }
    setTempValue(tempValue);
    handleInputChange(tempValue, name);
  }
  function handleClear(event: React.MouseEvent) {
    const type = event.currentTarget.getAttribute('data-type');
    setTempValue(tempValue);
    if (type) {
      if (clearDuration) {
        clearDuration(type);
      } else {
        console.log("ClearDur doesn't exist");
      }
    } else {
      console.log("Type doesn't exist");
    }
  }
  return (
    <TextField
      variant="standard"
      type="number"
      style={{ width: width || 35 }}
      name={name}
      value={formatValue(tempValue)}
      onChange={onChange}
      slotProps={{
        input: {
          disableUnderline: true,
          inputProps: {
            style: { textAlign: 'center' },
            min: 0,
            max: 10,
          },
          endAdornment:
            showClearTimeIcon &&
            (totalTime === 0 ? (
              <Tooltip title="HH:MM:SS" placement="top-start">
                <IconButton
                  size="small"
                  data-type={type}
                  style={{ marginBottom: 2 }}
                >
                  <TimelapseIcon fontSize="inherit" />
                </IconButton>
              </Tooltip>
            ) : (
              <IconButton size="small" onClick={handleClear} data-type={type}>
                <ClearIcon fontSize="inherit" />
              </IconButton>
            )),
        },
      }}
    />
  );
}

interface Props {
  name: string;
  value: number;
  handleInputChange: (tempValue: number, name: string) => void;
  width?: number;
  showClearTimeIcon?: boolean;
  clearDuration?: (type: string) => void;
  type?: string;
  totalTime?: number;
}

export default MUITextField;
