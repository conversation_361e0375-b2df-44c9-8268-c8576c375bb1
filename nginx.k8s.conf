# Remove "index.html" from the URI
map $uri $stripped_uri {
    ~*(.*)index.html$ $1;
    default         "";
}

# Determine the real scheme based on $http_x_forwarded_proto
map $http_x_forwarded_proto $real_scheme {
    ""      $scheme;
    default $http_x_forwarded_proto;
}

# Create the full URL using the real scheme
map "$real_scheme://$http_host$stripped_uri" $full_url {
    ~^(.*)(.*)(.*)$ $1$2$3;
    default          "";
}

server {
    listen       9000;
    server_name  localhost;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    server_tokens off;
    # can't run this under we work out how to build nginx
    # more_clear_headers Server;

    # Set the nonce variable using the request ID
    set $${empty_for_envsubst}cspNonce $${empty_for_envsubst}request_id;

    location / {
        location = /index.html {
            add_header Cache-Control "must-revalidate";
            internal;  # This prevents direct access from clients
        }

        # zip files to transmit faster
        gzip_static on;
        gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/vnd.ms-fontobject application/x-font-ttf font/opentype image/svg+xml image/x-icon application/javascript;

        # Where nginx should look for files
        root /usr/share/nginx/html;

        # Which files to try serving in order
        index index.html index.htm;

        # Try looking for a file, or directory, or default to index.html
        try_files $uri $uri/ /index.html;

        # Replacing HTML tags with dynamic values
        sub_filter 'aiware.run' '$api_host_without_protocol';
        sub_filter '<base href="/" />' '<base href="$full_url" />';
        sub_filter '<head>' '<head><script>window["aiwareConfig"]=$CONFIG</script>';

        # Making sure sub_filter only runs once
        sub_filter_once off;
    }

    location /api {
        client_max_body_size 5M;
        proxy_pass http://localhost:3002;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #error_page 404 and 403 to 200 and index.html
    error_page 404 =200 /index.html;
    error_page 403 =200 /index.html;
    location = /index.html {
        # Inject the nonce into the response
        sub_filter_once off;
        sub_filter_types *;
        sub_filter NGINX_CSP_NONCE $${empty_for_envsubst}cspNonce;
        sub_filter AIWARE_DOMAIN_TO_REPLACE $api_host_without_protocol;

        root /usr/share/nginx/html;
        add_header Cache-Control "must-revalidate";
        internal;

        add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options nosniff;
        add_header Referrer-Policy "strict-origin";
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
        add_header Content-Security-Policy "default-src 'self' *.$api_host_without_protocol; script-src 'self' 'unsafe-eval' 'nonce-$${empty_for_envsubst}cspNonce' *.$api_host_without_protocol https://*.pendo.io/ https://*.aiware.com/ https://*.segment.com/ https://*.ingest.sentry.io https://cdn.jsdelivr.net/ https://*.intercom.io/ https://*.intercomcdn.com/ https://*.google-analytics.com/ https://analytics-google.com/ https://*.googleapis.com https://www.googletagmanager.com/ *.force.com/ *.salesforce-scrt.com/ https://cdnjs.cloudflare.com/ data.pendo.io app.pendo.io cdn.pendo.io pendo-static-5865020918857728.storage.googleapis.com pendo-io-static.storage.googleapis.com; connect-src * blob:; img-src * data: blob: https://i.vimeocdn.com; font-src 'self' data: *.$api_host_without_protocol https://fonts.gstatic.com/ https://cdn.jsdelivr.net/ https://stackpath.bootstrapcdn.com/; media-src * blob:; frame-src 'self' *.$api_host_without_protocol https://td.doubleclick.net/ https://veritone.my.site.com/ https://*.veritone.com/ https://app.high.powerbigov.us/ pendo-static-5865020918857728.storage.googleapis.com https://player.vimeo.com portal.pendo.io app.pendo.io; worker-src 'self' blob: *.$api_host_without_protocol; style-src 'self' 'unsafe-inline' *.$api_host_without_protocol https://*.aiware.com/ https://*.googleapis.com https://cdn.jsdelivr.net/ https://stackpath.bootstrapcdn.com/ pendo-io-static.storage.googleapis.com pendo-static-5865020918857728.storage.googleapis.com app.pendo.io cdn.pendo.io;" always;
    }

    location ~* \.(css)$ {
        root /usr/share/nginx/html;
    }

    location ~* \.(mjs|js)$ {
        root /usr/share/nginx/html;
        types {
            text/javascript js mjs;
        }
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}