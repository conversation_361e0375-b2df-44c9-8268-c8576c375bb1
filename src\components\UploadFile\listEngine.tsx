import React, { Fragment } from 'react';
import makeStyles from '@mui/styles/makeStyles';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Typography from '@mui/material/Typography';

import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import Popover from '@mui/material/Popover';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import styles from './styles';
import { DagTemplate } from '../../state/modules/uploadFile/models';
import isEmpty from 'lodash/isEmpty';

const useStyles = makeStyles(styles);
function ListEngine({
  title,
  des,
  icon,
  categoryId,
  dagTemplates,
  handleClickDagTemplate,
  dagTemplateByCategorySelected,
  handleRunJobTemplate,
  unavailablEngineCategory,
  showProcessByCategory,
  percentageFilesUploaded,
}: Props) {
  const [anchorElDagTemplates, setAnchorElDagTemplates] = React.useState<
    (EventTarget & HTMLElement) | null
  >(null);
  const classes = useStyles();

  function handleClickDagtemplates(event: React.UIEvent<HTMLElement>) {
    event.stopPropagation();
    setAnchorElDagTemplates(event.currentTarget);
  }
  function handleCloseDagTemplates() {
    setAnchorElDagTemplates(null);
  }
  function onClickDagTemplate(event: React.MouseEvent<HTMLElement>) {
    event.stopPropagation();
    const id = event.currentTarget.getAttribute('data-id');
    handleClickDagTemplate(id, categoryId);
    setAnchorElDagTemplates(null);
  }
  const openDagTemplates = Boolean(anchorElDagTemplates);
  return (
    <Card data-testid="card-list">
      <CardContent className={classes.cardContent} data-testid="card-content">
        <div className={classes.icon} data-testid="engine-icon">
          <i className={icon} />
        </div>
        <Typography component="p" className={classes.cardTitle}>
          {title}
        </Typography>
        <Typography
          className={classes.cardDes}
          color="textSecondary"
          gutterBottom
        >
          {des}
        </Typography>

        {Array.isArray(dagTemplates) && dagTemplates.length > 0 && (
          <Fragment>
            <Button
              endIcon={<ArrowDropDownIcon />}
              onClick={(event) => handleClickDagtemplates(event)}
              data-testid="dag-templates-button"
            >
              {!isEmpty(dagTemplateByCategorySelected) &&
                dagTemplateByCategorySelected?.description}
            </Button>
            <Popover
              open={openDagTemplates}
              anchorEl={anchorElDagTemplates}
              onClose={handleCloseDagTemplates}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'center',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'center',
              }}
              data-testid="popover"
            >
              <div style={{ width: '100%' }}>
                {dagTemplates.map((dagTemplate) => (
                  <MenuItem
                    key={dagTemplate.id}
                    value={dagTemplate.id}
                    onClick={(event) => onClickDagTemplate(event)}
                    data-id={dagTemplate.id}
                    data-testid="dag-template-menu-item"
                  >
                    {dagTemplate.description}
                  </MenuItem>
                ))}
              </div>
            </Popover>
          </Fragment>
        )}
      </CardContent>
      <CardActions className={classes.cardActions}>
        <Button
          variant="contained"
          color="primary"
          data-test="next-step"
          onClick={handleRunJobTemplate}
          data-id={categoryId}
          disabled={unavailablEngineCategory || !isEmpty(showProcessByCategory)}
          data-testid="run-job-template"
        >
          Run
        </Button>
        {showProcessByCategory && showProcessByCategory[categoryId] && (
          <Box position="relative" display="inline-flex">
            <CircularProgress
              variant="determinate"
              value={percentageFilesUploaded}
            />
            <Box
              top={0}
              left={0}
              bottom={0}
              right={0}
              position="absolute"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <Typography
                variant="caption"
                component="div"
                color="textSecondary"
              >{`${Math.round(percentageFilesUploaded)}%`}</Typography>
            </Box>
          </Box>
        )}
      </CardActions>
    </Card>
  );
}
interface Props {
  title: string;
  des: string;
  icon: string;
  categoryId: string;
  dagTemplates: DagTemplate[];
  handleClickDagTemplate: (id: string | null, categoryId: string) => void;
  dagTemplateByCategorySelected: DagTemplate | { description?: string };
  handleRunJobTemplate: (event: React.MouseEvent<HTMLElement>) => void;
  unavailablEngineCategory?: boolean;
  showProcessByCategory: { [key: string]: boolean };
  percentageFilesUploaded: number;
}
export default ListEngine;
