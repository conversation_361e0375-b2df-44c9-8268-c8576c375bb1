import { isString, omitBy, cloneDeep } from 'lodash';
import { G<PERSON><PERSON><PERSON> } from '../../../helpers/gqlApi';
import {
  AsyncFuncActions,
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '../../../helpers/apiHelper';
import getApiRoot from '../../../helpers/getApiRoot';
import getGraphQLEndpoint from '../../../helpers/getGraphQLEndpoint';
import getApiAuthToken from '../../../helpers/getApiAuthToken';
import callGraph<PERSON>Api, {
  G<PERSON>A<PERSON>,
  createG<PERSON>FailureAction,
  createG<PERSON>RequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraphQLApi';
import fetchGraphQLApi from '../../../helpers/fetchGraphQLApi';
import { INTERNAL_FOLDER_NAME } from '../tdosTable';
import {
  Folders,
  FetchFoldersResponse,
  FoldersResponseRecord,
} from '../../../model';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { TDO } from '../universal/TDO';
import { FsaAction } from 'state/sagas';
import { PayloadAction, createAction, createReducer } from '@reduxjs/toolkit';

export const ON_FETCH_FOLDERS = createAction('initial fetch folders');
export const FETCH_FOLDERS = createAsyncFuncRequestAction(
  'request to fetch folders'
);
export const FETCH_FOLDERS_SUCCESS = createAsyncFuncSuccessAction<{
  rootFolder: FetchFoldersResponse;
}>('folders fetch successfully');
export const FETCH_FOLDERS_FAILURE = createAsyncFuncFailureAction(
  'folders fetch failed'
);
export const SET_SELECTED_FOLDER_ID = createAction<
  | {
      selectedFolderId: string;
      type?: 'initialPageLoad' | 'moveFolder';
    }
  | undefined
>('set selected folder id');

export const FETCH_SUB_FOLDERS_REQUEST = createAction<{
  folderId: string;
  expanded?: boolean;
  from?: string;
}>('request to fetch sub folders');
export const FETCH_SUB_FOLDERS =
  createAsyncFuncRequestAction('sub folders fetch');
export const FETCH_SUB_FOLDERS_SUCCESS = createAsyncFuncSuccessAction<{
  folder: FetchFoldersResponse;
}>('sub folders fetch successfully');
export const FETCH_SUB_FOLDERS_FAILURE = createAsyncFuncFailureAction(
  'sub folders fetch failed'
);

export const SHOW_FORM_CREATE_FOLDER = createAction<{
  type: string;
  caseOptions?: string;
}>('show form create folder');
export const HIDE_FORM_CREATE_FOLDER = createAction('hide form create folder');
export const FETCH_CREATE_FOLDER_REQUEST = createAction<string>(
  'request create folder'
);
export const FETCH_CREATE_FOLDER = createGQLRequestAction(
  'fetch create folder'
);
export const FETCH_CREATE_FOLDER_SUCCESS =
  createGQLSuccessAction<FetchCreateFolderResponse>(
    'fetch create folder successfully'
  );
export const FETCH_CREATE_FOLDER_FAILURE = createGQLFailureAction(
  'fetch create folder failed'
);

export const FETCH_UPDATE_FOLDER_REQUEST = createAction<{
  id: string;
  name: string;
}>('request update folder');
export const FETCH_UPDATE_FOLDER = createGQLRequestAction(
  'fetch update folder'
);
export const FETCH_UPDATE_FOLDER_SUCCESS = createGQLSuccessAction<{
  updateFolder: {
    id: string;
    name: string;
  };
}>('fetch update folder successfully');
export const FETCH_UPDATE_FOLDER_FAILURE = createGQLFailureAction(
  'fetch update folder failed'
);

export const SET_SELECTED_MOVE_FOLDER_ID = createAction<{
  selectedMoveFolderId: string | null;
}>('set selected move folder id');
export const FETCH_MOVE_SUB_FOLDERS_REQUEST = createAction<{
  folderId: string;
  expanded: boolean;
}>('request to fetch move sub folders');
export const FETCH_MOVE_SUB_FOLDERS = createAsyncFuncRequestAction(
  'move sub folders fetch'
);
export const FETCH_MOVE_SUB_FOLDERS_SUCCESS = createAsyncFuncSuccessAction<{
  folder: FetchFoldersResponse;
}>('move sub folders fetch successfully');
export const FETCH_MOVE_SUB_FOLDERS_FAILURE = createAsyncFuncFailureAction(
  'move sub folders fetch failed'
);

export const SHOW_MOVE_FOLDER = createAction<{
  type?: string;
  caseOptions: string;
}>('show move folder');
export const HIDE_MOVE_FOLDER = createAction('hide move folder');

export const FETCH_DELETE_FOLDER_REQUEST = createAction(
  'request delete folder'
);
export const FETCH_DELETE_FOLDER = createGQLRequestAction(
  'fetch delete folder'
);
export const FETCH_DELETE_FOLDER_SUCCESS = createGQLSuccessAction(
  'fetch delete folder successfully'
);
export const FETCH_DELETE_FOLDER_FAILURE = createGQLFailureAction(
  'fetch delete folder failed'
);

export const FETCH_MOVE_FOLDER_REQUEST = createAction('request to move folder');
export const FETCH_MOVE_FOLDER = createAsyncFuncRequestAction(
  'request to fetch move folders'
);
export const FETCH_MOVE_FOLDER_SUCCESS = createAsyncFuncSuccessAction<{
  rootFolder: FetchFoldersResponse;
}>('move folders fetch successfully');
export const FETCH_MOVE_FOLDER_FAILURE = createAsyncFuncFailureAction(
  'move folders fetch failed'
);

export const FETCH_UPDATE_MOVE_FILES_REQUEST = createAction(
  'request update to move files'
);
export const FETCH_UPDATE_MOVE_FILES = createAsyncFuncRequestAction(
  'update to fetch move files'
);
export const FETCH_UPDATE_MOVE_FILES_SUCCESS = createAsyncFuncSuccessAction(
  'move update files fetch successfully'
);
export const FETCH_UPDATE_MOVE_FILES_FAILURE = createAsyncFuncFailureAction(
  'move update files fetch failed'
);

export const DELETE_FILES_REQUEST = createAction('request to delete files');
export const DELETE_FILES = createAsyncFuncRequestAction('fetch delete files');
export const DELETE_FILES_SUCCESS = createAsyncFuncSuccessAction(
  'delete files fetch successfully'
);
export const DELETE_FILES_FAILURE = createAsyncFuncFailureAction(
  'delete files fetch failed'
);

export const FETCH_UPDATE_MOVE_FOLDER_REQUEST = createAction(
  'request update move folder'
);
export const FETCH_UPDATE_MOVE_FOLDER = createGQLRequestAction(
  'fetch update move folder'
);
export const FETCH_UPDATE_MOVE_FOLDER_SUCCESS = createGQLSuccessAction<{
  moveFolder: {
    name: string;
    id: string;
  };
}>('fetch update move folder successfully');
export const FETCH_UPDATE_MOVE_FOLDER_FAILURE = createGQLFailureAction(
  'fetch update move folder failed'
);

export const FETCH_CHILD_FOLDER_REQUEST = createAction<{
  folderId: string;
}>('fetch child folder request');
export const FETCH_CHILD_FOLDER =
  createAsyncFuncRequestAction('fetch child folder');
export const FETCH_CHILD_FOLDER_SUCCESS = createAsyncFuncSuccessAction<any>(
  'fetch child folder successfully'
);
export const FETCH_CHILD_FOLDER_FAILURE = createAsyncFuncFailureAction(
  'fetch child folder failed'
);
export const FETCH_OPEN_FOLDER = createAction<string>('fetch open folder');

export const UPDATE_FOLDER_ROOT = createAction<string>('update folder root');
export const HANDLE_EXPANDED = createAction<{
  folderId: string;
  expanded: boolean;
}>('set status expanded');
export const HANDLE_MOVE_EXPANDED = createAction<{
  folderId: string;
  expanded: boolean;
}>('set status move expanded');
export const SHOW_FOLDER = createAction('show folder');
export const HIDE_FOLDER = createAction('hide folder');

export const UPDATE_OPEN_FOLDER_IDS = createAction('update open folder ids');

const defaultState = {
  byId: {} as Folders,
  rootFolderId: '',
  typeShowCreateFolder: '',
  isShowMoveFolder: false,
  moveId: {} as Folders,
  isShowConfirmModal: false,
  fetchingFolders: false,
  fetchingFoldersError: undefined as string | undefined,
  fetchingMoveFolders: false,
  fetchingFolderRoot: false,
  subFolderIds: [],
  isShowFolderModal: false,
  selectedFolderIds: [] as {
    id: string;
    name?: string;
    level: number;
  }[],
  selectedFolderId: '',
  isFetchSubFolder: false,
  openFolderId: '',
  actionFolderId: '',
  selectedMoveFolderId: '',
  selectedMoveFolderIds: [] as { id: string; level: number; name?: string }[],
  openMoveFolderId: '',
  widthFolder: 1,
  openFolderIds: [] as {
    id: string;
    name?: string;
    level: number;
  }[],
  caseOptions: '',
  fromComponent: '',
};

function makeSubfolder(
  data: FoldersResponseRecord[],
  state: any,
  type: string
): Folders {
  const result = data.reduce(
    (
      subfolders,
      { id, name, treeObjectId, childFolders, parent, folderPath, description }
    ) => ({
      ...subfolders,
      [id]: {
        id,
        name,
        treeObjectId,
        expanded: false,
        subfolders:
          type === 'moveFolder'
            ? []
            : state.selectedFolderIds.some(
                  (item: { id: string }) => item.id === id
                )
              ? state.byId[id].subfolders
              : [],
        count: childFolders.count,
        root: false,
        parentId: parent.id,
        level: folderPath.length,
        description,
      },
    }),
    {}
  );
  return result;
}

const makeFolderRoot = (
  data: FetchFoldersResponse,
  fetchingType: string
): Folders => ({
  [data.id]: {
    id: data.id,
    name: data.name,
    treeObjectId: data.treeObjectId,
    expanded: true,
    [fetchingType]: false,
    subfolders: sortNameFolder(data.childFolders.records),
    count: data.childFolders.count,
    root: true,
    parentId: null,
    level: 1,
    description: data.description,
  },
});

const makeFolderParent = (
  action: FsaAction<{ folder: FetchFoldersResponse }>
) => ({
  [action.payload.folder.id]: {
    ...action.payload.folder,
    subfolders: sortNameFolder(action.payload.folder.childFolders.records),
    expanded: action.meta.variables.expanded,
    count: action.payload.folder.childFolders.count,
    root: false,
    parentId: action.payload.folder.parent.id,
    level: action.payload.folder.folderPath.length,
  },
});
const makeExpand = (
  state: any,
  action: PayloadAction<{ folderId: string; expanded?: boolean }>,
  type: string
) => {
  return {
    ...state,
    [type]: {
      ...state[type],
      [action.payload.folderId]: {
        ...state[type][action.payload.folderId],
        expanded: !!action.payload.expanded,
        subfolders: [],
      },
    },
  };
};
const makeSelected = (
  state: any,
  selectedMoveFolderId: string,
  type: string
) => {
  return {
    ...state,
    [type]: selectedMoveFolderId,
  };
};
const makeFetchSubfolder = (
  state: any,
  action: FsaAction<any, { variables: { folderId: string } }>,
  type: string,
  fetchingName: string
) => ({
  ...state,
  [type]: {
    ...state[type],
    [action.meta.variables.folderId]: {
      ...state[type][action.meta.variables.folderId],
      [fetchingName]: true,
      expanded: true,
    },
  },
  openFolderId: action.meta.variables.folderId,
});
const makeFetchFolder = (state: any, type: string, fetchingName: string) => {
  if (state.rootFolderId) {
    return {
      ...state,
      [type]: {
        ...state[type],
        [state.rootFolderId]: {
          ...state[type][state.rootFolderId],
          [fetchingName]: true,
        },
      },
      fetchingFolderRoot: true,
    };
  }
  return {
    ...state,
  };
};
const sortNameFolder = (data: { id: string; name: string }[]) =>
  data
    .filter(
      (subFolder) => subFolder.name && subFolder.name !== INTERNAL_FOLDER_NAME
    )
    .slice()
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(({ id }) => id);

const makeMoveFolder = (data: Folders, selectedFolderIds: { id: string }[]) => {
  selectedFolderIds.forEach((item) => {
    delete data[item.id];
  });
  return data;
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_FOLDERS, (state) => {
      return makeFetchFolder(state, 'byId', 'fetchingSubFolders');
    })
    .addCase(FETCH_FOLDERS_SUCCESS, (state, action) => {
      const rootFolder = get(action, 'payload.rootFolder');
      if (!rootFolder) {
        return state;
      }
      const openFolderIds = [];
      const level = rootFolder.folderPath.length;

      openFolderIds.push({
        id: rootFolder.id,
        name: rootFolder.name,
        level,
      });

      // moveFolder
      let selectedFolderIds = get(state, 'selectedFolderIds');
      const typeFetchSubFolder = get(action, 'meta.variables.type');
      const actionFolderId = get(state, 'actionFolderId');
      const selectedFolderId = get(state, 'selectedFolderId');
      const selectedFolderIdsFilter = selectedFolderIds.filter((item) =>
        [selectedFolderId, actionFolderId].includes(item.id)
      );
      // moveFolder where actionFolderId === selectedFolderId
      if (
        actionFolderId &&
        typeFetchSubFolder === 'moveFolder' &&
        actionFolderId === selectedFolderId
      ) {
        selectedFolderIds = [
          {
            id: rootFolder.id,
            name: rootFolder.name,
            level,
          },
          {
            id: actionFolderId,
            name: state.byId[actionFolderId]!.name,
            level: 2,
          },
        ];
      }
      // move folder where folder move is the parent folder of the selected folder
      if (
        selectedFolderIdsFilter.length === 2 &&
        typeFetchSubFolder === 'moveFolder'
      ) {
        const folderByActionFolderId = selectedFolderIds.find(
          (item) => item.id === actionFolderId
        )!; // safe due to FETCH_FOLDERS_SUCCESS,rootFolder always has value
        // openFolderIds is assigned to rootFolder
        // selectedFolderIds is assigned to openFolderIds
        // selectedFolderId is assigned to rootFolderId
        // and actionFolderId is assigned to selectedFolderId so folderByActionFolderId cannot be undefined

        const selectedFolderIdsByLevel = selectedFolderIds.filter(
          (item) => item.level >= folderByActionFolderId.level
        );
        let selectedFolderIdsUpdate: typeof selectedFolderIdsByLevel = [
          {
            id: rootFolder.id,
            name: rootFolder.name,
            level: 1,
          },
        ];
        selectedFolderIdsUpdate.push(...selectedFolderIdsByLevel);
        selectedFolderIdsUpdate = selectedFolderIdsUpdate.map((item, key) => {
          return {
            id: item.id,
            name: item.name,
            level: key + 1,
          };
        });
        selectedFolderIds = selectedFolderIdsUpdate;
      }
      return {
        ...state,
        rootFolderId: rootFolder.id,
        byId: {
          ...state.byId,
          ...makeFolderRoot(rootFolder, 'fetchingSubFolders'),
          ...omitBy(
            makeSubfolder(rootFolder.childFolders.records, state, 'folder'),
            { name: INTERNAL_FOLDER_NAME }
          ),
        },
        openFolderIds,
        openFolderId: rootFolder.id,
        fetchingFolderRoot: false,
        widthFolder:
          rootFolder.childFolders.records.length > 0
            ? openFolderIds.length + 1
            : state.widthFolder,
        selectedFolderIds,
        isFetchSubFolder: false,
      };
    })
    .addCase(FETCH_FOLDERS_FAILURE, (state, action) => {
      return {
        ...state,
        fetchingFolders: false,
        fetchingFoldersError: get(
          action,
          'payload[0].message',
          'Error fetching folders'
        ),
      };
    })
    .addCase(SET_SELECTED_FOLDER_ID, (state, action) => {
      const selectedFolderId = get(
        action,
        'payload.selectedFolderId',
        state.openFolderId
      );
      const type = get(action, 'payload.type');
      if (type !== 'moveFolder') {
        return {
          ...state,
          selectedFolderId,
          actionFolderId: selectedFolderId,
          selectedFolderIds: state.openFolderIds,
        };
      }
      return {
        ...state,
      };
    })
    .addCase(FETCH_SUB_FOLDERS, (state, action) => {
      return makeFetchSubfolder(state, action, 'byId', 'fetchingSubFolders');
    })
    .addCase(FETCH_SUB_FOLDERS_SUCCESS, (state, action) => {
      const openFolderIds = get(state, 'openFolderIds');
      const typeFetchSubFolder = get(action, 'meta.variables.type');
      const level = get(action, 'payload.folder.folderPath').length;
      let openFolderIdsFilterByLevel = openFolderIds.filter(
        (item) => item.level < level
      );

      openFolderIdsFilterByLevel.push({
        id: action.payload.folder.id,
        name: action.payload.folder.name,
        level,
      });

      let selectedFolderIds = get(state, 'selectedFolderIds');
      const actionFolderId = get(state, 'actionFolderId');
      const selectedFolderId = get(state, 'selectedFolderId');
      let openFolderId = get(state, 'openFolderId');
      let widthFolder;
      const selectedFolderIdsFilter = selectedFolderIds.filter((item) =>
        [selectedFolderId, actionFolderId].includes(item.id)
      );
      // move folder where folder selected === folder action
      if (
        actionFolderId &&
        typeFetchSubFolder === 'moveFolder' &&
        actionFolderId === selectedFolderId
      ) {
        if (
          !openFolderIdsFilterByLevel.some(
            (item: { id: string }) => item.id === actionFolderId
          )
        ) {
          openFolderIdsFilterByLevel.push({
            id: actionFolderId,
            name: state.byId[actionFolderId]?.name,
            level: openFolderIdsFilterByLevel.length + 1,
          });
        }
        selectedFolderIds = openFolderIdsFilterByLevel;
        openFolderId = selectedFolderId;

        widthFolder =
          (state.byId?.[actionFolderId]?.subfolders?.length ?? 0) > 0
            ? openFolderIdsFilterByLevel.length + 1
            : openFolderIdsFilterByLevel.length;
      } // move folder where folder move is the parent folder of the selected folder
      else if (
        selectedFolderIdsFilter.length === 2 &&
        typeFetchSubFolder === 'moveFolder'
      ) {
        const folderByActionFolderId = selectedFolderIds.find(
          (item) => item.id === actionFolderId
        );

        // safe due to FETCH_FOLDERS_SUCCESS, rootFolder always has value
        // openFolderIds is assigned to rootFolder
        // selectedFolderIds is assigned to openFolderIds
        // selectedFolderId is assigned to rootFolderId
        // and actionFolderId is assigned to selectedFolderId so folderByActionFolderId cannot be undefined
        const selectedFolderIdsByLevel = selectedFolderIds.filter(
          (item) => item.level >= folderByActionFolderId!.level
        );

        openFolderIdsFilterByLevel.push(...selectedFolderIdsByLevel);

        openFolderIdsFilterByLevel = openFolderIdsFilterByLevel.map(
          (item, key) => {
            return {
              id: item.id,
              name: item.name,
              level: key + 1,
            };
          }
        );
        selectedFolderIds = openFolderIdsFilterByLevel;
        openFolderId = selectedFolderId;
        widthFolder = openFolderIdsFilterByLevel.length;
      } else {
        widthFolder =
          action.payload.folder.childFolders.records.length > 0
            ? openFolderIdsFilterByLevel.length + 1
            : openFolderIdsFilterByLevel.length;
      }
      let byId = cloneDeep(state.byId || {});
      byId = {
        ...byId,
        ...makeFolderParent(action),
        ...makeSubfolder(
          action.payload.folder.childFolders.records,
          state,
          'folder'
        ),
      };

      for (const folder of Object.values(byId)) {
        if (folder.fetchingSubFolders) {
          folder.fetchingSubFolders = false;
        }
      }
      return {
        ...state,
        byId,
        openFolderIds: openFolderIdsFilterByLevel,
        isFetchSubFolder: false,
        widthFolder,
        selectedFolderIds,
        openFolderId,
      };
    })

    .addCase(FETCH_SUB_FOLDERS_FAILURE, (state) => {
      return {
        ...state,
        fetchingSubFolders: false,
        fetchingSubFordersFailed: true,
      };
    })
    .addCase(HANDLE_EXPANDED, (state, action) => {
      return makeExpand(state, action, 'byId');
    })

    .addCase(SHOW_FORM_CREATE_FOLDER, (state, action) => {
      const { type = '', caseOptions = '' } = action?.payload || {};
      return {
        ...state,
        typeShowCreateFolder: type,
        caseOptions: caseOptions,
      };
    })
    .addCase(HIDE_FORM_CREATE_FOLDER, (state) => {
      return {
        ...state,
        typeShowCreateFolder: '',
      };
    })

    .addCase(FETCH_UPDATE_FOLDER_SUCCESS, (state, action) => {
      const selectedFolderIds = state.selectedFolderIds;
      for (const selectedFolderId of selectedFolderIds) {
        if (selectedFolderId.id === action.payload.updateFolder.id) {
          selectedFolderId.name = action.payload.updateFolder.name;
        }
      }
      // safe due to action.payload.updateFolder.id === selectedFolderId || actionFolderId
      // selectedFolderId is assigned to rootFolderId
      // byId = makeFolderRoot with action.payload.updateFolder.id is the Key
      // so state.byId[action.payload.updateFolder.id] cannot be undefined
      state.byId[action.payload.updateFolder.id]!.name =
        action.payload.updateFolder.name;
    })

    .addCase(SHOW_MOVE_FOLDER, (state, action) => {
      const { caseOptions } = action.payload;
      let actionFolderId: any = get(state, 'actionFolderId');
      const selectedFolderId: any = get(state, 'selectedFolderId');
      if (caseOptions === 'tab') {
        actionFolderId = selectedFolderId;
      }
      return {
        ...state,
        isShowMoveFolder: true,
        caseOptions,
        actionFolderId,
      };
    })

    .addCase(HIDE_MOVE_FOLDER, (state) => {
      return {
        ...state,
        isShowMoveFolder: false,
      };
    })

    .addCase(SET_SELECTED_MOVE_FOLDER_ID, (state, action) => {
      const selectedMoveFolderId = get(
        action,
        'payload.selectedMoveFolderId',
        state.rootFolderId
      );
      return makeSelected(state, selectedMoveFolderId, 'selectedMoveFolderId');
    })

    .addCase(FETCH_MOVE_FOLDER, (state) => {
      return makeFetchFolder(state, 'moveId', 'fetchingMoveSubFolders');
    })
    .addCase(FETCH_MOVE_FOLDER_SUCCESS, (state, action) => {
      const rootFolder = get(action, 'payload.rootFolder');
      if (!rootFolder) {
        return state;
      }
      const selectedMoveFolderIds = [];
      const level = rootFolder.folderPath.length;
      selectedMoveFolderIds.push({ id: rootFolder.id, level: level });
      return {
        ...state,
        rootFolderId: rootFolder.id,
        fetchingMoveFolders: false,
        moveId: {
          ...state.moveId,
          ...makeFolderRoot(rootFolder, 'fetchingMoveSubFolders'),
          ...omitBy(
            makeSubfolder(rootFolder.childFolders.records, state, 'moveFolder'),
            { name: INTERNAL_FOLDER_NAME }
          ),
        },
        selectedMoveFolderIds,
        fetchingFolderRoot: false,
        selectedMoveFolderId: rootFolder.id,
      };
    })
    .addCase(FETCH_MOVE_FOLDER_FAILURE, (state, action) => {
      return {
        ...state,
        fetchingMoveFolders: false,
        fetchingMoveFoldersError: get(
          action,
          'payload[0].message',
          'Error fetching folders'
        ),
      };
    })

    .addCase(FETCH_MOVE_SUB_FOLDERS, (state, action) => {
      return makeFetchSubfolder(
        state,
        action,
        'moveId',
        'fetchingMoveSubFolders'
      );
    })
    .addCase(FETCH_MOVE_SUB_FOLDERS_SUCCESS, (state, action) => {
      const selectedMoveFolderIds = state.selectedMoveFolderIds;

      const level = action.payload.folder.folderPath.length;
      const selectedMoveFolderIdsFilter = selectedMoveFolderIds.filter(
        (item) => item.level < level
      );

      selectedMoveFolderIdsFilter.push({
        id: action.payload.folder.id,
        name: action.payload.folder.name,
        level: level,
      });

      let moveId: Folders = get(state, 'moveId', {});
      moveId = {
        ...state.moveId,
        ...makeFolderParent(action),
        ...makeSubfolder(
          action.payload.folder.childFolders.records,
          state,
          'moveFolder'
        ),
      };
      for (const folder of Object.values(moveId)) {
        if (folder.fetchingMoveSubFolders) {
          folder.fetchingMoveSubFolders = false;
        }
      }

      return {
        ...state,
        moveId: moveId,
        selectedMoveFolderIds: selectedMoveFolderIdsFilter,
        openMoveFolderId: action.payload.folder.id,
      };
    })

    .addCase(FETCH_MOVE_SUB_FOLDERS_FAILURE, (state) => {
      return {
        ...state,
        fetchingMoveSubFolders: false,
        fetchingMoveSubFordersFailed: true,
      };
    })
    .addCase(HANDLE_MOVE_EXPANDED, (state, action) => {
      return makeExpand(state, action, 'moveId');
    })

    .addCase(FETCH_UPDATE_MOVE_FOLDER_SUCCESS, (state, action) => {
      // safe due to actionFolderId is assigned to selectedFolderId
      // and selectedFolderId is assigned to rootFolderId
      // byId = makeFolderRoot with actionFolderId is the Key
      // so actionFolder cannot be undefined
      const actionFolder = state.byId[state.actionFolderId]!;
      const parentId = actionFolder.parentId!;
      const parentFolder = state.byId[parentId]!;

      return {
        ...state,
        byId: {
          ...state.byId,
          ...makeMoveFolder(cloneDeep(state.moveId), state.selectedFolderIds),
          [parentId]: {
            ...parentFolder,
            subfolders: parentFolder.subfolders.filter(
              (item) => item !== action.payload.moveFolder.id
            ),
            count: parentFolder.count - 1,
            expanded: parentFolder.count - 1 > 0 ? true : false,
          },
        },
        openFolderIds:
          state.selectedMoveFolderId === state.rootFolderId
            ? [{ id: state.rootFolderId, level: 1 }]
            : state.selectedMoveFolderIds,
      };
    })

    .addCase(FETCH_CHILD_FOLDER_SUCCESS, (state, action) => {
      const folders = get(action, 'payload', []);
      return {
        ...state,
        subFolderIds: folders.map(
          (item: { treeObjectId: string }) => item.treeObjectId
        ),
      };
    })

    .addCase(SHOW_FOLDER, (state) => {
      return {
        ...state,
        isShowFolderModal: true,
      };
    })
    .addCase(HIDE_FOLDER, (state) => {
      return {
        ...state,
        isShowFolderModal: false,
      };
    })
    .addCase(FETCH_OPEN_FOLDER, (state, action) => {
      return {
        ...state,
        actionFolderId: action.payload,
      };
    })

    .addCase(UPDATE_OPEN_FOLDER_IDS, (state: any) => {
      let widthFolder = get(state, 'selectedFolderIds', []).length;
      const openFolderId = get(state, 'selectedFolderId', '');
      const folders = get(state, 'byId');
      const folder = getFolderById(folders, openFolderId);
      if (folder && folder.count > 0) {
        widthFolder++;
      }
      return {
        ...state,
        openFolderIds: state.selectedFolderIds,
        widthFolder,
        openFolderId,
      };
    })
    .addCase(FETCH_SUB_FOLDERS_REQUEST, (state, action) => {
      const fromComponent = get(action, 'payload.from', '');
      return {
        ...state,
        fromComponent,
        isFetchSubFolder: true,
      };
    });
});

export const getFolderById = (folders: Folders, id: string) => {
  let folderData;
  for (const item in folders) {
    // Safe due to loop conditions - can we do something cleaner?
    if (folders[item]!.id === id) {
      folderData = folders[item];
    }
  }
  return folderData;
};

export const setSelectedFolderId = (
  folderId: string,
  type?: 'initialPageLoad' | 'moveFolder'
) =>
  SET_SELECTED_FOLDER_ID({
    selectedFolderId: folderId,
    type,
  });

export const setSelectedMoveFolderId = (moveFolderId: string) =>
  SET_SELECTED_MOVE_FOLDER_ID({
    selectedMoveFolderId: moveFolderId,
  });
export const onFetchFolders = () => ON_FETCH_FOLDERS();

export const fetchChildFolders = (folderId: string) =>
  FETCH_CHILD_FOLDER_REQUEST({ folderId });
export const fetchChildFoldersSuccess = (data: any) =>
  FETCH_CHILD_FOLDER_SUCCESS(data);
export const fetchChildFoldersFailed = () => FETCH_CHILD_FOLDER_FAILURE();

export interface FetchCreateFolderResponse {
  createFolder: {
    id: string;
    name: string;
    treeObjectId: string;
  };
}
export const fetchCreateFolder =
  (
    name: string,
    parentFolderId: string,
    actionTypes?: GQLActions<FetchCreateFolderResponse>
  ) =>
  async (dispatch: any, getState: any) => {
    const query = `
  mutation {
    createFolder(input: {
      name: "${name}",
      description: "",
      parentId: "${parentFolderId}",
      orderIndex: 0,
      rootFolderType: cms
    }) {
      id,
      name,
      treeObjectId
    }
  }
`;
    return await callGraphQLApi<FetchCreateFolderResponse>({
      actionTypes: actionTypes
        ? actionTypes
        : [
            FETCH_CREATE_FOLDER,
            FETCH_CREATE_FOLDER_SUCCESS,
            FETCH_CREATE_FOLDER_FAILURE,
          ],
      query,
      variables: { parentFolderId },
      bailout: undefined,
      dispatch,
      getState,
    });
  };

export const fetchUpdateFolder =
  (id: string, name: string) => async (dispatch: any, getState: any) => {
    const query = `
  mutation {
    updateFolder(input: {id: "${id}", name: "${name}"}) {
      id,
      name
    }
  }
  `;
    return await callGraphQLApi({
      actionTypes: [
        FETCH_UPDATE_FOLDER,
        FETCH_UPDATE_FOLDER_SUCCESS,
        FETCH_UPDATE_FOLDER_FAILURE,
      ],
      query,
      variables: { name },
      bailout: undefined,
      dispatch,
      getState,
    });
  };

export const fetchDeleteFolder =
  (treeObjectId: string, folderId: string) =>
  async (dispatch: any, getState: any) => {
    const query = `
  mutation {
    deleteFolder(input: {id: "${treeObjectId}", orderIndex: 0}) {
      id
    }
  }
  `;
    return await callGraphQLApi({
      actionTypes: [
        FETCH_DELETE_FOLDER,
        FETCH_DELETE_FOLDER_SUCCESS,
        FETCH_DELETE_FOLDER_FAILURE,
      ],
      query,
      variables: { folderId },
      bailout: undefined,
      dispatch,
      getState,
    });
  };

export const fetchMoveFolder =
  (
    treeObjectId: string,
    treeObjectNewParentId: string,
    treeObjectPrevParent: string
  ) =>
  async (dispatch: any, getState: any) => {
    const query = `
  mutation {
    moveFolder(input: {
      rootFolderType: cms,
      treeObjectId: "${treeObjectId}",
      newParentTreeObjectId: "${treeObjectNewParentId}",
      prevParentTreeObjectId: "${treeObjectPrevParent}",
      newOrderIndex: 0,
      prevOrderIndex: 0
    }) {
      id
      orderIndex,
      modifiedDateTime,
      parent {
        treeObjectId
      },
      name
    }
  }
  `;
    return await callGraphQLApi({
      actionTypes: [
        FETCH_UPDATE_MOVE_FOLDER,
        FETCH_UPDATE_MOVE_FOLDER_SUCCESS,
        FETCH_UPDATE_MOVE_FOLDER_FAILURE,
      ],
      query,
      variables: {},
      bailout: undefined,
      dispatch,
      getState,
    });
  };

export const createMoveFilesRequest =
  (selectedTdos: TDO[], treeObjectNewId: string) =>
  (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await moveFilesRequest(selectedTdos, treeObjectNewId, gql);
      return { data };
    };
    return callAsyncFunc({
      actionTypes: [
        FETCH_UPDATE_MOVE_FILES,
        FETCH_UPDATE_MOVE_FILES_SUCCESS,
        FETCH_UPDATE_MOVE_FILES_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function moveFilesRequest(
  selectedTdos: TDO[],
  treeObjectNewId: string,
  gql: GQLApi
) {
  const tdos = selectedTdos.map((tdo) => {
    return {
      id: tdo?.id,
      treeObjectId: tdo?.folders[0]?.treeObjectId ?? '',
    };
  });
  const resp = await gql.moveTDOs(tdos, treeObjectNewId);

  if (resp.errors?.length) {
    resp.errors.forEach((error) => {
      console.error(error.message);
    });
  }

  return resp.data;
}

export const createDeleteFilesRequest =
  (selectedTdoIds: string[]) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await moveDeleteRequest(selectedTdoIds, gql);
      return { data };
    };
    return callAsyncFunc({
      actionTypes: [DELETE_FILES, DELETE_FILES_SUCCESS, DELETE_FILES_FAILURE],
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function moveDeleteRequest(selectedTdoIds: string[], gql: GQLApi) {
  const resp = await gql.deleteTDOs(selectedTdoIds);

  if (resp.errors && resp.errors.length) {
    resp.errors.forEach((error) => {
      console.error(error.message);
    });
  }

  return resp.data;
}

export const fetchFolders =
  <T = any>(query: any, variables: any, actionTypes: GQLActions<T>) =>
  async (dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = getApiRoot(state) + '/' + getGraphQLEndpoint(state);
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;

    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    let response;
    try {
      if (isString(actionTypes[0])) {
        dispatch({
          type: actionTypes[0],
          meta: { variables },
        });
      } else {
        dispatch(actionTypes[0]({ variables }));
      }

      response = await fetchGraphQLApi<T>({
        endpoint,
        query,
        variables,
        token: token!,
        // The ! is safe because when the app booting at the first time, token is null
        // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
        // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
        // and received the payload from fetchUser response.
        // This non null assertion just allow running into that case without change so much in code
        veritoneAppId,
      });
    } catch (error) {
      if (isString(actionTypes[2])) {
        dispatch({
          type: actionTypes[2],
          payload: error,
        });
      } else {
        dispatch(actionTypes[2](error));
      }
      return;
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify(query) }
      );
    }

    return response.data;
  };

export function filterRootFolder<T extends { ownerId: string }>(
  rootFolders: T[]
) {
  if (isEmpty(rootFolders)) {
    return undefined;
  }
  for (const folder of rootFolders) {
    if (!folder.ownerId) {
      return folder;
    }
  }
  return rootFolders[0];
}

export const createFetchChildFoldersRequest =
  (folderId: string) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await getChildFolders(folderId, gql);
      return { data };
    };
    return callAsyncFunc({
      actionTypes: [
        FETCH_CHILD_FOLDER,
        FETCH_CHILD_FOLDER_SUCCESS,
        FETCH_CHILD_FOLDER_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function getChildFolders(folderId: string, gql: GQLApi) {
  let count = 0;
  let offset = 0;
  const limit = 1000;
  const results: { treeObjectId: string; name: string }[] = [];
  do {
    const data = await gql.getChildFolders({ folderId, limit, offset });
    offset += limit;
    const records = data?.folder?.childFolders?.records ?? [];
    count = records.length;
    if (count > 0) {
      results.push(...records);
    }
  } while (count === limit);
  return results;
}

export const createFetchFolderFromRootRequest =
  (actionTypes: AsyncFuncActions<any>, variablesFolder: object) =>
  (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const data = await fetchFolderFromRoot(gql, variablesFolder);
      return { data };
    };
    return callAsyncFunc({
      actionTypes,
      asyncfn,
      dispatch,
      getState,
    });
  };

export async function fetchFolderFromRoot(
  gql: GQLApi,
  variablesFolder: object
  // TODO: The Folder interface is an mixed of
  // with UI properties and data from api, and it also has different properties
  // used here. So, Folder can not be used. Instead, create RootFolder
  // interface temporarily.  We should have a interface for data
  // from api and have another interface for UI, which wraps data inside.
  // and consolidate RootFolder and Folder if possible.
): Promise<{ rootFolder: FetchFoldersResponse } | undefined> {
  let count = 0;
  let offset = 0;
  const limit = 1000;
  const results = [] as FetchFoldersResponse[];
  do {
    const response = await gql.fetchFolderFromRoot({
      limit,
      offset,
      variablesFolder,
    });

    const rootFolder = filterRootFolder(response.rootFolders);
    count = rootFolder?.childFolders?.count ?? 0;
    if (rootFolder) {
      results.push(rootFolder);
    }

    offset += limit;
  } while (count === limit);

  const subFolders = results.flatMap(
    (item) => item?.childFolders?.records ?? []
  );
  const rootFolder = results[0];
  if (!rootFolder) {
    return undefined;
  }
  const copyOfRootFolder = { ...rootFolder };
  copyOfRootFolder.childFolders.records = subFolders;
  copyOfRootFolder.childFolders.count = subFolders.length;
  const dataFolder = { rootFolder: copyOfRootFolder };
  return dataFolder;
}

export default reducer;
export const namespace = 'folders';
export const local = (state: any) => state[namespace] as typeof defaultState;
export const getRootFolder = (state: any) => local(state).byId;
export const getFolders = (state: any) => local(state).byId;
export const isFetchingFolders = (state: any) => local(state).fetchingFolders;
export const foldersFailedToFetch = (state: any) =>
  local(state).fetchingFoldersError;
export const getSelectedFolderId = (state: any) =>
  local(state).selectedFolderId;
export const getRootFolderId = (state: any) => local(state).rootFolderId;
export const getShowFormCreateFolder = (state: any) =>
  local(state).typeShowCreateFolder;
export const getShowMoveFolder = (state: any) => local(state).isShowMoveFolder;
export const getMoveFolders = (state: any) => local(state).moveId;
export const getSelectedMoveFolderId = (state: any) =>
  local(state).selectedMoveFolderId;
export const getFetchingMoveFolders = (state: any) =>
  local(state).fetchingMoveFolders;
export const getSubFolderIds = (state: any) => local(state).subFolderIds;
export const getShowFolder = (state: any) => local(state).isShowFolderModal;
export const getOpenFolderIds = (state: any) => local(state).openFolderIds;
export const getisFetchSubFolder = (state: any) =>
  local(state).isFetchSubFolder;
export const getOpenFolderId = (state: any) => local(state).openFolderId;
export const getActionFolderId = (state: any) => local(state).actionFolderId;
export const getFetchingFolderRoot = (state: any) =>
  local(state).fetchingFolderRoot;
export const getWidthFolder = (state: any) => local(state).widthFolder;
export const getSelectedFolderIds = (state: any) =>
  local(state).selectedFolderIds;
export const getCaseOptions = (state: any) => local(state).caseOptions;
export const getDispatcherComponent = (state: any) =>
  local(state).fromComponent;
