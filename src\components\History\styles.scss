.status {
  border-radius: 2px;
  text-align: center;
  color: #fff;
  font-family: <PERSON>o, sans-serif;
  font-size: 10px;
  font-weight: bold;
  letter-spacing: 0.25px;
  line-height: 11px;
  text-transform: uppercase;
  padding: 4px 0;
  width: 100px;
  display: block;
}

.status-failed {
  background: #d32f2f;
}

.status-complete {
  background: #15c853;
}

.status-running,
.status-queued {
  background: #2196f3;
}

.icon-close {
  margin-left: auto;
  width: 42px;
  height: 42px;
  padding: 0 12px;
}

.dialog-content {
  overflow-y: hidden;
  max-width: 600px;
}

.content-batch {
  text-align: center;
}

.loadding-data {
  position: absolute;
  top: 40%;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
}

.table-footer {
  color: rgba(0, 0, 0, 0.54);

  p {
    font-size: 0.75rem;
  }

  div > div > div {
    font-size: 0.75rem;
  }
}

.table-row {
  height: 57px;

  td {
    height: 57px;
    padding: 4px;

    p {
      font-size: 0.875rem;
      font-weight: 400;
    }
  }
}

.table {
  thead tr th {
    color: rgba(0, 0, 0, 0.54);
    font-size: 0.75rem;
    font-weight: 500;
  }
}
