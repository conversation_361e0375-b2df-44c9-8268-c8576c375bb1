import React, { ComponentType } from 'react';
import { Theme } from '@mui/material/styles';
import withStyles from '@mui/styles/withStyles';
import { ClassNameMap } from '@mui/styles';
import { ConnectedProps, connect } from 'react-redux';
import {
  getSearchResultTdosAll,
  getQueryToExportAll,
  getAutoSuggestedTags,
  TAGS_AUTOCOMPLETE,
} from 'state/modules/search';
import { APPLY_TAGS_TO_TDO_REQUEST } from 'state/modules/tags';

import {
  isSelectedAll,
  getTdosForExport,
  getTdoIdPreview,
} from 'state/modules/tdosTable';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Slide, { SlideProps } from '@mui/material/Slide';
import TagAutoComplete from './tagAutoComplete';
import * as Sentry from '@sentry/react';
import { arrayHasLength } from '@utils';
import { TDO } from 'state/modules/universal/TDO';
import { MultiValue } from 'react-select';

const Transition = React.forwardRef((props: SlideProps, ref) => (
  <Slide direction="up" {...props} ref={ref} />
));

Transition.displayName = 'Transition';

const style = (_theme: Theme) => ({
  paper: {
    overflow: 'visible !important',
  },
});

class AlertDialogSlide extends React.Component<Props> {
  state = {
    newTags: [] as Tag[],
    menuIsOpen: false,
    changeTags: false,
  };

  onSave = () => {
    const { inPreview, tdoIdSelected } = this.props;
    const { changeTags, newTags } = this.state;
    const tags = newTags.map((tag) => ({
      ...tag,
      value: tag.value,
      label: tag.value,
    }));
    // if users don't change any thing, do nothing and close the dialog
    if (!changeTags) {
      return this.props.handleClose();
    }

    // allow user to add/delete tags when selecting single TDO then click apply
    // tag in Toolbar, or clicking edit tag in File preview mode

    const payload: Payload = { tags };
    if (tdoIdSelected.length === 1 || inPreview) {
      const tdoId = inPreview ? this.props.tdoIdPreview : tdoIdSelected[0];
      payload.tdoId = tdoId;
      const tdoSearch = this.props.tdos.find((item) => item.id === tdoId);
      let detailsTags = tdoSearch?.details?.tags || [];

      // Check if output of get operation is not an array
      if (!Array.isArray(detailsTags)) {
        Sentry.captureMessage(`tag should be an array - ${tdoId}`);
        console.error('tag should be an array.', tdoId, detailsTags);
        detailsTags = [];
      }

      // Filter for batch id tags and add them to the payload
      const batchIdTags = detailsTags.filter(
        ({ key }) => key && key === 'batchId'
      );
      payload.tags.push(...batchIdTags);
    } else {
      // allow user to add tag ONLY when selecting multiple TDOs
      if (this.props.isSelectedAll) {
        payload.searchQuery = this.props.queryStore;
      } else {
        const tdos = [];
        for (const tdoId of tdoIdSelected) {
          for (const tdo of this.props.tdos) {
            if (tdo && tdo.id === tdoId) {
              tdos.push(tdo);
              break;
            }
          }
        }
        payload.tdos = tdos;
      }
    }
    this.props.applyTagsRequest(payload);
    this.props.handleClose();
  };

  onTagsChange = (tags: MultiValue<Tag>) => {
    this.setState({
      newTags: tags,
      changeTags: true,
    });
  };

  handleInputChange = (tag: string) => {
    const { suggestTags } = this.props;
    suggestTags(tag);
    if (tag) {
      this.setState({ menuIsOpen: true });
    } else {
      this.setState({ menuIsOpen: false });
    }
  };

  render() {
    const {
      classes,
      initialTagList,
      handleClose,
      open,
      tdoIdSelected,
      tdos,
      inPreview,
      tdoIdPreview,
    } = this.props;
    let existingTags: Tag[] = [];
    let tdoId = '';
    if (arrayHasLength(tdoIdSelected, 1) || inPreview) {
      tdoId = inPreview ? tdoIdPreview : tdoIdSelected[0]!; // Safe due to if conditions
      const tdoSearch = tdos.find((item) => item.id === tdoId);
      let detailsTags = tdoSearch?.details?.tags || [];

      // Check if output of get operation is not an array
      if (!Array.isArray(detailsTags)) {
        Sentry.captureMessage(`tag should be an array - ${tdoId}`);
        console.error('tag should be an array.', tdoId, detailsTags);
        detailsTags = [];
      }
      existingTags = detailsTags.filter(
        (tag) => tag?.value && !(tag?.key === 'batchId')
      );
    }

    return (
      <div>
        <Dialog
          open={open}
          TransitionComponent={Transition as ComponentType<SlideProps>}
          keepMounted
          onClose={handleClose}
          aria-labelledby="alert-dialog-slide-title"
          aria-describedby="alert-dialog-slide-description"
          classes={{ paper: classes.paper }}
        >
          <DialogTitle id="alert-dialog-slide-title">
            {tdoIdSelected.length > 1 && !inPreview
              ? 'Please choose one or more tags to add to selected items'
              : `Add or Remove Tags (${tdoId})`}
          </DialogTitle>
          <DialogContent className={classes.paper}>
            <TagAutoComplete
              onTagsChange={this.onTagsChange}
              initialTagList={initialTagList}
              menuIsOpen={this.state.menuIsOpen}
              onInputChange={this.handleInputChange}
              tdoIdSelected={tdoIdSelected}
              value={existingTags}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} color="primary">
              Cancel
            </Button>
            <Button
              data-test="tag-dialog-save-button"
              onClick={this.onSave}
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}

export interface Tag {
  value: string;
  key?: string;
}

interface Payload {
  tags: Tag[];
  tdoId?: string;
  searchQuery?: {
    index: string[];
    select: string[];
    query?: Record<string, any>;
    limit: number;
    offset: number;
  } | null;
  tdos?: TDO[];
}
type Props = PropsFromRedux & {
  classes: ClassNameMap;
  open: boolean;
  inPreview?: boolean;
  // queryStore: {
  //   index: number;
  //   limit: number;
  //   offset: number;
  //   query: {};
  //   select: string[];
  // };
  handleClose: () => void;
  suggestTags: (Tag: Tag) => void;
  applyTagsRequest: (Tags: Tag[]) => void;
};

// export default withStyles(style, { withTheme: true })(
//   connect(
//     (state) => ({
//       tdos: getSearchResultTdos(state),
//       isSelectedAll: isSelectedAll(state),
//       initialTagList: getAutoSuggestedTags(state),
//       queryStore: getQueryToExportAll(state),
//       tdoIdPreview: getTdoIdPreview(state),
//       tdoIdSelected: getTdosForExport(state),
//     }),
//     {
//       suggestTags: (payload: any) => TAGS_AUTOCOMPLETE(payload),
//       applyTagsRequest: (payload: any) => APPLY_TAGS_TO_TDO_REQUEST(payload),
//     }
//   )(AlertDialogSlide)
// );

const mapFromState = (state: any) => ({
  tdos: getSearchResultTdosAll(state),
  isSelectedAll: isSelectedAll(state),
  initialTagList: getAutoSuggestedTags(state),
  queryStore: getQueryToExportAll(state),
  tdoIdPreview: getTdoIdPreview(state),
  tdoIdSelected: getTdosForExport(state),
});

const mapDispatch = {
  suggestTags: (payload: any) => TAGS_AUTOCOMPLETE(payload),
  applyTagsRequest: (payload: any) => APPLY_TAGS_TO_TDO_REQUEST(payload),
};

const connector = connect(mapFromState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default withStyles(style, { withTheme: true })(
  connector(AlertDialogSlide)
);
