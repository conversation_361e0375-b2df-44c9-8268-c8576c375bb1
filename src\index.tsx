import React from 'react';
import { Provider } from 'react-redux';
import { createRoot } from 'react-dom/client';
// import { handleImplicitRedirect } from 'veritone-oauth-helpers';
import '@fontsource/roboto/300.css';
import '@fontsource/roboto/300-italic.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/400-italic.css';
import '@fontsource/roboto/500.css';
import '@fontsource/dosis/300.css';
import '@fontsource/dosis/400.css';
import '@fontsource/dosis/500.css';

import validateAppConfig from '~helpers/validateAppConfig';
import configureStore from 'state/configureStore';

import { create } from 'jss';
import createGenerateClassName from '@mui/styles/createGenerateClassName';
import jssPreset from '@mui/styles/jssPreset';
import StylesProvider from '@mui/styles/StylesProvider';

import 'resources/styles/global.scss';

import App from 'pages/App';

validateAppConfig();

/**
 * load aiwarejs
 */

document
  .getElementsByTagName('head')[0]
  ?.insertAdjacentHTML(
    'afterbegin',
    `<link rel="stylesheet" type="text/css" href="${window.config.aiwareJSPath}${window.config.aiwareJSVersion}/index.esm.css">`
  );
const aiwareJSScript = document.createElement('script');
aiwareJSScript.setAttribute(
  'src',
  `${window.config.aiwareJSPath}${window.config.aiwareJSVersion}/index.esm.js`
);
aiwareJSScript.setAttribute('type', 'module');
aiwareJSScript.setAttribute('defer', 'defer');
aiwareJSScript.setAttribute('nonce', 'NGINX_CSP_NONCE');
document.head.appendChild(aiwareJSScript);

function bootstrap() {
  if (window.name === '_auth') {
    // if this is an OAuth redirect window, deal with the OAuth response but
    // don't render the app.
    // return handleImplicitRedirect(window.location.hash, window.opener);
  }

  const generateClassName = createGenerateClassName({
    disableGlobal: true,
  });
  const jss = create({
    ...jssPreset(),
    insertionPoint: 'jss-insertion-point',
  });

  const jssComment = document.createComment('jss-insertion-point');
  document.head &&
    document.head.insertBefore(jssComment, document.head.firstChild);

  const store = configureStore();

  const container = document.getElementById('root')!;
  const root = createRoot(container);

  root.render(
    <StylesProvider jss={jss} generateClassName={generateClassName}>
      <Provider store={store}>
        <React.StrictMode>
          <App />
        </React.StrictMode>
      </Provider>
    </StylesProvider>
  );
}

bootstrap();
