import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { DateTime } from 'luxon';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    field: "gender"
                    operator: "term"
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'genderAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    field: "genderNonconforming"
                    operator: "term"
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'genderNonconformingAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataAry: { date: string; [key: string]: number | string }[] =
      data.genderAggregation?.map((dateAgg) => {
        return {
          date: dateAgg.key_as_string,
          Male: 0,
          Female: 0,
          'Trans Man/Boy': 0,
          'Trans Woman/Girl': 0,
          'Gender Non-conforming':
            data.genderNonconformingAggregation
              ?.find((b) => b.key_as_string === dateAgg.key_as_string)
              ?.genderNonconforming?.buckets?.find(
                (gb) => gb.key_as_string === 'true'
              )?.doc_count ?? 0,
          ...dateAgg?.gender.buckets.reduce(
            (acc: { [key: string]: number }, b) => {
              acc[b.key] = b.doc_count;
              return acc;
            },
            {} // this reduce need to return an object to update default values
          ),
        };
      });

    const addGenderNonconforming: {
      date: string;
      [key: string]: number | string;
    }[] = [];
    data.genderNonconformingAggregation?.forEach((dateAgg) => {
      const gendersOnDate = dataAry.find(
        (dataPointObj) => dateAgg.key_as_string === dataPointObj.date
      );
      const nonComformingAmount =
        dateAgg?.genderNonconforming?.buckets?.find(
          (gb) => gb.key_as_string === 'true'
        )?.doc_count ?? 0;
      if (!gendersOnDate) {
        addGenderNonconforming.push({
          date: dateAgg.key_as_string,
          Male: 0,
          Female: 0,
          'Trans Man/Boy': 0,
          'Trans Woman/Girl': 0,
          'Gender Non-conforming': nonComformingAmount,
        });
      }
    });
    addGenderNonconforming?.forEach((gn) => dataAry.push(gn));

    chart.data = dataAry.sort(
      (a, b) =>
        DateTime.fromISO(a.date).toUnixInteger() -
        DateTime.fromISO(b.date).toUnixInteger()
    );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Number of people';

    [
      'Male',
      'Female',
      'Trans Man/Boy',
      'Trans Woman/Girl',
      'Gender Non-conforming',
    ].forEach((pr) => {
      // Create series
      const series = chart.series.push(new am4charts.LineSeries());
      series.strokeWidth = config.lineWidth;
      series.dataFields.valueY = pr;
      series.dataFields.dateX = 'date';
      series.name = pr;
      series.tooltipText = '{name}: [bold]{valueY}[/]';
    });

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

export default config;

interface Data {
  genderAggregation: {
    key: number;
    key_as_string: string;
    doc_count: number;
    gender: {
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
      buckets: { key: string; doc_count: number }[];
    };
  }[];
  genderNonconformingAggregation: {
    key_as_string: string;
    genderNonconforming: {
      buckets: { key_as_string: string; doc_count: number }[];
    };
  }[];
}
