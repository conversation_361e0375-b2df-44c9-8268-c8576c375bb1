import React from 'react';
import { ConnectedProps, connect } from 'react-redux';
import Snackbar from '@mui/material/Snackbar';
import MuiAlert, { AlertProps } from '@mui/material/Alert';

import {
  selectSnackbarNotificationState,
  hideSnackbarNotification,
} from '../../state/modules/snackbar';

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  }
);

function GlobalSnackbar({ open, message, variant, onClose }: PropsFromRedux) {
  return variant ? (
    <Snackbar
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      open={open}
      autoHideDuration={5000}
      onClose={onClose}
    >
      <Alert onClose={onClose} severity={variant}>
        {message}
      </Alert>
    </Snackbar>
  ) : (
    <Snackbar
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      open={open}
      autoHideDuration={5000}
      onClose={onClose}
    />
  );
}

const mapState = (state: any) => ({
  open: selectSnackbarNotificationState(state).open,
  message: selectSnackbarNotificationState(state).message,
  variant: selectSnackbarNotificationState(state).variant,
});

const mapDispatch = {
  onClose: hideSnackbarNotification,
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(GlobalSnackbar);
