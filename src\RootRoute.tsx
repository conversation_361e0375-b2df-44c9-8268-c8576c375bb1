import universal from 'react-universal-component';
import { ConnectedProps, connect } from 'react-redux';
import { modules } from '@veritone/glc-redux';
const {
  user: { userIsAuthenticated },
} = modules;
import RouteLoadingScreen from 'components/RouteLoadingScreen';
import RouteErrorScreen from 'components/RouteErrorScreen';
import { selectRoutesMap, selectRouteType } from 'modules/routing';
import { bootDidFinish } from 'state/modules/app';

const RootRoute = ({
  pageComponent,
  isLoading = false,
  isPermittedToRenderCurrentPage = false,
  currentRoute,
}: PropsFromRedux) =>
  (isPermittedToRenderCurrentPage && (
    <UniversalComponent
      page={pageComponent}
      isLoading={isLoading}
      currentRoute={currentRoute}
    />
  )) ||
  null;
const UniversalComponent = universal<{
  page: PropsFromRedux['pageComponent'];
  currentRoute: PropsFromRedux['currentRoute'];
}>((props) => import(`pages/${props.page}/index`), {
  minDelay: 500,
  chunkName: (props) => props.page,
  loading: <RouteLoadingScreen />,
  error: <RouteErrorScreen />,
  onError: (error) => console.error('failed universal', error),
  timeout: 60000,
});

const mapState = (state: any) => {
  const routesMap = selectRoutesMap(state);
  const routeType = selectRouteType(state);
  const currentRoute = routesMap[routeType];

  const roles = state.app.roles;
  if (roles.length === 0) {
    return {
      currentRoute,
      isLoading: true,
      pageComponent: 'RouteLoadingScreen',
    };
  }
  // if (Array.isArray(enginesSchemasRequired) && enginesSchemasRequired.length) {
  //   return {
  //     currentRoute,
  //     isPermittedToRenderCurrentPage:
  //       !currentRoute.requiresAuth || userIsAuthenticated(state),
  //     pageComponent: 'EngineSchemaRequired',
  //     isLoading: false
  //   };
  // }
  return {
    currentRoute,
    isPermittedToRenderCurrentPage:
      !currentRoute.requiresAuth || userIsAuthenticated(state),
    pageComponent: currentRoute.component,
    // in addition to showing the loading screen as chunks load,
    // we render it until initial app bootup finishes
    isLoading: !bootDidFinish(state),
  };
};

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(RootRoute);
