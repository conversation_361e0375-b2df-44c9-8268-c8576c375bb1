import { mergeTags, buildTagTdoQuery, buildBulkTagPayload } from './index';

describe('mergeTags', () => {
  test('return empty tags when both empty', () => {
    expect(mergeTags(null, null)).toEqual([]);
    expect(mergeTags([], [])).toEqual([]);
  });

  test('return the other tags when one is empty', () => {
    const tag = [{ value: 'a' }];
    expect(mergeTags(tag, null)).toEqual(tag);
    expect(mergeTags([], tag)).toEqual(tag);
  });

  test('return merged tags', () => {
    const tags1 = [{ value: 'a', label: 'a', abc: 'def' }, { value: 'd' }];
    const tags2 = [
      { value: 'a', label: 'b' },
      { value: 'c', label: 'd' },
    ];
    const want = [
      { value: 'a', label: 'b', abc: 'def' },
      { value: 'd' },
      { value: 'c', label: 'd' },
    ];
    expect(mergeTags(tags1, tags2)).toEqual(want);
  });

  test('return merged tags does not erase batchId tags', () => {
    const tags1 = [
      { key: 'batchId', value: '123' },
      { value: 'a', label: 'a' },
    ];
    const tags2 = [
      { value: 'a', label: 'b' },
      { value: 'c', label: 'd' },
    ];
    const want = [
      { key: 'batchId', value: '123' },
      { value: 'a', label: 'b' },
      { value: 'c', label: 'd' },
    ];
    expect(mergeTags(tags1, tags2)).toEqual(want);
  });
});

describe('buildTagTdoQuery', () => {
  it('build query correctly', () => {
    const tdoTags = [
      {
        tdoId: '1100000068',
        tags: [
          { label: 'in redaction', redactionStatus: 'Draft' },
          { label: 'tag1', value: 'tag1', __isNew__: true },
        ],
      },
      {
        tdoId: '1100000069',
        tags: [{ label: 'tag1', value: 'tag1', __isNew__: true }],
      },
    ];
    const variablesWant = {
      input1100000068: {
        id: '1100000068',
        details: {
          tags: [
            { label: 'in redaction', redactionStatus: 'Draft' },
            { label: 'tag1', value: 'tag1', __isNew__: true },
          ],
        },
      },
      input1100000069: {
        id: '1100000069',
        details: {
          tags: [{ label: 'tag1', value: 'tag1', __isNew__: true }],
        },
      },
    };
    const { query, variables } = buildTagTdoQuery(tdoTags);
    expect(query).toMatchSnapshot();
    console.log(JSON.stringify(variables, null, 2));
    expect(variables).toEqual(variablesWant);
  });
});

describe('buildBulkTagPayload', () => {
  test('build payload with query', () => {
    const tags = [
      {
        label: 'tag2',
        value: 'tag2',
        __isNew__: true,
      },
    ];
    const searchQuery = {
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'parentTreeObjectIds',
            operator: 'terms',
            values: ['47b88540-30a3-4f0e-b566-0827b863d5dc'],
          },
        ],
      },
      select: ['veritone-job', 'veritone-file'],
      index: ['mine'],
      limit: 10,
      offset: 0,
    };
    const got = buildBulkTagPayload(tags, searchQuery, null);
    const want = {
      tagData: {
        tags: [
          {
            label: 'tag2',
            value: 'tag2',
            __isNew__: true,
          },
        ],
        searchQuery: {
          index: ['mine'],
          select: ['veritone-job', 'veritone-file'],
          query: {
            operator: 'and',
            conditions: [
              {
                field: 'parentTreeObjectIds',
                operator: 'terms',
                values: ['47b88540-30a3-4f0e-b566-0827b863d5dc'],
              },
            ],
          },
        },
      },
    };
    expect(got).toEqual(want);
  });

  test('build payload with tdo ids', () => {
    const tags = [
      {
        label: 'tag2',
        value: 'tag2',
        __isNew__: true,
      },
    ];
    const tdoIds = ['1100000068'];
    const got = buildBulkTagPayload(tags, null, tdoIds);
    const want = {
      tagData: {
        tags: [
          {
            label: 'tag2',
            value: 'tag2',
            __isNew__: true,
          },
        ],
        tdoIds: ['1100000068'],
      },
    };
    expect(got.tagData).toEqual(want.tagData);
  });

  test('build payload - if searchQuery and tdo ids provided, respect tdo ids only', () => {
    const tags = [
      {
        label: 'tag2',
        value: 'tag2',
        __isNew__: true,
      },
    ];
    const tdoIds = ['1100000068'];
    const got = buildBulkTagPayload(tags, {}, tdoIds);
    const want = {
      tagData: {
        tags: [
          {
            label: 'tag2',
            value: 'tag2',
            __isNew__: true,
          },
        ],
        tdoIds: ['1100000068'],
      },
    };
    expect(got.tagData).toEqual(want.tagData);
  });
});
