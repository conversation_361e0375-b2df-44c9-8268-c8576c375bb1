import React from 'react';
import RGL, { Layout, WidthProvider } from 'react-grid-layout';
import * as styles from './styles.scss';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import WordCloud from 'components/WordCloud';
import Sunburst from 'components/RvCharts/Sunburst';

const ResponsiveGridLayout = WidthProvider(RGL);

interface Props {
  readonly rowHeight?: number;
  readonly cols?: number;
  readonly className?: string;
  readonly isResizable?: boolean;
  readonly windowSize: number;
}

class AnalyticsBody extends React.Component<Props> {
  static defaultProps = {
    rowHeight: 80,
    cols: 2,
    className: styles['layout'],
    isResizable: true,
  };

  state = {
    layout: [],
    sizeSunburst: {
      w: 1,
      h: 8,
    },
    sizeCloud: {
      w: 1,
      h: 8,
    },
    wcContainerWidth: Number(localStorage.getItem('wcContainerWidth')) || 300,
    windowSize: 0,
  };

  componentDidMount = () => {
    const wordcloudContainer = document.getElementById('wordcloud-container');
    this.applyLocalLayout();
    this.setWordcloudContainerWidth(wordcloudContainer?.clientWidth);
  };

  componentDidUpdate = (_prevProps: any, prevState: any) => {
    const wordcloudContainer = document.getElementById('wordcloud-container');
    if (
      wordcloudContainer?.clientWidth &&
      prevState.wcContainerWidth !== wordcloudContainer?.clientWidth
    ) {
      this.setWordcloudContainerWidth(wordcloudContainer?.clientWidth);
    }
  };

  setWordcloudContainerWidth = (width?: string | number) => {
    this.setState({ wcContainerWidth: width || 0 });
    localStorage.setItem('wcContainerWidth', `${width || 0}`);
  };
  getLayoutFromLocalStorage = () => {
    try {
      return JSON.parse(localStorage.getItem('layout') ?? '');
    } catch (_error) {
      return null;
    }
  };
  applyLocalLayout = () => {
    const layout = this.getLayoutFromLocalStorage();
    const getLocalData = (type: any) => {
      const name = layout.filter((item: Layout) => item.i === type)[0];
      return {
        w: name.w,
        h: name.h,
      };
    };

    if (layout) {
      this.setState({
        layout,
        sizeSunburst: getLocalData('sunburst'),
        sizeCloud: getLocalData('wordcloud'),
      });
    } else {
      this.setState({
        layout: [
          { x: 0, y: 0, w: 2, h: 4, i: 'timeline', minH: 4, maxH: 8 },
          { x: 0, y: 4, w: 1, h: 8, i: 'wordcloud', minH: 4, maxH: 8 },
          { x: 1, y: 4, w: 1, h: 8, i: 'sunburst', minH: 4, maxH: 8 },
        ],
      });
    }
  };

  handleDragStop = (layout: Array<Layout>) => {
    this.saveToLocal(layout);
  };

  saveToLocal = (layout: Array<Layout>) => {
    const layoutToSave = layout.map((el) => {
      const propsToUse: (keyof Layout)[] = [
        'x',
        'y',
        'w',
        'h',
        'i',
        'minH',
        'maxH',
      ];
      const layoutFormat = propsToUse.reduce(
        (acc, val) => {
          acc[val] = el[val];
          return acc;
        },
        {} as Record<keyof Layout, (typeof el)[keyof typeof el]>
      );
      return layoutFormat;
    });
    localStorage.setItem('layout', JSON.stringify(layoutToSave));
  };

  recordSizeChange = (item: Layout) => {
    if (item.i === 'wordcloud') {
      const sizeCloud = {
        w: item.w,
        h: item.h,
      };
      this.setState({ sizeCloud });
    } else if (item.i === 'sunburst') {
      const sizeSunburst = {
        w: item.w,
        h: item.h,
      };
      this.setState({ sizeSunburst });
    }
  };

  handleResizeStart = (_layout: Array<Layout>, oldItem: Layout) => {
    if (oldItem.i === 'wordcloud') {
      this.recordSizeChange(oldItem);
    }
  };

  handleResizeStop = (
    layout: Array<Layout>,
    oldItem: Layout,
    newItem: Layout
  ) => {
    if (newItem.h - oldItem.h < 0) {
      // Smaller
      newItem.h = 4;
    } else {
      // Larger
      newItem.h = 8;
    }
    this.recordSizeChange(newItem);
    this.saveToLocal(layout);
  };

  render() {
    const { sizeCloud, wcContainerWidth, layout, sizeSunburst } = this.state;
    const { rowHeight, windowSize } = this.props;
    const cloudHeight = (rowHeight ?? 0) * sizeCloud.h;
    return (
      <ResponsiveGridLayout
        layout={layout}
        onDragStop={this.handleDragStop}
        useCSSTransforms
        onResizeStart={this.handleResizeStart}
        onResizeStop={this.handleResizeStop}
        {...this.props}
      >
        <div
          id="wordcloud-container"
          key={'wordcloud'}
          className={styles['wordcloud-container']}
        >
          <WordCloud
            sizeCloud={sizeCloud}
            containerWidth={wcContainerWidth}
            cloudHeight={cloudHeight}
            windowSize={windowSize}
          />
        </div>
        <div key={'sunburst'} className={styles['sunburst-container']}>
          <Sunburst windowSize={windowSize} sizeSunburst={sizeSunburst} />
        </div>
      </ResponsiveGridLayout>
    );
  }
}

export default AnalyticsBody;
