import { TokenConfigurationRow } from '../definitions';
import { DatabaseError, OrgHasTokenConfig } from '../errors';
import { Context } from '../../types';

const checkForTokenConfig = async (context: Context) => {
  const { req, log, queries } = context;

  try {
    return queries
      .getTokenConfig(req.body.orgId)
      .then((tokenConfig: TokenConfigurationRow) => {
        if (tokenConfig) {
          log.error('OrgHasTokenConfig: Org', req.body.orgId);
          throw new OrgHasTokenConfig();
        }

        return context;
      });
  } catch (e) {
    log.error('FetchTokenConfig failed', e);
    throw new DatabaseError(e);
  }
};

export default checkForTokenConfig;
