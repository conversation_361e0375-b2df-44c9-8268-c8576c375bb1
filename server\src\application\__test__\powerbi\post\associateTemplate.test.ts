import supertest from 'supertest';
import fs from 'fs';
import { request } from 'graphql-request';
import axios from 'axios';
import expressApp from '../../setupExpress';
import { tokenConfigNoRecordMock, templateRecordMock, adminPermissionMasksMock } from '../fixtures';

jest.mock('graphql-request', () => ({ request: jest.fn() }));
jest.mock('axios', () => ({ post: jest.fn(), get: jest.fn(), patch: jest.fn() }));
jest.mock('../../../powerbi/queries', () => () => ({
    getTemplateRecord: () => Promise.resolve(templateRecordMock),
    getTokenConfig: () => Promise.resolve(tokenConfigNoRecordMock),
    upsertTokenConfig: (): any => null
}));

jest.mock('uuid', () => ({ v4: () => '1234565678abcdef' }));

describe('POST /api/v1/powerbi/associateTemplate', () => {

    (request as jest.Mock).mockImplementation(({ document }) => {
        if (document.includes('validateToken')) {
            return Promise.resolve({ validateToken: { token: 'valid token' } });
        }
        if (document.includes('me')) {
            return Promise.resolve({ me: { organizationId: 123 } });
        }
        return Promise.resolve()
    });

    (axios.post as jest.Mock).mockImplementation((url) => {
        if (url.includes('oauth')) {
            return Promise.resolve({
                data: {
                    access_token: 'validServicePricipalToken',
                    expires_in: 3600
                }
            });
        }
        if (url.includes('profiles')) {
            return Promise.resolve({
                data: {
                    id: '12345',
                    name: 'Profile Name'
                }
            });
        }
        if (url.includes('groups')) {
            return Promise.resolve({
                data: {
                    id: '12345',
                    name: 'Profile Name'
                }
            });
        }
        return Promise.resolve()
    });

    (axios.get as jest.Mock).mockImplementation((url) => {
        if (url.includes('current-user')) {
            return Promise.resolve({
                data: {
                    permissionMasks: adminPermissionMasksMock
                }
            });
        }
        if (url.includes('capacities')) {
            return Promise.resolve({
                data: {
                    value: [{
                        id: 345345,
                        displayName: 'Capacity Name',
                        admins: ['adminuser'],
                        sku: 'A',
                        state: 'state',
                        capacityUserAccessRight: 'capacityUserAccessRight',
                        region: 'US'
                    }]
                }
            });
        }
        if (url.includes('PbixTemplates')) {
            return Promise.resolve({
                data: fs.createReadStream(__dirname + '/../PbixFixture.pbix')
            });
        }
        if (url.includes('datasources') && url.includes('datasets')) {
            return Promise.resolve({
                data: {
                    value: [{
                        gatewayId: 'gatewayid',
                        datasourceId: 'datasourceid'
                    }]
                }
            });
        }
        if (url.includes('datasets')) {
            return Promise.resolve({
                data: {
                    value: [{
                        id: '67890',
                        name: 'PbixFixture-1234565678abcdef'
                    }]
                }
            });
        }
        if (url.includes('groups') && url.includes('reports')) {
            return Promise.resolve({
                data: {
                    value: [{
                        id: '67890',
                        name: 'PbixFixture-1234565678abcdef',
                        embedUrl: 'embedUrl'
                    }]
                }
            });
        }

        return Promise.resolve()
    });

    it('returns embedToken', async () => {

        await supertest(expressApp)
            .post('/api/v1/powerbi/provisionOrg')
            .set('Authorization', 'Bearer valid-token')
            .send({
                orgName: 'Org Name',
                orgId: '2',
                templatePbixFileName: 'PbixFixture.pbix'
            })
            .expect(200)
            .then(res => {
                expect(res.headers['content-type']).toContain('application/json; charset=utf-8');
                expect(res.body).toMatchObject({
                    orgId: '2',
                });
            })
    }, 30000)
})

