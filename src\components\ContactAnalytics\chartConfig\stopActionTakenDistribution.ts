import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "actionsTakenDuringStop"
                field: "actionsTakenDuringStop"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'actionsTakenDuringStopAggregation',
      dataKey: 'actionsTakenDuringStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Actions Taken',
  filterTextType: 'Actions Taken by Type',
  filterType: 'actionsTakenDuringStop',
  filterTerms: {
    VEHICLE: [
      'Person removed from vehicle by order',
      'Person removed from vehicle by physical contact',
      'Field sobriety test conducted',
      'Vehicle impounded',
    ],
    DETENTION: [
      'Curbside detention',
      'Handcuffed or flex cuffed',
      'Patrol car detention',
    ],
    WEAPON: [
      'Firearm pointed at person',
      'Firearm discharged or used',
      'Electronic control device used',
      'Impact projectile discharged or used (EG: blunt impact projectile, rubber bullets, or bean bags)',
      'Baton or other impact weapon used',
      'Chemical spray used (EG: pepper spray, mace, tear gas, or other chemical irritants)',
    ],
    CANINE: [
      'Canine removed from vehicle or used to search',
      'Canine bit or held person',
    ],
    SEARCH: [
      'Asked for consent to search person',
      'Property was seized',
      'Search of person was conducted',
      'Asked for consent to search property',
      'Search of property was conducted',
      'Property was seized',
    ],
    K12: ['Admission or written statement obtained from student'],
    OTHER: ['Other Physical or Vehicle contact', 'Person photographed'],
    // 'No Action Taken': [''] TODO FIX THIS - NO DATA
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap = {
      VEHICLE: 0,
      DETENTION: 0,
      WEAPON: 0,
      CANINE: 0,
      SEARCH: 0,
      OTHER: 0,
      K12: 0,
      'No Action Taken': 0,
      ...data.actionsTakenDuringStopAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          if (
            [
              'Person removed from vehicle by order',
              'Person removed from vehicle by physical contact',
              'Field sobriety test conducted',
              'Vehicle impounded',
            ].includes(b.key)
          ) {
            acc['VEHICLE'] = acc['VEHICLE']
              ? acc['VEHICLE'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Curbside detention',
              'Handcuffed or flex cuffed',
              'Patrol car detention',
            ].includes(b.key)
          ) {
            acc['DETENTION'] = acc['DETENTION']
              ? acc['DETENTION'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Firearm pointed at person',
              'Firearm discharged or used',
              'Electronic control device used',
              'Impact projectile discharged or used (EG: blunt impact projectile, rubber bullets, or bean bags)',
              'Baton or other impact weapon used',
              'Chemical spray used (EG: pepper spray, mace, tear gas, or other chemical irritants)',
            ].includes(b.key)
          ) {
            acc['WEAPON'] = acc['WEAPON']
              ? acc['WEAPON'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Canine removed from vehicle or used to search',
              'Canine bit or held person',
            ].includes(b.key)
          ) {
            acc['CANINE'] = acc['CANINE']
              ? acc['CANINE'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Asked for consent to search person',
              'Property was seized',
              'Search of person was conducted',
              'Asked for consent to search property',
              'Search of property was conducted',
              'Property was seized',
            ].includes(b.key)
          ) {
            acc['SEARCH'] = acc['SEARCH']
              ? acc['SEARCH'] + b.doc_count
              : b.doc_count;
          }
          if (
            ['Admission or written statement obtained from student'].includes(
              b.key
            )
          ) {
            acc['K12'] = acc['K12'] ? acc['K12'] + b.doc_count : b.doc_count;
          }
          if (
            [
              'Other Physical or Vehicle contact',
              'Person photographed',
            ].includes(b.key)
          ) {
            acc['OTHER'] = acc['OTHER']
              ? acc['OTHER'] + b.doc_count
              : b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = [
      { typeOfAction: 'Vehicle', count: dataMap['VEHICLE'] },
      { typeOfAction: 'Detention', count: dataMap['DETENTION'] },
      { typeOfAction: 'Weapon', count: dataMap['WEAPON'] },
      { typeOfAction: 'Canine', count: dataMap['CANINE'] },
      { typeOfAction: 'Search', count: dataMap['SEARCH'] },
      { typeOfAction: 'K12', count: dataMap['K12'] },
      { typeOfAction: 'Other', count: dataMap['OTHER'] },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'typeOfAction';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  actionsTakenDuringStopAggregation: { key: string; doc_count: number }[];
}
