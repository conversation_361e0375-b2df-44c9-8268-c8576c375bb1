Cypress.Commands.add('Graphql', (query: string, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res: Cypress.Response<LoginResponse>) => cy.wrap(res));
});

Cypress.Commands.add('FileSearch', (query: object) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/api/search/file_search_authtoken`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: query,
  }).then((res: Cypress.Response<LoginResponse>) => cy.wrap(res));
});

Cypress.Commands.add('SearchAggregate', (query: object) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/api/search/aggregate`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: query,
  }).then((res: Cypress.Response<LoginResponse>) => cy.wrap(res));
});
