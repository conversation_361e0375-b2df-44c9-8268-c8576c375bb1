import { Knex } from 'knex';
import env from '../env';

async function isSafeToMigrate(db: Knex) {
  try {
    if (env.nodeEnv === 'production') {
      // Get the list of migrations applied to the database
      const migrations = await db.migrate.list();

      // Get list of pending migrations
      const pendingMigrations = migrations?.[1] ?? [];

      if (pendingMigrations.length > 0) {
        console.log(
          'pending migrations:',
          pendingMigrations.map(
            (migration: { file: string }) => migration?.file
          )
        );
        return true;
      } else {
        console.log('no migrations');
        return false;
      }
    } else {
      return true;
    }
  } catch (error) {
    console.error('Error checking migrations:', error);
    return false;
  }
}

export default isSafeToMigrate;
