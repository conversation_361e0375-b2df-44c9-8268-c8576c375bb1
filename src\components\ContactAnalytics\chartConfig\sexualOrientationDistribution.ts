import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{ field: "lgbt", operator: "term", limit: 10000 }]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'lgbtAggregation',
      dataKey: 'lgbt',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Sexualities',
  filterTextType: 'Sexuality by Type',
  filterType: 'lgbt',
  filterTerms: { LGBT: ['true'], 'Not LGBT': ['false'] },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap = {
      'Not LGBT': 0,
      LGBT: 0,
      ...data.lgbtAggregation.reduce((acc: { [key: string]: number }, b) => {
        if (b.key_as_string === 'false') {
          acc['Not LGBT'] = b.doc_count;
        }
        if (b.key_as_string === 'true') {
          acc['LGBT'] = b.doc_count;
        }
        return acc;
      }, {}), // this reduce need to return an object to update default values
    };

    // Add data
    chart.data = Object.entries(dataMap).map((kvp) => {
      const [lgbt, v] = kvp;
      return { isLGBT: lgbt, count: v };
    });

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'isLGBT';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  lgbtAggregation: { key: number; key_as_string: string; doc_count: number }[];
}
