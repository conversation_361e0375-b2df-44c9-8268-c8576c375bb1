import {
  ActionCreatorWithOptionalPayload,
  ActionCreatorWithPayload,
  createAction,
  createReducer,
} from '@reduxjs/toolkit';
import getApiAuthToken from '../../../helpers/getApiAuthToken';
import { returnSearchQuery } from '../../utils/queryConstructor';
import { get, isString } from 'lodash';
import { DurPerEngineClass } from './sagas';
import fetch from '../../../helpers/fetchRetry';

export const FETCH_MEDIA_AGGREGATIONS = createAction(
  'request to fetch media aggregations'
);
interface fetchMediaAggregationsResponse {
  aggregations: {
    doc_count: number;
    recordingId: { value: number };
    fileDuration: {
      value: number;
    };
  };
}
interface fetchTotalMediaResponse {
  aggregations: {
    count: { value: number };
  };
}
export const FETCH_MEDIA_AGGREGATIONS_SUCCESS =
  createAction<fetchMediaAggregationsResponse>(
    'fetch media aggregations success'
  );
export const FETCH_MEDIA_AGGREGATIONS_FAILURE = createAction<
  | {
      message: string;
    }[]
  | undefined
>('fetch media aggregations failure');
export const FETCH_PROCESSED_TIME_FOR_ONE_ENGINE =
  createAction<DurPerEngineClass>('fetch processed time for one engine');
export const FETCH_TOTAL_MEDIA_PROCESSED_TIME =
  createAction<fetchTotalMediaResponse>('fetch total media processed time');

export const FILE_TYPE_AGGREGATION = [
  {
    field: 'recordingId',
    operator: 'count',
  },
  {
    field: 'fileDuration',
    operator: 'sum',
  },
];

export const QUERY_RANGE = 'file';

const defaultState = {
  durPerEngineClass: [] as DurPerEngineClass[],
  totalMediaProcessedTime: '0',
  totalDuration: '0',
  fetchingMediaAggregations: false,
  fetchingMediaDataError: null as string | null,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_MEDIA_AGGREGATIONS, (state, _action) => {
      return {
        ...state,
        fetchingMediaAggregations: true,
        fetchingMediaDataError: null,
      };
    })
    .addCase(FETCH_MEDIA_AGGREGATIONS_SUCCESS, (state, action) => {
      const mediaAggregations = get(action, 'payload.aggregations');
      return {
        ...state,
        totalDuration: Math.ceil(
          (mediaAggregations?.fileDuration?.value ?? 0) / 3600
        ).toLocaleString(),
        fetchingMediaAggregations: false,
        fetchingMediaDataError: null,
      };
    })
    .addCase(FETCH_MEDIA_AGGREGATIONS_FAILURE, (state, action) => {
      return {
        ...state,
        fetchingMediaAggregations: false,
        fetchingMediaDataError: get(
          action,
          'payload[0].message',
          'Error fetching media aggregations'
        ),
      };
    })
    .addCase(FETCH_PROCESSED_TIME_FOR_ONE_ENGINE, (state, action) => {
      const value = get(action, 'payload');
      let durPerEngineClass = [...state.durPerEngineClass];
      if (durPerEngineClass.length < 8) {
        durPerEngineClass = [...durPerEngineClass, value];
      } else {
        durPerEngineClass = [...state.durPerEngineClass].map((dur) => {
          let holder = dur;
          if (dur.id === value.id) {
            holder = { ...value };
          }
          return holder;
        });
      }

      return {
        ...state,
        durPerEngineClass,
      };
    })
    .addCase(FETCH_TOTAL_MEDIA_PROCESSED_TIME, (state, action) => {
      const duration = get(action, 'payload.aggregations.count.value', 0);
      const totalMediaProcessedTime = Math.ceil(
        duration / 3600
      ).toLocaleString();
      return {
        ...state,
        totalMediaProcessedTime,
      };
    });
});

export default reducer;
export const namespace = 'dashboard';
export const local = (state: any) => state[namespace] as typeof defaultState;

export const fetchMediaAggregations =
  <S = any, F = unknown>(
    aggregateQuery: any,
    actionTypeSuccess: string | ActionCreatorWithPayload<S>,
    actionTypeFailure: string | ActionCreatorWithOptionalPayload<F>
  ) =>
  async (dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = `${state.config.apiRoot}/api/search/aggregate`;
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;
    const headers: { [key: string]: string } = {
      Authorization: 'Bearer ' + token,
      'Content-Type': 'application/json',
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }
    let result;

    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    try {
      result = await fetch(endpoint, {
        method: 'post',
        headers,
        body: JSON.stringify(aggregateQuery),
      })
        .then((res) => res.text())
        .then((resText) => JSON.parse(resText))
        .catch((error) => console.log(error));
    } catch (_err) {
      if (isString(actionTypeFailure)) {
        dispatch({
          type: actionTypeFailure,
        });
      } else {
        dispatch(actionTypeFailure());
      }
      return;
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify(aggregateQuery) }
      );
    }

    if (!result || result.error) {
      if (isString(actionTypeFailure)) {
        dispatch({
          type: actionTypeFailure,
          payload: get(result, 'data'),
        });
      } else {
        dispatch(actionTypeFailure(get(result, 'data')));
      }
      return;
    } else {
      if (isString(actionTypeSuccess)) {
        dispatch({
          type: actionTypeSuccess,
          payload: result,
        });
      } else {
        dispatch(actionTypeSuccess(result));
      }
    }
  };

export const fetchProcessedTime =
  (query: any) => async (_dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = `${state.config.apiRoot}/api/search/aggregate`;
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;
    if (token) {
      const headers: { [key: string]: string } = {
        Authorization: 'Bearer ' + token,
        'Content-Type': 'application/json',
      };
      if (veritoneAppId) {
        headers['x-veritone-application'] = veritoneAppId;
      }

      const enableTimeWarnings = endpoint.includes('stage');
      const reqStartTime = Date.now();

      let result;
      try {
        result = await fetch(endpoint, {
          method: 'post',
          headers,
          body: JSON.stringify(query),
        }).then((r) => r.json());
      } catch (error) {
        console.error('failed to fetchProcessedTime', error);
      }

      const reqEndTime = Date.now();
      if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
        console.error(
          `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
          { endpoint, query: JSON.stringify(query) }
        );
      }

      if (result?.error) {
        console.error('failed to fetchProcessedTime', result?.error);
      }
      return result;
    }
  };

export const emitFetchMediaAggregationsAction = () => (dispatch: any) => {
  dispatch(FETCH_MEDIA_AGGREGATIONS());
};

export const constructQueryProcessedTime = (
  fullSearchQuery: Record<string, any>,
  searchBarQuery: any,
  engineArr?: string[]
) => {
  const array = engineArr ? engineArr : [];

  const extraQuery = [...fullSearchQuery.query.conditions];
  const DURATION_AGGREGATION = [
    {
      name: 'count',
      field: 'fileDuration',
      operator: 'sum',
    },
  ];
  const engineQuery = returnSearchQuery(
    searchBarQuery,
    QUERY_RANGE,
    DURATION_AGGREGATION
  );
  const query = {
    operator: 'and',
    conditions: [
      {
        field: 'veritone-job.engineCategories',
        operator: 'terms',
        values: [...array],
      },
      ...extraQuery,
    ],
  };
  engineQuery.query = query;
  return engineQuery;
};

export const isFetchingMediaAggregations = (state: any) =>
  local(state).fetchingMediaAggregations;
export const mediaDataFailedToFetch = (state: any) =>
  local(state).fetchingMediaDataError;
export const getTotalDuration = (state: any) => local(state).totalDuration;
export const getDurationPerEngineClass = (state: any) =>
  local(state).durPerEngineClass;
export const getTotalMediaProcessedTime = (state: any) =>
  local(state).totalMediaProcessedTime;
