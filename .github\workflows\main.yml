name: Run Tests and Linter

on:
  pull_request:
    branches:
      - 'feature/**'
  push:
    branches:
      - 'master'
      - 'feature/**'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  NODE_OPTIONS: '--max_old_space_size=4096'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20.x'
      - run: yarn install
      - run: yarn
        working-directory: server
      - run: yarn run lint
      - run: yarn test
