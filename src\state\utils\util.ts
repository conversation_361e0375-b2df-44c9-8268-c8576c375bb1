import { select, call } from 'typed-redux-saga/macro';
import { modules, helpers } from '@veritone/glc-redux';
import fetch from '../../helpers/fetchRetry';

const { fetchGraphQLApi } = helpers;
const { auth: authModule, config: configModule } = modules;

// URI Parser from https://stackoverflow.com/a/39308026
export const ParseURI = (url: string) => {
  const m = url.match(
    /^(([^:/?#]+:)?(?:\/\/(([^/?#:]*)(?::([^/?#:]*))?)))?([^?#]*)(\?[^#]*)?(#.*)?$/
  );
  if (!m) {
    return null;
  }

  const r = {
    hash: m[8] || '', // #asd
    host: m[3] || '', // localhost:257
    hostname: m[4] || '', // localhost
    href: m[0] || '', // http://localhost:257/deploy/?asd=asd#asd
    origin: m[1] || '', // http://localhost:257
    pathname: m[6] || (m[1] ? '/' : ''), // /deploy/
    port: m[5] || '', // 257
    protocol: m[2] || '', // http:
    search: m[7] || '', // ?asd=asd
  };

  if (r.protocol.length === 2) {
    r.protocol = 'file:///' + r.protocol.toUpperCase();
    r.origin = r.protocol + '//' + r.host;
  }
  r.href = r.origin + r.pathname + r.search + r.hash;
  return r;
};

// http://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript
export function guid() {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  return `${s4()}-${s4()}-${s4()}`;
}

export function* getConfigModule() {
  const config = yield* select(configModule.getConfig<Window['config']>);
  const sessionToken = yield* select(authModule.selectSessionToken);
  const oauthToken = yield* select(authModule.selectOAuthToken);
  const { apiRoot, graphQLEndpoint } = config;
  const veritoneAppId = config.veritoneAppId;
  const token = sessionToken || oauthToken;
  return {
    apiRoot,
    graphQLEndpoint,
    token,
    veritoneAppId,
  };
}

export function* getGqlParams() {
  const { apiRoot, graphQLEndpoint, token, veritoneAppId } =
    yield* call(getConfigModule);
  const graphQLUrl = `${apiRoot}/${graphQLEndpoint}`;
  return {
    graphQLUrl,
    token,
    veritoneAppId,
  };
}

interface GQLError {
  message: string;
  locations?: Array<{ line: number; column: number }>;
  name?: string;
  path?: string[];
}

export function* handleRequest<T>({
  query,
  variables,
  ignoredError = false,
}: {
  query: string;
  variables?: Record<string, unknown>;
  ignoredError?: boolean;
}) {
  const { graphQLUrl, token, veritoneAppId } = yield* call(getGqlParams);
  const extraHeaders: HeadersInit = {};
  if (veritoneAppId) {
    extraHeaders['x-veritone-application'] = veritoneAppId;
  }
  let response: { data: T; errors?: GQLError[] };
  try {
    response = yield* call(fetchGraphQLApi, {
      endpoint: graphQLUrl,
      extraHeaders,
      query,
      variables,
      token,
    });

    if (!ignoredError) {
      if (response.errors) {
        let msg = '';
        response.errors.forEach((e) => {
          msg += e.message + '\n';
        });
        throw new Error(msg);
      }
    }
  } catch (error) {
    return {
      error,
    };
  }
  return {
    error: null,
    response,
  };
}

export async function fetchSearchMedia({
  searchQuery,
  apiRoot,
  token,
  veritoneAppId,
}: {
  searchQuery: SearchQuery;
  apiRoot: string;
  token: string | null;
  veritoneAppId?: string;
}) {
  const endpoint = `${apiRoot}/api/search/file_search_authtoken`;
  const headers: HeadersInit = {
    Authorization: 'Bearer ' + token,
    'Content-Type': 'application/json',
  };
  if (veritoneAppId) {
    headers['x-veritone-application'] = veritoneAppId;
  }
  const enableTimeWarnings = apiRoot.includes('stage');
  const reqStartTime = Date.now();
  let response;
  try {
    response = await fetch(endpoint, {
      method: 'post',
      body: JSON.stringify(searchQuery),
      headers,
    })
      .then((response) => response.text())
      .then((responseText) => JSON.parse(responseText));
  } catch (error) {
    console.log('error', error);
    return {
      error,
    };
  }

  const reqEndTime = Date.now();
  if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
    console.error(
      `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
      { endpoint, query: JSON.stringify(searchQuery) }
    );
  }
  return {
    error: null,
    response,
  };
}

export const getDateTimeNow = () => {
  const date = new Date();
  const year = date.getFullYear();
  let month: number | string = date.getMonth() + 1;
  let dt: number | string = date.getDate();

  let house: number | string = date.getHours();
  let min: number | string = date.getMinutes();

  if (dt < 10) {
    dt = '0' + dt;
  }
  if (month < 10) {
    month = '0' + month;
  }
  if (house < 10) {
    house = '0' + house;
  }
  if (min < 10) {
    min = '0' + min;
  }
  return year + '-' + month + '-' + dt + 'T' + house + ':' + min;
};

export function* fetchUpload(endpoint: string, body: RequestInit['body']) {
  const { veritoneAppId } = yield* call(getConfigModule);
  const headers: HeadersInit = {
    'Content-Type': 'text/plain',
  };
  if (veritoneAppId) {
    headers['x-veritone-application'] = veritoneAppId;
  }
  const enableTimeWarnings = endpoint.includes('stage');
  const reqStartTime = Date.now();
  try {
    yield fetch(endpoint, {
      method: 'PUT',
      mode: 'cors',
      headers,
      body,
    });
  } catch (error) {
    console.log('error', error);
  }
  const reqEndTime = Date.now();
  if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
    console.error(
      `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
      { endpoint, query: body }
    );
  }
}

interface SearchQuery {
  index: string[];
  select: string[];
  query?: Record<string, any>;
  limit: number;
  offset: number;
  sort?: {
    field: string;
    order: string;
  }[];
}
