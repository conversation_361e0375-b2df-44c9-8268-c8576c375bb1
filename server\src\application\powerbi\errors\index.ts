import ValidationError from './ValidationError';
import ForbiddenError from './ForbiddenError';
import UnauthorizedError from './UnauthorizedError';
import InternalServerError from './InternalServerError';
import ApiError from './ApiError';
import NoCapacitiesError from './NoCapacitiesError';
import NoDatasetsError from './NoDatasetsError';
import NoDatasourcesError from './NoDatasourcesError';
import ReportsError from './ReportsError';
import DatabaseError from './DatabaseError';
import OrgHasNoTokenRegisteredError from './OrgHasNoTokenRegisteredError';
import OrgNameAlreadyExists from './OrgNameAlreadyExists';
import OrgHasTokenConfig from './OrgHasTokenConfig';
import UnknownTemplateError from './UnknownTemplateError';
import NoQuestionDefError from './NoQuestionDefError';
import ActionError from './ActionError';
import ServicePrincipalTokenError from './ServicePrincipalTokenError';
import OrgsHaveNoTokenRegisteredError from './OrgsHaveNoTokenRegisteredError';

export {
  ValidationError,
  ForbiddenError,
  UnauthorizedError,
  InternalServerError,
  ApiError,
  NoCapacitiesError,
  NoDatasetsError,
  NoDatasourcesError,
  ReportsError,
  DatabaseError,
  OrgHasNoTokenRegisteredError,
  OrgNameAlreadyExists,
  OrgHasTokenConfig,
  UnknownTemplateError,
  NoQuestionDefError,
  ActionError,
  ServicePrincipalTokenError,
  OrgsHaveNoTokenRegisteredError,
};

export default {
  ValidationError,
  ForbiddenError,
  UnauthorizedError,
  InternalServerError,
  ApiError,
  NoCapacitiesError,
  NoDatasetsError,
  NoDatasourcesError,
  ReportsError,
  DatabaseError,
  OrgHasNoTokenRegisteredError,
  OrgNameAlreadyExists,
  OrgHasTokenConfig,
  UnknownTemplateError,
  NoQuestionDefError,
  ActionError,
  ServicePrincipalTokenError,
  OrgsHaveNoTokenRegisteredError,
};
