version: '3'
services:
  app:
    image: 026972849384.dkr.ecr.us-east-1.amazonaws.com/illuminate-app:latest
    container_name: illuminate-app
    ports:
      - "9000:9000"
    environment:
      - ENVIRONMENT=dev
      - APPLICATION=illuminate-app
  e2e:
    image: illuminate-e2e
    container_name: illuminate-e2e
    depends_on:
      - app
    links:
        - "app:local.veritone.com"
    environment:
      - CYPRESS_BASE_URL=http://local.veritone.com:9000
      - CYPRESS_apiRoot=https://api.dev.us-1.veritone.com
      - CYPRESS_username=${e2e_user}
      - CYPRESS_password=${e2e_password}
