import React from 'react';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import IconButton from '@mui/material/IconButton';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import Delete from '@mui/icons-material/Delete';
import makeStyles from '@mui/styles/makeStyles';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTime } from 'luxon';
import styles from './styles';
import { ContentTemplate } from '../../state/modules/uploadFile/models';
import { isNil } from 'lodash';

const useStyles = makeStyles(styles);
function FormAddContentTemplate({
  contentTemplate,
  onChange,
  removeContentTemplate,
  checkValidateTemplate,
}: Props) {
  const classes = useStyles();
  const records = (contentTemplate?.schemas?.records || []).find(
    (item) => item.status === 'published'
  );
  const properties = records?.definition?.properties ?? {};
  const required = records?.definition?.required ?? [];
  const onDateTimeChange = (
    value: DateTime | null | string,
    contentTemplateId: string,
    name: string
  ) => {
    onChange({
      value,
      contentTemplateId,
      name,
    });
  };
  const onTextFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { id: contentTemplateId, name, value } = event.target;
    onChange({
      value,
      contentTemplateId,
      name,
    });
  };
  const strictDateTime = (value: DateTime | null | undefined | string) => {
    if (isNil(value) || typeof value === 'string') {
      return null;
    }
    return value;
  };
  return (
    <Card
      className={classes.cardContentTemplate}
      data-testid="content-template-form"
    >
      <CardHeader
        action={
          <IconButton
            data-id={contentTemplate.id}
            onClick={removeContentTemplate}
            data-testid="remove-content-template-button"
            size="large"
          >
            <Delete />
          </IconButton>
        }
        title={contentTemplate.name}
        className={classes.cardHeaderContentTemplate}
      />

      <CardContent className={classes.cardMainContentTemplate}>
        {Object.keys(properties).map((item) => {
          const type = properties?.[item]?.type ?? '';
          if (type === 'dateTime') {
            return (
              <FormControl key={item} className={classes.formContentTemplate}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateTimePicker
                    label={item}
                    value={strictDateTime(contentTemplate?.data?.[item])}
                    onChange={(value: DateTime | null) =>
                      onDateTimeChange(value, contentTemplate.id, item)
                    }
                    timeSteps={{
                      hours: 1,
                      minutes: 1,
                      seconds: 5,
                    }}
                    slotProps={{
                      textField: {
                        variant: 'standard',
                        onKeyDown: (e) => {
                          e.preventDefault();
                        },
                        required: true,
                        error:
                          required.includes(item) &&
                          checkValidateTemplate &&
                          !contentTemplate?.data?.[item]
                            ? true
                            : false,
                      },
                    }}
                  />
                </LocalizationProvider>
              </FormControl>
            );
          }
          return (
            <FormControl key={item} className={classes.formContentTemplate}>
              <TextField
                id={contentTemplate.id}
                required={required.includes(item)}
                name={item}
                label={item}
                value={contentTemplate?.data?.[item]}
                onChange={onTextFieldChange}
                error={
                  required.includes(item) &&
                  checkValidateTemplate &&
                  !contentTemplate?.data?.[item]
                    ? true
                    : false
                }
                helperText=""
                data-test={item}
                slotProps={{
                  htmlInput: { 'data-testid': 'content-template-field' },
                }}
                variant="standard"
                type={`${type === 'integer' ? 'number' : 'text'}`}
                onKeyDown={(e) => {
                  if (
                    type === 'integer' &&
                    (e.key === 'e' ||
                      e.key === 'E' ||
                      e.key === '.' ||
                      e.key === ',')
                  ) {
                    e.preventDefault();
                  }
                }}
              />
            </FormControl>
          );
        })}
      </CardContent>
    </Card>
  );
}
interface Props {
  contentTemplate: ContentTemplate;
  onChange: ({
    value,
    contentTemplateId,
    name,
  }: {
    value: DateTime | null | string;
    contentTemplateId: string;
    name: string;
  }) => void;
  removeContentTemplate: (event: React.MouseEvent<HTMLElement>) => void;
  checkValidateTemplate: boolean;
}
export default FormAddContentTemplate;
