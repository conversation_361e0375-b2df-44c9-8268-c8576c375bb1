// adapted from
// https://decembersoft.com/posts/file-upload-progress-with-redux-saga/

import { buffers, channel, END, Channel } from 'redux-saga';
interface UploadDescriptor {
  bucket: string;
  expiresInSeconds: number;
  getUrl: string;
  key: string;
  unsignedUrl: string;
  url: string;
}

export interface UploadFilesChannelData {
  progress?: number;
  file: File;
  descriptor: UploadDescriptor;
  success?: boolean;
  aborted?: string;
  error?: string;
}

export function uploadFilesChannel(
  uploadDescriptors: UploadDescriptor[],
  files: File[],
  method = 'PUT'
) {
  if (uploadDescriptors.length !== files.length) {
    throw new Error('Need an upload descriptor for each file to be uploaded!');
  }

  const requestMap: Record<string, XMLHttpRequest> = {};
  const chan: Channel<UploadFilesChannelData> = channel(buffers.sliding(2));
  let remainingFiles = files.length;

  const onFileProgress = (
    file: File,
    descriptor: UploadDescriptor,
    {
      lengthComputable,
      loaded,
      total,
    }: { lengthComputable: boolean; loaded: number; total: number }
  ) => {
    // Keep session alive via SDK call while uploading files
    window.aiware?.auth?.reportAppActivity?.();

    if (lengthComputable) {
      const progress = (loaded / total) * 100;
      chan.put({ progress, file, descriptor });
    }
  };

  const onStatusCodeFailure = (file: File, descriptor: UploadDescriptor) => {
    chan.put({ error: 'Upload failed', file, descriptor });
  };

  const onXHRError = (
    file: File,
    descriptor: UploadDescriptor,
    _e: unknown
  ) => {
    chan.put({ error: 'File upload error', file, descriptor });
  };

  const onStatusCodeAbort = (file: File, descriptor: UploadDescriptor) => {
    chan.put({
      error: 'Upload failed',
      aborted: 'Upload aborted',
      file,
      descriptor,
    });
  };

  const onFileReadyStateChange = (
    file: File,
    descriptor: UploadDescriptor,
    { readyState, status }: XMLHttpRequest
  ) => {
    if (readyState === XMLHttpRequest.DONE) {
      remainingFiles -= 1;
      // Remove from requestMap cuz it finished
      delete requestMap[descriptor.key];

      if (status >= 200 && status < 300) {
        chan.put({ success: true, file, descriptor });
      } else if (status === 0) {
        onStatusCodeAbort(file, descriptor);
      } else {
        onStatusCodeFailure(file, descriptor);
      }

      if (remainingFiles === 0) {
        chan.put(END);
      }
    }
  };

  files.forEach((file, i) => {
    const descriptor = uploadDescriptors[i]!; // Safe because of prior file -> descriptors length check
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener(
      'progress',
      onFileProgress.bind(null, file, descriptor)
    );
    xhr.upload.addEventListener(
      'error',
      onXHRError.bind(null, file, descriptor)
    );
    xhr.onreadystatechange = onFileReadyStateChange.bind(
      null,
      file,
      descriptor,
      xhr
    );
    // Add to requestMap to enable abortions
    if (descriptor.key) {
      requestMap[descriptor.key] = xhr;
    }

    xhr.open(method, descriptor.url, true);
    // Need this header for azure
    xhr.setRequestHeader('x-ms-blob-type', 'BlockBlob');
    // chrome sets file type to video/x-ms-wma for wma, which breaks
    // SI2 Playback segment creator V3F (352556c7-de07-4d55-b33f-74b1cf237f25)
    // the standard format should be audio/x-ms-wma or video/x-ms-wmv
    const contentType = standardizeFileType(file.type);
    xhr.setRequestHeader('Content-Type', contentType);
    xhr.send(file);
  });

  return {
    channel: chan,
    requestMap,
  };
}

// chrome sets file type to video/x-ms-wma for wma, which breaks
// SI2 Playback segment creator V3F (352556c7-de07-4d55-b33f-74b1cf237f25)
// the standard format should be audio/x-ms-wma for audio or video/x-ms-wmv
// for video
export function standardizeFileType(fileType: string) {
  if (fileType === 'video/x-ms-wma') {
    return 'audio/x-ms-wma';
  }
  return fileType;
}
