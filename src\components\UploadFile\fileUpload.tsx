import React from 'react';
import ListItemText from '@mui/material/ListItemText';
import ListItem from '@mui/material/ListItem';
import List from '@mui/material/List';
import IconButton from '@mui/material/IconButton';
import CloudUpload from '@mui/icons-material/CloudUpload';
import ListItemIcon from '@mui/material/ListItemIcon';
import Add from '@mui/icons-material/Add';
import Edit from '@mui/icons-material/Edit';
import Delete from '@mui/icons-material/Delete';
import makeStyles from '@mui/styles/makeStyles';
import styles from './styles';
import ListFileUpload from './listFile';
import { UploadResult } from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);
function FileUpload({
  isShowListFile,
  handlePick,
  handleEditFile,
  handleRemoveFile,
  checkedFile,
  uploadResult,
  handleToggle,
  pickerComponent,
}: Props) {
  const classes = useStyles();
  return (
    <div className={classes.mainUpload}>
      <div className={classes.mainUploadHeader}>
        <List data-testid="list">
          <ListItem
            className={classes.iconUploadHeader}
            data-testid="list-item"
            secondaryAction={
              isShowListFile && (
                <>
                  <IconButton
                    data-type={'uploadFile'}
                    onClick={handlePick}
                    size="large"
                  >
                    <Add data-testid="add-file" />
                  </IconButton>
                  <IconButton
                    onClick={handleEditFile}
                    disabled={!checkedFile.length}
                    data-test="edit-file"
                    size="large"
                  >
                    <Edit data-testid="edit-file" />
                  </IconButton>
                  <IconButton
                    onClick={handleRemoveFile}
                    disabled={!checkedFile.length}
                    size="large"
                  >
                    <Delete data-testid="remove-file" />
                  </IconButton>
                </>
              )
            }
          >
            <ListItemIcon data-testid="list-item-icon">
              <CloudUpload />
            </ListItemIcon>
            <ListItemText
              className={classes.titleUploadFile}
              primary="Upload Media"
              data-testid="list-item-text"
            />
          </ListItem>
        </List>
      </div>
      <div className={classes.mainUploadBody}>
        {pickerComponent}
        {isShowListFile && (
          <ListFileUpload
            data={uploadResult}
            checked={checkedFile}
            handleToggle={handleToggle}
            indeterminate={
              !!checkedFile.length && checkedFile.length < uploadResult.length
            }
            checkedAll={!!checkedFile.length}
          />
        )}
      </div>
    </div>
  );
}
interface Props {
  isShowListFile: boolean;
  handlePick: (event: React.MouseEvent<HTMLElement>) => void;
  handleEditFile: () => void;
  handleRemoveFile: () => void;
  checkedFile: number[];
  uploadResult: UploadResult[];
  handleToggle: (event: React.MouseEvent<HTMLElement>) => void;
  pickerComponent: React.ReactNode;
}
export default FileUpload;
