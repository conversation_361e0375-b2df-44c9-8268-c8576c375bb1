# Define build type
ARG K8S_BUILD=FALSE
ARG BASE_IMAGE=registry.central.aiware.com/fed-nginx:latest
ARG NODE_VERSION=22

ARG app_name=illuminate-app

FROM node:${NODE_VERSION} AS builder
ARG app_name
ENV APPLICATION=$app_name

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq libpango1.0-dev && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app && \
    rm -rf /var/lib/apt/lists/*
COPY . /app
WORKDIR /app

RUN ls -a && \
    chmod +x /app/*.sh && \
    yarn && \
    yarn build

ENV APPLICATION=illuminate-app
RUN echo '### /app/buildinfo.sh...' && /app/buildinfo.sh

FROM node:${NODE_VERSION} AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app && \
    rm -rf /var/lib/apt/lists/*
RUN mkdir -p /app/api
WORKDIR /app/api
COPY server/package.json .
COPY server/.yarnrc.yml .
COPY server/yarn.lock .
RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .
RUN yarn
RUN yarn build
COPY server/package.json ./dist
COPY server/yarn.lock ./dist
WORKDIR /app/api/dist
RUN yarn workspaces focus --production
# VE-9906 fix vulnerabilities by remove tools folder
RUN rm -rfv /app/api/node_modules/@veritone/functional-permissions-lib/tools
RUN rm -rfv /app/api/dist/node_modules/@veritone/functional-permissions-lib/tools

# Set the final base image based on build arg BASE_NAME
FROM ${BASE_IMAGE} AS final

# Set the user to root
USER root

# Create a non-root user
RUN if command -v addgroup > /dev/null; then \
      addgroup --system appgroup && adduser --system --ingroup appgroup appuser; \
    else \
      groupadd --system appgroup && useradd --system --gid appgroup appuser; \
    fi

ARG K8S_BUILD
ARG NODE_VERSION

# Check if K8S_BUILD is TRUE or FALSE and check if the package manager is apk or dnf
RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo ncurses curl && \
        apk del tar; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo ncurses curl && \
        dnf remove -y tar && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    else \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache nginx envsubst && \
        mkdir -p /etc/nginx/conf.d && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo ncurses curl && \
        apk del tar; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y nginx gettext && \
        mkdir -p /etc/nginx/conf.d && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo ncurses curl && \
        dnf remove -y tar && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    fi

RUN node -v && npm -v

ARG app_name
ENV APPLICATION=$app_name
ENV NGINX_PORT=9000

EXPOSE ${NGINX_PORT}/tcp

# Regular Build
COPY --from=builder /app/getconfig-dynamicConfig.sh /getconfig-dynamicConfig.sh
COPY --from=builder /app/build-manifest.yml /build-manifest.yml
COPY --from=builder /app/build-manifest.yml /opt/build-manifest.yml
COPY --from=builder /app/configWhitelist.json /configWhitelist.json
COPY --from=builder /app/dynamicConfig-index-html.sh /dynamicConfig-index-html.sh
COPY --from=builder /app/entrypoint.sh /entrypoint.sh
COPY --from=builder /app/static /usr/share/nginx/html
COPY --from=builder /app/build-local /usr/share/nginx/html
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

COPY --from=backend /app/api/dist /api
COPY --from=backend /app/api/apiConfigWhitelist.json /apiConfigWhitelist.json
COPY --from=backend /app/api/apiConfigWhitelist.json /api/apiConfigWhitelist.json

# Copy nginx.conf to a temporary location
COPY --from=builder /app/nginx.conf /etc/nginx/conf.d/default.conf.temp

# K8S Build
COPY --from=builder /app/configWhitelist.json /usr/share/nginx/html/aiware-config.json
COPY --from=builder /app/dynamicConfig-index-html-k8s.sh /dynamicConfig-index-html-k8s.sh
COPY --from=builder /app/entrypoint-k8s.sh /entrypoint-k8s.sh
COPY --from=builder /app/nginx.k8s.conf /etc/nginx.conf.template

COPY --from=registry.central.aiware.com/aiware-spa:latest /usr/share/nginx/config /usr/share/nginx/html/config
COPY --from=registry.central.aiware.com/aiware-spa:latest /usr/share/nginx/config /api/config

ARG K8S_BUILD
# Merge entrypoint.sh, remove unnecessary files, and chmod configs
RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
        mv /etc/nginx/conf.d/default.conf.temp /etc/nginx/conf.d/default.conf; \
        rm /entrypoint-k8s.sh; \
        rm /usr/share/nginx/html/aiware-config.json; \
        rm /etc/nginx.conf.template; \
        rm /dynamicConfig-index-html-k8s.sh; \
    else \
        mv /entrypoint-k8s.sh /entrypoint.sh; \
        chmod +x /usr/share/nginx/html/config && \
        chmod +x /api/config && \
        chmod +x /entrypoint.sh; \
    fi

RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
        chown -R appuser:appgroup /tmp && chmod 700 /tmp && \
        chown appuser:appgroup /getconfig-dynamicConfig.sh && chmod 700 /getconfig-dynamicConfig.sh && \
        chown appuser:appgroup /dynamicConfig-index-html.sh && chmod 700 /dynamicConfig-index-html.sh; \
    else \
        chown appuser:appgroup /dynamicConfig-index-html-k8s.sh && chmod 700 /dynamicConfig-index-html-k8s.sh; \
    fi

RUN chown appuser:appgroup /entrypoint.sh && chmod 700 /entrypoint.sh && \
    chown -R appuser:appgroup /usr/share/nginx/html && chmod 700 /usr/share/nginx/html && \
    chown -R appuser:appgroup /api && chmod 700 /api && \
    chown -R appuser:appgroup /etc/nginx/conf.d

# Create necessary directories and set ownership
RUN mkdir -p /config && chown -R appuser:appgroup /config && \
    mkdir -p /var/cache/nginx && chown -R appuser:appgroup /var/cache/nginx

# Remove setuid and setgid bits from files
RUN find / -perm /6000 -type f -exec chmod a-s {} \; || true

# Set the user to appuser
USER appuser

HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:${NGINX_PORT}/ || exit 1

ENTRYPOINT ["/entrypoint.sh"]
