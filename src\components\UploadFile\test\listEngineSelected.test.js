import React from 'react';
import ListEngineSelected from '../listEngineSelected';
import { render, fireEvent } from '@testing-library/react';
describe('ListEngineSelected', () => {
  const props = {
    item: {
      categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
      categoryName: 'Transcription',
      engineIds: [
        {
          id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
          description: 'This engine converts US English speech to text.',
          edgeVersion: 3,
          mode: 'Chunk',
          name: 'Speechmatics Transcription - English (Global) V3',
          runtimeType: 'edge',
          standaloneJobTemplates: [],
          expand: true,
          libraryRequired: true,
          librariesSelected: 'library test two',
          fields: [
            {
              defaultValue: 'true',
              info: 'diarise',
              label: 'diarise',
              max: null,
              min: null,
              name: 'diarise',
              step: null,
              type: 'Picklist',
              options: [
                {
                  keys: 'false',
                  value: 'false',
                },
                {
                  keys: 'true',
                  value: 'true',
                },
              ],
            },
          ],
        },
      ],
    },
    librariesByCategories: {
      '67cd4dd0-2f75-445d-a6f0-2f297d6cd182': {
        '4e48925b-2cfc-43b3-8794-53e6e683651f': {
          coverImageUrl: '',
          createdDateTime: '2021-01-12T09:40:23.000Z',
          id: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          libraryId: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          name: 'library test',
          organizationId: '1',
          summary: { entityCount: 1, unpublishedEntityCount: 0 },
          version: 1,
        },
        '22222222-3333-4444-5555-66667777651f': {
          coverImageUrl: '',
          createdDateTime: '2021-01-12T09:40:23.000Z',
          id: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          libraryId: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          name: 'library test two',
          organizationId: '2',
          summary: { entityCount: 2, unpublishedEntityCount: 2 },
          version: 1,
        },
      },
    },
    handleRemoveEngine: jest.fn(),
    handleChangeLibrariesEngineSelected: jest.fn(),
    handleChangeFieldsEngine: jest.fn(),
    handleExpandClick: jest.fn(),
    checkValidateLibrary: false,
  };

  it('renders a title with content Transcription', () => {
    const { getByText } = render(<ListEngineSelected {...props} />);
    expect(getByText('Transcription')).toBeInTheDocument();
  });
  it('renders a engine selected card', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    expect(getByTestId('engine-selected-card')).toBeInTheDocument();
  });
  it('renders a engine selected card header', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    expect(getByTestId('engine-selected-card-header')).toBeInTheDocument();
  });
  it('renders a engine selected card content', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    expect(getByTestId('engine-selected-card-content')).toBeInTheDocument();
  });
  it('click expand Button', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    fireEvent.click(getByTestId('expand-click'));
    expect(props.handleExpandClick).toHaveBeenCalled();
  });
  it('click remove engine Button', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    fireEvent.click(getByTestId('remove-engine-click'));
    expect(props.handleRemoveEngine).toHaveBeenCalled();
  });
  it('renders a title with content Speechmatics Transcription - English (Global) V3', () => {
    const { getByText } = render(<ListEngineSelected {...props} />);
    expect(
      getByText('Speechmatics Transcription - English (Global) V3')
    ).toBeInTheDocument();
  });
  it('renders a description with content This engine converts US English speech to text.', () => {
    const { getByText } = render(<ListEngineSelected {...props} />);
    expect(
      getByText('This engine converts US English speech to text.')
    ).toBeInTheDocument();
  });
  it('renders a input label with content Choose Libraries', () => {
    const { getByText } = render(<ListEngineSelected {...props} />);
    expect(getByText('Choose Libraries')).toBeInTheDocument();
  });
  it('renders a Select libraries', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    expect(getByTestId('select-libraries')).toBeInTheDocument();
  });
  it('click select libraries', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    fireEvent.change(getByTestId('select-libraries'), {
      target: { value: 'library test' },
    });
    expect(props.handleChangeLibrariesEngineSelected).toHaveBeenCalled();
  });
  it('renders a Select engine', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    expect(getByTestId('fields-engine')).toBeInTheDocument();
  });
  it('click fields engine', () => {
    const { getByTestId } = render(<ListEngineSelected {...props} />);
    fireEvent.change(getByTestId('fields-engine'), {
      target: { value: 'false' },
    });
    expect(props.handleChangeFieldsEngine).toHaveBeenCalled();
  });
});
