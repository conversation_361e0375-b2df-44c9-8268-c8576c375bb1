import { TableRow } from '../types';

export const tables = {
  ContactStopData: 'ContactStopData',
  TokenConfiguration: 'TokenConfiguration',
  PbixTemplates: 'PbixTemplates',
  CustomQuestionAnswers: 'CustomQuestionAnswers',
  CustomQuestionDefinitions: 'CustomQuestionDefinitions',
};

export interface CustomQuestionDefinitionsRow extends TableRow {
  contactId: string;
  orgId: number;
  component: string;
  title: string;
  options: string;
  required: boolean;
  disabled: boolean;
  disabledDate: string;
  resultPath: string;
}

export interface CustomQuestionAnswersRow extends TableRow {
  recordId: number; // contact stop id
  orgId: number;
  personNumber: number;
  questionDefinitionId: number; // id of question in CC Defs
  answer: string; // CSV formatted if array
  questionKey: string; // the result path the question is saved to
}

export interface PbixTemplateRow extends TableRow {
  id: number;
  name: string;
  description: string;
  pbixFileName: string;
  created: string;
  modifed: string;
}

export interface TokenConfigurationRow extends TableRow {
  orgId: string;
  reportId: string;
  datasetId: string;
  profileId: string;
  workspaceId: string;
  embedUrl: string;
  lifetimeInMinutes: number;
  pbixFilePath: string;
}

export type TokenConfigurationRows = TokenConfigurationRow[];

export interface ContactStopDataRow extends RowType {
  recordId: string;
  orgId: number;
  basisForPropertySeizure: string;
  officerYearsOfExperience: number;
  basisForSearchNarrative: string;
  gender: string;
  ethnicity: string;
  city: string;
  disability: string;
  timeOfStop: string;
  reasonsForStop: string;
  personNumber: number;
  k12_school: boolean;
  basisForSearch: string;
  custodialArrestOffCode: string;
  citationOffCode: string;
  school: string;
  warningOffCode: string;
  responseToServiceCall: boolean;
  officerTypeOfAssignment: string;
  inFieldCiteAndReleaseCode: string;
  trafficViolationOffenseCode: string;
  trafficViolation: string;
  ecSubdivision: string;
  durationOfStop: number;
  reasonableSuspicion: string;
  suspicionOffenseCode: string;
  limitedEnglish: boolean;
  disciplineUnderEc: string;
  leaRecordId: string;
  contrabandOrEvidence: string;
  officerOtherAssignmentType: string;
  resultOfStop: string;
  actionsTakenDuringStop: string;
  datetimeOfStop: string;
  stopForAStudent: boolean;
  typeOfPropertySeized: string;
  location: string;
  genderNonconforming: boolean;
  reasonForStopNarrative: string;
  age: number;
  ethnicityExclusive: string;
  lgbt: boolean;
  raceOfOfficer: string;
  sexualOrientation: string;
  nonConforming: boolean;
  typeOfStop: string;
  unhoused: boolean;
  officerWorksWithNonPrimaryAgency: boolean;
  reasonGivenStoppedPerson: string;
  stopDuringWellnessCheck: boolean;
  typeOfAssignmentOfficer: string;
  stoppedPassenger: boolean;
  stoppedInsideResidence: boolean;
  consentType: string;
  probableCause: string;
  probableCauseCode: string;
  datetimeOfStopPST: string;
}

export const cacheKeys = {
  pbiToken: 'pbiBearerToken',
  azBlobToken: 'azBlobToken',
};
