import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
      if (tableExists) {
        const hasColumn = knex.schema.hasColumn('ContactStopData', 'nonBinaryOfficer')

        if (hasColumn) {
          await knex.schema.raw(`ALTER TABLE ContactStopData DROP COLUMN leaRecordId;`);

          return true;
        }
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('ContactStopData')
  ])
