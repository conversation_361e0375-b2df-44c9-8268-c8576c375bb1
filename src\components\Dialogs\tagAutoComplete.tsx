import React from 'react';
import NoSsr from 'react-no-ssr';
import Chip, { ChipProps } from '@mui/material/Chip';
import * as styles from './tagAutoComplete.scss';
import {
  CSSObjectWithLabel,
  GroupBase,
  MultiValue,
  MultiValueProps,
} from 'react-select';
import Creatable from 'react-select/creatable';

const MultiValueComponent = (
  props: MultiValueProps<Tag, true, GroupBase<Tag>>
) => {
  return (
    <Chip
      label={props.children ? props.children : props.data?.value}
      onDelete={props.removeProps?.onClick}
    />
  );
};

const IntegrationReactSelect = (props: Props) => {
  const { initialTagList, onTagsChange, menuIsOpen, onInputChange } = props;
  let tagsList;
  if (initialTagList) {
    tagsList = initialTagList.map((tag) => ({ value: tag, label: tag }));
  }
  const style = {
    control: (base: CSSObjectWithLabel) => ({
      ...base,
      margin: 10,
      border: 'none',
      borderBottom: '0.5px solid gray',
      borderRadius: 'none',
      boxShadow: 'none',
      '&:hover': {
        borderBottom: '2px solid black',
      },
    }),
    dropdownIndicator: (base: CSSObjectWithLabel, _state: any) => ({
      ...base,
      display: 'none',
    }),
    indicatorSeparator: (base: CSSObjectWithLabel, _state: any) => ({
      ...base,
      display: 'none',
    }),
  };
  return (
    <div
      data-test="tag-autocomplete"
      className={styles['autocomplete-container']}
    >
      <NoSsr>
        <Creatable
          isMulti
          menuIsOpen={menuIsOpen}
          onChange={onTagsChange}
          options={tagsList}
          components={{ MultiValue: MultiValueComponent }}
          onInputChange={onInputChange}
          defaultValue={props.value}
          styles={style}
          className="react-select-container"
        />
      </NoSsr>
    </div>
  );
};

interface Tag {
  value: string;
  key?: string;
}

interface Props {
  initialTagList: string[];
  onTagsChange: (Tags: MultiValue<Tag>) => void;
  value: Tag[];
  menuIsOpen: boolean;
  onInputChange: (tag: string) => void;
  tdoIdSelected: string[];
  children?: React.ReactNode;
  data?: { value: any };
  removeProps?: {
    onClick: ChipProps['onClick'];
  };
}

export default IntegrationReactSelect;
