import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
      if (tableExists) {
        const hasAllColumns = await Promise.allSettled([
          knex.schema.hasColumn('ContactStopData', 'genderOfOfficer'),
          knex.schema.hasColumn('ContactStopData', 'nonBinaryOfficer'),
        ]);

        const hasAllColumnsResult = hasAllColumns.every((result) => result.status === 'fulfilled' && result.value);

        if (!hasAllColumnsResult) {
          await knex.schema.raw(`ALTER TABLE ContactStopData ADD 
            genderOfOfficer varchar(256),
            nonBinaryOfficer bit;`
          );

          return true;
        }
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('ContactStopData')
  ])
