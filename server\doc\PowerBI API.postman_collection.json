{"info": {"_postman_id": "ec4eebe5-0fdb-4435-b3ce-17c67c3fbd61", "name": "PowerBI API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "4480694"}, "item": [{"name": "New Tenant Workflow", "item": [{"name": "Get Service Principal To<PERSON>", "event": [{"listen": "test", "script": {"exec": ["var res = JSON.parse(responseBody);", "pm.globals.set(\"oAuthBearerToken\", res.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "client_id", "value": "aaf1f70d-c139-4b99-9d53-9a20585f4958", "type": "text"}, {"key": "response_type", "value": "code", "type": "text"}, {"key": "response_mode", "value": "query", "type": "text"}, {"key": "scope", "value": "", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "aaf1f70d-c139-4b99-9d53-9a20585f4958", "type": "text"}, {"key": "client_secret", "value": "2_Dr6EeUK2Hy5-..1uGl7w6EwF-GG4iEd5", "type": "text"}, {"key": "scope", "value": "https://high.analysis.usgovcloudapi.net/powerbi/api/.default", "type": "text"}, {"key": "scope\n", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "https://login.microsoftonline.us/2b6ba87b-e3cb-4547-bd91-e4160125db25/oauth2/v2.0/token", "protocol": "https", "host": ["login", "microsoftonline", "us"], "path": ["2b6ba87b-e3cb-4547-bd91-e4160125db25", "oauth2", "v2.0", "token"]}}, "response": []}, {"name": "Create Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"displayName\": \"FirstProfile\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/profiles", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "profiles"]}}, "response": []}, {"name": "Create Workspace (as profile)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"ProfileWorkspace2\"\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups"], "query": [{"key": "workspaceV2", "value": "", "description": "boolean (Optional) Preview feature: Create a workspace V2. The only supported value is true.", "disabled": true}]}}, "response": []}, {"name": "Get Capacities (as profile)", "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/capacities", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "capacities"]}}, "response": []}, {"name": "Assign Capacity To Workspace (as profile)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"capacityId\": \"F10FA8CE-0ACF-4A5C-9705-9B16383417FB\"\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/AssignToCapacity", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "AssignToCapacity"]}}, "response": []}, {"name": "Add My User To Workspace (optional)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n\t\"groupUserAccessRight\": \"Admin\",\n\t\"identifier\": \"1913c56e-53e8-4f0a-8161-a03e9bf0ca8d\",\n\t\"principalType\": \"App\",\n    \"emailAddress\": \"<EMAIL>\"\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/a12be971-5408-46e8-8063-39f0269a26de/users", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "a12be971-5408-46e8-8063-39f0269a26de", "users"]}}, "response": []}, {"name": "Upload PBIX File to Workspace (as profile)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "", "type": "file", "src": "/Users/<USER>/TemplateReportDesktop.pbix"}]}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/imports?datasetDisplayName=UploadedTemplateReport&nameConflict=Abort", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "imports"], "query": [{"key": "datasetDisplayName", "value": "UploadedTemplateReport"}, {"key": "nameConflict", "value": "Abort"}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get Datasets in Workspace (as profile)", "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/datasets", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "datasets"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get Datasource Gateway (as profile)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/datasets/fe120cdf-8a70-4327-9284-bbb122b34bf8/datasources", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "datasets", "fe120cdf-8a70-4327-9284-bbb122b34bf8", "datasources"]}}, "response": []}, {"name": "Get Reports In Workspace", "request": {"method": "GET", "header": [{"key": "", "value": "", "type": "text", "disabled": true}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/reports", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "reports"]}}, "response": []}, {"name": "Set Dataset Datasource Credentials (as profile)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"credentialDetails\": {\n    \"credentials\": \"{\\\"credentialData\\\":[{\\\"name\\\":\\\"username\\\",\\\"value\\\":\\\"xxxx\\\"},{\\\"name\\\":\\\"password\\\",\\\"value\\\":\\\"xxxx\\\"}]}\",\n    \"credentialType\": \"Basic\",\n    \"encryptedConnection\": \"Encrypted\",\n    \"encryptionAlgorithm\": \"None\",\n    \"privacyLevel\": \"Private\"\n  }\n}\n"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/gateways/3a8f2e8d-eb4b-4eab-a5c7-86fbc28d64fd/datasources/c2801149-1198-4863-b159-530808198622", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "gateways", "3a8f2e8d-eb4b-4eab-a5c7-86fbc28d64fd", "datasources", "c2801149-1198-4863-b159-530808198622"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "Reports GenerateTokenInGroup (as profile)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"datasets\": [\n    {\n      \"id\": \"16fa1ac6-1beb-4e7a-85c1-a971fd6cd7c7\"\n    }\n  ],\n  \"reports\": [\n    {\n      \"id\": \"cd64b2e7-9149-4c45-a2b2-ee0755801da1\"\n    }\n  ],\n  \"identities\": [\n    {\n      \"username\": \"79\",\n      \"roles\": [\n        \"Org\"\n      ],\n      \"datasets\": [\n        \"16fa1ac6-1beb-4e7a-85c1-a971fd6cd7c7\"\n      ]\n    }\n  ],\n  \"lifetimeInMinutes\": 100\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/reports/cd64b2e7-9149-4c45-a2b2-ee0755801da1/GenerateToken", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "reports", "cd64b2e7-9149-4c45-a2b2-ee0755801da1", "GenerateToken"], "query": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "disabled": true}, {"key": "X-PowerBI-Profile-Id", "value": "a759b45f-7d00-429f-a4de-e99554a9be84", "disabled": true}]}}, "response": []}]}, {"name": "Other", "item": [{"name": "_Add Profile To Workspace (as profile)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "61b95607-ba42-4ec0-8384-4a0db5f04e5b", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"groupUserAccessRight\": \"Admin\",\n    \"identifier\": \"d6a4b83d-f329-4953-9b62-e8778cace6d1\",\n    \"principalType\": \"App\",\n    \"profile\": {\n        \"id\": \"61b95607-ba42-4ec0-8384-4a0db5f04e5b\"\n    }\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/d77437dd-ccf9-488a-9bcd-42e54939787f/users", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "d77437dd-ccf9-488a-9bcd-42e54939787f", "users"]}, "description": "identifier is Service Principal Object ID"}, "response": []}, {"name": "Get Groups (as profile)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "2f62df59-9443-4abf-8989-cbed886ba97c", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups"], "query": [{"key": "$filter", "value": "", "description": "string (Optional) Filters the results, based on a boolean condition", "disabled": true}, {"key": "$skip", "value": "", "description": "integer int32 (Optional) Skips the first n results", "disabled": true}, {"key": "$top", "value": "", "description": "integer int32 (Optional) Returns only the first n results", "disabled": true}]}}, "response": []}, {"name": "Get Profiles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/profiles", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "profiles"]}}, "response": []}, {"name": "Set Dataset Datasource (as profile)", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n  \"updateDetails\": [\n    {\n      \"datasourceSelector\": {\n        \"datasourceType\": \"Sql\",\n        \"connectionDetails\": {\n          \"server\": \"powerbi-contact.database.usgovcloudapi.net\",\n          \"database\": \"powerbi-contact\"\n        }\n      }\n    }\n  ]\n}"}, "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/a12be971-5408-46e8-8063-39f0269a26de/datasets/e6d3a3f4-e786-4818-a2cf-a6c35417ba29/Default.UpdateDatasources", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "a12be971-5408-46e8-8063-39f0269a26de", "datasets", "e6d3a3f4-e786-4818-a2cf-a6c35417ba29", "Default.UpdateDatasources"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}]}, {"name": "Delete Everything Workflow", "item": [{"name": "Get All Profiles", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "2f62df59-9443-4abf-8989-cbed886ba97c", "type": "text", "disabled": true}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/profiles", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "profiles"], "query": [{"key": "$filter", "value": "", "description": "string (Optional) Filters the results, based on a boolean condition", "disabled": true}, {"key": "$skip", "value": "", "description": "integer int32 (Optional) Skips the first n results", "disabled": true}, {"key": "$top", "value": "", "description": "integer int32 (Optional) Returns only the first n results", "disabled": true}]}}, "response": []}, {"name": "Get Workspaces (as profile)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "bf050702-ebc4-4875-b11b-05c4b78d38ee", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups"], "query": [{"key": "$filter", "value": "", "description": "string (Optional) Filters the results, based on a boolean condition", "disabled": true}, {"key": "$skip", "value": "", "description": "integer int32 (Optional) Skips the first n results", "disabled": true}, {"key": "$top", "value": "", "description": "integer int32 (Optional) Returns only the first n results", "disabled": true}]}}, "response": []}, {"name": "Delete Workspace (as profile)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "X-PowerBI-Profile-Id", "value": "bf050702-ebc4-4875-b11b-05c4b78d38ee", "type": "text"}], "url": {"raw": "https://api.high.powerbigov.us/v1.0/myorg/groups/21d5a8bb-1f25-4a8f-95e6-379b425b0007", "protocol": "https", "host": ["api", "high", "powerbigov", "us"], "path": ["v1.0", "myorg", "groups", "21d5a8bb-1f25-4a8f-95e6-379b425b0007"], "query": [{"key": "$filter", "value": "", "description": "string (Optional) Filters the results, based on a boolean condition", "disabled": true}, {"key": "$skip", "value": "", "description": "integer int32 (Optional) Skips the first n results", "disabled": true}, {"key": "$top", "value": "", "description": "integer int32 (Optional) Returns only the first n results", "disabled": true}]}}, "response": []}, {"name": "Remove all", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "bearer c333e0ef-11ac-440a-9337-c33dc84f48be", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"code\":  \"its ok, i know what i am doing 🤟\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:3002/api/v1/powerbi/removeAllPowerBiData", "protocol": "https", "host": ["localhost"], "port": "3002", "path": ["api", "v1", "powerbi", "removeAllPowerBiData"]}}, "response": []}]}]}