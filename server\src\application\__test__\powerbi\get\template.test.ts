import supertest from 'supertest';
import { request } from 'graphql-request';
import axios from 'axios';
import expressApp from '../../setupExpress';
import { templateRecordMock, adminPermissionMasksMock } from '../fixtures';

jest.mock('graphql-request', () => ({ request: jest.fn() }));
jest.mock('axios', () => ({ post: jest.fn(), get: jest.fn(), patch: jest.fn() }));
jest.mock('../../../powerbi/queries', () => () => ({
  getAllTemplates: () => Promise.resolve([templateRecordMock]),
}));
jest.mock('uuid', () => ({ v4: () => '1234565678abcdef' }));

describe('GET /api/v1/powerbi/template', () => {

  (request as jest.Mock).mockImplementation(({ document }) => {
    if (document.includes('validateToken')) {
      return Promise.resolve({ validateToken: { token: 'valid token' } });
    }
    if (document.includes('me')) {
      return Promise.resolve({ me: { organizationId: 123 } });
    }
    return Promise.resolve()
  });

  (axios.post as jest.Mock).mockImplementation((url) => {
    if (url.includes('oauth')) {
      return Promise.resolve({
        data: {
          access_token: 'validServicePricipalToken',
          expires_in: 3600
        }
      });
    }
    if (url.includes('profiles')) {
      return Promise.resolve({
        data: {
          id: '12345',
          name: 'Profile Name'
        }
      });
    }

    return Promise.resolve()
  });

  (axios.get as jest.Mock).mockImplementation((url) => {
    if (url.includes('current-user')) {
      return Promise.resolve({
        data: {
          permissionMasks: adminPermissionMasksMock
        }
      });
    }
  });
  it('returns templates', async () => {

    await supertest(expressApp)
      .get('/api/v1/powerbi/template')
      .set('Authorization', 'Bearer valid-token')
      .expect(200)
      .then(res => {
        expect(res.headers['content-type']).toContain('application/json; charset=utf-8');
        expect(res.body).toMatchObject({
          templates: [
            {
              created: "2022-07-13 00:00:00.000 Z",
              description: "Template description.",
              id: 1,
              modifed: "2022-07-13 00:00:00.000 Z",
              name: "Template Name",
              pbixFileName: "PbixTemplateName.pbix",
            },
          ],
        });
      })
  }, 30000)
})
