import { Request, Response } from 'express';
import { Schema } from 'joi';
import NodeCache from 'node-cache';

export interface RequestWithMeta extends Request {
  metadata: {
    correlationId: string;
  };
}

export type RequestHeader = Record<string, string>;

export interface ProvisionOrgRequest {
  orgName: string;
}

export type TableRow = Record<string, any>;
export interface Context {
  req: RequestWithMeta;
  res: Response;
  log: Logger;
  validation?: Schema;
  cache: NodeCache;
  queries: {
    [queryName: string]: (row?: TableRow) => Promise<TableRow>;
  };
  data?: any;
  returnCode?: number;
}
