import { useState, useCallback, useEffect } from 'react';
import DragHandle from '@mui/icons-material/DragHandle';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import throttle from 'lodash.throttle';
import * as styles from './styles.scss';
import { Chart } from '..';

const ReorderCharts = ({ charts, open, onClose, onConfirm }: Props) => {
  const [reorderCharts, setReorderCharts] = useState(charts);
  const [animationLock, setAnimationLock] = useState(false);
  const [draggingChartId, setDraggingChartId] = useState('');
  const blank = new Image();
  blank.src =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';

  const onDragOverQuestion = (e: React.DragEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (animationLock) {
      return;
    }

    const dragoverElement = e.target as HTMLDivElement;
    const draggingElementPositionY = e.pageY;
    const dragoverElementPositionTop =
      dragoverElement?.getBoundingClientRect().top;
    const dragoverElementPositionBottom =
      dragoverElement.getBoundingClientRect().bottom;

    if (
      dragoverElementPositionTop <= draggingElementPositionY &&
      draggingElementPositionY <= dragoverElementPositionBottom &&
      dragoverElement.id !== draggingChartId &&
      dragoverElement.id &&
      draggingChartId
    ) {
      const cReorder = reorderCharts.map((c) => ({ ...c }));
      const draggingIndex = cReorder.findIndex(
        (c) => `chart-${c.key}` === draggingChartId
      );
      const dragOverIndex = cReorder.findIndex(
        (c) => `chart-${c.key}` === dragoverElement.id
      );

      const reOrderDragging = cReorder[draggingIndex];
      const reOrderDragover = cReorder[dragOverIndex];
      if (
        draggingIndex !== -1 &&
        dragOverIndex !== -1 &&
        reOrderDragging &&
        reOrderDragover
      ) {
        const draggingOrder = reOrderDragging.order;
        const dragOverOrder = reOrderDragover.order;

        reOrderDragover.order = draggingOrder;
        reOrderDragging.order = dragOverOrder;
      }

      if (JSON.stringify(cReorder) !== JSON.stringify(reorderCharts)) {
        setReorderCharts(cReorder);
        setAnimationLock(true);
        setTimeout(() => setAnimationLock(false), 400);
      }
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onDragOverChartThrottled = useCallback(
    throttle(onDragOverQuestion, 50, { trailing: true, leading: false }),
    [draggingChartId, reorderCharts, animationLock]
  );

  useEffect(() => {
    setReorderCharts(charts.map((c, i) => ({ ...c, order: i })));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogTitle className={styles['reorder-charts-dialog-title']}>
        {' '}
        REORDER CHARTS{' '}
      </DialogTitle>
      <DialogContent>
        <div className={styles['reorder-charts-container']}>
          {reorderCharts?.map((_, i) => {
            const rc = reorderCharts.find((c) => c.order === i);
            if (!rc) {
              return <></>;
            }
            return (
              <div
                role="button"
                aria-label="Custom Question"
                className={styles['reorder-charts-chart']}
                id={`chart-${rc.key}`}
                key={`chart-${rc.key}`}
                data-testid={`chart-${rc.key}`}
                tabIndex={0}
                draggable
                style={{ top: (rc.order ?? 0) * 75 }}
                onDragStart={(e) => {
                  e.dataTransfer.setDragImage(blank, 0, 0);
                  const target = e.target as HTMLDivElement;
                  setDraggingChartId(target.id);
                }}
                onDragEnd={() => {
                  onDragOverChartThrottled.cancel();
                  setDraggingChartId('');
                }}
                onDragOver={(e) => {
                  e.persist();
                  onDragOverChartThrottled(e);
                }}
              >
                <DragHandle className={styles['reorder-charts-chart-drag']} />
                <div
                  className={styles['reorder-charts-chart-title']}
                  data-test={`chart-${rc.key}`}
                >
                  {rc.type}
                </div>
              </div>
            );
          })}
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={() => onConfirm(reorderCharts)} color="primary">
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReorderCharts;

interface Props {
  charts: Chart[];
  open: boolean;
  onClose: () => void;
  onConfirm: (charts: Chart[]) => void;
}
