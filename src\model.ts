export interface Notification {
  id?: string;
  schemaId?: string;
  data: {
    jobId: string;
    isRead: boolean;
    status: string;
    userId: string;
    engineId: string;
    applicationKey: string;
    organizationId: number;
    hide: boolean;
    exportName: string;
    tdoId: string;
    exportType: string;
  };
}

export interface BulkExportBatchSize {
  hasNative: number;
  noNative: number;
}

export interface BulkExportOption {
  hasTdoId: boolean;
  hasFilename: boolean;
  hasBaseFilename: boolean;
  hasFoldername: boolean;
  hasTag: boolean;
  hasPlainText: boolean;
  hasTtml: boolean;
  hasObjectNotation: boolean;
  hasBookmark: boolean;
  hasTranslation: boolean;
  hasNativeTranslation: boolean;
  hasObjectDetection: boolean;
  hasSentiment: boolean;
  hasNative: boolean;
  hasEngineName: boolean;
  filePathWindows: boolean;
  hasWord: boolean;
  hasClosedCaption: boolean;
}

export type BulkExportField =
  | 'tdoId'
  | 'filename'
  | 'foldername'
  | 'native'
  | 'ttml'
  | 'ttmlEdited'
  | 'plainText'
  | 'plainTextEdited'
  | 'word'
  | 'wordEdited'
  | 'speakerSeparation'
  | 'speakerSeparationEdited'
  | 'speakerSeparationObjectNotation'
  | 'speakerSeparationObjectNotationEdited'
  | 'objectNotation'
  | 'objectNotationEdited'
  | 'speakerSeparationWord'
  | 'speakerSeparationWordEdited'
  | 'bookmark'
  | 'tag'
  | 'exportStatus'
  | 'translation'
  | 'nativeTranslation'
  | 'sentiment'
  | 'baseFilename'
  | 'objectDetection'
  | 'engineName'
  | 'filePathWindows'
  | 'closedCaption';

export type BulkExportCustomizedNames = {
  [key in BulkExportField]?: string;
};

export interface BulkExportAssetContent extends BulkExportOption {
  exportDestination: string;
  emailAddress: string;
  exportName: string;
  maxConcurrency: number;
  organizationId: number;
  tdoData: Array<string>;
  searchQuery: object;
  customizedNames?: BulkExportCustomizedNames;
}

export interface BulkExportParam {
  tdoData: Array<string>;
  searchQuery: object;
  exportName: string;
  exportOption: BulkExportOption;
  exportBatchSize: BulkExportBatchSize;
  exportEngineId: string;
  clusterId: string;
  notificationSchemaId: string;
  organizationId: number;
  userId: string;
  emailAddress: string;
  parentFolderId: string;
  startDateTime: string;
  stopDateTime: string;
  exportDestination: string;
  maxConcurrency: number;
  password: string;
  customizedNames?: BulkExportCustomizedNames;
}

export interface ExportTemplateContent {
  name: string;
  userId: string;
  fields: BulkExportOption;
  customizedNames?: BulkExportCustomizedNames;
}

export interface ExportTemplateData {
  id: string;
  createdDateTime: string;
  data: ExportTemplateContent;
}

export interface Folder {
  id: string;
  name: string;
  treeObjectId: string;
  expanded: boolean;
  subfolders: string[];
  count: number;
  root: boolean;
  parentId: string | null;
  level: number;
  description: string;
  childFolders?: {
    count: number;
    records: FoldersResponseRecord[];
  };
  fetchingSubFolders?: boolean;
  fetchingMoveSubFolders?: boolean;
  parent?: {
    id: string;
  };
  folderPath?: {
    id: string;
    name: string;
    // treeObjectId: string;
  }[];
}

export interface Folders {
  [key: string]: Folder;
}

export interface FoldersResponseRecord {
  id: string;
  name: string;
  treeObjectId: string;
  description: string;
  parent: {
    id: string;
  };
  childFolders: {
    count: number;
  };
  folderPath: {
    id: string;
    name: string;
  }[];
}
export interface FetchFoldersResponse {
  id: string;
  name: string;
  ownerId: string;
  treeObjectId: string;
  description: string;
  childFolders: {
    count: number;
    records: FoldersResponseRecord[];
  };
  parent: {
    id: string;
  };
  folderPath: {
    id: string;
    name: string;
  }[];
}

export interface Role {
  id: string;
  name: string;
  appName: string;
}
