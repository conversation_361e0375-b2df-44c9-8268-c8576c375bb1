import React from 'react';
import Cards from 'components/Cards';
import AnalyticsBody from 'components/AnalyticsBody';
import { debounce } from 'lodash';

import { ConnectedProps, connect } from 'react-redux';
import {
  isFetchingMediaAggregations,
  getTotalDuration,
} from 'state/modules/dashboard';
import { getTotalResults, isSearchingMedia } from 'state/modules/search';
import * as styles from './styles.scss';

class Analytics extends React.Component<PropsFromRedux> {
  state = {
    anchorEl: null,
    tagDialogOpen: false,
    redactDialogOpen: false,
    anchorElExport: null,
    windowSize: 0,
  };

  componentDidMount() {
    // pre-fetch analytics schema when we first attach tabs to have data in the background,
    // not when app loads, not when user opens data explorer for the 1st time
    this.getWindowSize();
    window.addEventListener('resize', this.updateWindowSize);
  }

  componentWillUnmount = () => {
    window.removeEventListener('resize', this.updateWindowSize);
  };

  getWindowSize = () => {
    // window.innerWidth always accurately reflects the current width of the browser window
    const windowSize = window.innerWidth;
    this.setState({ windowSize });
  };

  updateWindowSize = debounce(() => {
    const windowSize = window.innerWidth;
    this.setState({ windowSize });
  }, 500);
  render() {
    const { totalDuration, totalResults, isDashboardLoading } = this.props;
    const { windowSize } = this.state;
    return (
      <div>
        <div className={styles['card-item']}>
          <Cards
            totalDuration={totalDuration}
            totalNumber={totalResults}
            isLoading={isDashboardLoading}
          />
        </div>
        <div>
          <AnalyticsBody windowSize={windowSize} />
        </div>
      </div>
    );
  }
}

const mapState = (state: any) => ({
  isDashboardLoading: isFetchingMediaAggregations(state),
  totalDuration: getTotalDuration(state),
  totalResults: getTotalResults(state),
  isSearchingMedia: isSearchingMedia(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Analytics);
