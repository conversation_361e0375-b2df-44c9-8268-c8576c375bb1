import React, { Fragment } from 'react';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import Paper from '@mui/material/Paper';
import AddCircleOutline from '@mui/icons-material/AddCircleOutline';
import Security from '@mui/icons-material/Security';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import makeStyles from '@mui/styles/makeStyles';
import cx from 'classnames';
import styles from './styles';
import {
  EnginesSelected,
  EngineCategory,
  EngineByCategories,
} from '../../state/modules/uploadFile/models';

const useStyles = makeStyles(styles);
function AvailableEngine({
  currentEngineCategory,
  handleChangeEngine,
  engineCategories,
  handleSearchEngine,
  engineByCategories,
  enginesSelected,
  engineNameSearch,
  handleAddEngine,
  isReprocess,
}: Props) {
  const classes = useStyles();
  const renderLogoEngine = (src: string, showSecurity: boolean) => {
    return (
      <div className={classes.iconHeaderEngines}>
        {showSecurity && <Security />}
        {src && <img src={src} width="100" height="50" />}
      </div>
    );
  };
  return (
    <Fragment>
      <FormControl className={classes.formEngines}>
        <InputLabel
          variant="standard"
          shrink
          className={classes.titleFormSelectEngine}
        >
          Available Engines
        </InputLabel>
        <Select
          value={currentEngineCategory}
          onChange={(event) =>
            handleChangeEngine(event as React.ChangeEvent<HTMLInputElement>)
          }
          displayEmpty
          className={classes.titleSelectEngine}
          data-test="select-available-engine"
          data-testid="select-available-engine"
          variant="standard"
        >
          {engineCategories.map((item) => {
            return (
              <MenuItem
                key={item.id}
                value={item.id}
                className={classes.titleEngineSelected}
                data-test={`list-available-engine_${item.id}`}
              >
                {item.name}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
      <FormControl className={classes.formEngines}>
        <TextField
          label="Search by Engine name"
          className={classes.titleSearchEngine}
          onChange={(event) =>
            handleSearchEngine(event as React.ChangeEvent<HTMLInputElement>)
          }
          slotProps={{
            htmlInput: { 'data-testid': 'input-engine-name' },
          }}
          variant="standard"
        />
      </FormControl>
      <Paper
        variant="outlined"
        square
        className={cx(classes.listEngines, {
          [classes.listEnginesReprocess]: isReprocess,
        })}
      >
        {engineByCategories &&
          Array.isArray(engineByCategories[currentEngineCategory]) &&
          engineByCategories[currentEngineCategory]! // Safe due to isArray check
            .filter(
              (item) =>
                !enginesSelected.some((engine) =>
                  engine.engineIds.some((engine) => engine.id === item.id)
                )
            )
            .filter((item) =>
              JSON.stringify(item.name)
                .toLowerCase()
                .includes(engineNameSearch.toLowerCase())
            )
            .map((item) => {
              return (
                <Card
                  key={item.id}
                  className={classes.cardEngines}
                  data-testid="available-engine-card"
                >
                  <CardHeader
                    avatar={
                      item.logoPath ||
                      item.deploymentModel === 'FullyNetworkIsolated'
                        ? renderLogoEngine(
                            item.logoPath,
                            item.deploymentModel === 'FullyNetworkIsolated'
                          )
                        : null
                    }
                    action={
                      <IconButton
                        className={classes.iconAddEngines}
                        data-id={item.id}
                        onClick={handleAddEngine}
                        data-test={`add-engine-available_${item.id}`}
                        data-testid="add-available-engine-card-header"
                        size="large"
                      >
                        <AddCircleOutline />
                      </IconButton>
                    }
                    title={
                      !item.logoPath && (
                        <Typography
                          component="p"
                          className={classes.titleHeaderEnginesAvailable}
                        >
                          {item.name}
                        </Typography>
                      )
                    }
                    className={classes.cardHeaderEngines}
                    data-testid="available-engine-card-header"
                  />
                  <CardContent
                    className={classes.cardContentEngines}
                    data-testid="available-engine-card-content"
                  >
                    <Typography
                      component="p"
                      className={classes.desContentEngines}
                    >
                      {item.description}
                    </Typography>
                    <Typography component="p" className={classes.ratingEngines}>
                      Rating
                    </Typography>
                  </CardContent>
                </Card>
              );
            })}
      </Paper>
    </Fragment>
  );
}
interface Props {
  currentEngineCategory: string;
  handleChangeEngine: (event: React.ChangeEvent<HTMLInputElement>) => void;
  engineCategories: EngineCategory[];
  handleSearchEngine: (event: React.ChangeEvent<HTMLInputElement>) => void;
  engineByCategories: EngineByCategories;
  enginesSelected: EnginesSelected[];
  engineNameSearch: string;
  handleAddEngine: (event: React.MouseEvent<HTMLElement>) => void;
  isReprocess: boolean;
}
export default AvailableEngine;
