server {
    listen       9000;
    server_name  localhost;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    server_tokens off;
    # can't run this under we work out how to build nginx
    # more_clear_headers Server;

    # Set the nonce variable using the request ID
    set $cspNonce $request_id;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    location /api {
        client_max_body_size 5M;
        proxy_pass http://localhost:3002;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #error_page 404 and 403 to 200 and index.html
    error_page 404 =200 /index.html;
    error_page 403 =200 /index.html;
    location = /index.html {
        # Inject the nonce into the response
        sub_filter_once off;
        sub_filter_types *;
        sub_filter NGINX_CSP_NONCE $cspNonce;

        root /usr/share/nginx/html;
        add_header Cache-Control "must-revalidate";
        internal;

        add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options nosniff;
        add_header Referrer-Policy "strict-origin";
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-eval' 'nonce-$cspNonce' https://*.pendo.io/ https://*.segment.com/ https://*.ingest.sentry.io https://cdn.jsdelivr.net/ https://*.intercom.io/ https://*.intercomcdn.com/ https://*.google-analytics.com/ https://analytics-google.com/ https://*.googleapis.com https://www.googletagmanager.com/ *.force.com/ *.salesforce-scrt.com/ https://cdnjs.cloudflare.com/ data.pendo.io app.pendo.io cdn.pendo.io pendo-static-5865020918857728.storage.googleapis.com pendo-io-static.storage.googleapis.com; connect-src * blob:; img-src * data: blob: https://i.vimeocdn.com; font-src 'self' data: https://fonts.gstatic.com/ https://cdn.jsdelivr.net/ https://stackpath.bootstrapcdn.com/; media-src * blob:; frame-src 'self' https://td.doubleclick.net/ https://veritone.my.site.com/ https://*.veritone.com/ https://app.high.powerbigov.us/ pendo-static-5865020918857728.storage.googleapis.com https://player.vimeo.com portal.pendo.io app.pendo.io; worker-src 'self' blob:; style-src 'self' 'unsafe-inline' https://*.googleapis.com https://cdn.jsdelivr.net/ https://stackpath.bootstrapcdn.com/ pendo-io-static.storage.googleapis.com pendo-static-5865020918857728.storage.googleapis.com app.pendo.io cdn.pendo.io;" always;
    }

    location ~* \.(css)$ {
        root /usr/share/nginx/html;
    }

    location ~* \.(mjs|js)$ {
        root /usr/share/nginx/html;
        types {
            text/javascript js mjs;
        }
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
