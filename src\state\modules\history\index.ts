import get from 'lodash/get';
import uniqBy from 'lodash/uniqBy';
import sortBy from 'lodash/sortBy';
import callGraph<PERSON><PERSON>pi, {
  createGQLFailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraphQLApi';
import { ExportHistory, FetchTotalCountError } from './models';
import { createAction, createReducer } from '@reduxjs/toolkit';
import { FetchGetJobResponse } from '../tdosTable';
import { SignedUri } from 'components/TopBar';
import { G<PERSON>Api } from '~helpers/gqlApi';
import {
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '~helpers/apiHelper';

export const FETCH_EXPORT_HISTORY = createGQLRequestAction(
  'start fetching export history'
);
export const FETCH_EXPORT_HISTORY_SUCCESS =
  createGQLSuccessAction<FetchExportHistoryResponse>(
    'fetching export history success'
  );
export const FETCH_EXPORT_HISTORY_FAILURE = createGQLFailureAction(
  'fetching export history fail'
);
export const HISTORY_TABLE_NEXT_PAGE = createAction<{
  limit: number;
  offset: number;
}>('history table next page');
export const FETCH_TOTAL_COUNT_SUCCESS = createAction<{
  count: number;
}>('fetching total export count successfully');
export const FETCH_TOTAL_COUNT_FAILURE = createAction<{
  error: FetchTotalCountError;
}>('fetching total export count failed');
export const ON_FETCH_URI_TO_EXPORT = createAction<{
  tdoId: string;
  exportName: string;
}>('fetch history export asset');
export const FETCH_URI_REQUEST = createGQLRequestAction(
  'fetch uri to export request'
);
export const FETCH_URI_SUCCESS = createGQLSuccessAction<FetchExportUriResponse>(
  'fetch uri to export successfully'
);
export const FETCH_URI_FAILURE = createGQLFailureAction(
  'fetch uri to export failed'
);
export const ON_FETCH_PAGE_SIZE = createAction<{
  limit: number;
  offset: number;
}>('fetching page size');
export const OPEN_EXPORT_MODAL = createAction('open export modal');
export const CLOSE_EXPORT_MODAL = createAction('close export modal');

export const FETCH_EXPORT_HISTORY_AUTO_REFRESH = createGQLRequestAction(
  'fetch export history auto refresh'
);
export const FETCH_EXPORT_HISTORY_AUTO_REFRESH_SUCCESS =
  createGQLSuccessAction<FetchExportHistoryAutoRefreshResponse>(
    'fetch export history auto refresh success'
  );
export const FETCH_EXPORT_HISTORY_AUTO_REFRESH_FAILURE = createGQLFailureAction(
  'fetch export history auto refresh failure'
);

export const FETCH_GET_JOB_EXPORT_HISTORY = createGQLRequestAction(
  'fetch get job export history '
);
export const FETCH_GET_JOB_EXPORT_HISTORY_SUCCESS =
  createGQLSuccessAction<FetchGetJobResponse>(
    'fetch get job export history success'
  );
export const FETCH_GET_JOB_EXPORT_HISTORY_FAILURE = createGQLFailureAction(
  'fetch get job export history failure'
);

export const UPDATE_EXPORT_HISTORY_STATUS_BY_ID = createAction<{
  [key: string]: { id: string; status: string };
}>('update export history status by id');

export const UPDATE_TOTAL_COUNT_EXPORT_SUCCESS = createAction<string>(
  'update total count export success'
);

export const REFRESH_EXPORT_HISTORY = createAction('refresh export history');

export const UPDATE_EXPORT_HISTORY_REQUEST = createAsyncFuncRequestAction(
  'update export history request'
);
export const UPDATE_EXPORT_HISTORY_SUCCESS = createAsyncFuncSuccessAction(
  'update export history success'
);
export const UPDATE_EXPORT_HISTORY_FAILURE = createAsyncFuncFailureAction(
  'update export history failure'
);

export const STATUS = {
  COMPLETE: 'complete',
  RUNNING: 'running',
  FAILED: 'failed',
  QUEUED: 'queued',
};

const defaultState = {
  exportHistory: [] as ExportHistory[],
  fetchingHistory: false,
  fetchingHistoryError: null,
  totalCount: 0,
  fetchingTotalCountError: null as FetchTotalCountError | null,
  currentPage: 0,
  limit: 10,
  offsetPage: 0,
  signedUris: [] as SignedUri[],
  isExportModalOpened: false,
  modalName: '',
  loadingFetchUriExport: false,
  noData: false,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_EXPORT_HISTORY, (state) => {
      return {
        ...state,
        fetchingHistory: true,
        fetchingHistoryError: null,
      };
    })
    .addCase(FETCH_EXPORT_HISTORY_SUCCESS, (state, action) => {
      const exportHistory = get(
        action,
        'payload.structuredDataObjects.records',
        []
      );
      const { currentPage, offset, limit } = action?.meta?.variables || {};
      return {
        ...state,
        fetchingHistory: false,
        exportHistory,
        currentPage: currentPage ? currentPage : 0,
        offsetPage: offset ? offset : 0,
        limit: limit ? limit : 10,
        noData: exportHistory.length ? false : true,
      };
    })
    .addCase(FETCH_EXPORT_HISTORY_FAILURE, (state, action) => {
      const error = get(action, 'payload.error');
      return {
        ...state,
        fetchingHistory: false,
        fetchingHistoryError: error,
      };
    })
    .addCase(FETCH_TOTAL_COUNT_SUCCESS, (state, action) => {
      return {
        ...state,
        totalCount: action.payload.count,
      };
    })
    .addCase(FETCH_TOTAL_COUNT_FAILURE, (state, action) => {
      const fetchingTotalCountError = get(action, 'payload.error');
      return {
        ...state,
        fetchingTotalCountError,
      };
    })
    .addCase(OPEN_EXPORT_MODAL, (state) => {
      return {
        ...state,
        isExportModalOpened: true,
      };
    })
    .addCase(CLOSE_EXPORT_MODAL, (state) => {
      return {
        ...state,
        isExportModalOpened: false,
        modalName: '',
        loadingFetchUriExport: false,
        signedUris: [],
      };
    })
    .addCase(UPDATE_EXPORT_HISTORY_STATUS_BY_ID, (state, action) => {
      const exportHistoryUpdate = get(action, 'payload');
      const exportHistory = state.exportHistory.map((item) => {
        const upd = exportHistoryUpdate[item.id];
        if (upd) {
          return {
            ...item,
            data: {
              ...item.data,
              status: upd.status,
            },
          };
        }
        return item;
      });
      return {
        ...state,
        exportHistory,
      };
    })
    .addCase(UPDATE_TOTAL_COUNT_EXPORT_SUCCESS, (state, action) => {
      const type = get(action, 'payload', '');
      return {
        ...state,
        totalCount:
          type === 'exportHistory'
            ? state.totalCount + 1
            : state.totalCount - 1,
      };
    })
    .addCase(ON_FETCH_URI_TO_EXPORT, (state, action) => {
      const exportName = get(action, 'payload.exportName', '');
      return {
        ...state,
        modalName: exportName ? exportName : 'Download Modal',
      };
    })
    .addCase(FETCH_URI_REQUEST, (state) => {
      return {
        ...state,
        loadingFetchUriExport: true,
      };
    })
    .addCase(FETCH_URI_SUCCESS, (state, action) => {
      const records = action.payload.temporalDataObject?.assets?.records || [];
      const dataAssets = records.map((item) => {
        const exportName = item.name.split('.')[0] ?? '';
        const exportNameSplit = exportName.split('_');
        const totalNumberZipBatch =
          (exportNameSplit.length &&
            exportNameSplit[exportNameSplit.length - 1]) ||
          '';
        const totalNumberZipBatchSplit = totalNumberZipBatch.split('-');
        const totalBatch =
          totalNumberZipBatchSplit.length > 0 ? totalNumberZipBatchSplit[0] : 0;
        const numberBatch =
          totalNumberZipBatchSplit.length > 1 ? totalNumberZipBatchSplit[1] : 0;
        const zipIndex =
          totalNumberZipBatchSplit.length > 2 ? totalNumberZipBatchSplit[2] : 0;
        return {
          ...item,
          totalBatch: Number(totalBatch),
          numberBatch: Number(numberBatch),
          zipIndex: Number(zipIndex),
        };
      });
      return {
        ...state,
        loadingFetchUriExport: false,
        signedUris: sortBy(uniqBy(dataAssets, 'name'), [
          'totalBatch',
          'numberBatch',
          'zipIndex',
        ]),
      };
    })
    .addCase(FETCH_URI_FAILURE, (state) => {
      return {
        ...state,
        loadingFetchUriExport: false,
      };
    });
});

interface FetchExportUriResponse {
  temporalDataObject: {
    assets: {
      records: {
        id: string;
        name: string;
        uri: string;
        signedUri: string;
      }[];
    };
  };
}
export const fetchExportUri =
  (tdoId: string) => (dispatch: any, getState: any) => {
    const query = `
  query getTdo{
    temporalDataObject(id: "${tdoId}"){
      assets(limit: 1000, assetType: "illuminate-export"
      ){
        records{
          id
          name
          uri
          signedUri
        }
      }
    }
  }
  `;
    return callGraphQLApi<FetchExportUriResponse>({
      actionTypes: [FETCH_URI_REQUEST, FETCH_URI_SUCCESS, FETCH_URI_FAILURE],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

interface FetchExportHistoryResponse {
  listSDOs: {
    structuredDataObjects: {
      records: {
        id: string;
        data: any;
        createdDateTime: string;
        modifiedDateTime: string;
      }[];
    };
  };
}
export const fetchExportHistory =
  ({
    schemaId,
    paginationInfo,
    organizationId,
    userId,
    type,
  }: {
    schemaId: string;
    paginationInfo: { limit: number; offset: number };
    organizationId: string;
    userId?: string;
    type: string;
  }) =>
  (dispatch: any, getState: any) => {
    const state = getState();
    const viewAllUserExport = get(
      state,
      'user.user.organization.kvp.features.illuminate.viewAllUserExport',
      'disabled'
    );

    let filter: {
      organizationId: string;
      applicationKey: string;
      userId?: string;
    } = {
      // organizationId has to be string in filter
      organizationId: `${organizationId}`,
      applicationKey: 'illuminate',
    };
    if (viewAllUserExport === 'disabled') {
      filter = {
        ...filter,
        userId,
      };
    }
    const query = `
  query listSDOs($schemaId: ID!, $limit: Int!, $offset: Int, $filter: JSONData) {
    structuredDataObjects(
      schemaId: $schemaId,
      limit: $limit,
      offset: $offset,
      orderBy: [{
        field: createdDateTime,
        direction: desc
      }],
      filter: $filter,
      owned: true) {
      records {
        id
        data
        createdDateTime
        modifiedDateTime
      }
    }
  }
  `;
    return callGraphQLApi<FetchExportHistoryResponse>({
      actionTypes: [
        FETCH_EXPORT_HISTORY,
        FETCH_EXPORT_HISTORY_SUCCESS,
        FETCH_EXPORT_HISTORY_FAILURE,
      ],
      query,
      variables: {
        schemaId,
        ...paginationInfo,
        type,
        filter,
      },
      dispatch,
      getState,
    });
  };

export const fetchTotalCount =
  ({
    schemaId,
    organizationId,
    userId,
  }: {
    schemaId: string;
    organizationId: string;
    userId?: string;
  }) =>
  async (dispatch: any, getState: any) => {
    const state = getState();
    const viewAllUserExport = get(
      state,
      'user.user.organization.kvp.features.illuminate.viewAllUserExport',
      'disabled'
    );

    const filter: {
      organizationId: string;
      applicationKey: string;
      userId?: string;
    } = {
      // organizationId has to be string in filter
      organizationId: `${organizationId}`,
      applicationKey: 'illuminate',
    };
    if (viewAllUserExport === 'disabled') {
      filter.userId = userId;
    }

    const gql = GQLApi.newGQLApi(state);
    const response = await gql.getSDOCount(schemaId, filter);
    if (response.errors) {
      console.error('failed to get export history count', response.errors);
      dispatch(FETCH_TOTAL_COUNT_FAILURE({ error: response.errors[0] }));
      return;
    }
    dispatch(FETCH_TOTAL_COUNT_SUCCESS({ count: response.count ?? 0 }));
  };

export const updateExportHistorys =
  (schemaId: string, exportHistorys: ExportHistory[]) =>
  (dispatch: any, getState: any) => {
    const state = getState();
    const gql = GQLApi.newGQLApi(state);
    const asyncfn = async () => {
      const resp = await gql.createStructuredData(schemaId, exportHistorys);
      if (resp.errors && resp.errors.length) {
        resp.errors.forEach((error) => {
          console.error(`failed to update export history - ${error.message}`);
        });
      }
      return resp;
    };
    return callAsyncFunc({
      actionTypes: [
        UPDATE_EXPORT_HISTORY_REQUEST,
        UPDATE_EXPORT_HISTORY_SUCCESS,
        UPDATE_EXPORT_HISTORY_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

type FetchExportHistoryAutoRefreshResponse = Record<
  string,
  {
    count: number;
    records: {
      id: string;
      data: any;
      createdDateTime: string;
      modifiedDateTime: string;
    }[];
  }
>;
export const fetchExportHistoryAutoRefresh =
  (
    schemaId: string,
    data: string[],
    userId: string,
    organizationId: string,
    applicationKey: string
  ) =>
  (dispatch: any, getState: any) => {
    const sdos: string[] = [];
    data.forEach((item, key) => {
      sdos.push(`
       getExportHistory_${key}: structuredDataObjects(
          schemaId: "${schemaId}",
          orderBy: [{
            field: modifiedDateTime,
            direction: desc
          }],
          filter:{
            batchSdoId:"${item}",
            userId:"${userId}",
            organizationId: "${organizationId}",
            applicationKey: "${applicationKey}",
          },
          limit:1) {
          count
          records {
            id
            data
            createdDateTime
            modifiedDateTime
          }
        }
    `);
    });

    const query = `query getExportHistoryAutoRefresh{
    ${sdos.join('\n')}
  }`;
    return callGraphQLApi<FetchExportHistoryAutoRefreshResponse>({
      actionTypes: [
        FETCH_EXPORT_HISTORY_AUTO_REFRESH,
        FETCH_EXPORT_HISTORY_AUTO_REFRESH_SUCCESS,
        FETCH_EXPORT_HISTORY_AUTO_REFRESH_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export default reducer;
export const namespace = 'history';
export const local = (state: any) => state[namespace] as typeof defaultState;
export const getHistory = (state: any) => local(state).exportHistory;
export const getTotalCount = (state: any) => local(state).totalCount;
export const getCurrentPage = (state: any) => local(state).currentPage;
export const getLimit = (state: any) => local(state).limit;
export const getOffsetPage = (state: any) => local(state).offsetPage;
export const getIsExportHistoryOpen = (state: any) =>
  local(state).isExportModalOpened;
export const getSignedUris = (state: any) => local(state).signedUris;
export const getModalName = (state: any) => local(state).modalName;
export const getFetchingHistory = (state: any) => local(state).fetchingHistory;
export const getLoadingFetchUriExport = (state: any) =>
  local(state).loadingFetchUriExport;
export const getNoData = (state: any) => local(state).noData;
