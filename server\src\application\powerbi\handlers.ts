import { NextFunction, Response } from 'express';
import NodeCache from 'node-cache';
import { RequestWithMeta, Context } from '../types';
import { schemas } from './schemas';
import A from './actions';
import E from './errors';

const createHandlers = ({
  queries,
  log,
  cache,
}: {
  queries: any;
  log: Logger;
  cache: NodeCache;
}) => ({
  post: {
    provisionOrg: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: { ...req.body },
        validation: schemas.post.provisionOrg,
      };

      try {
        await A.validateToken(cxt);
        await A.validateRequest(cxt);
        await A.refreshServicePrincipalToken(cxt);
        await A.refreshAzureBlobStorageToken(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await <PERSON>.checkForTokenConfig(cxt);
        await A.checkForRequestedTemplate(cxt);
        await A.createProfile(cxt);
        await A.createWorkspace(cxt);
        await A.getCapacities(cxt);
        await A.assignCapacityToWorkspace(cxt);
        await A.addSuperUserToWorkspace(cxt);
        await A.getPbixTemplate(cxt);
        await A.uploadPbixToWorkspace(cxt);
        await A.getDatasetsInWorkspace(cxt);
        await A.getDatasourceGateway(cxt);
        await A.getReportsInWorkspace(cxt);
        await A.setDatasourceCredentials(cxt);
        await A.registerTokenConfig(cxt);
        await A.cleanupTmp(cxt);
        await A.send((cxt: Context) => ({
          orgId: cxt.data.orgId,
          profileId: cxt.data.profileId,
          workspaceId: cxt.data.workspaceId,
          workspaceName: cxt.data.workspaceName,
          capacityId: cxt.data.capacityId,
          capacityName: cxt.data.capacityName,
          pbixFilePath: cxt.data.pbixFilePath,
          datasetId: cxt.data.datasetId,
          datasetName: cxt.data.datasetName,
          gatewayId: cxt.data.gatewayId,
          datasourceId: cxt.data.datasourceId,
          reportId: cxt.data.reportId,
          reportName: cxt.data.reportName,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(403)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(401)(e);
          case E.OrgHasTokenConfig:
          case E.OrgNameAlreadyExists:
            return A.sendError(cxt)(409)(e);
          case E.UnknownTemplateError:
            return A.sendError(cxt)(422)(e);
          case E.ApiError:
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
    generateEmbedToken: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
        validation: schemas.post.generateEmbedToken,
      };

      try {
        await A.validateToken(cxt);
        await A.refreshServicePrincipalToken(cxt);
        await A.getAiwareOrgId(cxt);
        await A.fetchTokenConfig(cxt);
        await A.generateEmbedToken(cxt);
        await A.send((cxt: Context) => ({
          embedUrl: cxt.data.embedUrl,
          embedToken: cxt.data.embedToken,
          embedTokenId: cxt.data.embedTokenId,
          embedTokenExp: cxt.data.embedTokenExp,
          embedTokenHostname: cxt.data.embedTokenHostname,
          orgId: cxt.data.orgId,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.OrgHasNoTokenRegisteredError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(403)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(401)(e);
          case E.ApiError:
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
    pushData: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
        validation: schemas.post.pushData,
      };
      try {
        await A.validateJwtToken(cxt);
        await A.validateRequest(cxt);
        await A.pushContactDataRows(cxt);
        await A.send((cxt: Context) => ({
          message: `${cxt.data.success} stops inserted successfully with ${cxt.data.errors.length} error(s)`,
          errors: cxt.data.errors,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(403)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(401)(e);
          case E.ActionError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
    pushCustomQuestionDefinitions: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
        validation: schemas.post.pushCustomQuestionDefinitions,
      };
      try {
        await A.validateJwtToken(cxt);
        await A.validateRequest(cxt);
        await A.pushQuestionDefinitionsRows(cxt);
        await A.send((cxt: Context) => ({
          message: `${cxt.req.body.data.length} row(s) inserted`,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(403)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(401)(e);
          case E.ApiError:
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
    associateTemplate: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: { ...req.body },
        validation: schemas.post.associateTemplate,
      };
      try {
        await A.validateToken(cxt);
        await A.validateRequest(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await A.refreshServicePrincipalToken(cxt);
        await A.refreshAzureBlobStorageToken(cxt);
        await A.checkForRequestedTemplate(cxt);
        await A.fetchTokenConfig(cxt);
        await A.getPbixTemplate(cxt);
        await A.uploadPbixToWorkspace(cxt);
        await A.getDatasetsInWorkspace(cxt);
        await A.getDatasourceGateway(cxt);
        await A.getReportsInWorkspace(cxt);
        await A.setDatasourceCredentials(cxt);
        await A.registerTokenConfig(cxt);
        await A.send((cxt: Context) => ({
          orgId: cxt.data.orgId,
          profileId: cxt.data.profileId,
          workspaceId: cxt.data.workspaceId,
          workspaceName: cxt.data.workspaceName,
          capacityId: cxt.data.capacityId,
          capacityName: cxt.data.capacityName,
          pbixFilePath: cxt.data.pbixFilePath,
          datasetId: cxt.data.datasetId,
          datasetName: cxt.data.datasetName,
          gatewayId: cxt.data.gatewayId,
          datasourceId: cxt.data.datasourceId,
          reportId: cxt.data.reportId,
          reportName: cxt.data.reportName,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          case E.ApiError:
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
    bulkAssociateTemplate: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: { ...req.body },
        validation: schemas.post.bulkAssociateTemplate,
      };
      try {
        await A.validateToken(cxt);
        await A.validateRequest(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await A.refreshServicePrincipalToken(cxt);
        await A.refreshAzureBlobStorageToken(cxt);
        await A.checkForRequestedTemplate(cxt);
        await A.checkForTokenConfigs(cxt);
        await A.bulkAssociateTemplate(cxt);
        await A.send((cxt: Context) => ({
          message: `Organizations: [${cxt.data.orgIds}] are associated with template: ${cxt.data.templatePbixFileName}`,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          case E.ApiError:
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
  get: {
    template: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
      };

      try {
        await A.validateToken(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await A.getTemplates(cxt);
        await A.send((cxt: Context) => ({
          templates: cxt.data.templates,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
  patch: {
    template: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
        validation: schemas.patch.template,
      };

      try {
        await A.validateToken(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await A.updateTemplate(cxt);
        await A.send(() => ({
          message: `OK`,
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
  put: {
    template: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: {},
      };

      try {
        await A.validateToken(cxt);
        await A.getAiwareOrgId(cxt);
        await A.checkIsRoot(cxt);
        await A.refreshAzureBlobStorageToken(cxt);
        await A.uploadPbixToBlobStorage(cxt);
        await A.registerTemplate(cxt);
        await A.send(() => ({
          message: 'OK',
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          case E.DatabaseError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
  delete: {
    removeAllPowerBiData: async (
      req: RequestWithMeta,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        queries,
        cache,
        data: { ...req.body },
        validation: schemas.delete.removeAllPowerBiData,
      };

      try {
        await A.validateToken(cxt);
        await A.refreshServicePrincipalToken(cxt);
        await A.checkIsRoot(cxt);

        // Allow cleanup to happen async to request
        A.cleanupPowerBI(cxt);

        await A.send(() => ({ message: 'Running...' }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          case E.ValidationError:
            return A.sendError(cxt)(400)(e);
          case E.ForbiddenError:
            return A.sendError(cxt)(401)(e);
          case E.UnauthorizedError:
            return A.sendError(cxt)(403)(e);
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
});

export default createHandlers;
