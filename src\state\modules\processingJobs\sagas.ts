import {
  fork,
  all,
  call,
  put,
  takeEvery,
  select,
} from 'typed-redux-saga/macro';

import { GQLApi } from '../../../helpers/gqlApi';
import {
  pageSize,
  statusFilter,
  getShowCustomRange,
  getCustomStartDate,
  getCustomEndDate,
  dateTimeRangeFilter,
  lastOffset,
  getRemainingProcessingJobs,
  createFetchProcessingJobCountRequest,
  getDataJobsFailed,
  getIsSelectedAllJobsFailed,
  createFetchProcessingJobsRequest,
} from '.';
import * as actions from './actions';
import { showNotification } from '../snackbar';
import getDefaultClusterId from '../../../helpers/getDefaultClusterId';
import { Job } from './models';

function* watchFetchProcessingJobs() {
  yield* takeEvery(
    [
      actions.REFRESH_PROCESSING_JOBS,
      actions.ON_CHANGE_PAGE_PROCESSING_JOBS,
      actions.ON_CHANGE_PAGE_SIZE_PROCESSING_JOBS,
      actions.FILTER_JOB_BY_STATUS,
      actions.FILTER_JOB_BY_DATE_TIME_RANGE,
      actions.FILTER_JOB_BY_CUSTOM_DATE_RANGE,
    ],
    function* () {
      yield* fetchProcessingJobs();
      yield* fetchCountProcessingJobs();
    }
  );
}

function* fetchProcessingJobs() {
  const dataPageSize = yield* select(pageSize);
  const dataDateTimeRangeFilter = yield* select(dateTimeRangeFilter);
  const dataStatusFilter = yield* select(statusFilter);
  const checkedStatus = getCheckedStatus(dataStatusFilter);
  const dataLastOffset = yield* select(lastOffset);
  const remainingProcessingJobs = yield* select(getRemainingProcessingJobs);
  const customStartDate = yield* select(getCustomStartDate);
  const customEndDate = yield* select(getCustomEndDate);
  const isShowCustomRange = yield* select(getShowCustomRange);

  yield* put(
    createFetchProcessingJobsRequest(
      dataPageSize,
      dataLastOffset,
      checkedStatus,
      dataDateTimeRangeFilter,
      remainingProcessingJobs,
      customStartDate,
      customEndDate,
      isShowCustomRange
    )
  );
}

export function* fetchCountProcessingJobs(): any {
  const timeFilter = yield* select(dateTimeRangeFilter);
  const dataStatusFilter = yield* select(statusFilter);
  const checkedStatus = getCheckedStatus(dataStatusFilter);
  const customStartDate = yield* select(getCustomStartDate);
  const customEndDate = yield* select(getCustomEndDate);
  const isShowCustomRange = yield* select(getShowCustomRange);
  yield* put(
    createFetchProcessingJobCountRequest(
      timeFilter,
      checkedStatus,
      customStartDate,
      customEndDate,
      isShowCustomRange
    )
  );
}
function getCheckedStatus(
  statusFilter: {
    name: string;
    checked: boolean;
    value: readonly string[];
  }[]
) {
  const checkedStatus: string[] = [];
  statusFilter.forEach((status) => {
    if (status.checked) {
      checkedStatus.push(...status.value);
    }
  });
  return checkedStatus;
}

function* fetchFailedJobForExport() {
  const status = ['failed'];
  const dataDateTimeRangeFilter = yield* select(dateTimeRangeFilter);
  const customStartDate = yield* select(getCustomStartDate);
  const customEndDate = yield* select(getCustomEndDate);
  const isShowCustomRange = yield* select(getShowCustomRange);

  const fromDateTime = new Date();
  fromDateTime.setHours(fromDateTime.getHours() - dataDateTimeRangeFilter);
  let fromDateTimeIso = fromDateTime.toISOString();
  let toDateTimeIso = new Date().toISOString();
  // Update from and to dateTime values if a custom date range is selected
  if (isShowCustomRange && customEndDate !== null && customStartDate !== null) {
    fromDateTimeIso = customStartDate?.toJSDate().toISOString();
    toDateTimeIso = customEndDate?.toJSDate().toISOString();
  }
  const state = yield* select();
  const gql = GQLApi.newGQLApi(state);
  const results: Job[] = [];
  const param = {
    status,
    fromDateTimeIso,
    toDateTimeIso,
  };
  const response = yield* call([gql, gql.getJobCount], param);
  if (response.errors) {
    console.error(`fail to getJobCount`, response.errors);
  }
  const total = response.count;
  if (total === 0) {
    return results;
  }

  let count = 0;
  let offset = 0;
  const limit = 1000;
  do {
    const param = {
      limit,
      offset,
      status,
      fromDateTimeIso,
      toDateTimeIso,
    };
    const response = yield* call([gql, gql.getProcessingJobsByOffset], param);
    if (response.errors) {
      const errors = [];
      for (const error of response.errors) {
        if (error?.message.includes('The requested TDO was not found')) {
          continue;
        }
        errors.push(error);
      }
      if (errors.length) {
        console.error('failed to fetchFailedJobForExport', errors);
      }
    }
    const records = response.data?.jobs?.records ?? [];
    offset += limit;
    count = records.length;
    if (count > 0) {
      results.push(...records);
    }
    let percentage = (results.length / total) * 100;
    if (percentage > 100) {
      percentage = 100;
    }
    yield* put(actions.exportFailedJobsPercentageUpdate(percentage));
  } while (count === limit);
  yield* put(actions.exportFailedJobsPercentageUpdate(100));

  const resultsWithTdo = [];
  const resultsWithoutTdo = [];
  for (const result of results) {
    if (result.target?.id) {
      resultsWithTdo.push(result);
    } else {
      resultsWithoutTdo.push(result);
    }
  }
  if (resultsWithoutTdo.length) {
    const errMsg = resultsWithoutTdo.map((r) => r.id).join(', ');
    console.warn(`exporting failed job: failed jobs without tdo: ${errMsg}`);
  }
  return resultsWithTdo;
}

export function* fetchAllJobsFailed() {
  const dataStatusFilter = yield* select(statusFilter);
  const status = getCheckedStatus(dataStatusFilter);

  const dataDateTimeRangeFilter = yield* select(dateTimeRangeFilter);
  const customStartDate = yield* select(getCustomStartDate);
  const customEndDate = yield* select(getCustomEndDate);
  const isShowCustomRange = yield* select(getShowCustomRange);

  const fromDateTime = new Date();
  fromDateTime.setHours(fromDateTime.getHours() - dataDateTimeRangeFilter);
  let fromDateTimeIso = fromDateTime.toISOString();
  let toDateTimeIso = new Date().toISOString();
  // Update from and to dateTime values if a custom date range is selected
  if (isShowCustomRange && customEndDate !== null && customStartDate !== null) {
    fromDateTimeIso = customStartDate?.toJSDate().toISOString();
    toDateTimeIso = customEndDate?.toJSDate().toISOString();
  }
  const state = yield* select();
  const gql = GQLApi.newGQLApi(state);
  const param = {
    fromDateTimeIso,
    toDateTimeIso,
    status,
  };
  const response = yield* call([gql, gql.getProcessingJobs], param);
  if (response.errors) {
    const errors = [];
    for (const error of response.errors) {
      if (error?.message.includes('The requested TDO was not found')) {
        continue;
      }
      errors.push(error);
    }
    if (errors.length) {
      console.error('failed to fetchAllJobsFailed', errors);
    }
  }
  const jobs: Job[] = response.jobs ?? [];
  const jobsWithTdo = [];
  const jobsWithoutTdo = [];
  for (const job of jobs) {
    if (job.target?.id) {
      jobsWithTdo.push(job);
    } else {
      jobsWithoutTdo.push(job);
    }
  }
  if (jobsWithoutTdo.length) {
    const errMsg = jobsWithoutTdo.map((r) => r.id).join(', ');
    console.warn(`exporting failed job: failed jobs without tdo: ${errMsg}`);
  }
  return jobsWithTdo;
}

export function* watchRetryJobs() {
  yield* takeEvery(actions.RETRY_JOBS, retryJobs);
}

function* retryJobs() {
  const batchSize = 10;
  const jobsFailed = yield* select(getDataJobsFailed);
  const isSelectedAll = yield* select(getIsSelectedAllJobsFailed);
  let dataJobsFailed: Job[];
  if (isSelectedAll) {
    dataJobsFailed = yield* call(fetchAllJobsFailed);
  } else {
    dataJobsFailed = jobsFailed;
  }

  let numOfSuccess = 0;
  if (dataJobsFailed.length === 0) {
    // show message if get jobs failed
    yield* showNotificationFailed();
    yield* put(actions.retryJobsFailed());
    return;
  }
  for (let i = 0; i < dataJobsFailed.length; i += batchSize) {
    const batch = dataJobsFailed.slice(i, i + batchSize);
    const jobResults = yield* all(
      batch.map((job) => {
        return call(recreateFailedJob, { id: job.id, targetId: job.target.id });
      })
    );
    for (const jobResult of jobResults) {
      if (!jobResult?.error) {
        numOfSuccess++;
      }
    }
    let percentage = ((i + batchSize) / dataJobsFailed.length) * 100;
    if (percentage > 100) {
      percentage = 100;
    }
    yield* put(actions.updatePercentageFailedJobsUploaded(percentage));
  }

  yield* put(
    showNotification(
      renderMessage(numOfSuccess, dataJobsFailed.length - numOfSuccess),
      'success'
    )
  );
  yield* put(actions.retryJobsSuccess());
}

export function* watchExportFailedJobs() {
  yield* takeEvery(actions.EXPORT_FAILED_JOBS, exportFailedJobs);
}

function* exportFailedJobs() {
  const jobsFailed = yield* select(getDataJobsFailed);
  const isSelectedAll = yield* select(getIsSelectedAllJobsFailed);
  let errorNum = 0;
  try {
    let failedJobs: Job[];
    if (isSelectedAll) {
      failedJobs = yield* call(fetchFailedJobForExport);
    } else {
      failedJobs = jobsFailed;
    }
    const { content, count } = buildFailedJobReport(failedJobs);
    errorNum = count;
    const filename = generateExportName();
    yield* call(downloadFile, filename, content);
  } catch (e) {
    console.error('failed to export errors', e);
    yield* put(showNotification('failed to export errors', 'error'));
    yield* put(actions.exportJobsFailed());
    return;
  }
  const message = `${errorNum} errors are exported successfully `;
  yield* put(showNotification(message, 'success'));
  yield* put(actions.exportJobsSuccess());
}

function* recreateFailedJob(dataJobsFailed: { id: string; targetId: string }) {
  const defaultClusterId = yield* select(getDefaultClusterId);
  const state = yield* select();
  const gql = GQLApi.newGQLApi(state);
  try {
    const jobResults = yield* call([gql, gql.getJob], dataJobsFailed.id);
    const variables = {
      input: {
        targetId: dataJobsFailed.targetId,
        tasks: jobResults?.tasks?.records,
        routes: jobResults?.jobConfig?.routes,
        clusterId: defaultClusterId,
      },
    };

    const jobId = yield* call([gql, gql.createJob], variables);
    return { jobId: jobId, error: null };
  } catch (e) {
    console.error('failed to recreate job', e);
    return { jobId: dataJobsFailed.id, error: e };
  }
}

function* showNotificationFailed() {
  yield* put(
    showNotification(
      'An error occurred while creating the job. Please try again later',
      'error'
    )
  );
}

function renderMessage(success: number, failed: number) {
  let message = `${success} jobs are created successfully `;
  if (failed > 0) {
    message += `${failed} jobs are failed`;
  }
  return message;
}

function generateExportName(): string {
  const [month, day, yearTime] = new Date().toLocaleString('en-US').split('/');
  const [year, time] = yearTime?.split(',') || [];

  const formattedDay = (day || '00').padStart(2, '0');
  const formattedMonth = (month || '00').padStart(2, '0');
  const formattedYear = year || '0000';
  const formattedTime = (time || '00:00:00 AM').replace(/[: ]/g, '_');

  return `errors_${formattedYear}${formattedMonth}${formattedDay}${formattedTime}`;
}

function buildFailedJobReport(failedJobs: Job[]) {
  let count = 0;
  let content =
    'File ID|Filename|Job ID|Job Submitted|Job Failed|Task ID|Engine ID|Engine Name|Error Details\n';
  for (const job of failedJobs) {
    for (const task of job.tasks?.records ?? []) {
      if (!actions.TASK_FAILED_STATUS.includes(task.status)) {
        continue;
      }
      count++;
      const fileId = job.target?.id ?? '';
      const filename = job.target?.name ?? '';
      const jobId = job.id;
      const jobSubmittedDateTime = job.createdDateTime;
      const JobFailedDateTime = task.completedDateTime;
      const taskId = task.id ?? '';
      const engineId = task.engine?.id ?? '';
      const engineName = task.engine.name ?? '';
      const errorDetails =
        task.taskOutput?.failureMessage ?? task.failureMessage ?? '';
      content =
        content +
        `${fileId}|${filename}|${jobId}|${jobSubmittedDateTime}|${JobFailedDateTime}|${taskId}|${engineId}|${engineName}|${errorDetails}` +
        '\n';
    }
  }
  return { content, count };
}

function downloadFile(filename: string, data: string) {
  const a = document.createElement('a');
  a.setAttribute(
    'href',
    'data:text/plain;charset=utf-8,' + encodeURIComponent(data)
  );
  a.setAttribute('download', filename);
  a.click();
}

export default function* processingJobs() {
  yield* all([
    fork(watchFetchProcessingJobs),
    fork(watchRetryJobs),
    fork(watchExportFailedJobs),
  ]);
}
