import { Folder } from './Item';
import { render, fireEvent } from '@testing-library/react';

describe('Folder', () => {
  const defaultProps = {
    folder: {
      id: '4f2b2457-d86e-47fe-ba75-67d4f51c41c1',
      name: 'My <PERSON><PERSON>',
      treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
      expanded: true,
      fetchingSubFolders: false,
      subfolders: ['81f28961-4fef-497f-b98f-23fbaa3b460'],
      count: 1,
      root: true,
      parentId: '',
      level: 1,
      description: '',
      childFolders: {
        count: 1,
        records: [],
      },
      fetchingMoveSubFolders: false,
      parent: {
        id: '1',
      },
      folderPath: [
        {
          id: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
          name: 'My Case<PERSON>',
          treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
        },
      ],
    },
    fetchSubFolder: jest.fn(),
    allFolders: {
      '437f72f3-bfdd-44fa-9973-bbb1a6019ed1': {
        id: '437f72f3-bfdd-44fa-9973-bbb1a6019ed1',
        name: 'My Cases',
        treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
        expanded: true,
        fetchingSubFolders: false,
        subfolders: ['81f28961-4fef-497f-b98f-23fbaa3b460'],
        count: 1,
        root: true,
        parentId: '',
        level: 1,
        description: '',
        childFolders: {
          count: 1,
          records: [],
        },
        fetchingMoveSubFolders: false,
        parent: {
          id: '1',
        },
        folderPath: [
          {
            id: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
            name: 'My Cases',
            treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
          },
        ],
      },
      '81f28961-4fef-497f-b98f-23fbaa3b460': {
        id: '81f28961-4fef-497f-b98f-23fbaa3b460',
        name: 'My Cases',
        treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
        expanded: true,
        fetchingSubFolders: false,
        subfolders: ['81f28961-4fef-497f-b98f-23fbaa3b460'],
        count: 1,
        root: true,
        parentId: '',
        level: 1,
        description: '',
        childFolders: {
          count: 1,
          records: [],
        },
        fetchingMoveSubFolders: false,
        parent: {
          id: '1',
        },
        folderPath: [
          {
            id: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
            name: 'My Cases',
            treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
          },
        ],
      },
    },
    handleShowMoveFolder: jest.fn(),
    handleShowCreateFolder: jest.fn(),
    handleShowConfirmModal: jest.fn(),
    fetchMoveFolder: jest.fn(),
    rootFolderId: '437f72f3-bfdd-44fa-9973-bbb1a6019ed1',
    selectedFolderIds: [
      {
        id: '437f72f3-bfdd-44fa-9973-bbb1a6019ed1',
        level: 10,
      },
    ],
    setOpenFolderId: jest.fn(),
    setStatusExpanded: jest.fn(),
    setSelectedFolderId: jest.fn(),
    fetchMoveSubFolder: jest.fn(),
    setSelectedMoveFolderId: jest.fn(),
    setStatusMoveExpanded: jest.fn(),
  };
  const propsWithRootType = {
    ...defaultProps,
    type: 'root',
  };
  const propsWithSubType = {
    ...defaultProps,
    type: 'sub',
  };

  it('renders a List with root type', () => {
    const { getByTestId } = render(<Folder {...propsWithRootType} />);
    expect(getByTestId('list')).toBeInTheDocument();
  });
  it('renders a ListItem with root type', () => {
    const { getByTestId } = render(<Folder {...propsWithRootType} />);
    expect(getByTestId('list-item')).toBeInTheDocument();
  });
  it('renders a ListItemIcon with root type', () => {
    const { getByTestId } = render(<Folder {...propsWithRootType} />);
    expect(getByTestId('list-item-icon')).toBeInTheDocument();
  });
  it('renders a ListItemText with root type', () => {
    const { getByTestId } = render(<Folder {...propsWithRootType} />);
    expect(getByTestId('list-item-text')).toBeInTheDocument();
  });

  it('renders a List with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('list')).toBeInTheDocument();
  });
  it('renders a ListItem with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('list-item')).toBeInTheDocument();
  });
  it('renders a ListItemIcon with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('list-item-icon')).toBeInTheDocument();
  });
  it('renders a ListItemText with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('list-item-text')).toBeInTheDocument();
  });
  it('renders a IconButton', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('icon-button')).toBeInTheDocument();
  });
  it('renders a WorkIcon with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('work-icon')).toBeInTheDocument();
  });
  it('renders a MoreVertIcon with sub type', () => {
    const { getByTestId } = render(<Folder {...propsWithSubType} />);
    expect(getByTestId('more-vert-icon')).toBeInTheDocument();
  });
  it('should handle click event', () => {
    const { getByRole, getByTestId, getAllByTestId, queryByTestId } = render(
      <Folder {...propsWithSubType} />
    );
    fireEvent.click(getByRole('button', { name: /menu-button/i }));
    expect(getByTestId('menu')).toBeInTheDocument();
    expect(getAllByTestId('menu-item')).toHaveLength(2);
    expect(getAllByTestId('menu-item')).toHaveLength(2);
    expect(queryByTestId('folder-icon')).not.toBeInTheDocument();
  });
});
