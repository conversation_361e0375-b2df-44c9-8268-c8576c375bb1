// External dependencies
import { all, put, select, takeLatest } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
import { isEmpty } from 'lodash';

// Internal dependencies
import { showNotification } from '../snackbar';
import { Action } from 'state/sagas';
import {
  BulkExportOption,
  BulkExportCustomizedNames,
  ExportTemplateContent,
} from 'src/model';
import {
  getExportTemplates,
  getExportTemplateDataSchemaId,
  upsertExportTemplate,
  deleteExportTemplate,
  getSchemaId,
  FETCH_EXPORT_TEMPLATE_SCHEMA_ID_SUCCESS,
  FETCH_SAVE_EXPORT_TEMPLATE_DATA_REQUEST,
  FETCH_SAVE_EXPORT_TEMPLATE_DATA_SUCCESS,
  FETCH_SAVE_EXPORT_TEMPLATE_DATA_FAILURE,
  FETCH_DELETE_EXPORT_TEMPLATE_DATA_REQUEST,
  FETCH_DELETE_EXPORT_TEMPLATE_DATA_SUCCESS,
  FETCH_DELETE_EXPORT_TEMPLATE_DATA_FAILURE,
} from './exportTemplate';

const {
  user: { selectUser },
} = modules;

export function* fetchExportTemplateSchemaId() {
  yield* put(getExportTemplateDataSchemaId());
}

export function* watchFetchExportTemplate() {
  yield* all([
    takeLatest(
      [
        FETCH_EXPORT_TEMPLATE_SCHEMA_ID_SUCCESS,
        FETCH_SAVE_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_DELETE_EXPORT_TEMPLATE_DATA_SUCCESS,
      ],
      handleFetchExportTemplate
    ),
  ]);
}

function* handleFetchExportTemplate() {
  const schemaId = yield* select(getSchemaId);

  if (schemaId) {
    yield* put(getExportTemplates(schemaId));
  }
}

export function* watchSaveExportTemplate() {
  yield* all([
    takeLatest(
      FETCH_SAVE_EXPORT_TEMPLATE_DATA_REQUEST,
      handleSaveExportTemplate
    ),
    takeLatest(
      [
        FETCH_SAVE_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_SAVE_EXPORT_TEMPLATE_DATA_FAILURE,
      ],
      function* (action) {
        if (FETCH_SAVE_EXPORT_TEMPLATE_DATA_FAILURE.match(action)) {
          yield* put(showNotification('Unable to save template', 'error'));
        } else {
          yield* put(showNotification('Template saved', 'success'));
        }
      }
    ),
  ]);
}

function* handleSaveExportTemplate(
  action: Action<{
    name: string;
    fields: BulkExportOption;
    customizedNames: BulkExportCustomizedNames;
    id: string;
  }>
) {
  const schemaId = yield* select(getSchemaId);
  const { userData } = yield* doGetUserData();

  const exportTemplateContent: ExportTemplateContent = {
    name: action.payload.name,
    userId: userData?.userId ?? '',
    fields: action.payload.fields,
  };
  if (!isEmpty(action.payload.customizedNames)) {
    exportTemplateContent.customizedNames = action.payload.customizedNames;
  }

  // create export template when id is empty
  // update export template when id is nonempty
  const id = action?.payload?.id ?? '';
  yield* put(upsertExportTemplate(schemaId, id, exportTemplateContent));
}

export function* watchDeleteExportTemplate() {
  yield* all([
    takeLatest(
      FETCH_DELETE_EXPORT_TEMPLATE_DATA_REQUEST,
      handleDeleteExportTemplate
    ),
    takeLatest(
      [
        FETCH_DELETE_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_DELETE_EXPORT_TEMPLATE_DATA_FAILURE,
      ],
      function* (action) {
        if (FETCH_DELETE_EXPORT_TEMPLATE_DATA_FAILURE.match(action)) {
          yield* put(showNotification('Unable to delete template', 'error'));
        } else {
          yield* put(showNotification('Template deleted', 'success'));
        }
      }
    ),
  ]);
}

function* handleDeleteExportTemplate(action: Action<{ id: string }>) {
  const schemaId = yield* select(getSchemaId);

  yield* put(deleteExportTemplate(schemaId, action.payload.id));
}

function* doGetUserData() {
  const userInfo = yield* select(selectUser);
  const userData = {
    userId: userInfo.userId,
  };
  return { userData };
}
