import React from 'react';
import SimpleProcessing from '../simpleProcessing';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('SimpleProcessing', () => {
  const props = {
    handleShowAdvancedCognitive: jest.fn(),
    engineCategories: [
      {
        id: '581dbb32-ea5b-4458-bd15-8094942345e3',
        name: 'Transcode',
        iconClass: 'icon-engine-transcode',
        description: 'Converts incoming data formats to another.',
      },
    ],
    handleClickDagTemplate: jest.fn(),
    handleRunJobTemplate: jest.fn(),
    dagTemplateByCategorySelected: {
      '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923': {
        id: 'febe3650-30ba-4572-89c5-aa1d3a48b6b5',
        name: 'Translation - Document (PDF, DOCX, EML) - Spanish to English',
        description: 'Document (PDF, DOCX, EML) - Spanish to English',
        tags: ['illuminate-simple-workflow'],
        cognitiveCategoryId: '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923',
      },
    },
    dagTemplatesByCategory: {
      '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923': [
        {
          id: 'bef25839-d4fe-4f31-8cba-73fc306064e0',
          name: 'illuminate_transcription_20',
          description: 'A Support to transcript audio file',
          tags: ['illuminate-simple-workflow'],
          cognitiveCategoryId: '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923',
        },
      ],
      '67cd4dd0-2f75-445d-a6f0-2f297d6cd182': [
        {
          id: '02b4ee91-2d35-448d-a9f1-043b243fafd5',
          name: 'Transcription - English (Global)',
          description: 'English (Global)',
          tags: ['illuminate-simple-workflow'],
          cognitiveCategoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        },
      ],
    },
    handleOpenFolder: jest.fn(),
    selectedFolder: {
      id: '4f2b2457-d86e-47fe-ba75-67d4f51c41c1',
      name: 'My Cases',
      treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
      expanded: true,
      fetchingSubFolders: false,
      count: 104,
      root: true,
      parentId: null,
      level: 1,
      description: null,
    },
    onKeyPress: jest.fn(),
    handleOnChangeTagsCustomize: jest.fn(),
    tagsCustomizeName: '',
    tagsCustomize: [
      {
        value: 'test',
      },
    ],
    handleRemoveTagsCustomize: jest.fn(),
    onClickAddTags: jest.fn(),
    isReprocess: false,
    showProcessByCategory: {},
  };

  it('renders a input folder', () => {
    const { getByTestId } = render(<SimpleProcessing {...props} />);
    expect(getByTestId('input-folder')).toHaveValue('My Cases');
  });
  it('renders a TagsCustomize component', () => {
    const { getByTestId } = render(<SimpleProcessing {...props} />);
    expect(getByTestId('tags-customize')).toBeInTheDocument();
  });
  it('renders a title with content Simple Cognitive Workflow', () => {
    const { getByText } = render(<SimpleProcessing {...props} />);
    expect(getByText('Simple Cognitive Workflow')).toBeInTheDocument();
  });
  it('renders a title with content Show Advanced Cognitive Workflow', () => {
    const { getByText } = render(<SimpleProcessing {...props} />);
    expect(getByText('Show Advanced Cognitive Workflow')).toBeInTheDocument();
  });
  it('renders a title with content No available engines.', () => {
    const newProps = {
      ...props,
      dagTemplatesByCategory: {},
    };
    const { getByText } = render(<SimpleProcessing {...newProps} />);
    expect(getByText('No available engines.')).toBeInTheDocument();
  });
  it('renders 2 Grid component', () => {
    const { getAllByTestId } = render(<SimpleProcessing {...props} />);
    expect(getAllByTestId('engine-category')).toHaveLength(
      Object.keys(props.dagTemplatesByCategory).length
    );
  });
  it('click Show Advanced Cognitive Workflow', () => {
    const { getByTestId } = render(<SimpleProcessing {...props} />);
    fireEvent.click(getByTestId('show-advanced-cognitive-workflow'));
    expect(props.handleShowAdvancedCognitive).toHaveBeenCalled();
  });
});
