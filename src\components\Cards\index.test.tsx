import { AnalyzeHeader } from './index';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

describe(AnalyzeHeader, () => {
  const props = {
    totalDuration: 'totalDuration',
    totalNumber: 100,
    isLoading: false,
    durPerEngineClass: [
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd181',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
    ],
    totalMediaProcessedTime: '100',
    dispatch: jest.fn()
  };

  it('renders 4 DashboardCard', () => {
    const { getAllByTestId } = render(<AnalyzeHeader {...props} />);
    expect(getAllByTestId('card')).toHaveLength(4);
  });
});
