import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                limit: 10000
                name: "disability"
                field: "disability"
                operator: "term"
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'disabilityAggregation',
      dataKey: 'disability',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Disabilities Distribution',
  filterTextType: 'Disabilities Distribution by Type',
  filterType: 'disability',
  filterTerms: [
    'Blind or limited vision',
    'Mental health condition',
    'Other disability',
    'Deafness or difficulty hearing',
    'Hyperactivity or impulsive behavior',
    'Intellectual or developmental disability, including dementia',
    'Speech impairment or limited use of language',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataMap = {
      'Blind or limited vision': 0,
      'Mental health condition': 0,
      'Other disability': 0,
      'Deafness or difficulty hearing': 0,
      'Hyperactivity or\n impulsive behavior': 0,
      'Intellectual or\n developmental disability,\n including dementia': 0,
      'Speech impairment\n or limited use\n of language': 0,
      ...data?.disabilityAggregation?.reduce(
        (acc: { [key: string]: number }, b) => {
          if (
            b.key ===
            'Intellectual or developmental disability, including dementia'
          ) {
            acc[
              'Intellectual or\n developmental disability,\n including dementia'
            ] = b.doc_count;
          } else if (b.key === 'Hyperactivity or impulsive behavior') {
            acc['Hyperactivity or\n impulsive behavior'] = b.doc_count;
          } else if (b.key === 'Speech impairment or limited use of language') {
            acc['Speech impairment\n or limited use\n of language'] =
              b.doc_count;
          } else {
            acc[b.key] = b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = Object.entries(dataMap).map((kvp) => {
      const [disability, v] = kvp;
      return { disability, count: v };
    });

    // Create axes
    const categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = 'disability';
    categoryAxis.title.text = 'Disability Type';

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Number of People';

    // Create series
    const series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = 'count';
    series.dataFields.categoryX = 'disability';
    series.name = 'Number of People';
    series.columns.template.tooltipText =
      'Series: {name}\nCategory: {categoryX}\nValue: {valueY}';
    series.columns.template.fill = am4core.color('#104547');

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  disabilityAggregation: { key: string; doc_count: number }[];
}
