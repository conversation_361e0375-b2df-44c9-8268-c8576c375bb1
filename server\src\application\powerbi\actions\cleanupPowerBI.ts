import axios from 'axios';
import { ActionError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface ProfilesResponse {
  '@odata.context': string;
  value: {
    id: string;
    displayName: string;
  }[];
}

export interface GroupsResponse {
  '@odata.context': string;
  value: {
    id: '1418a35b-356f-4c11-9c9d-2c50973697cb';
    isReadOnly: false;
    isOnDedicatedCapacity: true;
    capacityId: 'F10FA8CE-0ACF-4A5C-9705-9B16383417FB';
    defaultDatasetStorageFormat: 'Small';
    type: 'Workspace';
    name: 'TestOrgName64 Workspace';
  }[];
}

const getProfiles = async (
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string }
) =>
  await axios.get<ProfilesResponse>(
    `${powerbiApiRoot}/${powerbiApiVersionOrg}/profiles`,
    { headers: { Authorization: data.pbiBearerToken } }
  );

const deleteWorkspace = async (
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string },
  workspaceId: string,
  profileId: string
) =>
  await axios.delete<void>(
    `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${workspaceId}`,
    {
      headers: {
        Authorization: data.pbiBearerToken,
        'X-PowerBI-Profile-Id': profileId,
      },
    }
  );

const deleteProfile = async (
  log: Logger,
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string },
  profileId: string,
  displayName: string
) => {
  log.info(`Deleting profile ${displayName}`);

  await axios.delete<void>(
    `${powerbiApiRoot}/${powerbiApiVersionOrg}/profiles/${profileId}`,
    { headers: { Authorization: data.pbiBearerToken } }
  );
};

const getProfileWorkspaces = async (
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string },
  profileId: string
) =>
  await axios.get<GroupsResponse>(
    `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups`,
    {
      headers: {
        Authorization: data.pbiBearerToken,
        'X-PowerBI-Profile-Id': profileId,
      },
    }
  );

const createDeleteWorkspacesPromises = (
  groupsResponse: GroupsResponse,
  log: Logger,
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string },
  profileId: string
) =>
  groupsResponse.value.map(async ({ id: workspaceId }) => {
    log.info(`Deleting workspace ${workspaceId}`);

    await deleteWorkspace(
      powerbiApiRoot,
      powerbiApiVersionOrg,
      data,
      workspaceId,
      profileId
    );
  });

const createDeleteProfileAssetsPromises = (
  profilesResponse: ProfilesResponse,
  powerbiApiRoot: string,
  powerbiApiVersionOrg: string,
  data: { pbiBearerToken: string },
  log: Logger
) =>
  profilesResponse.value.map(async ({ id: profileId, displayName }) => {
    log.info(`Cleaning up profile ${displayName}: ${profileId}`);

    try {
      const { data: groupsResponse } = await getProfileWorkspaces(
        powerbiApiRoot,
        powerbiApiVersionOrg,
        data,
        profileId
      );
      const deleteProfileAssetsPromises = createDeleteWorkspacesPromises(
        groupsResponse,
        log,
        powerbiApiRoot,
        powerbiApiVersionOrg,
        data,
        profileId
      );

      try {
        await Promise.allSettled(deleteProfileAssetsPromises);
      } catch (e) {
        log.error('Delete profile assets failed:', e);
      }
      try {
        await deleteProfile(
          log,
          powerbiApiRoot,
          powerbiApiVersionOrg,
          data,
          profileId,
          displayName
        );
      } catch (e) {
        log.error('Delete profile failed:', e);
      }
    } catch (e) {
      log.error('Get profile workspaces failed:', e);
    }
  });

/* This will cleanup all the server-side PowerBI data - all workspaces, profiles, datasets, etc. */
/* It does not affect anything in the MsSql DB. You will probably want to drop and re-add all the  */
/* tables because the data will be out of sync. */
const cleanupPowerBI = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  if (data.code !== 'its ok, i know what i am doing 🤟') {
    const msg = 'Are you sure you should be calling this?';
    log.error(msg);
    throw new ActionError(msg);
  }

  const { data: profilesResponse } = await getProfiles(
    powerbiApiRoot,
    powerbiApiVersionOrg,
    data
  );

  const deleteProfileAssetsPromises = createDeleteProfileAssetsPromises(
    profilesResponse,
    powerbiApiRoot,
    powerbiApiVersionOrg,
    data,
    log
  );

  await Promise.allSettled(deleteProfileAssetsPromises);
};

export default cleanupPowerBI;
