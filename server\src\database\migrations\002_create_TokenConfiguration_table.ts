import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('TokenConfiguration').then(async (tableExists: boolean) => {
      if (!tableExists) {
        await knex.schema.raw(`CREATE TABLE TokenConfiguration (
          id int IDENTITY(1,1) PRIMARY KEY,
          orgId smallint UNIQUE,
          reportId varchar(64),
          datasetId varchar(64),
          profileId varchar(64),
          workspaceId varchar(64),
          embedUrl varchar(1024),
          lifetimeInMinutes smallint,
          pbixFilePath varchar(512),
        )`);
        return true;
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('TokenConfiguration')
  ])
