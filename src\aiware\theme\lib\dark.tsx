import { createTheme } from '@mui/material/styles';
import { deepOrange, orange } from '@mui/material/colors';
import { sharedTypography } from './sharedTypography';
import { defaultOverrides } from './defaultThemeOverrides';
import { defaultThemeProps } from './defaultThemeProps';

const colors = {
  primaryMain: orange[500],
  secondaryMain: deepOrange[900],
  textPrimary: '#E5E5E5',
  textSecondary: '#5C6269',
  tableheadColor: '#465364',
  tableRowSelectedBackground: '#F2F5F9',
  buttonContainedPrimaryHoverBackground: '#0D62D2',
  buttonDisabledColor: '#9CA8B4',
  buttonDisabledBackground: '#D5DFE9',
  buttonOutlinedBackground: '#EEF3F9',
  linkColor: '#6098D1',
  iconRootColor: '#555F7C',
  iconRootHover: '#1B1D1F',
  iconPrimaryHover: '#0D62D2',
  backgroundColor: '#1B1D1F',
  backgroundPaper: '#D5DFE9',
  divider: 'rgba(0,0,0,0.12)',
};

export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: colors.primaryMain,
    },
    secondary: {
      main: colors.secondaryMain,
    },
    text: {
      primary: colors.textPrimary,
      secondary: colors.textSecondary,
    },
    background: {
      default: colors.backgroundColor,
    },
    divider: colors.divider,
  },
  spacing: 5,
  typography: sharedTypography(),
  components: {
    ...defaultOverrides(colors),
    MuiDivider: {
      styleOverrides: {
        background: colors.divider,
      },
    },
    ...defaultThemeProps(),
  },
});

export default darkTheme;
