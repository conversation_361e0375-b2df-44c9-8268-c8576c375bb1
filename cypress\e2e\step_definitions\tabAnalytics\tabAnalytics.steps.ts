import {
  Given,
  Then,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';

Given('the user is logged in and on the Analytics page', () => {
  cy.LoginLandingPage();
  cy.NavigateToTestFolder();
  cy.get('[data-test="dashboard-card/render-card/Speaker Detection"]', {
    timeout: 60000,
  }).should('be.visible');
});

Then(
  'the user sees the summary cards with the following values:',
  (dataTable: DataTable) => {
    dataTable.hashes().forEach((row: { [key: string]: string }) => {
      const { 'Card Title': cardTitle, Value: value } = row;
      cy.get(`[data-test="${cardTitle}"]`).within(() => {
        cy.get('[data-testid="typography"]').should('have.text', value);
      });
    });
  }
);

Then(
  'the user sees the engine metrics with the following values:',
  (dataTable: DataTable) => {
    dataTable.hashes().forEach((row: { [key: string]: string }) => {
      const { 'Engine Name': engineName, Value: value } = row;
      cy.get(`[data-test="dashboard-card/render-card/${engineName}"]`).should(
        'have.text',
        value
      );
    });
  }
);

Then(
  'the user sees the following widgets are displayed:',
  (dataTable: DataTable) => {
    dataTable.hashes().forEach((row: { [key: string]: string }) => {
      const { widget } = row;
      if (widget) {
        cy.get(
          `[data-testid="${widget.toLowerCase().replace(' ', '')}"]`
        ).should('be.visible');
      }
    });
  }
);
