import React, { Fragment } from 'react';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Forward from '@mui/icons-material/Forward';
import makeStyles from '@mui/styles/makeStyles';
import AvailableEngine from './availableEngine';
import EngineSelected from './engineSelected';
import styles from './styles';
import {
  EnginesSelected,
  EngineCategory,
  EngineByCategories,
  Template,
  Library,
} from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);
function Processing({
  handleShowAdvancedCognitive,
  engineCategories,
  librariesByCategories,
  enginesSelected,
  currentEngineCategory,
  handleChangeEngine,
  handleSearchEngine,
  engineByCategories,
  engineNameSearch,
  templateSelected,
  handleChangeTemplates,
  templates,
  handleShowModalSaveTemplate,
  checkValidateLibrary,
  handleAddEngine,
  handleExpandClick,
  handleRemoveEngine,
  handleChangeLibrariesEngineSelected,
  handleChangeFieldsEngine,
  handleChangeJobPriority,
  handleRemoveTemplate,
  // loadingRemoveTemplate,
  isReprocess,
}: Props) {
  const classes = useStyles();
  return (
    <Fragment>
      <Grid container spacing={3} data-testid="processing">
        <Grid item xs={8}>
          <Typography component="p" className={classes.titleProcessing}>
            Advanced Cognitive Workflow
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography
            component="p"
            className={classes.showAdvanced}
            onClick={handleShowAdvancedCognitive}
            data-test="show-simple-cognitive-workflow"
            data-testid="show-simple-cognitive-workflow"
          >
            Show Simple Cognitive Workflow
          </Typography>
        </Grid>
      </Grid>

      <Typography color="textSecondary" gutterBottom>
        Build out the exact workflow you need by selecting from available
        categories and engines on the left and adding them to your new workflow
        on the right.
      </Typography>

      <Grid container spacing={3} style={{ marginTop: 10 }}>
        <Grid
          item
          xs={5}
          className={classes.availableEngines}
          data-testid="available-engine-grid"
        >
          <AvailableEngine
            currentEngineCategory={currentEngineCategory}
            handleChangeEngine={handleChangeEngine}
            engineCategories={engineCategories}
            handleSearchEngine={handleSearchEngine}
            engineByCategories={engineByCategories}
            enginesSelected={enginesSelected}
            engineNameSearch={engineNameSearch}
            handleAddEngine={handleAddEngine}
            isReprocess={isReprocess}
          />
        </Grid>
        <Grid
          item
          xs={2}
          className={classes.iconSelectedEngines}
          data-testid="forward-grid"
        >
          <Forward />
        </Grid>
        <Grid
          item
          xs={5}
          className={classes.selectedEngines}
          data-testid="engine-selected-grid"
        >
          <EngineSelected
            librariesByCategories={librariesByCategories}
            enginesSelected={enginesSelected}
            engineByCategories={engineByCategories}
            templateSelected={templateSelected}
            handleChangeTemplates={handleChangeTemplates}
            templates={templates}
            handleShowModalSaveTemplate={handleShowModalSaveTemplate}
            checkValidateLibrary={checkValidateLibrary}
            handleExpandClick={handleExpandClick}
            handleRemoveEngine={handleRemoveEngine}
            handleChangeLibrariesEngineSelected={
              handleChangeLibrariesEngineSelected
            }
            handleChangeFieldsEngine={handleChangeFieldsEngine}
            handleChangeJobPriority={handleChangeJobPriority}
            handleRemoveTemplate={handleRemoveTemplate}
            // loadingRemoveTemplate={loadingRemoveTemplate}
            isReprocess={isReprocess}
          />
        </Grid>
      </Grid>
    </Fragment>
  );
}
interface Props {
  handleShowAdvancedCognitive: () => void;
  engineCategories: EngineCategory[];
  librariesByCategories: {
    [key: string]: {
      [key: string]: Library;
    };
  };
  enginesSelected: EnginesSelected[];
  currentEngineCategory: string;
  handleChangeEngine: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSearchEngine: (event: React.ChangeEvent<HTMLInputElement>) => void;
  engineByCategories: EngineByCategories;
  engineNameSearch: string;
  templateSelected: string;
  handleChangeTemplates: (event: React.ChangeEvent<HTMLInputElement>) => void;
  templates: Template[];
  handleShowModalSaveTemplate: () => void;
  checkValidateLibrary: boolean;
  handleAddEngine: (event: React.MouseEvent<HTMLElement>) => void;
  handleExpandClick: (event: React.MouseEvent<HTMLElement>) => void;
  handleRemoveEngine: (event: React.MouseEvent<HTMLElement>) => void;
  handleChangeLibrariesEngineSelected: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeFieldsEngine: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeJobPriority: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleRemoveTemplate: (
    event: React.MouseEvent<HTMLElement>,
    id: string
  ) => void;
  loadingRemoveTemplate: boolean;
  isReprocess: boolean;
}
export default Processing;
