#!/bin/bash
. /getconfig-dynamicConfig.sh
echo "created env based config "
. /dynamicConfig-index-html.sh
echo "updated index.html"
cat /usr/share/nginx/html/index.html

shouldStartApi=$( jq -r .startApi /api/apiConfig.json )

if [ $shouldStartApi = "true" ]; then
  echo "Starting ngnix server...."
  nginx -g 'daemon on;';

  echo "Starting Node server...."
  exec node /api/src/start-server.js
else
  echo "Not Starting API"
  echo "Starting ngnix server...."
  nginx -g 'daemon off;';
fi
