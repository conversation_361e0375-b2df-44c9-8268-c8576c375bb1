import { DateTime } from 'luxon';
import { modules } from '@veritone/glc-redux';
import getApiAuthToken from './util/getApiAuthToken';
import getApiRoot from './util/getApiRoot';
import getGraphQLEndpoint from './util/getGraphQLEndpoint';
import callGraphQLApi from './util/callGraphQLApi';
import { fetchGraphQLApiThrowError } from './util/apiHelper';
import { createAction } from '@reduxjs/toolkit';
import {
  createGQLFailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '~helpers/callGraphQLApi';

const {
  config: { getConfig },
} = modules;

export const REQUERY = createAction(
  '[worker] requery all contact analytics data'
);
export const SET_CHARTS = createAction<{
  charts: any;
}>('[worker] set contact analytics charts');
export const SET_SETTINGS = createAction<{
  currentReportIndex: number;
  reports: any;
}>('[worker] set settings');
export const GET_CHART_DATA = createAction<{
  containerId: string;
  chartDef: any;
}>('[worker] request to search contact chart data');
export const SET_CHART_DATA = createAction<{
  containerId: string;
  data: any;
  chartDef: any;
  totalDataQueries: any;
  variables: any;
}>('[worker] set contact chart data');
export const SET_HAS_DATA = createAction<{
  containerId: string;
  hasAllData: boolean;
}>('[worker] chart has new data');
export const SET_HAS_RENDERED = createAction<{
  containerId: string;
  hasRendered: boolean;
}>('[worker] set has chart rendered');
export const SET_ANALYTICS_DATE_RANGE = createAction<{
  lowerDateBound: string;
  upperDateBound: string;
}>('[worker] set analytics date range');
export const REMOVE_CHART_DATA = createAction<{
  containerId: string;
}>('[worker] remove chart data');
export const ADD_QUERY_PENDING = createAction('[worker] add query pending');
export const SUBTRACT_QUERY_PENDING = createAction(
  '[worker] subtract query pending'
);
export const SET_HAS_FAILED = createAction<{
  containerId: string;
}>('[worker] set chart data query failed');
export const SET_CONTACT_ANALYTICS_CONFIG = createGQLRequestAction(
  '[worker] set contact analytics user config'
);
export const SET_CONTACT_ANALYTICS_CONFIG_SUCCESS =
  createGQLSuccessAction<UpdateChartUserConfigResponse>(
    '[worker] set contact analytics user config success'
  );
export const SET_CONTACT_ANALYTICS_CONFIG_FAILURE = createGQLFailureAction(
  '[worker] set contact analytics user config failure'
);
export const GET_CONTACT_ANALYTICS_CHART_CONFIG = createGQLRequestAction(
  '[worker] fetch contact analytics chart config'
);
export const GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS =
  createGQLSuccessAction<FetchChartUserConfigResponse>(
    '[worker] fetch contact analytics chart config success'
  );
export const GET_CONTACT_ANALYTICS_CHART_CONFIG_FAILURE =
  createGQLFailureAction(
    '[worker] fetch contact analytics chart config failure'
  );
export const SET_VARS = createAction<{
  schemaIdContactAnalyticsSDO: string;
  orgName: string;
  applicationId: string;
  config: object;
}>('[worker] set contact analytics vars');
export const SET_CHART_SETTINGS = createAction<{
  aggSize: string;
  useLegendSetting: boolean;
  useTitleSetting: boolean;
  lineWidthSetting: number;
  settingChanges: string[];
}>('[worker] set chart settings');
export const SET_CURRENT_REPORT = createAction<{
  currentReportIndex: number;
}>('[worker] set current report');
export const ADD_REPORT = createAction('[worker] add report');
export const REMOVE_REPORT = createAction<{
  reportIndex: number;
}>('[worker] remove report');
export const SET_REPORT_NAME = createAction<{
  reportIndex: number;
  name: string;
}>('[worker] set report name');
export const SET_LOADING_USER_CONFIG = createAction<{
  loadingUserConfig: any;
}>('[worker] set loading user config');
export const CONTACT_ANALYTICS_BOOT = createAction(
  '[worker] contact analytics boot'
);

export const setVars = (
  schemaIdContactAnalyticsSDO: string,
  orgName: string,
  applicationId: string,
  config: object
) =>
  SET_VARS({
    schemaIdContactAnalyticsSDO,
    orgName,
    applicationId,
    config,
  });

export const setChartData = ({
  containerId,
  data,
  chartDef,
  totalDataQueries,
  variables,
}: {
  containerId: string;
  data: any;
  chartDef: any;
  totalDataQueries: any;
  variables: any;
}) =>
  SET_CHART_DATA({ containerId, data, chartDef, totalDataQueries, variables });

export const setLoadingUserConfig = (loadingUserConfig: any) =>
  SET_LOADING_USER_CONFIG({ loadingUserConfig });

export const getChartData = ({
  containerId,
  chartDef,
}: {
  containerId: string;
  chartDef: any;
}) => GET_CHART_DATA({ containerId, chartDef });

export const requeryData = () => REQUERY();

export const setHasData = ({
  containerId,
  hasAllData,
}: {
  containerId: string;
  hasAllData: boolean;
}) => SET_HAS_DATA({ containerId, hasAllData });

export const setHasRendered = ({
  containerId,
  hasRendered,
}: {
  containerId: string;
  hasRendered: boolean;
}) => SET_HAS_RENDERED({ containerId, hasRendered });

export const setAnalyticsDateRange = ({
  lowerDateBound,
  upperDateBound,
}: {
  lowerDateBound: string;
  upperDateBound: string;
}) => SET_ANALYTICS_DATE_RANGE({ lowerDateBound, upperDateBound });

export const removeChartData = (containerId: string) =>
  REMOVE_CHART_DATA({ containerId });

export const addQueryPending = () => ADD_QUERY_PENDING();

export const subtractQueryPending = () => SUBTRACT_QUERY_PENDING();

export const setHasFailed = ({ containerId }: { containerId: string }) =>
  SET_HAS_FAILED({ containerId });

export const setChartSettings = (
  {
    aggSize,
    useLegendSetting,
    useTitleSetting,
    lineWidthSetting,
  }: {
    aggSize: string;
    useLegendSetting: boolean;
    useTitleSetting: boolean;
    lineWidthSetting: number;
  },
  settingChanges: string[]
) =>
  SET_CHART_SETTINGS({
    aggSize,
    useLegendSetting,
    useTitleSetting,
    lineWidthSetting,
    settingChanges,
  });

export const setCharts = (charts: any) => SET_CHARTS({ charts });

export const setSettings = ({
  currentReportIndex,
  reports,
}: {
  currentReportIndex: number;
  reports: any;
}) => SET_SETTINGS({ currentReportIndex, reports });

export const setCurrentReport = ({
  currentReportIndex,
}: {
  currentReportIndex: number;
}) => SET_CURRENT_REPORT({ currentReportIndex });

export const addReport = () => ADD_REPORT();

export const removeReport = ({ reportIndex }: { reportIndex: number }) =>
  REMOVE_REPORT({ reportIndex });

export const setReportName = ({
  reportIndex,
  name,
}: {
  reportIndex: number;
  name: string;
}) => SET_REPORT_NAME({ reportIndex, name });

export const sendBoot = () => CONTACT_ANALYTICS_BOOT();

export const searchContactData =
  (
    dataQuery: any,
    containerId: string,
    chartDef: any,
    isDateRangeUpdate: boolean
  ) =>
  async (dispatch: any, getState: any) => {
    try {
      const state = getState();
      const endpoint = getApiRoot(state) + '/' + getGraphQLEndpoint(state);
      const token = getApiAuthToken(state);
      const config = getConfig<Window['config']>(state);
      const { reports, currentReportIndex, schemaIdContactAnalyticsSDO } =
        state;
      const { dateRange, aggregationSize, charts } =
        reports[currentReportIndex];
      const { filterType, filterTerms } = chartDef;
      const hasQueryFilters =
        JSON.stringify(filterTerms)?.includes('queryFilter');
      const variables = {
        ...dateRange,
        filterType: filterType ?? '',
        filterTerms: hasQueryFilters
          ? filterTerms
          : Array.isArray(filterTerms)
            ? filterTerms
            : Object.keys(filterTerms ?? {}).reduce(
                (acc: string[], k: string) => [...acc, ...filterTerms[k]],
                []
              ),
        schemaId: schemaIdContactAnalyticsSDO,
      };
      const { useFilter, filter } =
        charts.find((c: any) => c.containerId === containerId) ?? {};
      let query = dataQuery.query;

      if (
        useFilter === 'filter' &&
        variables.filterTerms[filter]?.storageKey &&
        variables.filterTerms[filter]?.storageKey !== dataQuery.storageKey
      ) {
        // Skipping this query because we have a queryFilter
        return;
      }

      if (isDateRangeUpdate) {
        dispatch(setHasData({ containerId, hasAllData: false }));
        dispatch(setHasRendered({ containerId, hasRendered: false }));
      }
      if (dataQuery.isAggregation) {
        variables.dateBucketSize = aggregationSize;
      }

      if (useFilter === 'filter') {
        variables.filterTerms = Array.isArray(filterTerms)
          ? [filter]
          : filterTerms[filter];
      }

      if (!filterType || hasQueryFilters) {
        query = query.replace(
          /\s*{\s*field: \$filterType\s*operator: "terms"\s*values: \$filterTerms\s+}/,
          ''
        );
        query = query.replace(/\$filterType: String/, '');
        query = query.replace(/\$filterTerms: \[String\]/, '');

        variables.filterTerms = undefined;
        variables.filterType = undefined;
      }

      // TODO: Figure out what type this should be
      const { data } = await fetchGraphQLApiThrowError<any>({
        variables,
        endpoint,
        token,
        query,
        veritoneAppId: config.veritoneAppId,
      });

      dispatch(
        setChartData({
          containerId,
          chartDef,
          variables,
          data: {
            [dataQuery.storageKey]: dataQuery.isAggregation
              ? data.searchMedia.jsondata.aggregations[dataQuery.dataKey]
                  .buckets
              : (data.searchMedia?.jsondata?.aggregations?._id?.value ??
                data.searchMedia.jsondata[dataQuery.dataKey]),
          },
          totalDataQueries:
            useFilter === 'filter' && hasQueryFilters
              ? 1
              : chartDef.dataQueries.length,
        })
      );
    } catch (e) {
      console.error(e);
      dispatch(setHasFailed({ containerId }));
    } finally {
      dispatch(subtractQueryPending());
    }
  };

export const searchContactDemographicData =
  (
    demographicsConfig: any,
    containerId: string,
    chartDef: any,
    isDateRangeUpdate: boolean
  ) =>
  async (dispatch: any, getState: any) => {
    let eData;
    try {
      const state = getState();
      const endpoint = getApiRoot(state) + '/' + getGraphQLEndpoint(state);
      const token = getApiAuthToken(state);
      const config = getConfig<Window['config']>(state);
      const { reports, currentReportIndex, schemaIdContactAnalyticsSDO } =
        state;
      const { charts } = reports[currentReportIndex];
      const { useFilter, filter } =
        charts.find((c: any) => c.containerId === containerId) ?? {};
      const { filterType, filterTerms } = chartDef;
      const hasQueryFilters =
        JSON.stringify(filterTerms)?.includes('queryFilter');
      const variables = {
        ...reports[currentReportIndex].dateRange,
        schemaId: schemaIdContactAnalyticsSDO,
        filterTerms: hasQueryFilters
          ? filterTerms
          : Array.isArray(filterTerms)
            ? filterTerms
            : Object.keys(filterTerms ?? {}).reduce(
                (acc: string[], k: string) => [...acc, ...filterTerms[k]],
                []
              ),
        filterType: filterType ?? '',
      };
      let { dataQuery: query } = demographicsConfig;

      if (hasQueryFilters && useFilter === 'filter') {
        query = query.replace(
          /%%conditions%%/,
          variables.filterTerms[filter]?.queryFilter
        );
      } else {
        query = query.replace(/%%conditions%%/, '');
      }

      if (useFilter === 'filter') {
        variables.filterTerms = Array.isArray(filterTerms)
          ? [filter]
          : filterTerms[filter];
      }

      if (!filterType || hasQueryFilters) {
        query = query.replace(
          /\s*{\s*field: \$filterType\s*operator: "terms"\s*values: \$filterTerms\s+}/,
          ''
        );
        query = query.replace(/\$filterType: String/, '');
        query = query.replace(/\$filterTerms: \[String\]/, '');

        variables.filterTerms = undefined;
        variables.filterType = undefined;
      }

      if (isDateRangeUpdate) {
        dispatch(setHasData({ containerId, hasAllData: false }));
        dispatch(setHasRendered({ containerId, hasRendered: false }));
      }
      eData = {
        variables,
        endpoint,
        token,
        query,
        veritoneAppId: config.veritoneAppId,
      };
      const { data } =
        await fetchGraphQLApiThrowError<SearchContactDemographicResponse>({
          variables,
          endpoint,
          token,
          query,
          veritoneAppId: config.veritoneAppId,
        });

      dispatch(
        setChartData({
          containerId,
          chartDef,
          variables,
          data: {
            demographics: data.searchMedia.jsondata.aggregations,
          },
          totalDataQueries:
            useFilter === 'filter' && hasQueryFilters
              ? 1
              : chartDef.dataQueries.length,
        })
      );
    } catch (e) {
      dispatch(setHasFailed({ containerId }));
      console.error(e, eData);
    } finally {
      dispatch(subtractQueryPending());
    }
  };
interface SearchContactDemographicResponse {
  searchMedia: {
    jsondata: {
      aggregations: {
        gender: AggregationData;
        ethnicityExclusive: AggregationData;
        genderNonconforming: AggregationData;
      };
    };
  };
}

interface AggregationData {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Array<any>;
}

interface UpdateChartUserConfigResponse {
  updateUserSetting: {
    key: string;
    value: string;
  };
}
export const updateChartUserConfig =
  ({
    currentReportIndex,
    reports,
  }: {
    currentReportIndex: number;
    reports: any;
  }) =>
  (dispatch: any, getState: any) => {
    const state = getState();
    try {
      const variables = {
        applicationId: state.veritoneAppId,
        value: JSON.stringify({
          currentReportIndex,
          reports,
          modifiedDate: DateTime.now().toISO(),
        }),
      };

      const query = `mutation setUserSettings(
      $applicationId: ID!
      $value: String!
    ) {
      updateUserSetting(
        input: {
          application: $applicationId
          key: "contactAnalyticsConfig"
          value: $value
        }
      ) {
        key
        value
      }
    }`;

      return callGraphQLApi<UpdateChartUserConfigResponse>({
        actionTypes: [
          SET_CONTACT_ANALYTICS_CONFIG,
          SET_CONTACT_ANALYTICS_CONFIG_SUCCESS,
          SET_CONTACT_ANALYTICS_CONFIG_FAILURE,
        ],
        query,
        variables,
        dispatch,
        getState,
      });
    } catch (e) {
      console.error(e);
      console.error({
        currentReportIndex,
        reports,
        modifiedDate: DateTime.now().toISO(),
      });
    }
  };

interface FetchChartUserConfigResponse {
  payload: {
    getUserSettings: {
      key: string;
      value: string;
    }[];
  };
}
export const fetchChartUserConfig = () => (dispatch: any, getState: any) => {
  try {
    const state = getState();
    const variables = { applicationId: state.veritoneAppId };

    const query = `query getUserSettings($applicationId: ID!) {
    getUserSettings(
      application: $applicationId
      keys: ["contactAnalyticsConfig"]
    ) {
      key
      value
    }
  }`;
    return callGraphQLApi<FetchChartUserConfigResponse>({
      actionTypes: [
        GET_CONTACT_ANALYTICS_CHART_CONFIG,
        GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS,
        GET_CONTACT_ANALYTICS_CHART_CONFIG_FAILURE,
      ],
      query,
      variables,
      dispatch,
      getState,
    });
  } catch (e) {
    console.error('worker fetchChartUserConfig', e);
  }
};
