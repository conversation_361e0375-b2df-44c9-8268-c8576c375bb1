import React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import * as styles from './styles.scss';

const ConfirmNotificationModal = ({
  open,
  title,
  description,
  onConfirm,
  onClose,
  cancelButtonText,
  confirmButtonText,
}: Props) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogTitle className={styles.title}>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText className={styles.description}>
          {description}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          {cancelButtonText}
        </Button>
        <Button onClick={onConfirm} color="primary" data-test="confirm-button">
          {confirmButtonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

interface Props {
  open: boolean;
  onClose: () => void;
  onConfirm: (even: React.MouseEvent<HTMLElement>) => void;
  title: string;
  description: string;
  cancelButtonText: string;
  confirmButtonText: string;
}
export default ConfirmNotificationModal;
