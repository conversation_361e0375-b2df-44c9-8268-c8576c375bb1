import { useEffect, useState } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import CropIcon from '@mui/icons-material/Crop';
import { withStyles } from '@mui/styles';

import { formatBytes } from '../../../../helpers/format';

import styles from './styles';

interface Props {
  onRemoveFile: (index: number) => void;
  onFileResize: (file: File) => void;
  enableResize?: boolean;
  file: File;
  index: number;
  classes?: any;
}

function FileListItem(props: Props) {
  const [dataUrl, setDataUrl] = useState('');

  useEffect(() => {
    readImageFile(props.file);
  }, [props.file]);

  function readImageFile(file: File) {
    const fileReader = new FileReader();
    fileReader.onload = () => {
      setDataUrl(fileReader.result as string);
    };

    if (/^image\//i.test(file.type)) {
      fileReader.readAsDataURL(file);
    } else {
      setDataUrl('');
    }
  }

  function handleRemoveFile() {
    props.onRemoveFile(props.index);
  }

  function onResizeImage() {
    props.onFileResize(props.file);
  }

  const { classes, enableResize } = props;
  return (
    <div className={classes.item} data-testid="file-list-item">
      <div className={classes.itemPreviewContainer}>
        {dataUrl.length ? (
          <div
            style={{ backgroundImage: `url(${dataUrl})` }}
            className={classes.itemImage}
          />
        ) : (
          <div className={classes.itemFolderIcon}>
            <i className="icon-empty-folder" />
          </div>
        )}
      </div>

      <div className={classes.itemTextContainer}>
        <span className={classes.itemNameText} data-testid="item-name-text">
          {props.file.name}
        </span>
        <span
          className={classes.itemFileSizeText}
          data-testid="item-file-size-text"
        >
          {formatBytes(props.file.size)}
        </span>
      </div>

      <div className={classes.itemActionContainer}>
        {isImageFile(props.file.type) && enableResize && (
          <IconButton
            data-test="filePickerResizeBtn"
            className={classes.itemDeleteIcon}
            aria-label="Resize"
            onClick={onResizeImage}
            size="large"
          >
            <CropIcon />
          </IconButton>
        )}
        <IconButton
          data-testid="file-picker-delete-btn"
          className={classes.itemDeleteIcon}
          aria-label="Delete"
          onClick={handleRemoveFile}
          size="large"
        >
          <DeleteIcon />
        </IconButton>
      </div>
    </div>
  );
}

function isImageFile(fileType: string) {
  const validImageTypes = ['image/gif', 'image/jpeg', 'image/png'];
  return validImageTypes.includes(fileType);
}

export default withStyles(styles)(FileListItem);
