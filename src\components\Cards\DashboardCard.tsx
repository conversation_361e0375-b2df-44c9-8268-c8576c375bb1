import { Fragment } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  Typography,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  ThemeProvider,
  StyledEngineProvider,
  createTheme,
} from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import cx from 'classnames';
import * as styles from './styles.scss';
const cardCustomStyle = createTheme({
  components: {
    MuiCardContent: {
      styleOverrides: {
        root: {
          '&:last-child': {
            paddingBottom: 16,
          },
        },
      },
    },
  },
});

const options: Options = {
  1: {
    title: 'TOTAL NUMBER OF FILES',
    tooltipTitle: "The total number of file across this folder's data",
  },
  2: {
    title: 'TOTAL MEDIA HOURS',
    tooltipTitle: 'The total number of hours in this folder',
  },
  3: {
    title: 'TOTAL MEDIA HOURS PROCESSED',
    tooltipTitle: 'The total number of media hours processed by engine',
  },
};

export const DashboardCard = ({
  stylescard,
  data,
  option,
  durPerEngineClass,
}: Props) => {
  const makeFourSpinners: () => (number | DurPerEngineClass)[] = () =>
    Array.from(Array(4).keys());
  const firstLine = makeFourSpinners();
  const secondLine = makeFourSpinners();
  if (durPerEngineClass) {
    // display first 8 engine category
    for (let i = 0; i < durPerEngineClass.length && i < 8; i++) {
      if (i < 4) {
        firstLine[i] = durPerEngineClass[i]!; // Safe due to i value check
      } else {
        secondLine[i - 4] = durPerEngineClass[i]!; // Safe due to i value check
      }
    }
  }
  const renderCard = (array: (DurPerEngineClass | number)[]) => {
    return (
      <div className={cx(styles['card-content-process'])}>
        {array.map((engine) => {
          if (typeof engine === 'number') {
            return (
              <Grid key={engine} size={{ xs: 3 }} data-testid="grid">
                <div className={cx(styles['total-process'])}>
                  <div className={cx(styles['content-process'])}>
                    {/* Displays if engine name is not defined */}
                    <CircularProgress
                      size={15}
                      className={cx(styles['spinner'])}
                    />
                  </div>
                </div>
              </Grid>
            );
          }
          return (
            <Grid key={engine.id} size={{ xs: 3 }} data-testid="grid">
              <Tooltip title={engine.name} placement="top-start">
                <div
                  className={cx(styles['content-process'])}
                  data-testid={`engine-list-${engine.id}`}
                >
                  <i className={engine.iconClass} />
                  <div
                    data-test={`dashboard-card/render-card/${engine.name}`}
                    className={cx(styles['total-process'])}
                  >
                    {engine.duration}
                  </div>
                </div>
              </Tooltip>
            </Grid>
          );
        })}
      </div>
    );
  };

  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={cardCustomStyle}>
        <Card className={stylescard} data-testid="card">
          {option !== 4 ? (
            <Fragment>
              <CardHeader
                title={
                  <Typography className={cx(styles['title'])}>
                    {options[option]?.title}
                  </Typography>
                }
                className={cx(styles['card-title'])}
                data-testid="card-header"
              />
              <CardContent data-testid="card-content">
                <div
                  data-test={options[option]?.title}
                  className={cx(styles['card-content'])}
                >
                  <Typography
                    className={cx(styles['content'])}
                    component="p"
                    data-testid="typography"
                  >
                    {data}
                  </Typography>
                </div>
              </CardContent>
            </Fragment>
          ) : (
            <Fragment>
              <div className={cx(styles['card-right'])} />
              <CardContent>
                {renderCard(firstLine)}
                {renderCard(secondLine)}
              </CardContent>
            </Fragment>
          )}
        </Card>
      </ThemeProvider>
    </StyledEngineProvider>
  );
};
interface DurPerEngineClass {
  duration: string;
  iconClass: string;
  id: string;
  name: string;
}
interface Props {
  stylescard: string;
  data: string;
  option: number;
  durPerEngineClass?: DurPerEngineClass[];
}

interface Options {
  [key: number]: {
    title: string;
    tooltipTitle: string;
  };
}

export default DashboardCard;
