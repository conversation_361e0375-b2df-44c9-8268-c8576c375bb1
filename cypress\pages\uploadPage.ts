import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTextExtraction,
  engineIdTextExtraction,
  categoryIdTranscription,
  engineIdTranscription,
  categoryIdSpeakerDetection,
  engineIdSpeakerSeparation,
  categoryIdTranslate,
  engineIdTranslateSpanishToEnglish,
  veritoneAppId,
} from '../fixtures/variables';
import { deleteTDOByFilePath } from '../../src/state/modules/tdo/index';

export const uploadPage = {
  navigateToUploadFolder: (nameToDelete: string): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(endpoint, token, veritoneAppId, nameToDelete)
      );
      return;
    });
    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  navigateToUploadFolderAndDeleteFile: (): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-pdf.pdf'
        )
      );
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-email.eml'
        )
      );
      return;
    });

    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  uploadImage: (): void => {
    const imageName = 'image-plant-jpeg.jpeg';
    const imagePath = '../setup/image-plant-jpeg.jpeg';
    const mimeTypeImage = 'image/png';
    cy.UploadFileBasic(
      imageName,
      imagePath,
      mimeTypeImage,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadPDFFile: (): void => {
    const pdfName = 'spanish-pdf.pdf';
    const pdfPath = '../setup/spanish-pdf.pdf';
    const mimeTypePdf = 'application/pdf';
    cy.UploadFileBasic(
      pdfName,
      pdfPath,
      mimeTypePdf,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadEMLFile: (): void => {
    const emlName = 'spanish-email.eml';
    const emlPath = '../setup/spanish-email.eml';
    const mimeTypeEml = 'message/rfc822';
    cy.UploadFileBasic(
      emlName,
      emlPath,
      mimeTypeEml,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadVideoFile: (): void => {
    const fileName = 'bloomberg.mp4';
    const filePath = '../setup/bloomberg.mp4';
    const mimeType = 'video/mp4';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  uploadAudioFile: (): void => {
    const fileName = 'e2e_audio.mp3';
    const filePath = '../setup/e2e_audio.mp3';
    const mimeType = 'audio/mp3';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  UploadTextFile: (): void => {
    const textName = 'spanish-txt.txt';
    const textPath = '../setup/spanish-txt.txt';
    const mimeTypeText = 'text/plain';
    cy.UploadFileBasic(
      textName,
      textPath,
      mimeTypeText,
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish
    );
  },
};
