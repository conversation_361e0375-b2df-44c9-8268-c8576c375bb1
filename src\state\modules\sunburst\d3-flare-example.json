{"children": [{"name": "Broken Eyeglasses", "hex": "#4748b1", "children": [{"name": "Lens", "hex": "#4748b1", "children": [{"name": "AgglomerativeCluster", "hex": "#4748b1", "value": 3938}, {"name": "CommunityStructure", "hex": "#4748b1", "value": 3812}, {"name": "HierarchicalCluster", "hex": "#4748b1", "value": 6714}, {"name": "Merge<PERSON>dge", "hex": "#4748b1", "value": 743}]}, {"name": "<PERSON>ame", "hex": "#4748b1", "children": [{"name": "BetweennessCentrality", "hex": "#4748b1", "value": 3534}, {"name": "LinkDistance", "hex": "#4748b1", "value": 5731}, {"name": "MaxFlowMinCut", "hex": "#4748b1", "value": 7840}, {"name": "ShortestPaths", "hex": "#4748b1", "value": 5914}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "hex": "#4748b1", "value": 3416}]}, {"name": "Nose piece", "hex": "#4748b1", "children": [{"name": "Tightness", "hex": "#4748b1", "value": 7074}]}]}, {"name": "Vendor pricing", "hex": "#fc9021", "children": [{"name": "Easing", "hex": "#fc9021", "value": 17010}, {"name": "FunctionSequence", "hex": "#fc9021", "value": 5842}, {"name": "interpolate", "hex": "#fc9021", "children": [{"name": "ArrayInterpolator", "hex": "#fc9021", "value": 1983}, {"name": "hexInterpolator", "hex": "#fc9021", "value": 2047}, {"name": "DateInterpolator", "hex": "#fc9021", "value": 1375}, {"name": "Interpolator", "hex": "#fc9021", "value": 8746}, {"name": "MatrixInterpolator", "hex": "#fc9021", "value": 2202}, {"name": "NumberInterpolator", "hex": "#fc9021", "value": 1382}, {"name": "ObjectInterpolator", "hex": "#fc9021", "value": 1629}, {"name": "PointInterpolator", "hex": "#fc9021", "value": 1675}, {"name": "RectangleInterpolator", "hex": "#fc9021", "value": 2042}]}, {"name": "ISchedulable", "hex": "#fc9021", "value": 1041}, {"name": "<PERSON><PERSON><PERSON>", "hex": "#fc9021", "value": 5176}, {"name": "Pause", "hex": "#fc9021", "value": 449}, {"name": "Scheduler", "hex": "#fc9021", "value": 5593}, {"name": "Sequence", "hex": "#fc9021", "value": 5534}, {"name": "Transition", "hex": "#fc9021", "value": 9201}, {"name": "Transitioner", "hex": "#fc9021", "value": 19975}, {"name": "TransitionEvent", "hex": "#fc9021", "value": 1116}, {"name": "Neonate", "hex": "#fc9021", "value": 6006}]}, {"name": "Embezzlement", "hex": "#f25650", "children": [{"name": "<PERSON>", "hex": "#f25650", "value": 8833}, {"name": "April Showers", "hex": "#f25650", "value": 1732}, {"name": "May Flowers", "hex": "#f25650", "value": 3623}, {"name": "<PERSON>", "hex": "#f25650", "value": 10066}]}, {"name": "Disgruntled Employee", "hex": "#499ff1", "children": [{"name": "Foul Language", "hex": "#499ff1", "value": 8258}, {"name": "Break this", "hex": "#499ff1", "value": 10001}, {"name": "Hate this job", "hex": "#499ff1", "value": 8217}, {"name": "No enough compensation", "hex": "#499ff1", "value": 12555}, {"name": "She's trouble", "hex": "#499ff1", "value": 10993}, {"name": "My boss", "hex": "#499ff1", "children": [{"name": "Pushes too hard", "hex": "#499ff1", "value": 9354}, {"name": "Doesn't care", "hex": "#499ff1", "value": 1233}]}, {"name": "Smoking", "hex": "#499ff1", "value": 335}, {"name": "Drinking", "hex": "#499ff1", "value": 383}, {"name": "Drugs", "hex": "#499ff1", "value": 874}, {"name": "Harassment", "hex": "#499ff1", "value": 17705}, {"name": "Swearing", "hex": "#499ff1", "value": 1486}, {"name": "Fighting", "hex": "#499ff1", "children": [{"name": "<PERSON>", "hex": "#499ff1", "value": 6367}, {"name": "<PERSON>", "hex": "#499ff1", "value": 10229}]}, {"name": "Property damage", "hex": "#499ff1", "value": 5559}, {"name": "Speeding", "hex": "#499ff1", "value": 19118}, {"name": "Carelessness", "hex": "#499ff1", "value": 6887}]}, {"name": "Manufacturing Flaw", "hex": "#3eb259", "children": [{"name": "Conveyor Jam", "hex": "#3eb259", "children": [{"name": "Axes", "hex": "#3eb259", "value": 1302}, {"name": "Axis", "hex": "#3eb259", "value": 24593}, {"name": "AxisGridLine", "hex": "#3eb259", "value": 652}, {"name": "AxisLabel", "hex": "#3eb259", "value": 636}, {"name": "CartesianAxes", "hex": "#3eb259", "value": 6703}]}, {"name": "controls", "hex": "#3eb259", "children": [{"name": "AnchorControl", "hex": "#3eb259", "value": 2138}, {"name": "ClickControl", "hex": "#3eb259", "value": 3824}, {"name": "Control", "hex": "#3eb259", "value": 1353}, {"name": "ControlList", "hex": "#3eb259", "value": 4665}, {"name": "DragControl", "hex": "#3eb259", "value": 2649}, {"name": "ExpandControl", "hex": "#3eb259", "value": 2832}, {"name": "HoverControl", "hex": "#3eb259", "value": 4896}, {"name": "IControl", "hex": "#3eb259", "value": 763}, {"name": "PanZoomControl", "hex": "#3eb259", "value": 5222}, {"name": "SelectionControl", "hex": "#3eb259", "value": 7862}, {"name": "TooltipControl", "hex": "#3eb259", "value": 8435}]}, {"name": "data", "hex": "#3eb259", "children": [{"name": "Data", "hex": "#3eb259", "value": 20544}, {"name": "DataList", "hex": "#3eb259", "value": 19788}, {"name": "DataSprite", "hex": "#3eb259", "value": 10349}, {"name": "EdgeSprite", "hex": "#3eb259", "value": 3301}, {"name": "NodeSprite", "hex": "#3eb259", "value": 19382}, {"name": "render", "hex": "#3eb259", "children": [{"name": "ArrowType", "hex": "#3eb259", "value": 698}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 5569}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 353}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 2247}]}, {"name": "ScaleBinding", "hex": "#3eb259", "value": 11275}, {"name": "Tree", "hex": "#3eb259", "value": 7147}, {"name": "TreeBuilder", "hex": "#3eb259", "value": 9930}]}, {"name": "events", "hex": "#3eb259", "children": [{"name": "DataEvent", "hex": "#3eb259", "value": 2313}, {"name": "SelectionEvent", "hex": "#3eb259", "value": 1880}, {"name": "TooltipEvent", "hex": "#3eb259", "value": 1701}, {"name": "VisualizationEvent", "hex": "#3eb259", "value": 1117}]}, {"name": "legend", "hex": "#3eb259", "children": [{"name": "Legend", "hex": "#3eb259", "value": 20859}, {"name": "LegendItem", "hex": "#3eb259", "value": 4614}, {"name": "LegendRange", "hex": "#3eb259", "value": 10530}]}, {"name": "operator", "hex": "#3eb259", "children": [{"name": "distortion", "hex": "#3eb259", "children": [{"name": "BifocalDistortion", "hex": "#3eb259", "value": 4461}, {"name": "Distortion", "hex": "#3eb259", "value": 6314}, {"name": "FisheyeDistortion", "hex": "#3eb259", "value": 3444}]}, {"name": "encoder", "hex": "#3eb259", "children": [{"name": "hexEncoder", "hex": "#3eb259", "value": 3179}, {"name": "Encoder", "hex": "#3eb259", "value": 4060}, {"name": "PropertyEncoder", "hex": "#3eb259", "value": 4138}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 1690}, {"name": "valueEncoder", "hex": "#3eb259", "value": 1830}]}, {"name": "filter", "hex": "#3eb259", "children": [{"name": "FisheyeTreeFilter", "hex": "#3eb259", "value": 5219}, {"name": "GraphDistanceFilter", "hex": "#3eb259", "value": 3165}, {"name": "VisibilityFilter", "hex": "#3eb259", "value": 3509}]}, {"name": "IOperator", "hex": "#3eb259", "value": 1286}, {"name": "label", "hex": "#3eb259", "children": [{"name": "Labeler", "hex": "#3eb259", "value": 9956}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 3899}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hex": "#3eb259", "value": 3202}]}, {"name": "layout", "hex": "#3eb259", "children": [{"name": "AxisLayout", "hex": "#3eb259", "value": 6725}, {"name": "BundledEdgeRouter", "hex": "#3eb259", "value": 3727}, {"name": "CircleLayout", "hex": "#3eb259", "value": 9317}, {"name": "CirclePackingLayout", "hex": "#3eb259", "value": 12003}, {"name": "DendrogramLayout", "hex": "#3eb259", "value": 4853}, {"name": "ForceDirectedLayout", "hex": "#3eb259", "value": 8411}, {"name": "IcicleTreeLayout", "hex": "#3eb259", "value": 4864}, {"name": "IndentedTreeLayout", "hex": "#3eb259", "value": 3174}, {"name": "Layout", "hex": "#3eb259", "value": 7881}, {"name": "NodeLinkTreeLayout", "hex": "#3eb259", "value": 12870}, {"name": "PieLayout", "hex": "#3eb259", "value": 2728}, {"name": "RadialTreeLayout", "hex": "#3eb259", "value": 12348}, {"name": "RandomLayout", "hex": "#3eb259", "value": 870}, {"name": "StackedAreaLayout", "hex": "#3eb259", "value": 9121}, {"name": "TreeMapLayout", "hex": "#3eb259", "value": 9191}]}, {"name": "Operator", "hex": "#3eb259", "value": 2490}, {"name": "OperatorList", "hex": "#3eb259", "value": 5248}, {"name": "OperatorSequence", "hex": "#3eb259", "value": 4190}, {"name": "OperatorSwitch", "hex": "#3eb259", "value": 2581}, {"name": "SortOperator", "hex": "#3eb259", "value": 2023}]}, {"name": "Visualization", "hex": "#3eb259", "value": 16540}]}]}