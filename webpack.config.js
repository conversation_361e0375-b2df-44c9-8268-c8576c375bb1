const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin');
const CspHtmlWebpackPlugin = require('csp-html-webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');

const fs = require('fs');
const { pick } = require('lodash');
const path = require('path');

const processEnv = process.env.ENVIRONMENT || 'local';

const buildPath = 'build' + (processEnv ? '-' + processEnv : '');

const deployedConfigPath = `./config-${processEnv}.json`;
const localConfigPath = './config.json';
const isDeployed = processEnv && fs.existsSync(deployedConfigPath);

const dirName = __dirname;
const appConfig = require(isDeployed ? deployedConfigPath : localConfigPath);
const safeConfigKeys = require('./configWhitelist.json');
const safeConfig = pick(appConfig, safeConfigKeys);

const child_process = require('child_process');
child_process.execSync('git fetch --tags');
const packageJson = require('./package.json');
const appVersion = packageJson.version;
const versionCommit = child_process
  .execSync(`git rev-list -n 1 ${appVersion}`)
  .toString()
  .replace(/(\r\n|\n|\r)/gm, '');
const buildNumber = child_process
  .execSync(`git rev-list ${versionCommit}..HEAD --count`)
  .toString()
  .trim();

module.exports = (env, argv) => {
  const config = {
    // webpack will take the files from ./src/index
    entry: dirName + '/src/index',
    devtool:
      argv.mode === 'development'
        ? 'eval-cheap-module-source-map'
        : 'source-map',
    output: {
      filename: '[name].[contenthash].bundle.js',
      path: path.resolve(dirName, buildPath),
      publicPath: '/',
      globalObject: 'this',
    },
    devServer: {
      static: [path.resolve(__dirname, 'static')],
      compress: true,
      allowedHosts: ['local.veritone.com'],
      // Disables the React Overlay Page regarding any compilation warnings
      // https://webpack.js.org/configuration/dev-server/#overlay
      client: {
        overlay: {
          errors: true,
          warnings: false,
          runtimeErrors: false,
        },
      },
      historyApiFallback: true,
      hot: true,
      port: 8080,
    },
    module: {
      rules: [
        {
          test: /\.([tj]sx?)$/,
          exclude: /node_modules/,
          loader: 'babel-loader',
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.scss$/,
          use: [
            MiniCssExtractPlugin.loader,
            {
              loader: 'css-loader',
              options: {
                modules: true,
                importLoaders: 1,
              },
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: () => [require('autoprefixer')],
                },
              },
            },
            {
              loader: 'sass-loader',
              options: {
                api: 'legacy',
                sassOptions: {
                  includePaths: [
                    path.resolve('./src'),
                    path.resolve('./resources'),
                  ],
                },
              },
            },
          ],
          exclude: /global\.scss$/,
        },
        {
          test: /\.scss$/,
          use: [
            MiniCssExtractPlugin.loader,
            {
              loader: 'css-loader',
              options: {
                modules: false,
                importLoaders: 1,
              },
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: () => [require('autoprefixer')],
                },
              },
            },
            {
              loader: 'sass-loader',
              options: {
                api: 'legacy',
                sassOptions: {
                  includePaths: [
                    path.resolve('./src'),
                    path.resolve('./resources'),
                  ],
                },
              },
            },
          ],
          include: /global\.scss$/,
        },
        // allows imports with package.json that specify
        // a module to not specify file extensions.
        // AKA - webpack wont force esm naming conventions
        // https://github.com/graphql/graphql-js/issues/2721
        {
          test: /\.m?js/,
          resolve: {
            fullySpecified: false,
          },
        },
        {
          test: /\.(png|svg|jpg|jpeg|gif|ico|webp)$/,
          exclude: /node_modules/,
          type: 'asset/resource',
        },
      ],
    },
    optimization: {
      minimizer: [`...`, new CssMinimizerPlugin()],
    },
    plugins: [
      // copy image links in the html template HtmlWebpackPlugin
      new CopyPlugin({
        patterns: [
          {
            from: 'resources/images/favicon',
            to: 'assets/favicon',
          },
        ],
      }),
      new HtmlWebpackPlugin({
        appMountId: 'root',
        template: require('html-webpack-template'),
        title: 'Veritone Illuminate',
        attributes: {
          defer: true,
          nonce: 'NGINX_CSP_NONCE',
        },
        links: [
          {
            href: './assets/favicon/apple-touch-icon.png',
            rel: 'apple-touch-icon',
            sizes: '180x180',
          },
          {
            href: './assets/favicon/favicon-32x32.png',
            rel: 'icon',
            sizes: '32x32',
            type: 'image/png',
          },
          {
            href: './assets/favicon/favicon-16x16.png',
            rel: 'icon',
            sizes: '16x16',
            type: 'image/png',
          },
          {
            href: './assets/favicon/favicon.ico',
            rel: 'shortcut icon',
            type: 'image/x-icon',
          },
        ],
        headHtmlSnippet: `
        <script type="text/javascript" nonce="NGINX_CSP_NONCE">
          window['config'] = ${JSON.stringify(safeConfig)};
          window['version'] = '${appVersion}.${buildNumber}';
        </script>`,
      }),
      new CspHtmlWebpackPlugin(
        {
          'default-src': ["'self'", '*.AIWARE_DOMAIN_TO_REPLACE'],
          'script-src': [
            "'self'",
            "'unsafe-eval'",
            "'nonce-NGINX_CSP_NONCE'",
            '*.AIWARE_DOMAIN_TO_REPLACE',
            'https://*.pendo.io',
            'https://*.segment.com/',
            'https://*.ingest.sentry.io',
            'https://cdn.jsdelivr.net/',
            'https://*.intercom.io/',
            'https://*.intercomcdn.com/',
            'https://*.google-analytics.com/',
            'https://analytics-google.com/',
            'https://*.googleapis.com',
            'https://www.googletagmanager.com/',
            '*.force.com/',
            '*.salesforce-scrt.com/',
            'https://cdnjs.cloudflare.com/',
            'data.pendo.io',
            'app.pendo.io',
            'cdn.pendo.io',
            'pendo-static-5865020918857728.storage.googleapis.com',
            'pendo-io-static.storage.googleapis.com',
          ],
          // set * because it needs to connect assets like media hls segments
          'connect-src': ['*', 'blob:'],
          'img-src': ['*', 'data:', 'blob:', 'https://i.vimeocdn.com'],
          'font-src': [
            "'self'",
            'data:',
            '*.AIWARE_DOMAIN_TO_REPLACE',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net/',
            'https://stackpath.bootstrapcdn.com/',
          ],
          'media-src': ['*', 'blob:'],
          'frame-src': [
            "'self'",
            '*.AIWARE_DOMAIN_TO_REPLACE',
            'https://td.doubleclick.net/',
            'https://veritone.my.site.com/',
            'https://*.veritone.com/',
            'https://app.high.powerbigov.us/',
            'pendo-static-5865020918857728.storage.googleapis.com',
            'https://player.vimeo.com',
            'portal.pendo.io',
            'app.pendo.io',
          ],
          'worker-src': ["'self'", 'blob:', '*.AIWARE_DOMAIN_TO_REPLACE'],
          'style-src': [
            "'self'",
            "'unsafe-inline'",
            '*.AIWARE_DOMAIN_TO_REPLACE',
            'https://*.googleapis.com',
            'https://cdn.jsdelivr.net/',
            'https://stackpath.bootstrapcdn.com/',
            'pendo-io-static.storage.googleapis.com',
            'pendo-static-5865020918857728.storage.googleapis.com',
            'app.pendo.io',
            'cdn.pendo.io',
          ],
        },
        {
          hashingMethod: 'sha256',
          hashEnabled: {
            'script-src': false,
            'style-src': false,
          },
          nonceEnabled: {
            'script-src': false,
            'style-src': false,
          },
        }
      ),
      new MiniCssExtractPlugin({
        filename: 'assets/[name].[contenthash].css',
        chunkFilename: 'assets/[id].[contenthash].css',
      }),
      new ForkTsCheckerWebpackPlugin(),
      sentryWebpackPlugin({
        authToken:
          '668437560627487c93be789c3d4e1e581f8341921ef1458bbde55f8af93cf6c0',
        org: 'veritone',
        project: 'illuminate-app',
      }),
      new ReactRefreshWebpackPlugin(),
    ],
    // adding .ts and .tsx to resolve.extensions will help babel look for .ts and .tsx files to transpile
    resolve: {
      alias: {
        resources: path.join(dirName, 'resources'),
        components: path.join(dirName, 'src/components'),
        pages: path.join(dirName, 'src/pages'),
        state: path.join(dirName, 'src/state'),
        modules: path.join(dirName, 'src/state/modules'),
        sagas: path.join(dirName, 'src/state/sagas'),
        '~helpers': path.join(dirName, 'src/helpers'),
        '@utils': path.resolve(dirName, 'src/utils'),
        handlebars: 'handlebars/dist/handlebars.min.js',
        react: path.resolve(dirName, 'node_modules/react'),
        'react-dom': path.resolve(dirName, 'node_modules/react-dom'),
      },
      extensions: [
        '.tsx',
        '.ts',
        '.js',
        '.jsx',
        '.css',
        '.scss',
        '.json',
        '.gif',
        '.svg',
        '.jpg',
        '.png',
      ],
      fallback: {
        path: require.resolve('path-browserify'),
        stream: require.resolve('stream-browserify'),
        zlib: false,
        url: false,
        assert: false,
        util: false,
      },
      roots: [path.resolve(dirName)],
      plugins: [new TsconfigPathsPlugin()],
    },
  };

  return config;
};
