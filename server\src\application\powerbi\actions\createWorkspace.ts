import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface WorkspaceRequest {
  name: string;
}

export interface WorkspaceResponse {
  '@odata.context': string;
  id: string;
  isReadOnly: boolean;
  isOnDedicatedCapacity: string;
  name: string;
}

const createWorkspace = async (context: Context) => {
  const { log, req, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const { data: resp } = await axios.post<
      WorkspaceResponse,
      AxiosResponse<WorkspaceResponse>,
      WorkspaceRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups`,
      { name: `${req.body.orgName} Workspace` },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    data.workspaceId = resp.id;
    data.workspaceName = resp.name;

    return context;
  } catch (e) {
    log.error('Workspace API failed', e);
    throw new ApiError(e);
  }
};

export default createWorkspace;
