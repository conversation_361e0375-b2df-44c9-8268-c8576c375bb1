import { createAction, createReducer } from '@reduxjs/toolkit';
import getApiAuthToken from '../../../helpers/getApiAuthToken';
import getApiRoot from '../../../helpers/getApiRoot';
import { get, isEmpty } from 'lodash';
import { TDO_TAGS_UPDATED } from '../tags';
import { guid } from '../../../helpers/guid';
import { TDO } from '../universal/TDO';
import { GQLApi } from '../../../helpers/gqlApi';
import {
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '../../../helpers/apiHelper';
import { EngineCategoriesIds } from 'components/TdosTable';
import { UPDATE_MOVED_TDOS, MoveFolderData } from '../tdosTable';
import fetch from '../../../helpers/fetchRetry';

export const SEARCH_MEDIA = createAction<number>('request to search media');
export const SEARCH_MEDIA_SUCCESS = createAction<FetchSearchMediaResponse>(
  'searched media success'
);
export const SEARCH_MEDIA_FAILURE = createAction<{
  error?: { message: string };
}>('searched media failed');
export const GET_SEARCH_TDOS_BY_ID =
  createAsyncFuncRequestAction('tdos by id request');
export const GET_SEARCH_TDOS_BY_ID_SUCCESS = createAsyncFuncSuccessAction<{
  currentPage: number;
  data: TDO[];
}>('tdos by id success');
export const GET_SEARCH_TDOS_BY_ID_FAILURE =
  createAsyncFuncFailureAction('tdos by id failed');
export const ON_SEARCH_BAR_QUERY_CHANGE = createAction<{
  searchQuery: SEARCH_BAR_QUERY;
}>('request on search bar query change');

export const QUERY_TYPE = {
  APPLY_FILTER: 'apply_filter',
  ANALYTICS: 'analytics',
};

interface FolderQueryChange {
  field: string;
  operator: string;
  values: string[];
}
export const ON_FOLDER_QUERY_CHANGE = createAction<{
  searchQuery: FolderQueryChange | null;
}>('request on folder query change');

export const ON_SEARCH_LIMIT_CHANGE = createAction<{
  limit: number;
}>('request on search query limit change');
type EntityTypeQueryChange =
  | {
      field: string;
      operator: string;
      value: string;
    }
  | {
      operator: string;
      conditions: {
        operator: string;
        field: string;
        value: string;
      }[];
    };
export const ON_ENTITY_TYPES_QUERY_CHANGE = createAction<{
  searchQuery: EntityTypeQueryChange | null;
  queryType: string;
}>('request on entity types query change');
interface FileTypeQuery {
  operator: string;
  conditions: {
    field: string;
    operator: string;
    value: string;
  }[];
}
export const ON_FILE_TYPE_QUERY_CHANGE = createAction<{
  searchQuery: FileTypeQuery | null;
}>('request on file type query change');
interface DateQueryChange {
  operator: string;
  field: string;
  gte: string;
  lte: string;
}
export const ON_DATE_QUERY_CHANGE = createAction<DateQueryChange>(
  'request on date query change'
);

export const ON_TOPICS_QUERY_CHANGE = createAction<{
  searchQuery: any;
  queryType: string;
}>('request on topics query change');

export const SAVE_QUERY_FOR_EXPORT_ALL = createAction<{
  index: string[];
  limit: number;
  offset: number;
  query?: Query;
  select: string[];
  sort?: {
    field: string;
    order: string;
  }[];
}>('save query for export all tdos');
export const TAGS_AUTOCOMPLETE = createAction<string>(
  'tags autocomplete suggestion'
);

export const TAG_SUGGESTIONS = createAction<{
  fields: {
    'tags.displayName': {
      doc_count: number;
      key: string;
    }[];
  };
}>('tags suggestion list');

export const UPDATE_SEARCH_RESULT_TDOS = createAction<{
  tdoIds: string[];
  type: TDO['status'];
}>('remove search tdos by ids');
export const GET_TRANSCRIPT_DATA_PREVIEW = 'get transcript data preview';
export const UPDATE_CURRENT_PAGE = createAction<number>('update current page');
export const ON_CLICK_FILES_TAB = createAction('onclick files tab');

export const UPDATE_SEARCH_PARAMETERS = createAction<{ csp: UpdateParamType }>(
  'update search parameters'
);

export const ON_FILE_NAMES_QUERY_CHANGE = createAction<{
  searchByFileNames: string[];
}>('ON_FILE_NAMES_QUERY_CHANGE');

export const ON_IDS_QUERY_CHANGE = createAction<{
  searchByIds: string[];
}>('ON_IDS_QUERY_CHANGE');

export const ON_ENGINES_RUN_QUERY_CHANGE = createAction<{
  searchByEnginesRun: string[];
}>('ON_ENGINES_RUN_QUERY_CHANGE');

export const ON_DURATION_QUERY_CHANGE = createAction<{
  duration: {
    hoursGt: number;
    minutesGt: number;
    secondsGt: number;
    hoursLte: number;
    minutesLte: number;
    secondsLte: number;
  };
}>('ON_DURATION_QUERY_CHANGE');

export const ON_SORT_QUERY_CHANGE = createAction<{
  sortQuery: { columnName: string; direction: string }[];
}>('ON_SORT_QUERY_CHANGE');

export const ON_SORT_CHANGE = createAction<{
  sortQuery: { columnName: string; direction: string }[];
}>('ON_SORT_CHANGE');

export const CLEAR_FILTERS_QUERY = createAction('clear filters query');

export const UPDATE_TDO = createAction<{
  tdoId: string;
  name?: string;
  tags?: { value: string }[];
}>('update tdo');

export const DEFAULT_SEARCH_ALL_MEDIA_QUERY: SEARCH_BAR_QUERY = {
  index: ['mine'],
  select: ['veritone-job', 'veritone-file'],
};

export const ON_CURRENT_PAGE_CHANGE = createAction<{
  currentPage: number;
}>('request on current page change');

// Object Detection
export const OBJECT_CONDITION_TYPE = '088a31be-9bd6-4628-a6f0-e4004e362ea0';

export const QUERY_FIELD = {
  fileNames: 'veritone-file.filename',
  ids: 'recordingId',
  enginesRun: 'veritone-job.engineCategories',
  duration: 'fileDuration',
};

export const SORT_FIELD: { [key: string]: string } = {
  id: 'recordingId',
  mimeType: 'fileType',
  createdDateTime: 'createdTime',
  name: 'veritone-file.filename',
};

const DEFAULT_LIMIT = 10;
const DEFAULT_OFFSET = 0;

export interface SEARCH_BAR_QUERY {
  index: string[];
  select: string[];
  query?: Query;
}

interface QueryCondition {
  operator: string;
  field?: string;
  value?: string;
  values?: string[];
  not?: boolean;
  conditions?: QueryCondition[];
}

export interface Query {
  operator: string;
  conditions: QueryCondition[];
}

interface Sentiment {
  positive: string;
  negative: string;
}

interface CustomizedSearch {
  sentiment: Sentiment;
}

interface Search {
  dayPartStartTime?: string;
  dayPartEndTime?: string;
  selectedDays?: boolean[];
  stationBroadcastTime?: boolean;
}

interface State {
  exclude?: boolean;
  id?: string;
  type?: string;
  label?: string;
  image?: string;
  description?: string;
  advancedOptions?: Record<string, unknown>;
  customizedSearch?: CustomizedSearch;
  language?: string;
  latitude?: number;
  longitude?: number;
  distance?: number;
  units?: string;
  includeSpecialCharacters?: boolean;
  operator?: string;
  value1?: string;
  value2?: string;
  select?: string;
  schemaId?: string;
  name?: string;
  field?: string;
  search?: Search;
}

interface Item {
  engineCategoryId?: string;
  state: State;
}

export interface UpdateParamType {
  csp: {
    and?: Item[];
    or?: Item[];
    customizedSearch: CustomizedSearch;
  };
}
interface SearchParameters {
  id: string;
  conditionType: string;
  value: { id: string; label: string };
}
const defaultState = {
  searchResults: [] as SearchResult[],
  searchResultTdos: [] as TDO[],
  totalResults: 0, // approximate number of search results from ES
  hasNextPage: true,
  searchToken: '',
  searching: false,
  searchError: null as string | null,
  searchBarQuery: DEFAULT_SEARCH_ALL_MEDIA_QUERY,
  byFolderQuery: null as FolderQueryChange | null,
  entityTypes: null as EntityTypeQueryChange | null,
  dateRangeQuery: null as DateQueryChange | null,
  fileTypeQuery: null as FileTypeQuery | null,
  topicsQuery: null,
  limit: DEFAULT_LIMIT,
  offset: DEFAULT_OFFSET,
  autoSuggestTag: [] as string[],
  queryStore: null as {
    index: string[];
    limit: number;
    offset: number;
    query?: Query;
    select: string[];
  } | null,
  fetchingTdos: false,
  engineCategoriesIds: {} as EngineCategoriesIds,
  currentPage: 0,
  searchParameters: [] as SearchParameters[],
  searchByFileNameQuery: {},
  searchByIdsQuery: {},
  searchByEnginesRunQuery: {},
  searchByDurationQuery: {},
  sortQuery: [
    {
      field: QUERY_FIELD.ids,
      order: 'desc',
    },
  ],
  noData: undefined as boolean | undefined,
  lastCurrentPage: 0,
  indexesByTdo: [] as number[],
  searchResultsAll: [] as SearchResult[],
  searchResultTdosAll: [] as TDO[],
};

interface SearchResult {
  aibDuration: number;
  cost: number;
  fileLocation: string;
  fileType: string;
  hitEndTime: string;
  hitStartTime: string;
  isOwn: boolean;
  isPrimaryProgram: boolean;
  isPublic: boolean;
  mediaId: number;
  mediaSourceId: number;
  mediaSourceTypeId: number;
  mediaStartTime: string;
  programId: number;
  programLiveImage: string;
  programName: string;
  sliceTime: string;
  transcriptUrl: string;
  metadata: {
    'veritone-file': { fileName: string };
    'veritone-job': { engineCategories: string[]; engines: string[] };
  };
}

export interface FetchSearchMediaResponse {
  currentPage: number;
  result: {
    searchToken: string;
    totalRecords: number;
    records: SearchResult[];
  };
}

export const mergedTdos = (allTdos: TDO[], selectedTdos: TDO[]) => {
  if (
    (!Array.isArray(allTdos) || !allTdos.length) &&
    (!Array.isArray(selectedTdos) || !selectedTdos.length)
  ) {
    return [];
  }
  if (!Array.isArray(allTdos) || !allTdos.length) {
    return [...selectedTdos];
  }
  if (!Array.isArray(selectedTdos) || !selectedTdos.length) {
    return [...allTdos];
  }
  const mergedTdoHash = Object.create(null);
  // Merge selectedTdos into allTdos by selectedTdos ID
  allTdos.concat(selectedTdos).forEach(function (tdo) {
    mergedTdoHash[tdo.id] = Object.assign(mergedTdoHash[tdo.id] || {}, tdo);
  });
  return Object.keys(mergedTdoHash).map(function (key) {
    return mergedTdoHash[key];
  });
};

const buildSearchQuery = (data: string[], field: string) => {
  if (!data.length) {
    return {};
  }
  return {
    operator: 'or',
    conditions: mapQueryString(data, field),
  };
};

const mapQueryString = (data: string[], field: string) => {
  return (
    Array.isArray(data) &&
    data.map((item) => {
      return {
        operator: 'query_string',
        field: field,
        value: `*${item.toLowerCase()}*`,
      };
    })
  );
};

const buildSearchByEnginesRunQuery = (data: string[], field: string) => {
  if (!data.length) {
    return {};
  }
  return {
    field: field,
    operator: 'terms',
    values: [...data],
  };
};

const buildSearchDurationQuery = (
  data: {
    hoursGt: number;
    minutesGt: number;
    secondsGt: number;
    hoursLte: number;
    minutesLte: number;
    secondsLte: number;
  },
  field: string
) => {
  const gt = data.hoursGt * 3600 + data.minutesGt * 60 + data.secondsGt;
  const lte = data.hoursLte * 3600 + data.minutesLte * 60 + data.secondsLte;
  if (gt === 0 && lte === 0) {
    return {};
  }
  const query: {
    operator: string;
    field: string;
    lte?: number;
    gt?: number;
  } = {
    operator: 'range',
    field: field,
  };
  if (gt === 0 && lte > 0) {
    query.lte = lte;
    return query;
  }
  if (lte === 0 && gt > 0) {
    query.gt = gt;
    return query;
  }
  query.gt = gt;
  query.lte = lte;
  return query;
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(SEARCH_MEDIA, (state) => {
      return {
        ...state,
        searching: true,
        searchError: null,
      };
    })
    .addCase(SEARCH_MEDIA_SUCCESS, (state, action) => {
      const searchResults = action.payload.result.records;
      const totalResults = action.payload.result.totalRecords;
      const searchToken = action.payload.result.searchToken;

      // when navigating back and forth, the searchResults might already in
      // state.searchResultTdos. so need to dedup and overwrite with value
      // from searchResults.
      // const stateSearchResultsAll = [...state.searchResultsAll];
      // for (const searchResult of searchResults) {
      //   const index = stateSearchResultsAll.findIndex(
      //     (cur) => cur.mediaId === searchResult.mediaId
      //   );
      //   if (index >= 0) {
      //     stateSearchResultsAll[index] = searchResult;
      //   } else {
      //     stateSearchResultsAll.push(searchResult);
      //   }
      // }

      // Revert code
      const newState = {
        ...state,
        searchResults: searchResults,
        totalResults: totalResults,
        searchToken,
        searchError: null,
        engineCategoriesIds: {
          ...state.engineCategoriesIds,
          ...searchResults.reduce(
            (
              data: { [key: string]: { engineCategoriesIds: string[] } },
              item: SearchResult
            ) => {
              const engineCategories = get(
                item,
                'metadata["veritone-job"].engineCategories',
                []
              );
              return {
                ...data,
                [item.mediaId]: {
                  engineCategoriesIds: engineCategories,
                },
              };
            },
            {}
          ),
        },
        noData: searchResults.length ? false : true,
        // searchResultsAll: stateSearchResultsAll,
      };
      if (!searchResults.length) {
        newState.searchResultTdos = [];
        newState.searching = false;
        newState.hasNextPage = false;
      }
      return newState;
    })
    .addCase(SEARCH_MEDIA_FAILURE, (state, action) => {
      const searchError = get(
        action,
        'payload.error.message',
        'Error searching query'
      );
      return {
        ...state,
        searching: false,
        searchError,
      };
    })
    .addCase(ON_CURRENT_PAGE_CHANGE, (state, action) => {
      const currentPage = action.payload.currentPage;
      const lastCurrentPage = state.lastCurrentPage;
      return {
        ...state,
        currentPage,
        lastCurrentPage:
          currentPage > lastCurrentPage ? currentPage : lastCurrentPage,
      };
    })
    .addCase(ON_SEARCH_LIMIT_CHANGE, (state, action) => {
      return {
        ...state,
        limit: action.payload?.limit,
        ...cleanUpSearchResults,
      };
    })
    .addCase(ON_SEARCH_BAR_QUERY_CHANGE, (state, action) => {
      const searchBarQuery = get(action, 'payload.searchQuery');
      return {
        ...state,
        searchBarQuery,
        ...cleanUpSearchResults,
      };
    })
    .addCase(ON_FOLDER_QUERY_CHANGE, (state, action) => {
      const byFolderQuery = get(action, 'payload.searchQuery');
      return {
        ...state,
        byFolderQuery,
        ...cleanUpSearchResults,
      };
    })
    .addCase(ON_ENTITY_TYPES_QUERY_CHANGE, (state, action) => {
      const entityTypes = action.payload?.searchQuery;
      return {
        ...state,
        entityTypes,
        ...cleanUpSearchResults,
      };
    })
    .addCase(ON_FILE_TYPE_QUERY_CHANGE, (state, action) => {
      const fileTypeQuery = get(action, 'payload.searchQuery');
      return {
        ...state,
        fileTypeQuery,
        ...cleanUpSearchResults,
      };
    })
    .addCase(ON_DATE_QUERY_CHANGE, (state, action) => {
      const dateRangeQuery = get(action, 'payload');
      return {
        ...state,
        ...cleanUpSearchResults,
        dateRangeQuery,
      };
    })
    .addCase(GET_SEARCH_TDOS_BY_ID, (state) => {
      return {
        ...state,
        fetchingTdos: true,
      };
    })
    .addCase(ON_TOPICS_QUERY_CHANGE, (state, action) => {
      const topicsQuery = get(action, 'payload.searchQuery');
      return {
        ...state,
        topicsQuery,
        searchResults: [],
        searchResultTdos: [],
        totalResults: 0,
        hasNextPage: true,
        offset: 0,
      };
    })
    .addCase(GET_SEARCH_TDOS_BY_ID_SUCCESS, (state, action) => {
      const receivedTdos = get(action, 'payload.data');
      const lastCurrentPage = state.lastCurrentPage;
      const limit = state.limit;

      const tdos: TDO[] = [...state.searchResultTdosAll];
      // when navigateing back and forth, the receivedTdo might be already in
      // state.searchResultTdos. so need to dedup and overwrite with received one.
      for (const receivedTdo of receivedTdos) {
        const index = tdos.findIndex((tdo) => tdo.id === receivedTdo.id);
        if (index >= 0) {
          tdos[index] = receivedTdo;
        } else {
          tdos.push(receivedTdo);
        }
      }

      const newSearchResultTdosAll = tdos.slice(
        0,
        (lastCurrentPage + 1) * limit
      );
      return {
        ...state,
        searching: false,
        searchError: null,
        // accumulate search tdos for table to avoid fetching when user pages over known set of data
        searchResultTdos: receivedTdos,
        fetchingTdos: false,
        searchResultTdosAll: newSearchResultTdosAll,
      };
    })
    .addCase(GET_SEARCH_TDOS_BY_ID_FAILURE, (state, action) => {
      const searchError = get(
        action,
        'payload[0].message',
        'Error loading media metadata'
      );
      return {
        ...state,
        searching: false,
        searchError,
      };
    })
    .addCase(SAVE_QUERY_FOR_EXPORT_ALL, (state, action) => {
      const queryStore = get(action, 'payload');
      return {
        ...state,
        queryStore,
      };
    })
    .addCase(UPDATE_CURRENT_PAGE, (state, action) => {
      return {
        ...state,
        currentPage: action.payload,
      };
    })
    .addCase(UPDATE_SEARCH_RESULT_TDOS, (state, action) => {
      const { tdoIds = [], type } = action.payload;
      const newSearchResultTdos = state.searchResultTdos.map((item) => {
        if (tdoIds.includes(item.id)) {
          return {
            ...item,
            status: type,
          };
        }
        return item;
      });
      return {
        ...state,
        searchResultTdos: newSearchResultTdos,
        totalResults: state.totalResults - tdoIds.length,
      };
    })
    .addCase(TAG_SUGGESTIONS, (state, action) => {
      const tagsDisplayName = action?.payload?.fields || {};
      const tagsList = tagsDisplayName['tags.displayName'];
      if (tagsList.length) {
        const autoSuggestTag = tagsList.map((tag) => tag.key);
        return {
          ...state,
          autoSuggestTag,
        };
      }
      return {
        ...state,
      };
    })
    .addCase(TDO_TAGS_UPDATED, (state, action) => {
      const tdoTags: { [key: string]: { label: string; value: string }[] } = {};
      const payload = action.payload || {};
      Object.values(payload).forEach((item) => {
        tdoTags[item.id] = item.details.tags;
      });

      const { searchResultTdos, searchResultTdosAll } = state;
      for (const searchResultTdo of searchResultTdos) {
        const tags = tdoTags[searchResultTdo.id];
        if (tags) {
          searchResultTdo.details.tags = tags;
        }
      }
      for (const searchResultTdoAll of searchResultTdosAll) {
        const tags = tdoTags[searchResultTdoAll.id];
        if (tags) {
          searchResultTdoAll.details.tags = tags;
        }
      }
    })
    .addCase(ON_CLICK_FILES_TAB, (state) => ({
      ...state,
      ...cleanUpSearchResults,
    }))
    .addCase(ON_FILE_NAMES_QUERY_CHANGE, (state, action) => {
      const searchByFileNames = action.payload?.searchByFileNames || [];
      return {
        ...state,
        ...cleanUpSearchResults,
        searchByFileNameQuery: buildSearchQuery(
          searchByFileNames,
          QUERY_FIELD.fileNames
        ),
      };
    })
    .addCase(ON_IDS_QUERY_CHANGE, (state, action) => {
      const searchByIds = action.payload?.searchByIds || [];
      return {
        ...state,
        ...cleanUpSearchResults,
        searchByIdsQuery: buildSearchQuery(searchByIds, QUERY_FIELD.ids),
      };
    })
    .addCase(ON_ENGINES_RUN_QUERY_CHANGE, (state, action) => {
      const searchByEnginesRun = action.payload?.searchByEnginesRun || [];
      return {
        ...state,
        ...cleanUpSearchResults,
        searchByEnginesRunQuery: buildSearchByEnginesRunQuery(
          searchByEnginesRun,
          QUERY_FIELD.enginesRun
        ),
      };
    })
    .addCase(ON_DURATION_QUERY_CHANGE, (state, action) => {
      const duration = action.payload.duration;
      return {
        ...state,
        ...cleanUpSearchResults,
        searchByDurationQuery: buildSearchDurationQuery(
          duration,
          QUERY_FIELD.duration
        ),
      };
    })
    .addCase(ON_SORT_QUERY_CHANGE, (state, action) => {
      const sortQuery = action.payload?.sortQuery || [];
      get(action, 'payload.sortQuery', []);
      return {
        ...state,
        sortQuery: sortQuery.map((item) => {
          const columnName = SORT_FIELD[item.columnName];
          return {
            field: columnName ? columnName : QUERY_FIELD.ids,
            order: item.direction,
          };
        }),
      };
    })
    .addCase(ON_SORT_CHANGE, (state) => {
      return {
        ...state,
        ...cleanUpSearchResults,
      };
    })
    .addCase(UPDATE_SEARCH_PARAMETERS, (state, action) => {
      const { csp = {} } = action.payload;
      return {
        ...state,
        searchParameters: csp ? CSPToSearchParameters(csp, null) : [],
      };
    })
    .addCase(CLEAR_FILTERS_QUERY, (state) => {
      return {
        ...state,
        entityTypes: null,
        fileTypeQuery: null,
        topicsQuery: null,
        dateRangeQuery: null,
        searchByFileNameQuery: {},
        searchByIdsQuery: {},
        searchByEnginesRunQuery: {},
        searchByDurationQuery: {},
        ...cleanUpSearchResults,
      };
    })
    .addCase(UPDATE_TDO, (state, action) => {
      const { tdoId, name, tags } = action.payload;
      const { searchResultTdos, searchResultTdosAll } = state;

      for (const searchResultTdo of searchResultTdos) {
        if (searchResultTdo.id === tdoId) {
          if (name) {
            searchResultTdo.details.veritoneFile.filename = name;
          }
          if (tags?.length) {
            searchResultTdo.details.tags = tags;
          }
        }
      }

      for (const searchResultTdo of searchResultTdosAll) {
        if (searchResultTdo.id === tdoId) {
          if (name) {
            searchResultTdo.details.veritoneFile.filename = name;
          }
          if (tags?.length) {
            searchResultTdo.details.tags = tags;
          }
        }
      }
    });
});

const cleanUpSearchResults = {
  searchResults: [],
  searchResultTdos: [],
  totalResults: 0,
  hasNextPage: true,
  offset: 0,
  searchResultTdosAll: [],
  currentPage: 0,
};

export default reducer;
export const namespace = 'search';
export const local = (state: any) => state[namespace] as typeof defaultState;

export const dispatchSearchMediaAction =
  (currentPage: number) => (dispatch: any) => {
    dispatch(SEARCH_MEDIA(currentPage));
  };

export const onClickFilesTab = () => ON_CLICK_FILES_TAB();

export const updateSearchResultTdos = (tdoIds: string[], type: TDO['status']) =>
  UPDATE_SEARCH_RESULT_TDOS({ tdoIds, type });

export const searchMedia =
  (searchQuery: any, currentPage: number) =>
  async (dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = `${state.config.apiRoot}/api/search/file_search_authtoken`;
    const endpointCount = `${state.config.apiRoot}/api/search/file_search_authtoken/count`;
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;

    const enableTimeWarnings = state.config.apiRoot.includes('stage');
    const reqStartTime = Date.now();
    try {
      const [searchResult, searchResultCount] = await Promise.all([
        fetchSearchMedia(endpoint, token, veritoneAppId, searchQuery),
        fetchSearchMedia(endpointCount, token, veritoneAppId, searchQuery),
      ]);

      const reqEndTime = Date.now();
      if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
        console.error(
          `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
          { endpoint, query: JSON.stringify(searchQuery) }
        );
      }

      if (searchResult?.error) {
        const message = searchResult.error.errors
          .map((e) => e.message)
          .join(',');
        dispatch(SEARCH_MEDIA_FAILURE({ error: { message } }));
        return;
      }

      const result = {
        records: searchResult?.records ?? [],
        searchToken: searchResult?.searchToken ?? '',
        totalRecords:
          searchResultCount?.totalRecords ?? searchResult?.totalRecords ?? 0,
      };
      dispatch(SEARCH_MEDIA_SUCCESS({ result, currentPage }));
    } catch (err: any) {
      dispatch(SEARCH_MEDIA_FAILURE({ error: { message: err } }));
    }
  };

export async function fetchSearchMedia(
  endpoint: string,
  token: string | null,
  // Type null here were safe.
  // The token parameter in the fetchSearchMedia function can have a type of null because of how the getApiAuthToken function is designed.
  // It is safe because when the app booting at the first time, token is null
  // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
  // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
  // and received the payload from fetchUser response.
  // This non null assertion just allow running into that case without change so much in code
  veritoneAppId: string,
  searchQuery: Record<string, any> | string
): Promise<
  | {
      totalRecords?: number;
      searchToken?: string;
      records?: SearchResult[];
      error?: {
        errors: Error[];
      };
    }
  | undefined
> {
  const headers: { [key: string]: string } = {
    Authorization: 'Bearer ' + token,
    'Content-Type': 'application/json',
  };
  if (veritoneAppId) {
    headers['x-veritone-application'] = veritoneAppId;
  }

  return await fetch(endpoint, {
    method: 'post',
    body: JSON.stringify(searchQuery),
    headers,
  })
    .then((response) => response.text())
    .then((responseText) => JSON.parse(responseText))
    .catch((fetchError) => console.log(fetchError));
}

export const createGetTdosRequest =
  (tdoIds: string[]) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);
    const { deletedTdos, movedTdos } = state.tdosTable;
    const foldersState = state.folders;
    const selectedFolderId = foldersState?.selectedFolderId;

    const asyncfn = async () => {
      const resp = await getTdos(
        tdoIds,
        gql,
        deletedTdos,
        movedTdos,
        dispatch,
        selectedFolderId
      );
      return { data: { data: resp } };
    };
    return callAsyncFunc({
      actionTypes: [
        GET_SEARCH_TDOS_BY_ID,
        GET_SEARCH_TDOS_BY_ID_SUCCESS,
        GET_SEARCH_TDOS_BY_ID_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

async function getTdos(
  tdoIds: string[],
  gql: GQLApi,
  deletedTdos: TDO[],
  movedTdos: MoveFolderData[],
  dispatch: any,
  selectedFolderId: string
) {
  const resp = await gql.getTDOs(tdoIds);
  const currentTdosMap = resp.data.reduce<{ [key: string]: TDO }>(
    (map, item) => {
      map[item.id] = item;
      return map;
    },
    {}
  );
  const filteredMovedTdos = [] as MoveFolderData[];
  for (const tdo of movedTdos) {
    const currentTdo = currentTdosMap[tdo.id];
    if (currentTdo && selectedFolderId !== tdo.oldFolderId) {
      continue;
    }
    filteredMovedTdos.push(tdo);
  }

  dispatch(UPDATE_MOVED_TDOS(filteredMovedTdos));
  if (resp.errors?.length) {
    resp.errors.forEach((error) => {
      console.error(`failed to get tdo - ${error.message}`);
    });
  }
  const results = [];
  for (const tdoId of tdoIds) {
    const tdo = currentTdosMap[tdoId];
    const movedTdo = filteredMovedTdos.find((item) => item.id === tdoId);

    if (tdo) {
      results.push(movedTdo ? { ...tdo, status: 'moving' } : tdo);
    } else {
      const deletedTdo = deletedTdos.find((item) => item.id === tdoId);
      if (deletedTdo) {
        results.push({ ...deletedTdo, status: 'deleting' });
      } else if (movedTdo) {
        results.push({ ...movedTdo, status: 'moving' });
      } else {
        results.push({
          id: tdoId,
          status: 'error',
        });
      }
    }
  }
  return results;
}

export const updateSearchByFolderQuery =
  (folderTreeObjectId: string, isRoot: boolean) => (dispatch: any) => {
    const searchQuery = getFoldersSearchQueryCondition(
      folderTreeObjectId,
      isRoot
    );
    dispatch(ON_FOLDER_QUERY_CHANGE({ searchQuery }));
  };

const getFoldersSearchQueryCondition = (
  folderTreeObjectId: string,
  isRoot: boolean
) => {
  if (isRoot) {
    // if on the root folder level search across entire org
    return null;
  }
  return {
    field: 'parentTreeObjectIds',
    operator: 'terms',
    values: [folderTreeObjectId],
  };
};

export const updateEntityTypesQuery =
  (
    fieldPath: string[],
    values: { propertyValue?: string; propertyValueParent?: string },
    queryType: string
  ) =>
  (dispatch: any) => {
    // null is valid state - means no analytics query
    let searchQuery = null;
    const { propertyValue = '', propertyValueParent = '' } = values;

    if (fieldPath && propertyValue) {
      // support only fulltext contains query, extend later if needed
      if (fieldPath.length > 1) {
        searchQuery = {
          operator: 'and',
          conditions: [
            {
              operator: 'query_string',
              field: `${fieldPath[0]}.fulltext`,
              value: propertyValueParent,
            },
            {
              operator: 'query_string',
              field: `${fieldPath[1]}.fulltext`,
              value: propertyValue,
            },
          ],
        };
      } else {
        searchQuery = {
          field: `${fieldPath[0]}.fulltext`,
          operator: 'query_string',
          value: propertyValue,
        };
      }
    }

    dispatch(ON_ENTITY_TYPES_QUERY_CHANGE({ searchQuery, queryType }));
  };

export const updateFileTypeQuery =
  (mimeTypes: string[] | null, exclude: boolean | null) => (dispatch: any) => {
    // null is valid state - means no fileType query
    let searchQuery: any = null;
    if (mimeTypes && Array.isArray(mimeTypes) && mimeTypes.length) {
      if (exclude) {
        searchQuery = {
          operator: 'terms',
          field: 'fileType',
          values: mimeTypes,
          not: true,
        };
      } else {
        searchQuery = {
          operator: 'or',
          conditions: [],
        };
        mimeTypes.forEach((mimeType) =>
          searchQuery.conditions.push({
            field: 'fileType',
            operator: 'term',
            value: mimeType,
          })
        );
      }
    }
    dispatch(ON_FILE_TYPE_QUERY_CHANGE({ searchQuery }));
  };

export const fetchTagSuggestions =
  (payload: string) => (_dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = getApiRoot(state) + '/api/search/autocomplete';
    const query = {
      limit: 10,
      index: ['mine', 'global'],
      fields: ['tags.displayName'],
      returnContext: false,
      text: payload,
    };
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;
    const headers: { [key: string]: string } = {
      Authorization: 'Bearer ' + token,
      'Content-type': 'application/json',
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }

    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    let result: any;
    try {
      result = fetch(endpoint, {
        method: 'post',
        headers,
        body: JSON.stringify(query),
      })
        .then((res) => res.text())
        .then((resText) => JSON.parse(resText))
        .catch((error) => console.error(error));
    } catch (error) {
      console.error('failed to search autocomplete', error);
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify(query) }
      );
    }

    if (!result || result.error) {
      console.error('no result or failed to search autocomplete', result);
      return;
    }
    return result;
  };

export const updateTopicsQuery =
  (fieldPath: string, topics: string[] | null, queryType: string) =>
  (dispatch: any) => {
    if (isEmpty(fieldPath) || !Array.isArray(topics)) {
      return;
    }
    let searchQuery: any = null;
    if (!isEmpty(fieldPath) && !isEmpty(topics)) {
      searchQuery = {
        operator: 'or',
        conditions: [],
      };
      topics.forEach((item) => {
        searchQuery.conditions.push({
          operator: 'query_string',
          field: `${fieldPath}.fulltext`,
          value: item,
        });
      });
    }
    dispatch(ON_TOPICS_QUERY_CHANGE({ searchQuery, queryType }));
  };

export const updateFileNamesQuery = (searchByFileNames: string[]) =>
  ON_FILE_NAMES_QUERY_CHANGE({ searchByFileNames });

export const updateIdsQuery = (searchByIds: string[]) =>
  ON_IDS_QUERY_CHANGE({ searchByIds });

export const updateEnginesRunQuery = (searchByEnginesRun: string[]) =>
  ON_ENGINES_RUN_QUERY_CHANGE({ searchByEnginesRun });

export const updateDurationQuery = (duration: {
  hoursGt: number;
  minutesGt: number;
  secondsGt: number;
  hoursLte: number;
  minutesLte: number;
  secondsLte: number;
}) => ON_DURATION_QUERY_CHANGE({ duration });

export const updateSortQuery = (
  sortQuery: { columnName: string; direction: string }[]
) => ON_SORT_QUERY_CHANGE({ sortQuery });

export const onSortChange = (
  sortQuery: { columnName: string; direction: string }[]
) => ON_SORT_CHANGE({ sortQuery });

type SearchQuery = SEARCH_BAR_QUERY & {
  limit: number;
  offset: number;
  sort?: ReturnType<typeof getSortQuery>;
};

const isQueryCondition = (query: any): query is QueryCondition => {
  return (
    query && typeof query === 'object' && typeof query.operator === 'string'
  );
};

export const getFullSearchQuery = (state: any) => {
  const searchBarQuery = getSearchBarQuery(state);
  const limit = getLimit(state);
  const offset = getCurrentPage(state) * limit;
  // TODO: this needs better typing
  // const searchQuery: Record<string, any> = { ...searchBarQuery, limit, offset };
  const searchQuery: SearchQuery = { ...searchBarQuery, limit, offset };
  const illuminateAppFolders: {
    id: string;
    treeObjectId: string;
  }[] = state.tdosTable.illuminateAppFolders;
  const { subFolderIds, rootFolderId, selectedFolderId } = get(
    state,
    'folders',
    {}
  );
  const isRoot = rootFolderId === selectedFolderId;
  const showFilesInCurrentFolder = get(
    state,
    'user.user.organization.kvp.features.illuminate.showFilesInCurrentFolder',
    'disabled'
  );

  const excludeIlluminateAppFolder = {
    field: 'parentTreeObjectIds',
    operator: 'terms',
    values: illuminateAppFolders.map((f) => f.treeObjectId),
    not: true,
  };

  const excludeSubFolder = {
    field: 'parentTreeObjectIds',
    operator: 'terms',
    values: [] as string[],
    not: true,
  };
  const byRootFolderQuery = {
    operator: 'exists',
    name: 'time-slice.parentTreeObjectIds',
    not: true,
  };

  if (
    Array.isArray(subFolderIds) &&
    subFolderIds.length > 0 &&
    showFilesInCurrentFolder === 'enabled' &&
    !isRoot
  ) {
    excludeSubFolder.values.push(...subFolderIds);
  }
  searchQuery.query = {
    operator: 'and',
    conditions: [],
  };
  if (!isEmpty(excludeIlluminateAppFolder.values)) {
    searchQuery.query.conditions.push(excludeIlluminateAppFolder);
  }
  if (!isEmpty(excludeSubFolder.values)) {
    searchQuery.query.conditions.push(excludeSubFolder);
  }
  if (get(searchBarQuery, 'query.conditions.length')) {
    searchQuery.query.conditions.push(
      ...(searchBarQuery?.query?.conditions ?? [])
    );
  }
  const byFolderQuery = getByFolderQuery(state);
  if (isQueryCondition(byFolderQuery)) {
    searchQuery.query.conditions.push(byFolderQuery);
  }
  const entityTypes = getEntityTypesQuery(state);
  if (isQueryCondition(entityTypes)) {
    searchQuery.query.conditions.push(entityTypes);
  }
  const fileTypeQuery = getFileTypeQuery(state);
  if (isQueryCondition(fileTypeQuery)) {
    searchQuery.query.conditions.push(fileTypeQuery);
  }
  const dateRangeQuery = getDateRangeQuery(state);
  if (isQueryCondition(dateRangeQuery)) {
    searchQuery.query.conditions.push(dateRangeQuery);
  }
  const topicsQuery = getTopicsQuery(state);
  if (isQueryCondition(topicsQuery)) {
    searchQuery.query.conditions.push(topicsQuery);
  }
  const searchByFileNameQuery = getSearchByFileNameQuery(state);
  if (isQueryCondition(searchByFileNameQuery)) {
    searchQuery.query.conditions.push(searchByFileNameQuery);
  }
  const searchByIdsQuery = getSearchByIdsQuery(state);
  if (isQueryCondition(searchByIdsQuery)) {
    searchQuery.query.conditions.push(searchByIdsQuery);
  }
  const searchByEnginesRunQuery = getSearchByEnginesRunQuery(state);
  if (isQueryCondition(searchByEnginesRunQuery)) {
    searchQuery.query.conditions.push(searchByEnginesRunQuery);
  }
  const searchByDurationQuery = getSearchByDurationQuery(state);
  if (isQueryCondition(searchByDurationQuery)) {
    searchQuery.query.conditions.push(searchByDurationQuery);
  }
  const sortQuery = getSortQuery(state);
  if (sortQuery.length) {
    searchQuery.sort = sortQuery;
  }

  if (showFilesInCurrentFolder === 'enabled' && isRoot) {
    searchQuery.query.conditions.push(byRootFolderQuery);
  }
  return searchQuery;
};

function CSPToSearchParameters(
  cognitiveSearchProfile: any,
  parentJoinOperator: any
) {
  // handle case where csp is just a single term without any join groups
  if (cognitiveSearchProfile.state && cognitiveSearchProfile.engineCategoryId) {
    return [
      {
        id: guid(),
        conditionType: cognitiveSearchProfile.engineCategoryId,
        value: cognitiveSearchProfile.state,
      },
    ];
  }

  const getJoinOperator = (query: any) => {
    const operators = Object.keys(query);
    return operators[0];
  };

  let searchParameters = [];
  const cspJoinOperator = getJoinOperator(cognitiveSearchProfile);
  if (!cspJoinOperator) {
    return [];
  }
  const joinOperator = cspJoinOperator.replace('(', '');
  const conditions = cognitiveSearchProfile[cspJoinOperator];
  const shouldAddParens =
    cspJoinOperator.endsWith('(') ||
    (parentJoinOperator === 'and' && cspJoinOperator === 'or');
  if (shouldAddParens) {
    searchParameters.push({ id: guid(), conditionType: 'group', value: '(' });
  }

  for (let i = 0; i < conditions.length; i++) {
    if ('engineCategoryId' in conditions[i]) {
      const newSearchPill = {
        id: guid(),
        conditionType: conditions[i].engineCategoryId,
        value: conditions[i].state,
      };
      searchParameters.push(newSearchPill);
    } else {
      const subSearchParameters: any = CSPToSearchParameters(
        conditions[i],
        cspJoinOperator
      );
      searchParameters = [...searchParameters, ...subSearchParameters];
    }
    if (i < conditions.length - 1) {
      searchParameters.push({
        id: guid(),
        conditionType: 'join',
        value: joinOperator,
      });
    }
  }
  if (shouldAddParens) {
    searchParameters.push({ id: guid(), conditionType: 'group', value: ')' });
  }
  return searchParameters;
}

export const getSearchBarQuery = (state: any) => local(state).searchBarQuery;
export const getByFolderQuery = (state: any) => local(state).byFolderQuery;
export const getEntityTypesQuery = (state: any) => local(state).entityTypes;
export const getFileTypeQuery = (state: any) => local(state).fileTypeQuery;
export const getDateRangeQuery = (state: any) => local(state).dateRangeQuery;
export const getTopicsQuery = (state: any) => local(state).topicsQuery;
export const getLimit = (state: any) => local(state).limit;
export const getOffset = (state: any) => local(state).offset;
export const getSearchResults = (state: any) => local(state).searchResults;
export const getSearchResultTdos = (state: any) =>
  local(state).searchResultTdos;
// approximate number of total results from ES
export const getTotalResults = (state: any) => local(state).totalResults;
export const hasNextPage = (state: any) => local(state).hasNextPage;
export const isSearchingMedia = (state: any) => local(state).searching;
export const getSearchError = (state: any) => local(state).searchError;
export const getQueryToExportAll = (state: any) => local(state).queryStore;
export const getFetchingTdos = (state: any) => local(state).fetchingTdos;
export const getEngineCategoriesIds = (state: any) =>
  local(state).engineCategoriesIds;
export const getCurrentPage = (state: any) => local(state).currentPage;
export const getAutoSuggestedTags = (state: any) => local(state).autoSuggestTag;
export const getNoData = (state: any) => local(state).noData || false;
export const getSearchParameters = (state: any) =>
  local(state).searchParameters;
// TODO: can we define this type from default state somehow?
export const getSentiment = (state: any) =>
  state.user.user.organization.kvp.features.illuminate?.sentiment as
    | { positive: string; negative: string }
    | undefined;
export const getSearchByFileNameQuery = (state: any) =>
  local(state).searchByFileNameQuery;
export const getSearchByIdsQuery = (state: any) =>
  local(state).searchByIdsQuery;
export const getSearchByEnginesRunQuery = (state: any) =>
  local(state).searchByEnginesRunQuery;
export const getSearchByDurationQuery = (state: any) =>
  local(state).searchByDurationQuery;
export const getSortQuery = (state: any) => local(state).sortQuery;
export const getlastCurrentPage = (state: any) => local(state).lastCurrentPage;
export const getSearchResultTdosAll = (state: any) =>
  local(state).searchResultTdosAll;
