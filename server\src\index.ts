import fs from 'fs';
import https from 'https';
import parseArgs from 'minimist';
import { createExpressApp } from './application/index';
import createConfig from './config';
import env from './env';

export function start() {
  const config = createConfig({ env });
  const expressApp = createExpressApp({ config });

  config.log.info({ port: env.port }, 'Starting application');
  if (env.nodeEnv === 'development') {
    const args = parseArgs(process.argv.slice(2));
    if (args.key && args.cert) {
      const privateKey = fs.readFileSync(args.key, 'utf8');
      const certificate = fs.readFileSync(args.cert, 'utf8');
      https
        .createServer({ key: privateKey, cert: certificate }, expressApp)
        .listen(env.port);
    } else {
      console.error('Missing cert args');
      process.exit(1);
    }
  } else {
    expressApp.listen(env.port);
  }
  config.log.info(`${env.serviceName} accepting connections over ${env.port}`);
}
