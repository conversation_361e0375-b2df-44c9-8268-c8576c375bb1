import { ExtendedError } from '@utils';
import {
  fetchGraphQLApiThrowError,
  getApiConnectionParameters,
} from '../apiHelper';
import fetchGraphQLApi from '../fetchGraphQLApi';

const batchQuerySize = 10;

export class GQL<PERSON><PERSON> {
  constructor(
    private endpoint: string,
    private token: string,
    private veritoneAppId: string
  ) {}

  static newGQLApi(state: any) {
    const { endpoint, token, veritoneAppId } =
      getApiConnectionParameters(state);
    // The ! is safe because when the app booting at the first time, token is null
    // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
    // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
    // and received the payload from fetchUser response.
    // This non null assertion just allow running into that case without change so much in code
    return new GQLApi(endpoint, token!, veritoneAppId);
  }

  static async putFileToSignedUri({
    signedUri,
    data,
    contentType = 'application/json',
    veritoneAppId,
  }: {
    signedUri: string;
    data: any;
    contentType: string;
    veritoneAppId?: string;
  }) {
    const headers: HeadersInit = {
      'Content-Type': contentType,
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }
    const resp = await fetch(signedUri, {
      method: 'PUT',
      mode: 'cors',
      headers,
      body: JSON.stringify(data),
    });
    return resp;
  }

  async upload({
    data,
    contentType = 'application/json',
  }: {
    data: any;
    contentType?: string;
  }) {
    const signedWritableUrl = await this.getSignedWritableUri({
      type: 'asset',
    });
    await GQLApi.putFileToSignedUri({
      signedUri: signedWritableUrl.url,
      data,
      contentType: contentType,
      veritoneAppId: this.veritoneAppId,
    });
    return signedWritableUrl;
  }

  async getSignedWritableUri({
    type,
    path,
    key,
    expiresInSeconds = 86400,
  }: {
    type?: string;
    path?: string;
    key?: string;
    expiresInSeconds?: number;
  }) {
    const query = `
      query ($key: String, $type: String, $path: String, $expiresInSeconds: Int) {
        getSignedWritableUrl(
          key: $key,
          type: $type,
          path: $path,
          expiresInSeconds: $expiresInSeconds,
        ) {
          url
          getUrl
          unsignedUrl
        }
      }`;
    const variables = {
      key,
      path,
      type,
      expiresInSeconds,
    };

    const response = await fetchGraphQLApiThrowError<{
      getSignedWritableUrl: {
        url: string;
        getUrl: string;
        unsignedUrl: string;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.getSignedWritableUrl;
  }

  async createAsset({
    tdoId,
    unsignedUrl,
    engineId,
    taskId,
    description,
    details,
    assetType,
    contentType,
  }: {
    tdoId: string;
    unsignedUrl: string;
    engineId: string;
    taskId: string;
    description: string;
    details: Record<string, any>;
    assetType: string;
    contentType: string;
  }) {
    const variables = {
      assetType,
      contentType,
      description,
      details,
      sourceData: {
        engineId,
        taskId,
      },
      tdoId,
      uri: unsignedUrl,
    };

    const query = `
      mutation ($tdoId: ID!, $contentType: String!, $assetType: String!, $details: JSONData!,
        $description: String, $sourceData: SetAssetSourceData, $uri: String!) {
      createAsset(input: {
        containerId: $tdoId
        contentType: $contentType
        description: $description
        assetType: $assetType
        details: $details
        sourceData: $sourceData
        uri: $uri
        isUserEdited: true
      }) {
        id
        uri
        type
      }
      }`;

    const response = await fetchGraphQLApiThrowError<{
      createAsset: {
        id: string;
        uri: string;
        type: string;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.createAsset;
  }

  async createTDOWithAsset({
    startDateTime,
    stopDateTime,
    parentFolderId,
    name,
    addToIndex = false,
    contentType = 'application/json',
    assetType,
    uri,
  }: {
    startDateTime: string;
    stopDateTime: string;
    parentFolderId: string;
    name: string;
    addToIndex: boolean;
    contentType: string;
    assetType: string;
    uri: string;
  }) {
    const query = `mutation createTDOWithAsset {
    createTDOWithAsset (input: {
        startDateTime: "${startDateTime}",
        stopDateTime:"${stopDateTime}",
        parentFolderId: "${parentFolderId}",
        name: "${name}",
        addToIndex: ${addToIndex}
        contentType: "${contentType}",
        assetType:  "${assetType}",
        uri: "${uri}"
    }) {
      id
      assets{
        records{
          id
          name
          contentType
          assetType
          uri
        }
      }
    }
  }`;
    const variables = {};
    const response = await fetchGraphQLApiThrowError<{
      createTDOWithAsset: {
        id: string;
        assets: {
          records: {
            id: string;
            name: string;
            contentType: string;
            assetType: string;
            uri: string;
          }[];
        };
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.createTDOWithAsset;
  }

  // dataArray is an array of objects in shape of {id, data}. If the id
  // is empty string, a new SDO will be created with data. If id is an sdo id, then
  // the sdo will updated with the data.
  async createStructuredData(
    schemaId: string,
    dataArray: Array<{ id?: string; data: any }>
  ) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < dataArray.length; i += batchQuerySize) {
      const batch = dataArray.slice(i, i + batchQuerySize);
      const inputStr: Array<string> = [];
      const updateQuery: Array<string> = [];
      const variables: {
        [key: string]: { id: string; schemaId: string; data: any };
      } = {};
      batch.forEach((item, index) => {
        inputStr.push(`$input${index}: CreateStructuredData!`);
        updateQuery.push(
          `createStructuredData_${index}:createStructuredData(input: $input${index}) {id}`
        );
        variables[`input${index}`] = {
          id: item.id ? item.id : '',
          schemaId,
          data: JSON.parse(JSON.stringify(item.data)),
        };
      });
      const query = `mutation createStructuredData(${inputStr.join(',')}){
        ${updateQuery.join('\n  ')}
      }`;

      const response = await fetchGraphQLApi<
        Record<
          string,
          {
            id: string;
          }
        >
      >({
        endpoint: this.endpoint,
        query,
        variables,
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });
      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors && response.errors.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async createJob({
    targetId,
    tasks,
    routes = [],
    clusterId = '',
  }: {
    targetId: string;
    tasks: Array<object>;
    routes?: Array<object>;
    clusterId?: string;
  }) {
    const query = `
      mutation createJob($input: CreateJob){
        createJob(input: $input) {
          id
        }
      }`;

    const variables = {
      input: {
        targetId,
        tasks,
        routes,
        clusterId,
      },
    };

    const response = await fetchGraphQLApiThrowError<{
      createJob: { id: string };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.createJob?.id;
  }

  async getTDOs(tdoIds: string[]) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];

      batch.forEach((tdoId, index) => {
        updateQuery.push(
          `temporalDataObject_${tdoId}_${index}:temporalDataObject(id: "${tdoId}") {
            id
            name
            createdDateTime
            startDateTime
            stopDateTime
            details
            folders {
              treeObjectId
            }
            assets(assetType: "vtn-standard"){
              records{
                id
                signedUri
                assetType
                contentType
              }
            }
            primaryAsset(assetType: "media") {
              id
              signedUri
              contentType
            }
            thumbnailUrl
            sourceImageUrl
            previewUrl
            streams {
              protocol
              uri
            }
          }`
        );
      });
      const query = `query { ${updateQuery.join('\n')} }`;

      const response = await fetchGraphQLApi<
        Record<
          string,
          {
            id: string;
            name: string;
            createdDateTime: string;
            startDateTime: string;
            stopDateTime: string;
            details: any;
            folders: {
              treeObjectId: string;
            }[];
            assets: {
              records: {
                id: string;
                signedUri: string;
                assetType: string;
                contentType: string;
              }[];
            };
            primaryAsset: {
              id: string;
              signedUri: string;
              contentType: string;
            };
            thumbnailUrl: string;
            sourceImageUrl: string;
            previewUrl: string;
            streams: {
              protocol: string;
              uri: string;
            }[];
          }
        >
      >({
        endpoint: this.endpoint,
        query,
        variables: { tdoIds },
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors?.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async moveTDOs(
    tdos: { id: string; treeObjectId: string }[],
    newTreeObjectId: string
  ) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdos.length; i += batchQuerySize) {
      const batch = tdos.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];
      batch.forEach((tdo, index) => {
        updateQuery.push(
          `tdo_${tdo.id}_${index}:moveTemporalDataObject(input: {
              tdoId: "${tdo.id}",
              oldFolderId: "${tdo.treeObjectId}",
              newFolderId: "${newTreeObjectId}",
            }) {
              id
              folders {
                folderPath{id}
              },
            }`
        );
      });

      const query = `mutation { ${updateQuery.join('\n')} }`;
      const response = await fetchGraphQLApi<
        Record<
          string,
          {
            id: string;
            folders: {
              folderPath: { id: string }[];
            }[];
          }
        >
      >({
        endpoint: this.endpoint,
        query,
        variables: {},
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res && res.id));
      if (response.errors && response.errors.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async deleteTDOs(tdoIds: string[]) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const deleteQuery: Array<string> = [];

      batch.forEach((tdoId, index) => {
        deleteQuery.push(
          `deleteTDO_${tdoId}_${index}:deleteTDO(id: "${tdoId}") {
            id
            message
          }`
        );
      });
      const query = `mutation { ${deleteQuery.join('\n')} }`;
      const response = await fetchGraphQLApi<{
        [key: string]: { id: string; message: string };
      }>({
        endpoint: this.endpoint,
        query,
        variables: {},
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors && response.errors.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }
}
