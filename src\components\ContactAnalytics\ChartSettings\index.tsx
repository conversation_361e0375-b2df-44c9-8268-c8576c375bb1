import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import {
  selectAggregationSize,
  selectUseLegend,
  selectUseTitle,
  selectLineWidth,
} from '../worker/reducer';
import * as styles from './styles.scss';
import { workerConnect } from '../worker/workerConnect';
import * as actions from '../worker/actions';

const ChartSettings = ({
  aggregationSize,
  onClose,
  open,
  useLegend,
  useTitle,
  lineWidth,
  cleanupCharts,
  setChartSettings,
}: Props) => {
  const [aggSize, setAggSize] = useState(aggregationSize);
  const [useLegendSetting, setUseLegendSetting] = useState(useLegend);
  const [useTitleSetting, setUseTitleSetting] = useState(useTitle);
  const [lineWidthSetting, setLineWidthSetting] = useState<number>(lineWidth);
  const [settingChanges, setSettingChanges] = useState<string[]>([]);

  useEffect(() => {
    if (open) {
      setAggSize(aggregationSize);
      setUseLegendSetting(useLegend);
      setUseTitleSetting(useTitle);
      setLineWidthSetting(lineWidth);
      setSettingChanges([]);
    }
  }, [open, aggregationSize, lineWidth, useLegend, useTitle]);

  const handleUseTitleSettingChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setUseTitleSetting(e.target.checked);
    setSettingChanges([...settingChanges, 'useTitle']);
  };
  const handleUseLegendSettingChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setUseLegendSetting(e.target.checked);
    setSettingChanges([...settingChanges, 'useLegend']);
  };
  const handleAggSizeChange = (e: SelectChangeEvent<string>) => {
    setAggSize(e.target.value);
    setSettingChanges([...settingChanges, 'aggSize']);
  };
  const handleLineWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLineWidthSetting(parseInt(e.target.value));
    setSettingChanges([...settingChanges, 'lineWidth']);
  };
  const handleConfirm = () => {
    window.setTimeout(() => cleanupCharts(), 100);
    setChartSettings(
      {
        aggSize,
        useLegendSetting,
        useTitleSetting,
        lineWidthSetting,
      },
      settingChanges
    );
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogTitle className={styles['chart-settings-dialog-title']}>
        {' '}
        CHART SETTINGS{' '}
      </DialogTitle>
      <DialogContent>
        <div className={styles['chart-settings-dialog']}>
          <FormControl
            className={styles['chart-settings-dialog-data-agg-size']}
            variant="outlined"
          >
            <InputLabel id="chart-settings-dialog-label">
              Data Aggregation Size
            </InputLabel>
            <Select
              labelId="chart-settings-dialog-label"
              value={aggSize}
              onChange={handleAggSizeChange}
            >
              <MenuItem value="1d">1 Day</MenuItem>
              <MenuItem value="1w">1 Week</MenuItem>
              <MenuItem value="1M">1 Month</MenuItem>
              <MenuItem value="1y">1 Year</MenuItem>
            </Select>
          </FormControl>

          <TextField
            label="Line Width"
            type="number"
            className={styles['chart-settings-dialog-line-width']}
            variant="outlined"
            value={lineWidthSetting}
            onChange={handleLineWidthChange}
            slotProps={{
              htmlInput: {
                min: 1,
                max: 10,
              },
            }}
          />
          <FormControlLabel
            className={styles['chart-settings-dialog-use-chart-legend']}
            control={
              <Switch
                checked={useLegendSetting}
                onChange={handleUseLegendSettingChange}
                color="primary"
              />
            }
            label="Show Chart Legend"
          />
          <FormControlLabel
            className={styles['chart-settings-dialog-use-chart-title']}
            control={
              <Switch
                checked={useTitleSetting}
                onChange={handleUseTitleSettingChange}
                color="primary"
              />
            }
            label="Show Chart Title"
          />
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleConfirm} color="primary">
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

interface Props {
  open: boolean;
  onClose: () => void;
  aggregationSize: string;
  useLegend: boolean;
  useTitle: boolean;
  lineWidth: number;
  setChartSettings: (
    {
      aggSize,
      useLegendSetting,
      useTitleSetting,
      lineWidthSetting,
    }: {
      aggSize: string;
      useLegendSetting: boolean;
      useTitleSetting: boolean;
      lineWidthSetting: number;
    },
    settingChanges: string[]
  ) => void;
  cleanupCharts: () => void;
}

const mapStateToProps = (state: any) => ({
  aggregationSize: selectAggregationSize(state),
  useLegend: selectUseLegend(state),
  useTitle: selectUseTitle(state),
  lineWidth: selectLineWidth(state),
});

const mapDispatchToProps = {
  ...actions,
};

export default workerConnect(
  mapStateToProps,
  mapDispatchToProps
)(connect(null, null)(ChartSettings));
