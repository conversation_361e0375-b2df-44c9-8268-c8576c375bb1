import callGrap<PERSON><PERSON><PERSON><PERSON>, {
  createG<PERSON>FailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraph<PERSON>A<PERSON>';
import { createReducer } from '@reduxjs/toolkit';

export const FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO = createGQLRequestAction(
  'fetch schemas contact analytics data sdo'
);
export const FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO_SUCCESS =
  createGQLSuccessAction<FetchSchemasContactAnalyticsDataSDOResponse>(
    'fetch schemas contact analytics data sdo success'
  );
export const FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO_FAILURE =
  createGQLFailureAction('fetch schemas contact analytics data sdo failure');

const defaultState = {
  schemaIdContactAnalyticsSDO: '' as string | null,
};

const reducer = createReducer(defaultState, (builder) => {
  builder.addCase(
    FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO_SUCCESS,
    (state, action) => {
      return {
        ...state,
        schemaIdContactAnalyticsSDO:
          action.payload?.dataRegistry?.publishedSchema?.id || null,
      };
    }
  );
});

export default reducer;
export const namespace = 'contactAnalytics';

interface FetchSchemasContactAnalyticsDataSDOResponse {
  dataRegistry: {
    publishedSchema: {
      id: string;
    };
  };
}
export const fetchSchemasContactAnalyticsDataSDO =
  () => (dispatch: any, getState: any) => {
    const query = `query schemaId($dataRegistryId: ID!) {
    dataRegistry(id: $dataRegistryId) {
      publishedSchema {
        id
      }
    }
  }`;
    return callGraphQLApi({
      actionTypes: [
        FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO,
        FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO_SUCCESS,
        FETCH_SCHEMAS_CONTACT_ANALYTICS_DATA_SDO_FAILURE,
      ],
      query,
      variables: {
        dataRegistryId: getState().config.contactAnalyticsDataRegistryId,
      },
      dispatch,
      getState,
    });
  };

export const local = (state: any) => state[namespace];
export const selectSchemaIdContactAnalyticsSDO = (state: any) =>
  local(state).schemaIdContactAnalyticsSDO ?? 0;
