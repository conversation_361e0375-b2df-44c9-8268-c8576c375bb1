import {
  takeLatest,
  put,
  all,
  call,
  fork,
  select,
} from 'typed-redux-saga/macro';
import { showNotification } from '../snackbar';
import getDefaultClusterId from '../../../helpers/getDefaultClusterId';
import getEngineIdBulkTag from '../../../helpers/getEngineIdBulkTag';
import { getApiConnectionParameters } from '../../../helpers/apiHelper';
import {
  applyTags,
  APPLY_TAGS_TO_TDO_REQUEST,
  APPLY_TAGS_TO_TDO_FAILURE,
  APPLY_TAGS_TO_TDO_SUCCESS,
  TDO_TAGS_UPDATED,
} from '.';
import { Action } from 'state/sagas';
import { TDO } from '../universal/TDO';

function* watchApplyTagsToTdos() {
  yield* takeLatest(APPLY_TAGS_TO_TDO_REQUEST, handleTagTdo);
  yield* takeLatest(APPLY_TAGS_TO_TDO_FAILURE, function* (action) {
    yield* put(showNotification(action.payload.message, 'error'));
  });
  yield* takeLatest(APPLY_TAGS_TO_TDO_SUCCESS, function* (action) {
    yield* put(showNotification(action.payload.message, 'success'));
  });
}

function* handleTagTdo(
  action: Action<{
    tdoId: string;
    tags: { value: string; label: string }[];
    tdos?: TDO[];
    searchQuery?: any;
  }>
) {
  const clusterId = yield* select(getDefaultClusterId);
  const engineId = yield* select(getEngineIdBulkTag);
  const { endpoint, token, veritoneAppId } = yield* select(
    getApiConnectionParameters
  );

  try {
    const result = yield* call(
      applyTags,
      endpoint,
      token!,
      // The ! is safe because when the app booting at the first time, token is null
      // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
      // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
      // and received the payload from fetchUser response.
      // This non null assertion just allow running into that case without change so much in code
      veritoneAppId,
      action.payload,
      clusterId,
      engineId
    );
    if (!result) {
      const message = 'No file selected.';
      const error = new Error(message);
      yield* put(APPLY_TAGS_TO_TDO_FAILURE({ error, message }));
    } else {
      let message = 'Tagging files is in process.';
      if (result.type === 'api') {
        yield* put(TDO_TAGS_UPDATED({ ...result.apiResults }));
        message = 'Successfully tagged files.';
      }
      yield* put(APPLY_TAGS_TO_TDO_SUCCESS({ message }));
    }
  } catch (error) {
    const message = 'Failed to tag files.';
    yield* put(APPLY_TAGS_TO_TDO_FAILURE({ error, message }));
    console.error(message, error);
  }
}

export function* tagSaga() {
  yield* all([fork(watchApplyTagsToTdos)]);
}
