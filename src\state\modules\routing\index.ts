import { createAction } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { TABS } from 'state/modules/tabs';

export const ROUTE_AUTH = createAction<{
  query: {
    nextType: string;
    nextPayload: string | undefined;
  };
}>('route/ROUTE_AUTH');
export const ROUTE_HOME = createAction('route/ROUTE_HOME');
export const ROUTE_EXAMPLE_TAKEOVER = createAction(
  'route/ROUTE_EXAMPLE_TAKEOVER'
);
export const ROUTE_EXAMPLE_TABS = createAction<{
  tab: string;
}>('route/ROUTE_EXAMPLE_TABS');
export const ROUTE_FORBIDDEN = createAction('route/ROUTE_FORBIDDEN');
export const ROUTE_TABS = createAction<{
  tab: string;
  isClosePreview?: boolean;
}>('route/ROUTE_TABS');
export const ROUTE_FILE = createAction<{
  tab: string;
  tdoId: string | null;
  initFullScreen: boolean;
}>('route/ROUTE_FILE');
// export const refreshRoute = () => (dispatch, getState) => {
//   const currentLocation = getState().location;
//
//   dispatch({
//     type: currentLocation.type,
//     payload: currentLocation.payload,
//     meta: {
//       query: currentLocation.query
//     }
//   });
// };
type TabsKeys = keyof typeof TABS;
export interface Query {
  [key: string]: string | any;
}
export interface Payload {
  query?: Query | undefined;
  [key: string]: any;
  tab: (typeof TABS)[TabsKeys];
}

export const selectCurrentRoutePayload = (state: any) =>
  state.location.payload as Payload;
export const selectRouteType = (state: any) => state.location.type;
export const selectRoutesMap = (state: any) => state.location.routesMap;
export const selectPreviousRoute = (state: any) => state.location.prev;
export const selectCurrentRouteReturnTo = (state: any) =>
  get(selectRoutesMap(state), [selectRouteType(state), 'returnTo']);

export const navigateCurrentRouteReturnTo =
  () => (dispatch: any, getState: any) => {
    const action = get(selectCurrentRouteReturnTo(getState()), 'route');
    dispatch(action);
  };

export const navigateCurrentTab = () => (dispatch: any, getState: any) => {
  const tab = selectCurrentRoutePayload(getState())?.tab ?? TABS.Analytics;
  const action = ROUTE_TABS({ tab });
  dispatch(action);
};
