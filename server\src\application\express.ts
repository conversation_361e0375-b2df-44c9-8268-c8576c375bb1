import express, { NextFunction, Response } from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { v4 as uuidV4 } from 'uuid';
import { RequestWithMeta } from './types';
import { Config } from '../config';

function createExpressApp({ config }: { config: Config }) {
  const app = express();

  function primeRequestContext(
    req: RequestWithMeta,
    res: Response,
    next: NextFunction
  ) {
    const correlationId = uuidV4();

    req.metadata = { correlationId };
    res.set({ correlationId });

    next();
  }

  app.use(bodyParser.json());
  app.use(primeRequestContext);
  app.use(cors());

  app
    .route('/api/health')
    .get(config.healthCheckApp.handlers.get.handleHealthCheck);

  app
    .route('/api/v1/powerbi/provisionOrg')
    .post(config.powerbiApp.handlers.post.provisionOrg);
  app
    .route('/api/v1/powerbi/generateEmbedToken')
    .post(config.powerbiApp.handlers.post.generateEmbedToken);
  app
    .route('/api/v1/powerbi/pushData')
    .post(config.powerbiApp.handlers.post.pushData);
  app
    .route('/api/v1/powerbi/pushCustomQuestionDefinitions')
    .post(config.powerbiApp.handlers.post.pushCustomQuestionDefinitions);
  app
    .route('/api/v1/powerbi/template/:fileName')
    .put(config.powerbiApp.handlers.put.template);
  app
    .route('/api/v1/powerbi/template')
    .patch(config.powerbiApp.handlers.patch.template)
    .get(config.powerbiApp.handlers.get.template);
  app
    .route('/api/v1/powerbi/associateTemplate')
    .post(config.powerbiApp.handlers.post.associateTemplate);
  app
    .route('/api/v1/powerbi/bulkAssociateTemplate')
    .post(config.powerbiApp.handlers.post.bulkAssociateTemplate);

  app
    .route('/api/v1/powerbi/removeAllPowerBiData')
    .delete(config.powerbiApp.handlers.delete.removeAllPowerBiData);

  return app;
}

export default createExpressApp;
