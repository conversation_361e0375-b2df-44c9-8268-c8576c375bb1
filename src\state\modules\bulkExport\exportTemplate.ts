// External dependencies
import get from 'lodash/get';

// Internal dependencies
import {
  BulkExportCustomizedNames,
  BulkExportOption,
  ExportTemplateContent,
  ExportTemplateData,
} from 'src/model';
import { GQLA<PERSON> } from '../../../helpers/gqlApi';
import {
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '../../../helpers/apiHelper';
import { createAction, createReducer } from '@reduxjs/toolkit';

export const FETCH_EXPORT_TEMPLATE_SCHEMA_ID = createAsyncFuncRequestAction(
  'fetch export template schema id'
);
export const FETCH_EXPORT_TEMPLATE_SCHEMA_ID_SUCCESS =
  createAsyncFuncSuccessAction<{
    dataRegistry: {
      id: string;
      name: string;
      publishedSchema: {
        id: string;
      };
    };
  }>('fetch export template schema id success');
export const FETCH_EXPORT_TEMPLATE_SCHEMA_ID_FAILURE =
  createAsyncFuncFailureAction('fetch export template schema id failure');
export const FETCH_EXPORT_TEMPLATE_DATA = createAsyncFuncRequestAction(
  'start fetching export template'
);

interface ExportTemplateResponse {
  structuredDataObjects: {
    count: number;
    records: ExportTemplateData[];
  };
}
export const FETCH_EXPORT_TEMPLATE_DATA_SUCCESS =
  createAsyncFuncSuccessAction<ExportTemplateResponse>(
    'fetching export template success'
  );
export const FETCH_EXPORT_TEMPLATE_DATA_FAILURE = createAsyncFuncFailureAction(
  'fetching export template fail'
);
export const FETCH_SAVE_EXPORT_TEMPLATE_DATA_REQUEST = createAction<{
  name: string;
  fields: BulkExportOption;
  customizedNames: BulkExportCustomizedNames;
  id: string;
}>('request create export template data');
export const FETCH_SAVE_EXPORT_TEMPLATE_DATA = createAsyncFuncRequestAction(
  'create export template data'
);
export const FETCH_SAVE_EXPORT_TEMPLATE_DATA_SUCCESS =
  createAsyncFuncSuccessAction<{ id: string }[]>(
    'create export template data success'
  );
export const FETCH_SAVE_EXPORT_TEMPLATE_DATA_FAILURE =
  createAsyncFuncFailureAction('create export template data failure');
export const FETCH_DELETE_EXPORT_TEMPLATE_DATA_REQUEST = createAction<{
  id: string;
}>('request delete export template');
export const FETCH_DELETE_EXPORT_TEMPLATE_DATA = createAsyncFuncRequestAction(
  'delete export template data'
);
export const FETCH_DELETE_EXPORT_TEMPLATE_DATA_SUCCESS =
  createAsyncFuncSuccessAction<{
    deleteStructureData: {
      id: string;
    };
  }>('delete export template data success');

export const FETCH_DELETE_EXPORT_TEMPLATE_DATA_FAILURE =
  createAsyncFuncFailureAction('delete export template data failure');

const defaultState = {
  exportTemplateSchemaId: '',
  exportTemplateData: [] as ExportTemplateData[],
  fetchingExportTemplate: false,
  fetchingExportTemplateError: null,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_EXPORT_TEMPLATE_SCHEMA_ID_SUCCESS, (state, action) => {
      const schemaId = get(
        action,
        'payload.dataRegistry.publishedSchema.id',
        ''
      );
      return {
        ...state,
        fetchingExportTemplate: true,
        fetchingExportTemplateError: null,
        exportTemplateSchemaId: schemaId,
      };
    })
    .addCase(FETCH_EXPORT_TEMPLATE_SCHEMA_ID_FAILURE, (state, action) => ({
      ...state,
      fetchingExportTemplate: false,
      fetchingExportTemplateError: get(action, 'payload.error'),
    }))
    .addCase(FETCH_EXPORT_TEMPLATE_DATA, (state) => {
      return {
        ...state,
        fetchingExportTemplate: true,
        fetchingExportTemplateError: null,
      };
    })
    .addCase(FETCH_EXPORT_TEMPLATE_DATA_SUCCESS, (state, action) => {
      const exportTemplateData = get(
        action,
        'payload.structuredDataObjects.records',
        []
      );
      return {
        ...state,
        exportTemplateData: exportTemplateData,
        fetchingExportTemplate: false,
      };
    })
    .addCase(FETCH_EXPORT_TEMPLATE_DATA_FAILURE, (state, action) => {
      const error = get(action, 'payload.error');
      return {
        ...state,
        fetchingExportTemplate: false,
        fetchingExportTemplateError: error,
      };
    });
});

export const getExportTemplateDataSchemaId =
  () => (dispatch: any, getState: any) => {
    const dataRegistryId = getState().config.exportTemplateDataRegistryId;
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);

    const asyncfn = async () => {
      const resp = await gql.getSDOSchemaId(dataRegistryId);
      return resp;
    };

    return callAsyncFunc({
      actionTypes: [
        FETCH_EXPORT_TEMPLATE_SCHEMA_ID,
        FETCH_EXPORT_TEMPLATE_SCHEMA_ID_SUCCESS,
        FETCH_EXPORT_TEMPLATE_SCHEMA_ID_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export const getExportTemplates =
  (schemaId: string) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);

    const asyncfn = async () => {
      const resp = await gql.getSDOs(schemaId);
      return resp;
    };

    return callAsyncFunc({
      actionTypes: [
        FETCH_EXPORT_TEMPLATE_DATA,
        FETCH_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_EXPORT_TEMPLATE_DATA_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

// create ExportTemplate when id is empty string.
// update the existing ExportTemplate for the given nonempty id
export const upsertExportTemplate =
  (schemaId: string, id: string, data: ExportTemplateContent) =>
  (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);

    const asyncfn = async () => {
      const resp = await gql.createStructuredData(schemaId, [{ id, data }]);
      return resp;
    };

    return callAsyncFunc({
      actionTypes: [
        FETCH_SAVE_EXPORT_TEMPLATE_DATA,
        FETCH_SAVE_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_SAVE_EXPORT_TEMPLATE_DATA_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export const deleteExportTemplate =
  (schemaId: string, id: string) => (dispatch: any, getState: any) => {
    const state: any = getState();
    const gql = GQLApi.newGQLApi(state);

    const asyncfn = async () => {
      const resp = await gql.deleteSDO(schemaId, id);
      return resp;
    };

    return callAsyncFunc({
      actionTypes: [
        FETCH_DELETE_EXPORT_TEMPLATE_DATA,
        FETCH_DELETE_EXPORT_TEMPLATE_DATA_SUCCESS,
        FETCH_DELETE_EXPORT_TEMPLATE_DATA_FAILURE,
      ],
      asyncfn,
      dispatch,
      getState,
    });
  };

export default reducer;
export const namespace = 'exportTemplate';
export const local = (state: any) => state[namespace] as typeof defaultState;
export const getExportTemplateData = (state: any) =>
  local(state).exportTemplateData;
export const getSchemaId = (state: any) => local(state).exportTemplateSchemaId;
