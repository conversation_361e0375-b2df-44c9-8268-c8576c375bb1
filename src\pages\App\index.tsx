import React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import RootRoute from '../../RootRoute';
import materialUITheme from '../../materialUITheme';
import * as Sentry from '@sentry/react';
const config = window.config;

if (process.env.NODE_ENV !== 'development') {
  Sentry.init({
    dsn: config.sentryDSN,
    environment: config.nodeEnv,
  });
}
// todo: add global stuff -- snackbar/modal targets, intercom button, ...

class App extends React.Component {
  render() {
    return (
      <div style={{ height: '100%', overflow: 'hidden' }}>
        <CssBaseline />
        <StyledEngineProvider injectFirst>
          <ThemeProvider theme={materialUITheme}>
            <RootRoute />
          </ThemeProvider>
        </StyledEngineProvider>
      </div>
    );
  }
}

export default App;
