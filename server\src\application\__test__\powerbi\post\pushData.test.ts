import supertest from 'supertest';
import { request } from 'graphql-request';
import axios from 'axios';
import expressApp from '../../setupExpress';
import { adminPermissionMasksMock, contactPushDataRequestMock } from '../fixtures';

jest.mock('graphql-request', () => ({ request: jest.fn() }));
jest.mock('axios', () => ({ post: jest.fn(), get: jest.fn() }));
jest.mock('../../../powerbi/queries', () => () => ({
    insertContactDataRows: () => Promise.resolve(),
    findQuestionDefinition: () => Promise.resolve({
        id: 1
    }),
}));

describe('POST /api/v1/powerbi/pushData', () => {
    (request as jest.Mock).mockImplementation(({ document }) => {
        if (document.includes('validateToken')) {
            return Promise.resolve({ validateToken: { token: 'valid token' } });
        }
        if (document.includes('me')) {
            return Promise.resolve({ me: { organizationId: 123 } });
        }
        return Promise.resolve()
    });

    (axios.post as jest.Mock).mockImplementation((url) => {
        if (url.includes('GenerateToken')) {
            return Promise.resolve({
                data: {
                    token: 'avalidembedtoken',
                    tokenId: '12345-67890',
                    expiration: 3600
                }
            });
        }
        if (url.includes('oauth')) {
            return Promise.resolve({
                data: {
                    access_token: 'validServicePricipalToken',
                    expires_in: 3600
                }
            });
        }
        return Promise.resolve()
    });


    (axios.get as jest.Mock).mockImplementation((url) => {
        if (url.includes('current-user')) {
            return Promise.resolve({
                data: {
                    permissionMasks: adminPermissionMasksMock
                }
            });
        }

        return Promise.resolve()
    });

    it('returns the number of rows inserted', async () => {
        await supertest(expressApp)
            .post('/api/v1/powerbi/pushData')
            .set('Authorization', 'Bearer valid-token')
            .send({ ...contactPushDataRequestMock })
            .expect(200)
            .then(res => {
                expect(res.headers['content-type']).toContain('application/json; charset=utf-8');
                expect(res.body).toMatchObject({
                    message: "2 stops inserted successfully with 0 error(s)"
                });
            })
    })
})

