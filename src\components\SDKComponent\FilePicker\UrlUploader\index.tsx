import FormControl from '@mui/material/FormControl';
import Input from '@mui/material/Input';
import InputLabel from '@mui/material/InputLabel';
import { withStyles, ClassNameMap } from '@mui/styles';
import mime from 'mime-types';
import React, { useRef, useState } from 'react';
import fetch from '../../../../helpers/fetchRetry';
import styles from './styles';

interface UrlUploaderProps {
  onUpload: (file: File) => void;
  acceptedFileTypes?: string[];
  classes: ClassNameMap;
}

const UrlUploader: React.FC<UrlUploaderProps> = ({
  onUpload,
  acceptedFileTypes = [],
  classes,
}) => {
  const [image, setImage] = useState<string>('');
  const [, setFetchingImage] = useState<boolean>(false);
  const [uploadError, setUploadError] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const preventInput = (evt: React.KeyboardEvent) => {
    evt.preventDefault();
  };

  const handlePaste = (evt: React.ClipboardEvent<HTMLInputElement>) => {
    setImage('');
    setFetchingImage(true);

    const imageUrl = evt.clipboardData.getData('Text');

    fetch(imageUrl)
      .then((response) => {
        if (response.status === 200 || response.status === 0) {
          return response.blob();
        } else {
          throw new Error(`Error loading: ${imageUrl}`);
        }
      })
      .then((responseBlob) => {
        if (validateFileType(responseBlob.type)) {
          return responseBlob;
        } else {
          throw new Error(
            `${imageUrl} did not match any of the allowed fileTypes`
          );
        }
      })
      .then((responseBlob) => {
        const fileReader = new FileReader();
        fileReader.onload = () => {
          setImage(fileReader.result as string);
          setFetchingImage(false);
          setUploadError(false);
        };

        fileReader.readAsDataURL(responseBlob);
        return responseBlob;
      })
      .then((responseBlob) => {
        const fileType = responseBlob.type;
        const extension = mime.extension(fileType) || 'bin';
        const tryFilename = /(?=\w+\.\w{3,4}$).+/;
        const urlFileName = imageUrl.match(tryFilename);
        const fileName = urlFileName
          ? urlFileName[0]
          : `${imageUrl}.${extension}`;

        onUpload(new File([responseBlob], fileName, { type: fileType }));

        return responseBlob;
      })
      .catch((error) => {
        console.error(error);
        setFetchingImage(false);
        setUploadError(true);
      });
  };

  const handleChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    if (uploadError && !evt.target.value.length) {
      setImage('');
      setUploadError(false);
    }
  };

  const validateFileType = (fileType: string) => {
    if (acceptedFileTypes.length) {
      return acceptedFileTypes.includes(fileType);
    } else {
      return true;
    }
  };

  return (
    <div className={classes.urlUploader}>
      <FormControl className={classes.urlTextField} error={uploadError}>
        <InputLabel
          classes={{
            error: classes.fileUrlInputError,
            focused: classes.fileUrlInputFocused,
          }}
          htmlFor="url-input"
        >
          Paste an Image URL here
        </InputLabel>
        <Input
          classes={{
            root: classes.fileUrlPickerInputRoot,
            input: classes.fileUlrPickerInput,
          }}
          id="url-input"
          onKeyPress={preventInput}
          onPaste={handlePaste}
          onChange={handleChange}
          inputRef={inputRef}
        />
      </FormControl>
      {image.length ? (
        <div className={classes.imageContainer}>
          <div className={classes.fileImage}>
            <img src={image} alt="Uploaded content" />
          </div>
        </div>
      ) : (
        <div className={classes.urlUploaderInfoBox}>
          <span className={classes.correctUrlText}>
            If the URL is correct the image will display here.
          </span>
          <span className={classes.confirmLicenseText}>
            Remember, only use images that you have confirmed that you have the
            license to use
          </span>
        </div>
      )}
    </div>
  );
};

export default withStyles(styles)(UrlUploader);
