import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

export default {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "responseToServiceCall"
                field: "responseToServiceCall"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'responseToCallAggregation',
      dataKey: 'responseToServiceCall',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            select: [""]
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'totalRecords',
      dataKey: 'totalResults',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stop Response To Call',
  filterTextType: 'Response To Call by Type',
  filterType: 'responseToServiceCall',
  filterTerms: {
    'Response to Call': ['true'],
    'Not Response to Call': ['false'],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataMap = {
      'Response to Call': 0,
      'Not Response to Call': 0,
      'Total People': data?.totalRecords,
      ...data?.responseToCallAggregation?.reduce(
        (acc: { [key: string]: number }, b) => {
          if (b.key_as_string === 'true') {
            acc['Response to Call'] = b.doc_count;
          }
          if (b.key_as_string === 'false') {
            acc['Not Response to Call'] = b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = [
      {
        typeOfAction: 'Response to Call',
        count: dataMap['Response to Call'],
      },
      {
        typeOfAction: 'Not Response to Call',
        count: dataMap['Not Response to Call'],
      },
      {
        typeOfAction: 'Total People',
        count: dataMap['Total People'],
      },
    ];

    // Create axes
    const categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = 'typeOfAction';
    categoryAxis.title.text = 'Stop Type';

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Number of People';

    // Create series
    const series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = 'count';
    series.dataFields.categoryX = 'typeOfAction';
    series.name = 'Number of People';
    series.columns.template.tooltipText =
      'Series: {name}\nCategory: {categoryX}\nValue: {valueY}';
    series.columns.template.fill = am4core.color('#104547');

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

interface Data {
  demographics: DemographicsType;
  totalRecords: number;
  responseToCallAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
    responseToServiceCall: {
      buckets: {
        doc_count: number;
        key: number;
        key_as_string: string;
      }[];
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
    };
  }[];
}
