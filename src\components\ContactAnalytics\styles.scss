.chart-section-export-button {
  > ul {
    height: 100%;
    width: 100%;
    top: 0 !important;
    left: 0 !important;
    position: absolute;

    a[aria-label='Export [Press ENTER or use arrow keys to navigate]'] {
      color: transparent;
    }

    > li {
      height: 100%;
      width: 100% !important;
      background-color: transparent !important;
      top: -3px !important;
      left: -3px;
    }

    > li:hover,
    > li.active {
      background-color: transparent;
    }
  }
}

.contact-analytics {
  /* stylelint-disable no-descending-specificity */
  --primary-color: #0c53b0;
  --primary-color-fade: #0c53b099;
  --delete-color: #8d0807;
  --border-color: #c4cfda;
  --inactive-border-color: #d5dfe9;
  --inactive-color: #fafafa;
  --inactive-text-color: #5c6269;
  --inactive-add-color: #73737382;
  --font-primary: #2a323c;
  --icon-color: #555f7c;

  padding: 10px 0;
  min-width: 1005px;
  min-height: 500px;

  * {
    text-transform: none;
  }

  .add-report-type-button {
    border: solid 2px var(--primary-color);
    background: var(--primary-color);
    color: white;
    margin: 0 0 0 15px;
    height: 36px;
    padding: 6px 10px;
    font-weight: 400;

    svg {
      fill: white;
      margin-right: 4px;
    }
  }

  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 21.5px;
    height: 41px;

    .filter-bar-left {
      display: flex;
    }
    .date-range {
      border: solid 1px var(--border-color);
      border-radius: 4px;
      position: relative;
      width: 222px;
    }
    .settings-dialog-button {
      border: solid 2px var(--primary-color);
      color: var(--primary-color);
      margin-left: 15px;
      height: 36px;
      font-weight: 400;

      svg {
        height: 20px;
        width: 20px;
        margin-right: 4px;
        margin-bottom: 2px;
        fill: var(--primary-color);
      }
    }
    .date-range-input {
      border: none;
      color: black;
      padding: 9px;
      cursor: pointer;
      font-size: 14px;
      width: 190px;
      text-align: center;
      font-weight: 400;

      &:focus-visible {
        outline: none;
      }
    }

    .date-range-icon {
      position: absolute;
      right: 5px;
      top: 8px;
      height: 18px;
      pointer-events: none;
      color: black;
      margin: 0 5px;
    }

    .reorder-button,
    .export-all-button {
      border: solid 2px var(--primary-color);
      color: var(--primary-color);
      margin-left: 15px;
      height: 36px;
      font-weight: 400;
    }
    .export-all-button {
      svg {
        margin-right: 3px;
        margin-bottom: 1px;
      }
    }
    .export-all-button-disabled {
      color: lightgrey;
      border-color: lightgrey;

      svg path {
        fill: lightgrey;
      }
    }
  }
  .contact-analytics-report-tab-container {
    padding: 24px 43px 0;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: var(--inactive-color);
    border-top: solid 1px var(--inactive-border-color);
    border-bottom: solid 1px var(--inactive-border-color);

    .contact-analytics-report-tab {
      transition:
        min-width 400ms,
        padding 400ms;
      font-weight: 500;
      opacity: 0.5;
      font-size: 0.875rem;
      text-align: center;
      margin-right: -2px;
      border-top: 1px solid var(--inactive-border-color);
      border-left: 1px solid var(--inactive-border-color);
      border-right: 1px solid var(--inactive-border-color);
      color: var(--inactive-text-color);
      transform: translateY(2px);
      display: inline-block;
      width: 145px;
      height: 47px;
      line-height: 43px;
      border-radius: 2px 2px 0 0;
      padding: 0 10px;
      cursor: pointer;
      user-select: none;
      overflow-x: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow-y: hidden;

      .contact-analytics-report-tab-input {
        color: black;
      }

      &[data-active='true'] {
        border-bottom: solid 1px white;
        z-index: 10;
        opacity: 1;
        font-weight: 500;
        height: 47px;
        min-width: 145px;
        border-top: 1px solid var(--border-color);
        border-left: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);
        color: black;
        background-color: white;
      }

      .contact-analytics-report-tab-delete {
        font-size: 13px;
        margin: 15px 0;
        display: none;
        position: absolute;
        right: 7px;
        line-height: 47px;
        background-color: var(--inactive-add-color);
      }

      &:hover {
        padding-right: 24px;
        border-bottom: solid 2px white;

        .contact-analytics-report-tab-delete {
          display: inline-flex;
          border-radius: 50%;
          color: white;

          &:hover {
            background-color: var(--primary-color);
          }
        }
      }
    }
    &.loading {
      .contact-analytics-report-tab {
        pointer-events: none;
        cursor: initial;
        color: var(--primary-color-fade);
        border-top: 2px solid var(--primary-color-fade);
        border-left: 2px solid var(--primary-color-fade);
        border-right: 2px solid var(--primary-color-fade);
      }
    }
    &.editing {
      .contact-analytics-report-tab {
        &:hover {
          transition: none;
          padding-right: 10px;
        }
      }
    }
    input {
      width: 100%;
      height: 25px;
      line-height: 25px;
    }

    .contact-analytics-report-tab-add {
      display: inline-flex;
      border-radius: 50%;
      line-height: 16px;
      margin-left: 12px;
      width: 16px;
      height: 16px;
      justify-content: center;
      align-items: center;
      font-weight: 900;
      font-size: 12px;
      transform: translateY(2px);
      border: solid 2px var(--inactive-add-color);
      color: var(--inactive-add-color);
      cursor: pointer;
      user-select: none;

      &:hover {
        border: solid 2px var(--primary-color);
        color: var(--primary-color);
      }
      &:active {
        background-color: var(--primary-color);
        color: white;
      }
    }
  }

  .contact-analytics-add-report-prompt {
    height: calc(100% - 255px);
    z-index: 9999999;
    color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    margin-top: -10px;
    width: calc(100% - 15px);
    text-align: center;
    max-width: 1890px;

    .contact-analytics-add-report-prompt-button {
      color: var(--primary-color);
      border: solid 2px var(--primary-color);

      svg {
        margin-right: 5px;
        margin-bottom: 2px;
      }
    }
  }

  .contact-analytics-charts-loading-overlay {
    height: calc(100% - 255px);
    backdrop-filter: blur(7px);
    z-index: 99999999;
    color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    margin-top: -90px;
    width: calc(100% - 15px);
    text-align: center;
    max-width: 1900px;
  }
  .chart-section {
    border: solid 1px var(--inactive-border-color);
    padding: 20px;
    height: 400px;
    resize: vertical;
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    width: 100%;
    margin-bottom: 20px;

    .chart-section-filters-container {
      display: flex;
      justify-content: center;
      align-items: center;

      div[role='radiogroup'] {
        flex-direction: initial;
        span {
          font-size: 14px;
        }
      }
      div[role='button'] {
        padding: 7px 40px 7px 20px;
        font-size: 13px;
      }
    }

    .chart-section-select-chart-type {
      margin-top: 6px;

      ::before,
      ::after {
        border-bottom: none;
      }
    }
    .chart-section-buttons-container {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 10000000;
    }
    .chart-section-export-button {
      margin-left: 30px;
      position: relative;
    }

    .chart-section-remove-button {
      opacity: 0.7;
      margin-left: 20px;
      cursor: pointer;
      path {
        fill: var(--icon-color);
      }
    }

    .chart-section-controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
  }
  .chart-area-container {
    margin: 20px 21.5px;
    padding: 17px 0 0;
    position: relative;
  }
  .chart-area {
    display: flex;
    height: 100%;
  }
  .chart-section-chart {
    display: inline-block;
    font-size: 11px;
    width: 100%;
    height: calc(100% - 41px);
    padding: 0 0 20px 0;
  }
  .has-demographics {
    width: calc(100% - 320px);
    padding-bottom: 40px;
  }
  .chart-section-demographics-divider {
    height: 442px;
    border-right: 1px solid var(--inactive-border-color);
    margin: 23px 13px 23px 23px;
  }
  .chart-section-demographics {
    display: inline-block;
    padding: 10px 10px 10px 20px;
    width: 320px;
    height: calc(100% - 61px);
    background-color: white;
  }
}

.contact-analytics-report-tab-tooltip {
  font-size: 16px;
}
