import { prepareSearchQueryForEngine } from './query';

describe('prepareSearchQueryForEngine', () => {
  test('remove offset and limit from query', () => {
    const searchQuery = {
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_string',
            field: 'text-document.text',
            value: 'hello',
          },
        ],
      },
      select: ['veritone-job', 'veritone-file'],
      index: ['mine'],
      limit: 10,
      offset: 0,
    };
    const got = prepareSearchQueryForEngine(searchQuery);
    const want = {
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_string',
            field: 'text-document.text',
            value: 'hello',
          },
        ],
      },
      select: ['veritone-job', 'veritone-file'],
      index: ['mine'],
    };
    expect(got).toEqual(want);
  });

  test('return same query for the given query without offset and limit', () => {
    const searchQuery = {
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_string',
            field: 'text-document.text',
            value: 'hello',
          },
        ],
      },
      select: ['veritone-job', 'veritone-file'],
      index: ['mine'],
    };
    const got = prepareSearchQueryForEngine(searchQuery);
    expect(got).toEqual(searchQuery);
  });
});
