import { callGQL } from '../../../util/callGraphql';
import { ForbiddenError, UnauthorizedError } from '../errors';
import { Context } from '../../types';

interface validateJWTTokenResponse {
  verifyJWT: {
    jwtToken: string;
    payload: {
      contentOrganizationId: string;
    };
  };
}

const validateJWTTokenQuery = (token: string) => `
  mutation verifyJWT{
    verifyJWT(jwtToken: "${token}")
    {
      jwtToken
      payload
    }
  }
`;

const validateJwtToken = async (context: Context) => {
  const { req, log, data } = context;

  const bearerHeader = req.get('Authorization');
  if (bearerHeader) {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];
    if (bearerToken) {
      try {
        const query = validateJWTTokenQuery(
          bearerToken.replace(/[Bb]earer /, '')
        );
        const headers = { Authorization: req.headers.authorization };
        const response = await callGQL<validateJWTTokenResponse>(
          context,
          headers,
          query
        );

        if (response?.verifyJWT?.jwtToken) {
          data.authorizedOrgId =
            response.verifyJWT.payload.contentOrganizationId;
          return context;
        }
      } catch (err) {
        log.error('API JWT Token Validation failed');
        throw new ForbiddenError(err);
      }
    } else {
      log.error('API JWT Token Validation failed (b)');
      throw new UnauthorizedError('API JWT Token Validation failed (b)');
    }
  } else {
    log.error('API JWT Token Validation failed (c)');
    throw new UnauthorizedError('API JWT Token Validation failed (c)');
  }
};

export default validateJwtToken;
