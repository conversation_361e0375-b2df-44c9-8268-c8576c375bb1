import supertest from 'supertest';
import { request } from 'graphql-request';
import axios from 'axios';
import fs from 'fs';
import expressApp from '../../setupExpress';
import { adminPermissionMasksMock, templateRecordMock } from '../fixtures';

jest.mock('graphql-request', () => ({ request: jest.fn() }));
jest.mock('axios', () => ({ post: jest.fn(), get: jest.fn(), patch: jest.fn(), put: jest.fn() }));
jest.mock('../../../powerbi/queries', () => () => ({
  insertTemplate: () => Promise.resolve(templateRecordMock),
}));
jest.mock('uuid', () => ({ v4: () => '1234565678abcdef' }));

describe('PUT /api/v1/powerbi/template', () => {

  (request as jest.Mock).mockImplementation(({ document }) => {
    if (document.includes('validateToken')) {
      return Promise.resolve({ validateToken: { token: 'valid token' } });
    }
    if (document.includes('me')) {
      return Promise.resolve({ me: { organizationId: 123 } });
    }
    return Promise.resolve()
  });

  (axios.post as jest.Mock).mockImplementation((url) => {
    if (url.includes('oauth')) {
      return Promise.resolve({
        data: {
          access_token: 'validServicePricipalToken',
          expires_in: 3600
        }
      });
    }

    return Promise.resolve()
  });

  (axios.get as jest.Mock).mockImplementation((url) => {
    if (url.includes('current-user')) {
      return Promise.resolve({
        data: {
          permissionMasks: adminPermissionMasksMock
        }
      });
    }
  });
  it('returns ok', async () => {

    await supertest(expressApp)
      .put('/api/v1/powerbi/template/PbixFixture.pbix')
      .set('Authorization', 'Bearer valid-token')
      .send(fs.readFileSync(__dirname + '/../PbixFixture.pbix'))
      .expect(200)
      .then(res => {
        expect(res.headers['content-type']).toContain('application/json; charset=utf-8');
        expect(res.body).toMatchObject({
          message: 'OK',
        });
      })
  }, 30000)
})

