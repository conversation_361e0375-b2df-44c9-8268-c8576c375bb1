import { ReactElement } from 'react';

import { CssBaseline } from '@mui/material';
import ScopedCssBaseline from '@mui/material/ScopedCssBaseline';

interface Props {
  scoped: boolean;
  children: ReactElement;
}

export function BaselineStyles({ scoped, children }: Props) {
  // scopedStyles must be explicitly set to false to continue using CssBaseline instead of ScopedCssBaseline
  return scoped === false ? (
    <>
      <CssBaseline />
      {children}
    </>
  ) : (
    <ScopedCssBaseline>{children}</ScopedCssBaseline>
  );
}
