import { ExtendedError } from '@utils';
import {
  fetchGraphQLApiThrowError,
  getApiConnectionParameters,
} from '../apiHelper';
import fetchGraphQLApi from '../fetchGraphQLApi';
import { FetchFoldersResponse } from 'src/model';
import { TDO } from '../../state/modules/universal/TDO';
import { Job } from '../../state/modules/processingJobs/models';
import { isEmpty } from 'lodash';
import fetch from '../fetchRetry';

const batchQuerySize = 10;

export class GQLApi {
  constructor(
    private endpoint: string,
    private token: string,
    private veritoneAppId: string
  ) {}

  static newGQLApi(state: any) {
    const { endpoint, token, veritoneAppId } =
      getApiConnectionParameters(state);
    // The ! is safe because when the app booting at the first time, token is null
    // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
    // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
    // and received the payload from fetchUser response.
    // This non null assertion just allow running into that case without change so much in code
    return new GQLApi(endpoint, token!, veritoneAppId);
  }

  static async putFileToSignedUri({
    signedUri,
    data,
    contentType = 'application/json',
    veritoneAppId,
  }: {
    signedUri: string;
    data: any;
    contentType: string;
    veritoneAppId?: string;
  }) {
    const headers: HeadersInit = {
      'Content-Type': contentType,
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }
    const resp = await fetch(signedUri, {
      method: 'PUT',
      mode: 'cors',
      headers,
      body: JSON.stringify(data),
    });
    return resp;
  }

  async upload({
    data,
    contentType = 'application/json',
  }: {
    data: any;
    contentType?: string;
  }) {
    const signedWritableUrl = await this.getSignedWritableUri({
      type: 'asset',
    });
    await GQLApi.putFileToSignedUri({
      signedUri: signedWritableUrl.url,
      data,
      contentType: contentType,
      veritoneAppId: this.veritoneAppId,
    });
    return signedWritableUrl;
  }

  async getSignedWritableUri({
    type,
    path,
    key,
    expiresInSeconds = 86400,
  }: {
    type?: string;
    path?: string;
    key?: string;
    expiresInSeconds?: number;
  }) {
    const query = `
      query ($key: String, $type: String, $path: String, $expiresInSeconds: Int) {
        getSignedWritableUrl(
          key: $key,
          type: $type,
          path: $path,
          expiresInSeconds: $expiresInSeconds,
        ) {
          url
          getUrl
          unsignedUrl
        }
      }`;
    const variables = {
      key,
      path,
      type,
      expiresInSeconds,
    };

    const response = await fetchGraphQLApiThrowError<{
      getSignedWritableUrl: {
        url: string;
        getUrl: string;
        unsignedUrl: string;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    if (!response?.data?.getSignedWritableUrl) {
      throw new Error('getSignedWritableUrl failed - got empty url');
    }
    return response.data.getSignedWritableUrl;
  }

  async getSDOSchemaId(dataRegistryId: string) {
    const query = `query fetchSchemaId {
      dataRegistry(id: "${dataRegistryId}") {
        id
        name
        publishedSchema {
          id
        }
      }
    }`;

    const response = await fetchGraphQLApi<{
      dataRegistry: {
        id: string;
        name: string;
        publishedSchema: {
          id: string;
        };
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return response;
  }

  async createAsset({
    tdoId,
    unsignedUrl,
    engineId,
    taskId,
    description,
    details,
    assetType,
    contentType,
  }: {
    tdoId: string;
    unsignedUrl: string;
    engineId: string;
    taskId: string;
    description: string;
    details: Record<string, any>;
    assetType: string;
    contentType: string;
  }) {
    const variables = {
      assetType,
      contentType,
      description,
      details,
      sourceData: {
        engineId,
        taskId,
      },
      tdoId,
      uri: unsignedUrl,
    };

    const query = `
      mutation ($tdoId: ID!, $contentType: String!, $assetType: String!, $details: JSONData!,
        $description: String, $sourceData: SetAssetSourceData, $uri: String!) {
      createAsset(input: {
        containerId: $tdoId
        contentType: $contentType
        description: $description
        assetType: $assetType
        details: $details
        sourceData: $sourceData
        uri: $uri
        isUserEdited: true
      }) {
        id
        uri
        type
      }
      }`;

    const response = await fetchGraphQLApiThrowError<{
      createAsset: {
        id: string;
        uri: string;
        type: string;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.createAsset;
  }

  async createTDOWithAsset({
    startDateTime,
    stopDateTime,
    parentFolderId,
    name,
    addToIndex = false,
    contentType = 'application/json',
    assetType,
    uri,
  }: {
    startDateTime: string;
    stopDateTime: string;
    parentFolderId: string;
    name: string;
    addToIndex: boolean;
    contentType: string;
    assetType: string;
    uri: string;
  }) {
    const query = `mutation createTDOWithAsset {
    createTDOWithAsset (input: {
        startDateTime: "${startDateTime}",
        stopDateTime:"${stopDateTime}",
        parentFolderId: "${parentFolderId}",
        name: "${name}",
        addToIndex: ${addToIndex}
        contentType: "${contentType}",
        assetType:  "${assetType}",
        uri: "${uri}"
    }) {
      id
      assets{
        records{
          id
          name
          contentType
          assetType
          uri
        }
      }
    }
  }`;
    const variables = {};
    const response = await fetchGraphQLApiThrowError<{
      createTDOWithAsset: {
        id: string;
        assets: {
          records: {
            id: string;
            name: string;
            contentType: string;
            assetType: string;
            uri: string;
          }[];
        };
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    if (!response?.data?.createTDOWithAsset) {
      throw new Error('createTDOWithAsset failed - created empty asset');
    }
    return response.data.createTDOWithAsset;
  }

  // dataArray is an array of objects in shape of {id, data}. If the id
  // is empty string, a new SDO will be created with data. If id is an sdo id, then
  // the sdo will updated with the data.
  async createStructuredData(
    schemaId: string,
    dataArray: Array<{ id?: string; data: any }>
  ) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < dataArray.length; i += batchQuerySize) {
      const batch = dataArray.slice(i, i + batchQuerySize);
      const inputStr: Array<string> = [];
      const updateQuery: Array<string> = [];
      const variables: {
        [key: string]: { id: string; schemaId: string; data: any };
      } = {};
      batch.forEach((item, index) => {
        inputStr.push(`$input${index}: CreateStructuredData!`);
        updateQuery.push(
          `createStructuredData_${index}:createStructuredData(input: $input${index}) {id}`
        );
        variables[`input${index}`] = {
          id: item.id ? item.id : '',
          schemaId,
          data: JSON.parse(JSON.stringify(item.data)),
        };
      });
      const query = `mutation createStructuredData(${inputStr.join(',')}){
        ${updateQuery.join('\n  ')}
      }`;

      const response = await fetchGraphQLApi<Record<string, { id: string }>>({
        endpoint: this.endpoint,
        query,
        variables,
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });
      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res && res.id));
      if (response.errors && response.errors.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async createJobByTask({
    targetId,
    tasks,
    routes = [],
    clusterId = '',
  }: {
    targetId: string;
    tasks: Array<object>;
    routes?: Array<object>;
    clusterId?: string;
  }) {
    const variables = {
      input: {
        targetId,
        tasks,
        routes,
        clusterId,
      },
    };
    const id = await this.createJob(variables);
    return id;
  }

  async createJob(variables: { [key: string]: unknown }) {
    const query = `
      mutation createJob($input: CreateJob){
        createJob(input: $input) {
          id
        }
      }`;

    const response = await fetchGraphQLApiThrowError<{
      createJob: { id: string };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    if (!response?.data?.createJob.id) {
      throw new Error('createJob failed - created empty job');
    }
    return response.data.createJob.id;
  }

  async getJob(jobId: string) {
    const query = `
      query getJob {
        job(id:"${jobId}") {
          id
          status
          jobConfig
          tasks {
            records {
              engineId
              payload
              executionPreferences{
                priority
                parentCompleteBeforeStarting
              }
              ioFolders{
                referenceId
                mode
                type
              }
            }
          }
        }
      }`;
    const response = await fetchGraphQLApiThrowError<{
      job: {
        id: string;
        status: string;
        jobConfig: {
          routes: {
            childIoFolderReferenceId: string;
            parentIoFolderReferenceId: string;
          }[];
        };
        tasks: {
          records: {
            engineId: string;
            payload: unknown;
            executionPreferences: {
              priority: number;
              parentCompleteBeforeStarting: boolean;
            };
            ioFolders: {
              referenceId: string;
              mode: string;
              type: string;
            }[];
          }[];
        };
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data?.job;
  }

  async getTDOs(tdoIds: string[]) {
    const validData = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];

      batch.forEach((tdoId, index) => {
        updateQuery.push(
          `temporalDataObject_${tdoId}_${index}:temporalDataObject(id: "${tdoId}") {
            id
            name
            createdDateTime
            startDateTime
            stopDateTime
            details
            folders {
              treeObjectId
            }
            assets(assetType: "vtn-standard"){
              records{
                id
                signedUri
                assetType
                contentType
              }
            }
            primaryAsset(assetType: "media") {
              id
              signedUri
              contentType
            }
            thumbnailUrl
            sourceImageUrl
            previewUrl
            streams {
              protocol
              uri
            }
          }`
        );
      });
      const query = `query { ${updateQuery.join('\n')} }`;

      const response = await fetchGraphQLApi<TDO[]>({
        endpoint: this.endpoint,
        query,
        variables: { tdoIds },
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors?.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async moveTDOs(
    tdos: { id: string; treeObjectId: string }[],
    newTreeObjectId: string
  ) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdos.length; i += batchQuerySize) {
      const batch = tdos.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];
      batch.forEach((tdo, index) => {
        updateQuery.push(
          `tdo_${tdo.id}_${index}:moveTemporalDataObject(input: {
              tdoId: "${tdo.id}",
              oldFolderId: "${tdo.treeObjectId}",
              newFolderId: "${newTreeObjectId}",
            }) {
              id
              folders {
                folderPath{id}
              },
            }`
        );
      });

      const query = `mutation { ${updateQuery.join('\n')} }`;
      const response = await fetchGraphQLApi<
        Record<
          string,
          {
            id: string;
            folders: {
              folderPath: { id: string }[];
            }[];
          }
        >
      >({
        endpoint: this.endpoint,
        query,
        variables: {},
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors?.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async deleteTDOs(tdoIds: string[]) {
    const validData: { id: string }[] = [];
    const validError: ExtendedError[] = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const deleteQuery: Array<string> = [];

      batch.forEach((tdoId, index) => {
        deleteQuery.push(
          `deleteTDO_${tdoId}_${index}:deleteTDO(id: "${tdoId}") {
            id
            message
          }`
        );
      });
      const query = `mutation { ${deleteQuery.join('\n')} }`;
      const response = await fetchGraphQLApi<
        Record<string, { id: string; message: string }>
      >({
        endpoint: this.endpoint,
        query,
        variables: {},
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(response?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (response.errors?.length) {
        validError.push(...response.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async deleteSDO(schemaId: string, id: string) {
    const query = `
    mutation deleteSdo {
      deleteStructuredData(input: {
        id: "${id}"
        schemaId: "${schemaId}"
      }) {
        id
      }
    }`;
    const response = await fetchGraphQLApi<{
      deleteStructureData: {
        id: string;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response;
  }

  async getSDOs(schemaId: string) {
    const query = `
    query getSdos {
      structuredDataObjects(schemaId: "${schemaId}") {
        count
        records {
          id
          data
          createdDateTime
        }
      }
    }`;

    const response = await fetchGraphQLApi<{
      structuredDataObjects: {
        count: number;
        records: {
          id: string;
          data: any;
          createdDateTime: string;
        }[];
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return response;
  }

  async getSDOCount(schemaId: string, filter?: object) {
    let total = 0;
    const pageSize = 10000;
    let offset = 0;
    let count = 0;
    let errors = undefined as any[] | undefined;
    do {
      const response = await this.getSDOCountByOffset({
        schemaId,
        offset,
        limit: pageSize,
        filter,
      });

      if (response.errors) {
        if (!errors) {
          errors = [];
        }
        errors.push(...response.errors);
        break;
      }
      count = response?.data?.structuredDataObjects?.count || 0;
      total += count;
      offset += pageSize;
    } while (count === pageSize);
    const result = isEmpty(errors) ? { count: total } : { errors };
    return result;
  }

  async getSDOCountByOffset({
    schemaId,
    offset,
    limit,
    filter,
  }: {
    schemaId: string;
    offset: number;
    limit: number;
    filter?: object;
  }) {
    const query = `
    query getSdoCount($schemaId: ID!, $limit: Int, $offset: Int, $filter: JSONData) {
      structuredDataObjects(
        schemaId: $schemaId,
        limit: $limit,
        offset: $offset,
        orderBy: [{
          field: createdDateTime,
          direction: desc
        }],
        filter: $filter,
        owned: true) {
        count
      }
    }`;

    const variables = {
      schemaId,
      limit,
      offset,
      filter,
    };

    const response = await fetchGraphQLApi<{
      structuredDataObjects: {
        count: number;
      } | null;
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return response;
  }

  async getChildFolders({
    folderId,
    limit,
    offset,
  }: {
    folderId: string;
    limit: number;
    offset: number;
  }) {
    const query = `query folder($id:ID!, $offset: Int, $limit: Int){
      folder(id: $id) {
        treeObjectId
        name
        childFolders(offset: $offset, limit: $limit){
          records {
            treeObjectId,
            name
          }
        }
      }
    }`;

    const variables = {
      id: folderId,
      limit: limit,
      offset: offset,
    };

    const response = await fetchGraphQLApiThrowError<ResponseFolder>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response?.data;
  }

  async fetchFolderFromRoot({
    limit,
    offset,
    variablesFolder,
  }: {
    limit: number;
    offset: number;
    variablesFolder: object;
  }) {
    const query = ` query folder($offset: Int, $limit: Int) {
      rootFolders(type: cms) {
        id
        name
        ownerId
        treeObjectId
        description
        childFolders(offset: $offset, limit: $limit){
          count
          records {
            id
            name
            treeObjectId
            description
            parent{
              id
            }
            childFolders{
              count
            }
            folderPath{
              id
              name
            }
          }
        }
        parent{
          id
        }
        folderPath{
          id
           name
         }
      }
    }`;

    const variables = {
      limit: limit,
      offset: offset,
      ...variablesFolder,
    };

    const response = await fetchGraphQLApiThrowError<{
      rootFolders: FetchFoldersResponse[];
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    if (!response?.data) {
      throw new Error('fetchFolderFromRoot failed - got empty folder');
    }
    return response.data;
  }

  async getProcessingJobs({
    fromDateTimeIso,
    toDateTimeIso,
    status,
    pageSize = 1000,
  }: {
    fromDateTimeIso: string;
    toDateTimeIso: string;
    status: string[];
    pageSize?: number;
  }) {
    const results = [];
    let offset = 0;
    let count = 0;
    let errors: any[] | null = null;
    do {
      const response = await this.getProcessingJobsByOffset({
        fromDateTimeIso,
        toDateTimeIso,
        status,
        offset,
        limit: pageSize,
      });
      if (response.errors) {
        if (!errors) {
          errors = [];
        }
        errors.push(...response.errors);
      }
      const records = response.data?.jobs?.records ?? [];
      offset += pageSize;
      count = records.length;
      if (count > 0) {
        results.push(...records);
      }
    } while (count === pageSize);
    return {
      jobs: results,
      errors: errors,
    };
  }

  async getProcessingJobsByOffset({
    fromDateTimeIso,
    toDateTimeIso,
    status,
    offset,
    limit,
  }: {
    fromDateTimeIso: string;
    toDateTimeIso: string;
    status: string[];
    offset: number;
    limit: number;
  }) {
    const query = `query processing($offset: Int, $limit: Int, $status: [JobStatusFilter!]){
        jobs (
            offset: $offset
            limit: $limit
            orderBy: [{
              field:modifiedDateTime
              direction: desc
            }]
            dateTimeFilter: {
              field: modifiedDateTime
              fromDateTime: "${fromDateTimeIso}"
              toDateTime: "${toDateTimeIso}"
            }
            status: $status
        ) {
            records {
              id
              status
              target {
                  id
                  name
                  details
              }
              createdDateTime
              modifiedDateTime
              tasks {
                  records {
                    id
                    status
                    engine {
                      id
                      name
                    }
                    failureMessage
                    completedDateTime
                    taskOutput
                  }
              }
            }
        }
      }`;

    const variables = {
      limit,
      offset,
      status: status.length ? status : null,
    };
    const response = await fetchGraphQLApi<{
      jobs: {
        records: Job[];
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response;
  }

  async getJobCountByOffset({
    fromDateTimeIso,
    toDateTimeIso,
    status,
    offset,
    limit,
  }: {
    fromDateTimeIso: string;
    toDateTimeIso: string;
    status: string[];
    offset: number;
    limit: number;
  }) {
    const query = `query processing($offset: Int, $limit: Int, $status: [JobStatusFilter!]){
      jobs (
          offset: $offset
          limit: $limit
          dateTimeFilter: {
          field: modifiedDateTime
          fromDateTime: "${fromDateTimeIso}"
          toDateTime: "${toDateTimeIso}"
          }
          status: $status
      ) {
        count
      }
    }
    `;

    const variables = {
      limit,
      offset,
      status: status.length ? status : null,
    };

    const response = await fetchGraphQLApi<{
      jobs: {
        count: number;
      };
    }>({
      endpoint: this.endpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return response;
  }

  async getJobCount({
    fromDateTimeIso,
    toDateTimeIso,
    status,
  }: {
    fromDateTimeIso: string;
    toDateTimeIso: string;
    status: string[];
  }) {
    let total = 0;
    const pageSize = 1000;
    let offset = 0;
    let count = 0;
    let errors: any[] | null = null;
    do {
      const response = await this.getJobCountByOffset({
        fromDateTimeIso,
        toDateTimeIso,
        status,
        offset,
        limit: pageSize,
      });

      if (response.errors) {
        if (!errors) {
          errors = [];
        }
        errors.push(...response.errors);
      }
      count = response?.data?.jobs?.count || 0;
      total += count;
      offset += pageSize;
    } while (count === pageSize);
    return {
      count: total,
      errors: errors,
    };
  }
}

interface ResponseFolder {
  folder: {
    treeObjectId: string;
    name: string;
    childFolders: {
      records: { treeObjectId: string; name: string }[];
    };
  };
}
