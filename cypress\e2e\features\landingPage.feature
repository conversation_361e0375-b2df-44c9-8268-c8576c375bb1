Feature: Landing Page

  @e2e @landing-page
  Scenario: Landing Page verification
    Given The user is in Landing Page
    Then The user verifies the search bar and more actions button are visible
    And The user verifies the page title is "<PERSON><PERSON>UMINAT<PERSON>"
    And The user verifies "My Cases" is the selected folder in the breadcrumbs

  @e2e @landing-page @top-bar
  Scenario: Verify cards under app bar
    Given The user is in Landing Page
    Then The user can see the folder icon in the top bar
    And The user can see the breadcrumbs in the top bar
    And The user can see the upload button in the top bar
    And The user can see the filters button in the top bar
