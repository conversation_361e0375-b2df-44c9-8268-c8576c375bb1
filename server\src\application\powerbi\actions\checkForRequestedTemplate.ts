import { UnknownTemplateError } from '../errors';
import { Context } from '../../types';

const checkForRequestedTemplate = async (context: Context) => {
  const { req, log, queries, data } = context;

  const templateRecord = await queries.getTemplateRecord(
    req.body.templatePbixFileName
  );

  if (!templateRecord) {
    log.error(
      `checkForRequestedTemplate failed: ${req.body.templatePbixFileName} is unknown.`
    );
    throw new UnknownTemplateError(req.body.templatePbixFileName);
  }

  data.templateRecord = templateRecord;

  return context;
};

export default checkForRequestedTemplate;
