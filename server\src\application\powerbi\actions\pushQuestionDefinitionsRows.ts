import { stringify } from 'csv-stringify/sync';
import { DatabaseError } from '../errors';
import { Context } from '../../types';

interface QuestionDefintion {
  orgId: number;
  contactId: string;
  component: string;
  title: string;
  options: string[];
  required: boolean;
  disabled: boolean;
  isMultiSelect: boolean;
  disabledDate: string;
}

const pushQuestionDefinitionsRows = async (context: Context) => {
  const { log, queries, req, data } = context;

  try {
    const rowsWithCSVStringOptions = req.body.data.map(
      (row: QuestionDefintion) => ({
        ...row,
        options: row?.options
          ? stringify([row.options]).replace('\n', '')
          : null,
        orgId: data.authorizedOrgId,
      })
    );

    await queries.insertQuestionDefinitionsRows(rowsWithCSVStringOptions);

    return context;
  } catch (e) {
    log.error('pushQuestionDefinitionsRows failed', e);
    throw new DatabaseError(e);
  }
};

export default pushQuestionDefinitionsRows;
