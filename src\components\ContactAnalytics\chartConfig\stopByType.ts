import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    name: "reasonsForStop"
                    field: "reasonsForStop"
                    operator: "term"
                    limit: 10000
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'reasonsForStopAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stops By Type',
  filterTextType: 'Type of Stop ',
  filterType: 'reasonsForStop',
  filterTerms: {
    Traffic: ['Traffic Violation'],
    Criminal: [
      'Reasonable suspicion that the person was engaged in criminal activity',
      'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
      'Knowledge of outstanding arrest warrant/wanted person',
      'Investigation to determine whether the person was truant',
    ],
    Other: [
      'Consensual Encounter resulting in a search',
      'Possible conduct warranting discipline under Education Code sections 48900, et al',
      'Determine whether the student violated school policy',
    ],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = data.reasonsForStopAggregation.map((dateAgg) => {
      return {
        date: dateAgg.key_as_string,
        Traffic: 0,
        Criminal: 0,
        Other: 0,
        ...dateAgg.reasonsForStop.buckets.reduce(
          (acc: { [key: string]: number }, b) => {
            if ('Traffic Violation' === b.key) {
              acc['Traffic'] = acc['Traffic']
                ? acc['Traffic'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Reasonable suspicion that the person was engaged in criminal activity',
                'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
                'Knowledge of outstanding arrest warrant/wanted person',
                'Investigation to determine whether the person was truant',
              ].includes(b.key)
            ) {
              acc['Criminal'] = acc['Criminal']
                ? acc['Criminal'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Consensual Encounter resulting in a search',
                'Possible conduct warranting discipline under Education Code sections 48900, et al',
                'Determine whether the student violated school policy',
              ].includes(b.key)
            ) {
              acc['Other'] = acc['Other']
                ? acc['Other'] + b.doc_count
                : b.doc_count;
            }
            return acc;
          },
          {} // this reduce need to return an object to update default values
        ),
      };
    });

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    ['Traffic', 'Criminal', 'Other'].forEach((pr) => {
      // Create series
      const series = chart.series.push(new am4charts.LineSeries());
      series.strokeWidth = config.lineWidth;
      series.dataFields.valueY = pr;
      series.dataFields.dateX = 'date';
      series.name = pr;
      series.tooltipText = '{name}: [bold]{valueY}[/]';
    });

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  reasonsForStopAggregation: {
    key_as_string: string;
    key: number;
    doc_count: number;
    reasonsForStop: {
      buckets: { key: string; doc_count: number }[];
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
    };
  }[];
}
