import React, { useState, useReducer, useRef } from 'react';
import {
  <PERSON>vider,
  Box,
  Button,
  Dialog,
  IconButton,
  FormGroup,
  FormControlLabel,
  Paper,
  TextField,
  Tabs,
  Tab,
  Typography,
  Slide,
  Radio,
  RadioGroup,
  Switch,
} from '@mui/material';
import { DateTime } from 'luxon';
import { Delete, Edit } from '@mui/icons-material';

import {
  BulkExportField,
  BulkExportOption,
  ExportTemplateData,
  BulkExportCustomizedNames,
} from 'src/model';
import collapseIcon from '../../../resources/images/to_close.svg';
import { useStyles } from './useStyles';
import { AIWareThemeProvider } from '../../aiware/theme';
import ExportTemplateNamingDialog from './ExportTemplateNamingDialog';
import ExportTemplateDropdown from './ExportTemplateDropdown';
import { GroupedCheckbox, ExportOptionDetail } from './GroupedCheckbox';
import DeleteTemplateDialog from 'components/Dialogs/deleteTemplateDialog';

const exportTabs = {
  Fields: { index: 1, value: 'fields' },
  Options: { index: 2, value: 'Options' },
};

const nameRegex = /^([a-zA-Z0-9_-]{3,})$/;

const initExportOptions: ExportOptionDetail = {
  exportOption: [
    {
      name: 'File ID',
      checked: true,
      disabled: true,
      field: 'tdoId',
      jsonField: 'hasTdoId',
      optionType: 'fileProperties',
      customizedNameFields: [{ label: 'File ID', field: 'tdoId' }],
    },
    {
      name: 'Filename',
      checked: false,
      disabled: false,
      field: 'filename',
      jsonField: 'hasFilename',
      optionType: 'fileProperties',
      customizedNameFields: [{ label: 'Filename', field: 'filename' }],
    },
    {
      name: 'Base Filename',
      checked: false,
      disabled: false,
      field: 'baseFilename',
      jsonField: 'hasBaseFilename',
      optionType: 'fileProperties',
      customizedNameFields: [{ label: 'Base Filename', field: 'baseFilename' }],
    },
    {
      name: 'Folder Name',
      checked: false,
      disabled: false,
      field: 'foldername',
      jsonField: 'hasFoldername',
      optionType: 'fileProperties',
      customizedNameFields: [{ label: 'Folder Name', field: 'foldername' }],
    },
    {
      name: 'Tags',
      checked: false,
      disabled: false,
      field: 'tag',
      jsonField: 'hasTag',
      optionType: 'fileProperties',
      customizedNameFields: [{ label: 'Tags', field: 'tag' }],
    },
    {
      name: 'Plain Text (TXT)',
      checked: true,
      disabled: false,
      field: 'plainText',
      jsonField: 'hasPlainText',
      optionType: 'transcription',
      customizedNameFields: [
        { label: 'Plain Text (TXT)', field: 'plainText' },
        { label: 'Plain Text Edited', field: 'plainTextEdited' },
        { label: 'Speaker Separation', field: 'speakerSeparation' },
        {
          label: 'Speaker Separation Edited',
          field: 'speakerSeparationEdited',
        },
      ],
    },
    {
      name: 'Time Text Markup Language (TTML)',
      checked: false,
      disabled: false,
      field: 'ttml',
      jsonField: 'hasTtml',
      optionType: 'transcription',
      customizedNameFields: [
        { label: 'Time Text Markup Language (TTML)', field: 'ttml' },
        {
          label: 'Time Text Markup Language (TTML) Edited',
          field: 'ttmlEdited',
        },
      ],
    },
    {
      name: 'Word Document',
      checked: false,
      disabled: false,
      field: 'word',
      jsonField: 'hasWord',
      optionType: 'transcription',
      customizedNameFields: [
        { label: 'Word Document', field: 'word' },
        { label: 'Word Document Edited', field: 'wordEdited' },
        {
          label: 'Speaker Separation Word Document',
          field: 'speakerSeparationWord',
        },
        {
          label: 'Speaker Separation Word Document Edited',
          field: 'speakerSeparationWordEdited',
        },
      ],
    },
    {
      name: 'AI Object Notation (JSON)',
      checked: false,
      disabled: false,
      field: 'objectNotation',
      jsonField: 'hasObjectNotation',
      optionType: 'transcription',
      customizedNameFields: [
        { label: 'AI Object Notation (JSON)', field: 'objectNotation' },
        {
          label: 'AI Object Notation (JSON) Edited',
          field: 'objectNotationEdited',
        },
        {
          label: 'Speaker Separation AI Object Notation (JSON)',
          field: 'speakerSeparationObjectNotation',
        },
        {
          label: 'Speaker Separation AI Object Notation (JSON) Edited',
          field: 'speakerSeparationObjectNotationEdited',
        },
      ],
    },
    {
      name: 'Bookmarks',
      checked: false,
      disabled: false,
      field: 'bookmark',
      jsonField: 'hasBookmark',
      optionType: 'transcription',
      customizedNameFields: [{ label: 'Bookmarks', field: 'bookmark' }],
    },
    {
      name: 'Transcription as Closed Caption',
      checked: false,
      disabled: false,
      field: 'closedCaption',
      jsonField: 'hasClosedCaption',
      optionType: 'transcription',
      customizedNameFields: [
        { label: 'Transcription as Closed Caption', field: 'closedCaption' },
      ],
    },
    {
      name: 'Translation (TXT)',
      checked: false,
      disabled: false,
      field: 'translation',
      jsonField: 'hasTranslation',
      optionType: 'otherCognition',
      customizedNameFields: [
        { label: 'Translation (TXT)', field: 'translation' },
      ],
    },
    {
      name: 'Translation (Native File)',
      checked: false,
      disabled: false,
      field: 'nativeTranslation',
      jsonField: 'hasNativeTranslation',
      optionType: 'otherCognition',
      customizedNameFields: [
        { label: 'Translation (Native File)', field: 'nativeTranslation' },
      ],
    },
    {
      name: 'Object Detection',
      checked: false,
      disabled: false,
      field: 'objectDetection',
      jsonField: 'hasObjectDetection',
      optionType: 'otherCognition',
      customizedNameFields: [
        { label: 'Object Detection', field: 'objectDetection' },
      ],
    },
    {
      name: 'Sentiment Analysis',
      checked: false,
      disabled: false,
      field: 'sentiment',
      jsonField: 'hasSentiment',
      optionType: 'otherCognition',
      customizedNameFields: [
        { label: 'Sentiment Analysis', field: 'sentiment' },
      ],
    },
    {
      name: 'Include Original Native Files',
      checked: false,
      disabled: false,
      field: 'native',
      jsonField: 'hasNative',
      optionType: 'optionsGroup',
      customizedNameFields: [
        { label: 'Include Original Native Files', field: 'native' },
      ],
    },
    {
      name: 'Engine Name (Language)',
      checked: false,
      disabled: false,
      field: 'engineName',
      jsonField: 'hasEngineName',
      optionType: 'optionsGroup',
      customizedNameFields: [
        { label: 'Engine Name (Language)', field: 'engineName' },
      ],
    },
    {
      name: 'File Path Delimiter',
      checked: false,
      disabled: false,
      field: 'filePathWindows',
      jsonField: 'filePathWindows',
      optionType: 'optionsOther',
    },
  ],
};

export function BulkExport({
  onClose,
  open,
  onConfirm,
  defaultExportFileName,
  exportTemplateData,
  onSaveTemplate,
  handleDeletingExportTemplate,
  isExportTemplateEditEnabled,
}: BulkExportProps) {
  const initExportName = defaultExportFileName
    ? defaultExportFileName
    : generateExportName();
  const templateSelectedDefault = { label: '', value: '' };
  const [currentTab, setCurrentTab] = useState(exportTabs.Fields.index);
  const [password, setPassword] = useState('');
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [deleteTemplateDialog, setDeleteTemplateDialog] = useState(false);
  const [exportFileName, setExportFileName] = useState(initExportName);
  const [exportOptionState, exportOptionDispatch] = useReducer(
    exportOptionReducer,
    initExportOptions
  );
  const [customizedNameState, setCustomizedNameState] =
    useState<BulkExportCustomizedNames>({});

  const [openTemplateNaming, setOpenTemplateNaming] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<{
    label: string;
    value: string;
  }>(templateSelectedDefault);
  const passwordRef = useRef<HTMLInputElement>();
  const classes = useStyles();
  const bulkExportOption = toBulkExportOption(exportOptionState);

  const handleTabChange = (_: React.ChangeEvent<EventTarget>, tab: number) => {
    setCurrentTab(tab);
  };

  const isValidName = (name: string) => nameRegex.test(name);

  const handleExportFileNameChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExportFileName(event.target.value);
  };

  const handleSavePassword = () => {
    if (passwordRef && passwordRef.current) {
      setPassword(passwordRef.current.value);
    }
    setPasswordDialogOpen(false);
  };

  const handlePasswordDialogOpen = () => {
    setPasswordDialogOpen(true);
  };

  const handlePasswordDialogClose = () => {
    setPasswordDialogOpen(false);
  };

  const handleDeleteTemplateDialogOpen = () => {
    setDeleteTemplateDialog(true);
  };

  const handleDeleteTemplateDialogClose = () => {
    setDeleteTemplateDialog(false);
  };

  const clearExportOptions = () => {
    setSelectedTemplate(templateSelectedDefault);
    setCustomizedNameState({});
    exportOptionState.exportOption.map((item) => {
      if (item.checked && item.field !== 'tdoId') {
        exportOptionDispatch({ type: 'field', field: item.field });
      }
    });
  };

  const handleDeletingExportTemplateClick = () => {
    handleDeletingExportTemplate?.(selectedTemplate.value);
    clearExportOptions();
    handleDeleteTemplateDialogClose();
  };

  const onDropdownChange = (
    _event: unknown,
    option: { label: string; value: string } | null
  ) => {
    if (!option || !exportTemplateData) {
      clearExportOptions();
      setSelectedTemplate(templateSelectedDefault);
      return;
    }
    setSelectedTemplate(option);
    const selectedTemplate = exportTemplateData.find(
      (item) => item.id === option.value
    );
    if (!selectedTemplate) {
      return;
    }
    exportOptionDispatch({
      type: 'template',
      template: selectedTemplate.data.fields,
    });

    setCustomizedNameState({
      ...selectedTemplate.data.customizedNames,
    });
  };

  const saveTemplate = (templateName: string) => {
    onSaveTemplate(
      templateName,
      bulkExportOption,
      customizedNameState,
      selectedTemplate.value
    );
  };

  const handleSaveTemplate = () => {
    if (selectedTemplate.value) {
      // update saved data
      saveTemplate(selectedTemplate?.label);
    } else {
      // create new entry
      setOpenTemplateNaming(true);
    }
  };

  const panelElement = (
    <Paper
      className={`${classes.container} ${classes.noShadow}`}
      elevation={3}
      square
      data-test={'panel-bulk-export'}
    >
      <div className={classes.tabName}>
        <Typography variant="h1" className={classes.title}>
          advanced export
        </Typography>
        <div className={classes.actionIcons}>
          {/* Close button */}
          <IconButton
            onClick={onClose}
            data-test={`close-button-bulk-export`}
            size="medium"
          >
            <img src={collapseIcon} alt="icon" draggable="false" />
          </IconButton>
        </div>
      </div>
      <Divider className={classes.divider} />
      <div className={classes.content}>
        <div className={classes.firstBox}>
          <div className={classes.boxDescription}>
            <Typography
              variant="body1"
              className={classes.manageBasicInfoLabel}
            >
              Configure Your Export Package
            </Typography>
            <Typography variant="body2" className={classes.paragraph}>
              Navigate through the tab selections below. Make your choices in
              each category and click export when ready.
            </Typography>
          </div>
          <div className={classes.exportTemplateDropdown}>
            {exportTemplateData && (
              <ExportTemplateDropdown
                dropdownOptions={exportTemplateData.map((item) => ({
                  label: item.data.name,
                  value: item.id,
                }))}
                onDropdownChange={onDropdownChange}
                selectedTemplate={selectedTemplate}
              />
            )}
          </div>
        </div>
        <Box display="flex" flexDirection="column">
          <Box>
            <div className={classes.tabsParent}>
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                aria-label="export configuration tabs"
                style={{ minHeight: '40px', height: '40px' }}
              >
                <Tab
                  label="Fields"
                  value={exportTabs.Fields.index}
                  role="tab"
                  classes={{ root: classes.tab }}
                  id={`export-configuration-tab-${exportTabs.Fields.value}`}
                  aria-controls={`export-configuration-tabpanel-${exportTabs.Fields.value}`}
                />
                <Tab
                  label="Options"
                  value={exportTabs.Options.index}
                  role="tab"
                  classes={{ root: classes.tab }}
                  id={`export-configuration-tab-${exportTabs.Options.value}`}
                  aria-controls={`export-configuration-tabpanel-${exportTabs.Options.value}`}
                />
              </Tabs>
            </div>
            <Divider className={classes.divider} />
            <div className={classes.tabPanel}>
              <TabPanel value={exportTabs.Fields.index} index={currentTab}>
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                  }}
                >
                  <GroupedCheckbox
                    exportOptionDetail={exportOptionState}
                    onExportOptionDetailChange={(param) => {
                      exportOptionDispatch(param);
                    }}
                    customizedNames={{ ...customizedNameState }}
                    onCustomizedNameChange={(names) => {
                      setCustomizedNameState({
                        ...customizedNameState,
                        ...names,
                      });
                    }}
                    groupOfExportOption={[
                      {
                        label: 'File Properties',
                        optionType: 'fileProperties',
                      },
                      { label: 'Transcription', optionType: 'transcription' },
                      {
                        label: 'Other Cognition',
                        optionType: 'otherCognition',
                      },
                    ]}
                  />
                </Box>
              </TabPanel>
              <TabPanel value={exportTabs.Options.index} index={currentTab}>
                <>
                  <Typography className={classes.optionsLabel}>
                    Archive Name*
                  </Typography>
                  <div className={classes.dividerSpan} />
                  <TextField
                    required
                    fullWidth
                    data-testid="export-file-name"
                    id="export-file-name"
                    variant="standard"
                    value={exportFileName}
                    error={!isValidName(exportFileName)}
                    helperText={
                      isValidName(exportFileName)
                        ? ' '
                        : 'invalid name. Minimum 3 characters required. Only A-Z, a-z, 0-9, _, - are allowed'
                    }
                    onChange={handleExportFileNameChange}
                  />
                  <Button
                    onClick={handlePasswordDialogOpen}
                    className={classes.primaryButton}
                    color="primary"
                    size="medium"
                    variant="outlined"
                  >
                    Set Password ...
                  </Button>
                  <div className={classes.dividerSpan} />
                  <FormGroup row>
                    {exportOptionState.exportOption
                      .filter((item) => item.optionType === 'optionsGroup')
                      .map((item) => {
                        return (
                          <FormControlLabel
                            className={classes.formControl}
                            control={
                              <Switch
                                classes={{
                                  track: classes.switch_track,
                                  switchBase: classes.switch_base,
                                  checked: classes.checked,
                                }}
                                checked={item.checked}
                                onChange={() =>
                                  exportOptionDispatch({
                                    type: 'field',
                                    field: item.field,
                                  })
                                }
                                value={item.name}
                                name={item.name}
                              />
                            }
                            label={
                              <Typography className={classes.formControlLabel}>
                                {item.name}
                              </Typography>
                            }
                            key={item.field}
                          />
                        );
                      })}
                  </FormGroup>
                  <div className={classes.dividerSpan} />
                  {exportOptionState.exportOption
                    .filter((item) => item.field === 'filePathWindows')
                    .map((item) => {
                      return (
                        <span key={item.field}>
                          <Typography className={classes.optionsLabel}>
                            {item.name}
                          </Typography>
                          <div className={classes.dividerSpan} />
                          <RadioGroup
                            aria-labelledby="file-path-system"
                            name="file-path-system"
                            value={item.checked ? 'Windows' : 'Linux'}
                            onChange={() =>
                              exportOptionDispatch({
                                type: 'field',
                                field: item.field,
                              })
                            }
                          >
                            <FormControlLabel
                              className={classes.formControl}
                              value="Linux"
                              data-testid="linux-file-path"
                              control={<Radio />}
                              label={
                                <Typography
                                  className={classes.formControlLabel}
                                >
                                  {'/ (macOS and Linux File Systems)'}
                                </Typography>
                              }
                            />
                            <FormControlLabel
                              className={classes.formControl}
                              value="Windows"
                              data-testid="windows-file-path"
                              control={<Radio />}
                              label={
                                <Typography
                                  className={classes.formControlLabel}
                                >
                                  {'\\ (Windows File System)'}
                                </Typography>
                              }
                            />
                          </RadioGroup>
                        </span>
                      );
                    })}
                  <div className={classes.dividerSpan} />
                  <Dialog
                    onClose={handlePasswordDialogClose}
                    aria-labelledby="simple-dialog-title"
                    open={passwordDialogOpen}
                  >
                    <Box textAlign="center">
                      <TextField
                        id="encryption-password"
                        label="Password"
                        variant="standard"
                        defaultValue={password}
                        className={classes.margin}
                        inputRef={passwordRef}
                      />
                      <br />
                      <Button
                        onClick={handlePasswordDialogClose}
                        color="primary"
                        className={classes.margin}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleSavePassword}
                        color="primary"
                        variant="contained"
                        className={classes.margin}
                      >
                        Save
                      </Button>
                    </Box>
                  </Dialog>
                </>
              </TabPanel>
            </div>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Box>
              {selectedTemplate.value && isExportTemplateEditEnabled ? (
                <>
                  <Button
                    onClick={handleDeleteTemplateDialogOpen}
                    color="primary"
                    className={classes.margin}
                    startIcon={<Delete />}
                  >
                    Delete
                  </Button>
                  <Button
                    onClick={() => setOpenTemplateNaming(true)}
                    color="primary"
                    startIcon={<Edit />}
                  >
                    Rename
                  </Button>
                </>
              ) : null}
            </Box>
            <Box display="flex" justifyContent="flex-end">
              <Button
                onClick={(e) => {
                  onClose(e);
                  clearExportOptions();
                }}
                color="primary"
                className={classes.margin}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveTemplate}
                color="primary"
                variant="outlined"
                className={classes.margin}
                disabled={!isExportTemplateEditEnabled}
              >
                Save Template
              </Button>
              <Button
                onClick={() =>
                  onConfirm(
                    exportFileName,
                    bulkExportOption,
                    password,
                    customizedNameState
                  )
                }
                color="primary"
                data-test="export-select-button"
                variant="contained"
                className={classes.margin}
                disabled={!isValidName(exportFileName)}
              >
                Export
              </Button>
            </Box>
          </Box>
          <ExportTemplateNamingDialog
            open={openTemplateNaming}
            setOpenTemplateNaming={setOpenTemplateNaming}
            selectedTemplate={selectedTemplate}
            setSelectedTemplate={setSelectedTemplate}
            exportTemplateData={exportTemplateData}
            onSaveTemplate={saveTemplate}
          />
        </Box>
      </div>
      <DeleteTemplateDialog
        open={deleteTemplateDialog}
        onConfirm={handleDeletingExportTemplateClick}
        handleClose={handleDeleteTemplateDialogClose}
      />
    </Paper>
  );

  return (
    <AIWareThemeProvider>
      <Slide
        data-id="123456"
        in={open}
        direction={'left'}
        mountOnEnter
        unmountOnExit
      >
        {panelElement}
      </Slide>
    </AIWareThemeProvider>
  );
}

function exportOptionReducer(
  state: ExportOptionDetail,
  action: ExportOptionAction
): ExportOptionDetail {
  switch (action.type) {
    case 'template': {
      const templateOption = action.template;
      type ObjectKey = keyof typeof templateOption;
      const newExportOption = state.exportOption.map((item) => {
        let checked = false;
        if (item.field === 'tdoId') {
          // hasTdoId has always to be checked. The engine use hasTdoId:true
          // as a flag for new granular export option.
          // TODO remove this only when export engine remove hasTdoId:true
          // when no more old export option used.
          checked = true;
        } else if (templateOption) {
          checked = templateOption[item.jsonField as ObjectKey];
        }
        return {
          name: item.name,
          checked,
          disabled: item.disabled,
          field: item.field,
          jsonField: item.jsonField,
          optionType: item.optionType,
          customizedNameFields: item.customizedNameFields,
        };
      });
      return { ...state, exportOption: newExportOption };
    }
    case 'setAll': {
      const newExportOption = state.exportOption.map((item) => {
        let checked = item.checked;
        if (item.field === 'tdoId') {
          // hasTdoId has always to be checked. The engine use hasTdoId:true
          // as a flag for new granular export option.
          checked = true;
        } else if (item.optionType === action.optionType) {
          checked = action.setAll ?? false;
        }
        return {
          name: item.name,
          checked,
          disabled: item.disabled,
          field: item.field,
          jsonField: item.jsonField,
          optionType: item.optionType,
          customizedNameFields: item.customizedNameFields,
        };
      });
      return { ...state, exportOption: newExportOption };
    }
    case 'field': {
      const newExportOption = state.exportOption.map((item) => {
        let checked = false;
        if (item.field === 'tdoId') {
          // hasTdoId has always to be checked. The engine use hasTdoId:true
          // as a flag for new granular export option.
          checked = true;
        } else if (action.field === item.field) {
          checked = !item.checked;
        } else {
          checked = item.checked;
        }
        return {
          name: item.name,
          checked,
          disabled: item.disabled,
          field: item.field,
          jsonField: item.jsonField,
          optionType: item.optionType,
          customizedNameFields: item.customizedNameFields,
        };
      });
      return { ...state, exportOption: newExportOption };
    }
    default:
      return state;
  }
}

function toBulkExportOption(state: ExportOptionDetail) {
  const bulkExportOption: BulkExportOption = {
    hasTdoId:
      state.exportOption.find((item) => item.field === 'tdoId')?.checked ??
      false,
    hasFilename:
      state.exportOption.find((item) => item.field === 'filename')?.checked ??
      false,
    hasBaseFilename:
      state.exportOption.find((item) => item.field === 'baseFilename')
        ?.checked ?? false,
    hasFoldername:
      state.exportOption.find((item) => item.field === 'foldername')?.checked ??
      false,
    hasTag:
      state.exportOption.find((item) => item.field === 'tag')?.checked ?? false,
    hasPlainText:
      state.exportOption.find((item) => item.field === 'plainText')?.checked ??
      false,
    hasTtml:
      state.exportOption.find((item) => item.field === 'ttml')?.checked ??
      false,
    hasWord:
      state.exportOption.find((item) => item.field === 'word')?.checked ??
      false,
    hasObjectNotation:
      state.exportOption.find((item) => item.field === 'objectNotation')
        ?.checked ?? false,
    hasBookmark:
      state.exportOption.find((item) => item.field === 'bookmark')?.checked ??
      false,
    hasClosedCaption:
      state.exportOption.find((item) => item.field === 'closedCaption')
        ?.checked ?? false,
    hasTranslation:
      state.exportOption.find((item) => item.field === 'translation')
        ?.checked ?? false,
    hasNativeTranslation:
      state.exportOption.find((item) => item.field === 'nativeTranslation')
        ?.checked ?? false,
    hasObjectDetection:
      state.exportOption.find((item) => item.field === 'objectDetection')
        ?.checked ?? false,
    hasSentiment:
      state.exportOption.find((item) => item.field === 'sentiment')?.checked ??
      false,
    hasNative:
      state.exportOption.find((item) => item.field === 'native')?.checked ??
      false,
    hasEngineName:
      state.exportOption.find((item) => item.field === 'engineName')?.checked ??
      false,
    filePathWindows:
      state.exportOption.find((item) => item.field === 'filePathWindows')
        ?.checked ?? false,
  };
  return bulkExportOption;
}

export function formatDateTimeForExportName(date: Date): string {
  return DateTime.fromJSDate(date).toFormat('yyyyMMdd_HH_mm_ss');
}

function generateExportName(): string {
  const now = new Date();
  const exportDateTime = formatDateTimeForExportName(now);
  return `export_${exportDateTime}`;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      style={{ width: '100%', height: '100%' }}
      hidden={value !== index}
      id={`export-configuration-tabpanel-${index}`}
      aria-labelledby={`export-configuration-tab-${index}`}
      {...other}
    >
      {value === index && children}
    </div>
  );
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface BulkExportProps {
  onClose: (event: React.MouseEvent<HTMLElement>) => void;
  open: boolean;
  onConfirm: (
    exportFileName: string,
    bulkExportOption: BulkExportOption,
    password: string,
    customizedNames: BulkExportCustomizedNames
  ) => void;
  defaultExportFileName?: string;
  exportTemplateData?: ExportTemplateData[];
  onSaveTemplate: (
    name: string,
    fields: BulkExportOption,
    customizedNames: BulkExportCustomizedNames,
    id: string
  ) => void;
  handleDeletingExportTemplate: (id: string) => void;
  isExportTemplateEditEnabled: boolean;
}

interface ExportOptionAction {
  type: 'field' | 'setAll' | 'template';
  field?: BulkExportField;
  setAll?: boolean;
  template?: BulkExportOption;
  optionType?: string;
}
