import React from 'react';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

function AutocompleteFilter({
  handleAutocompleteChange,
  searchValue,
  handleInputChange,
  label,
}: Props) {
  return (
    <Autocomplete
      multiple
      limitTags={2}
      id="multiple-limit-tags"
      options={[]}
      freeSolo
      style={{ width: '100%' }}
      onChange={(_e, newVal, _reason) => {
        handleAutocompleteChange(newVal);
      }}
      value={searchValue}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          label={label}
          onChange={handleInputChange}
        />
      )}
    />
  );
}

interface Props {
  handleAutocompleteChange: (newValue: string[]) => void;
  searchValue: string[];
  handleInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
}

export default AutocompleteFilter;
