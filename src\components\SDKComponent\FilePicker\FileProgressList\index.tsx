import cx from 'classnames';
import { get } from 'lodash';
import React from 'react';

import AbortIcon from '@mui/icons-material/Delete';
import VideoIcon from '@mui/icons-material/LocalMovies';
import ImageIcon from '@mui/icons-material/Photo';
import AudioIcon from '@mui/icons-material/PlayCircleOutline';
import TextIcon from '@mui/icons-material/ShortText';
import IconButton from '@mui/material/IconButton';
import LinearProgress from '@mui/material/LinearProgress';
import { withStyles, ClassNameMap } from '@mui/styles';

import { formatBytes } from '../../../../helpers/format';
import { typedObjectKeys } from '@utils';
import styles from './styles';

interface File {
  key: string;
  value: {
    name?: string;
    type: string;
    size: number;
    percent: number;
    error?: string;
    aborted?: string;
  };
}

interface FileProgressListProps {
  percentByFiles: File[];
  showErrors?: boolean;
  handleAbort?: (fileKey: string) => void;
  classes?: ClassNameMap;
}

const FileProgressList: React.FC<FileProgressListProps> = ({
  percentByFiles,
  showErrors = false,
  handleAbort,
  classes,
}) => {
  const getFileMediaIcon = (file: File) => {
    const type = get(file, 'value.type');
    const icons = {
      audio: (
        <AudioIcon
          className={classes?.fileIcon || ''}
          data-test-target="audio"
        />
      ),
      video: (
        <VideoIcon
          className={classes?.fileIcon || ''}
          data-test-target="video"
        />
      ),
      image: (
        <ImageIcon
          className={classes?.fileIcon || ''}
          data-test-target="image"
        />
      ),
      text: (
        <TextIcon className={classes?.fileIcon || ''} data-test-target="text" />
      ),
    };

    const iconKeys = typedObjectKeys(icons);
    for (const key of iconKeys) {
      if (type && type.includes(key)) {
        return (
          <div
            className={cx(
              classes?.fileIconContainer || '',
              classes?.[key] || ''
            )}
          >
            {icons[key]}
          </div>
        );
      }
    }

    return (
      <div
        className={cx(classes?.fileIconContainer || '', classes?.text || '')}
      >
        {icons.text}
      </div>
    );
  };

  const handleAbortFile = (fileKey: string) => () => {
    handleAbort && handleAbort(fileKey);
  };

  const files = !showErrors
    ? percentByFiles
    : percentByFiles.filter(
        (file) => get(file, 'value.error') && !get(file, 'value.aborted')
      );

  return (
    <div>
      {files.map((file) => (
        <div
          key={file.key}
          className={classes?.fileProgressItem || ''}
          data-test-target={file.key}
          data-testid="file-picker-list"
        >
          <LinearProgress
            className={classes?.fileProgressBar || ''}
            classes={{
              barColorPrimary: get(file, 'value.error')
                ? classes?.fileProgressBarError || ''
                : classes?.fileProgressBarPrimary || '',
            }}
            variant="determinate"
            value={showErrors ? 0 : file.value.percent}
            data-testid={file.key}
          />
          <div className={classes?.fileProgressItemOverlay || ''}>
            {getFileMediaIcon(file)}
            <span className={classes?.fileName || ''}>
              {file.value.name || file.key}
            </span>
            <div className={classes?.sizeContainer || ''}>
              <span className={classes?.fileSize || ''}>
                {formatBytes(file.value.size)}
              </span>
            </div>
            {handleAbort && file.value.percent !== 100 && (
              <div className={classes?.abortContainer || ''}>
                <IconButton
                  onClick={handleAbortFile(file.key)}
                  size="large"
                  data-testid="file-picker-abort-icon"
                >
                  <AbortIcon />
                </IconButton>
              </div>
            )}
          </div>
          {!showErrors && file.value.percent !== 100 && (
            <div
              className={classes?.progressTextOverlay || ''}
              style={{ marginLeft: `${file.value.percent}%` }}
            >
              <span className={classes?.progressText || ''}>
                {file.value.percent}%
              </span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default withStyles(styles)(FileProgressList);
