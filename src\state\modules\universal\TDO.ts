import { Flavor } from '@utils';

export type TDOId = Flavor<string, 'TDO'>;

export enum JobStatus {
  Pending = 'pending',
  Complete = 'complete',
  Running = 'running',
  Cancelled = 'cancelled',
  Queued = 'queued',
  Failed = 'failed',
}

export enum TaskStatus {
  Pending = 'pending',
  Running = 'running',
  Complete = 'complete',
  Queued = 'queued',
  Accepted = 'accepted',
  Failed = 'failed',
  Cancelled = 'cancelled',
  StandbyPending = 'standby_pending',
  Waiting = 'waiting',
  Resuming = 'resuming',
  Aborted = 'aborted',
  Paused = 'paused',
}

export interface TDOTask {
  readonly targetId: TDOId;
  readonly status: TaskStatus;
  readonly createdDateTime: string;
  readonly startedDateTime: string | null;
  readonly engine: {
    readonly id: string;
    readonly categoryId: string;
  };
  readonly job: {
    readonly status: JobStatus;
  };
  readonly jobId: string;
}

export interface TDOJob {
  readonly targetId: TDOId;
  readonly status: TaskStatus;
  readonly createdDateTime: string;
}

export interface VeritoneFile {
  readonly audioSampleRate?: number;
  readonly duration?: number;
  readonly fileName?: string;
  readonly fileType?: string;
  readonly filename: string;
  readonly hasAudio?: boolean;
  readonly hasVideo?: boolean;
  readonly mimetype: string;
  readonly segmented?: boolean;
  readonly height?: number;
  readonly width?: number;
  readonly videoFrameRate?: number;
  readonly size?: number;
}

export interface TDO {
  readonly id: TDOId;
  readonly name: string;
  readonly status?:
    | 'downloaded'
    | 'recording'
    | 'recorded'
    | 'error'
    | 'deleting'
    | 'moving';
  readonly thumbnailUrl: string;
  readonly createdDateTime: string; // ISO
  readonly modifiedDateTime?: string; // ISO
  readonly startDateTime: string; // ISO
  readonly stopDateTime: string; // ISO
  readonly folders: { treeObjectId: string }[];
  readonly details: {
    readonly addToIndex?: boolean;
    readonly applicationId?: string;
    readonly createdDateTime?: number;
    readonly modifiedDateTime?: number;
    readonly recordingId?: string;
    readonly startDateTime?: number;
    readonly status?: string;
    readonly stopDateTime?: number;
    readonly isExport?: boolean;
    tags?: { value: string; key?: string }[];
    veritoneFile: {
      fileName?: string;
      filename?: string;
    };
  };
  readonly assets: {
    readonly records: ReadonlyArray<{
      readonly assetType: string;
      readonly signedUri: string;
      readonly details?: {
        readonly audioRedactions: number;
        readonly boundingPolysCount: number;
        readonly selectedPolyGroupsCount: number;
        readonly tdos: Record<
          TDOId,
          {
            readonly auditLogAssetId: string;
            readonly auditLogName: string;
            readonly redactedMediaAssetId: string;
            readonly redactedMediaName: string;
          }
        >;
      };
    }>;
  };
  readonly jobs?: {
    readonly records: ReadonlyArray<TDOJob>;
  };
  readonly tasks?: {
    readonly records: ReadonlyArray<TDOTask>;
  };
  readonly previewUrl: string;
  readonly primaryAsset: {
    readonly id: string;
    readonly signedUri: string;
    readonly contentType: string;
  };
  readonly streams?: ReadonlyArray<{
    readonly protocol: string;
    readonly uri: string;
  }>;
  readonly sourceImageUrl: string;
  readonly icon?: string;
}
