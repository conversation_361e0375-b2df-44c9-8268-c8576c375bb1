import React, { Fragment, KeyboardEvent } from 'react';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import makeStyles from '@mui/styles/makeStyles';
import styles from './styles';
import TagsCustomize from './tagsCustomize';
const useStyles = makeStyles(styles);
function Customize({
  handleOpenFolder,
  selectedFolder,
  onKeyPress,
  handleOnChangeTagsCustomize,
  tagsCustomizeName,
  tagsCustomize,
  handleRemoveTagsCustomize,
  onClickAddTags,
}: Props) {
  const classes = useStyles();
  return (
    <Fragment>
      <div className={classes.contentCustomize}>
        <Typography component="p" className={classes.titleCustomize}>
          Ingestion Customization
        </Typography>
        <Typography color="textSecondary" gutterBottom component="p">
          Manage and help organize the location of the files being ingested.
        </Typography>
      </div>
      <div className={classes.contentCustomize}>
        <Typography component="p" className={classes.titleCustomize}>
          Select Folder
        </Typography>
        <Typography color="textSecondary" gutterBottom component="p">
          Choose a folder for these files.
        </Typography>
        <TextField
          label="Folder"
          InputProps={{
            readOnly: true,
          }}
          onClick={handleOpenFolder}
          value={selectedFolder?.name ?? ''}
          data-test="select-folder"
          inputProps={{ 'data-testid': 'select-folder' }}
          variant="standard"
        />
      </div>
      <div className={classes.contentCustomize}>
        <Typography component="p" className={classes.titleCustomize}>
          Tags
        </Typography>
        <Typography color="textSecondary" gutterBottom component="p">
          Label and group your ingested files by using keywords or terms to help
          describe them.
        </Typography>
        <TagsCustomize
          type="uploadFile"
          onKeyPress={onKeyPress}
          handleOnChangeTagsCustomize={handleOnChangeTagsCustomize}
          tagsCustomizeName={tagsCustomizeName}
          tagsCustomize={tagsCustomize}
          handleRemoveTagsCustomize={handleRemoveTagsCustomize}
          onClickAddTags={onClickAddTags}
        />
      </div>
    </Fragment>
  );
}
interface Props {
  handleOpenFolder: () => void;
  selectedFolder?: {
    name: string;
    treeObjectId: string;
  };
  onKeyPress: (event: KeyboardEvent<HTMLInputElement>) => void;
  handleOnChangeTagsCustomize: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  tagsCustomizeName: string;
  tagsCustomize: { value: string }[];
  handleRemoveTagsCustomize: (name: string, type: string) => void;
  onClickAddTags: (event: React.MouseEvent<HTMLElement>) => void;
}
export default Customize;
