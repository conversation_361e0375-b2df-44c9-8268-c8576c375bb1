import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import './styles.scss';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              { field: "stopForAStudent", operator: "term", limit: 10000 }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'studentsAggregation',
      dataKey: 'stopForAStudent',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'Students Distribution',
  filterTextType: 'Students Distribution by Type',
  filterType: 'stopForAStudent',
  filterTerms: { Student: ['true'], 'Non-Student': ['false'] },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataObj = {
      Student: 0,
      'Non-Student': 0,
      ...data.studentsAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          if (b.key_as_string === 'true') {
            acc['Student'] = b.doc_count;
          }
          if (b.key_as_string === 'false') {
            acc['Non-Student'] = b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to add 2 properties "Student" and "Non-Student"
      ),
    };

    // Add data
    chart.data = Object.entries(dataObj).map((kvp) => {
      const [ag, v] = kvp;
      return { ageGroup: ag, count: v };
    });

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'ageGroup';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  studentsAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
  }[];
}
