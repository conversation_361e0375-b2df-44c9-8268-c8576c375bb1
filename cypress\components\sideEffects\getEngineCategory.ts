import promisify from 'cypress-promise';

export const getEngineCategory = (id: string) => {
  const query = {
    index: ['mine'],
    select: ['veritone-job', 'veritone-file'],
    aggregate: [
      {
        name: 'count',
        field: 'fileDuration',
        operator: 'sum',
      },
    ],
    query: {
      operator: 'and',
      conditions: [
        {
          field: 'veritone-job.engineCategories',
          operator: 'terms',
          values: [id],
        },
      ],
    },
  };
  return promisify(cy.SearchAggregate(query));
};
