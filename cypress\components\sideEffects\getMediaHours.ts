import promisify from 'cypress-promise';

export const getMediaHours = (additionCondition: any[]) => {
  const query = {
    index: ['mine'],
    select: ['veritone-job', 'veritone-file'],
    limit: 10,
    offset: 0,
    query: { operator: 'and', conditions: additionCondition },
    aggregate: [
      {
        field: 'recordingId',
        operator: 'count',
      },
      {
        field: 'fileDuration',
        operator: 'sum',
      },
    ],
  };
  return promisify(cy.SearchAggregate(query));
};
