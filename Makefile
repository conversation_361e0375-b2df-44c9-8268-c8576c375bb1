RC_URL=registry.central.aiware.com

ifeq ($(GIT_COMMIT),)
	GIT_COMMIT=$(shell git rev-parse HEAD)
endif

build: build-illuminate build-illuminate-k8s

build-illuminate:
	docker build -t illuminate-app:${GIT_COMMIT} -f Dockerfile --build-arg K8S_BUILD=FALSE --build-arg APPLICATION=illuminate-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag illuminate-app:${GIT_COMMIT} illuminate-app:latest
	docker tag illuminate-app:${GIT_COMMIT} illuminate-app:dev

build-dev:
	docker build -t illuminate-with-backend -f Dockerfile.local --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .

run-dev:
	docker run -p 443:443 illuminate-with-backend

build-illuminate-k8s:
	docker build -t illuminate-k8s-app:${GIT_COMMIT} -f Dockerfile --build-arg K8S_BUILD=TRUE --build-arg APPLICATION=illuminate-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag illuminate-k8s-app:${GIT_COMMIT} illuminate-k8s-app:latest
	docker tag illuminate-k8s-app:${GIT_COMMIT} illuminate-app:k8s

run-k8s:
	docker run -p 9000:9000 -e ENVIRONMENT=dev -e APPLICATION=illuminate-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} illuminate-k8s-app:latest

publish: publish-illuminate publish-illuminate-k8s

publish-illuminate:
	$(MAKE) docker-tag SERVICE=illuminate-app
	$(MAKE) docker-push SERVICE=illuminate-app

publish-illuminate-k8s:
	$(MAKE) docker-tag SERVICE=illuminate-k8s-app
	$(MAKE) docker-push SERVICE=illuminate-k8s-app

docker-tag:
	echo "Tagging ${SERVICE} with Registry Central registry ${RC_URL}"
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:latest
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:dev

docker-push:
	echo "Pushing ${SERVICE} to ${RC_URL}"
	docker push ${RC_URL}/${SERVICE}:latest
	docker push ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker push ${RC_URL}/${SERVICE}:dev