import axios, { AxiosResponse } from 'axios';
import fpl from '@veritone/functional-permissions-lib';
import { ForbiddenError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface CurrentUserResponse {
  permissionMasks: number[];
}

const checkIsRoot = async (context: Context) => {
  const { log, req, data } = context;
  const { apiRoot } = env;
  try {
    const {
      data: { permissionMasks },
    } = await axios.get<
      CurrentUserResponse,
      AxiosResponse<CurrentUserResponse>,
      void
    >(`${apiRoot}/v1/admin/current-user`, {
      headers: {
        Authorization: req.headers.authorization,
      },
    });

    const isSuperAdmin = fpl.util.hasAccessTo(
      fpl.permissions.superadmin,
      permissionMasks
    );

    if (!isSuperAdmin) {
      throw new ForbiddenError('Must be root');
    }

    // We will only be performing actions for other orgs as root
    data.authorizedOrgId = undefined;

    return context;
  } catch (e) {
    log.error('checkIsRoot failed', e);
    throw e;
  }
};

export default checkIsRoot;
