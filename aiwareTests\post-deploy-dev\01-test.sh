#!/bin/sh
# The post-deploy-dev is disable by dev-ops as of
# https://steel-ventures.atlassian.net/browse/AIRM-184
#
#
# echo 'Running post-deploy-dev illuminate-e2e';
# cd ../../
# export CYPRESS_username=`aws secretsmanager get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --region us-east-1 --output json | jq -r ".SecretString" | jq -r ".e2e_user"`
# export CYPRESS_password=`aws secretsmanager get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --region us-east-1 --output json | jq -r ".SecretString" | jq -r ".e2e_password"`
# docker build . -f e2e.Dockerfile -t illuminate-e2e && docker run -e ENVIRONMENT=dev -e CYPRESS_username -e CYPRESS_password illuminate-e2e
