import { modules } from '@veritone/glc-redux';
import getApiAuthToken from './getApiAuthToken';
import fetchGraphQLApi from './fetchGraphQLApi';
import { ExtendedError } from '@utils';

const {
  config: { getConfig },
} = modules;

export function getApiConnectionParameters(state: any): {
  endpoint: string;
  token: string | null;
  veritoneAppId: string;
} {
  const config = getConfig<Window['config']>(state);
  const endpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
  const token = getApiAuthToken(state);
  const veritoneAppId = config.veritoneAppId;

  return { endpoint, token, veritoneAppId };
}

export async function fetchGraphQLApiThrowError<T>({
  endpoint,
  query,
  variables,
  operationName,
  token,
  veritoneAppId,
}: Props) {
  const result = await fetchGraphQLApi<T>({
    endpoint,
    query,
    variables,
    operationName,
    token,
    veritoneAppId,
  });

  if (result.errors) {
    const error = new Error(
      result.errors[0]?.message || 'Unknown Error'
    ) as ExtendedError;
    error.errors = result.errors;
    throw error;
  }
  return result;
}

export async function callAsyncFunc({
  actionTypes: [requestType, successType, failureType],
  asyncfn,
  dispatch,
}: PropsCallAsyncFunc) {
  dispatch({
    type: requestType,
  });
  let response;
  try {
    response = await asyncfn();
  } catch (e) {
    dispatch({
      type: failureType,
      error: true,
      payload: e,
      meta: {
        response,
      },
    });
    throw e;
  }

  if (response.errors && response.errors.length) {
    dispatch({
      type: failureType,
      error: true,
      payload: response.errors,
      meta: {
        response,
      },
    });

    const error = new Error('callAsyncFuncs failed') as ExtendedError;
    error.errors = response.errors;
    throw error;
  }

  dispatch({
    type: successType,
    payload: response.data,
    meta: {
      response,
    },
  });

  return response;
}

interface Props {
  endpoint: string;
  query: string;
  variables: Record<string, any>;
  operationName?: string;
  token: string;
  veritoneAppId: string;
}

interface PropsCallAsyncFunc {
  actionTypes: [string, string, string];
  asyncfn: () => any;
  dispatch: (action: {
    type: string;
    error?: boolean;
    payload?: any;
    meta?: { response: any };
  }) => void;
  getState: () => any;
}
