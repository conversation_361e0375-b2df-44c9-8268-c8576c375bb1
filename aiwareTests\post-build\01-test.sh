#!/bin/sh
# disabled because aws dev is very unstable for test.
# will enable the test when aws dev is stablized.
#
#
# echo 'Running post-build illuminate-e2e';
# cd ../../
# docker build . -f e2e.Dockerfile -t illuminate-e2e
# echo 'pulling latest illuminate-app docker image';
# echo $(aws ecr get-login-password --region us-east-1)|docker login --password-stdin --username AWS ************.dkr.ecr.us-east-1.amazonaws.com
# docker pull ************.dkr.ecr.us-east-1.amazonaws.com/illuminate-app:latest
# echo 'running e2e test';
# cd aiwareTests/post-build/
# export e2e_user=`aws secretsmanager get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --region us-east-1 --output json | jq -r ".SecretString" | jq -r ".e2e_user"`
# export e2e_password=`aws secretsmanager get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --region us-east-1 --output json | jq -r ".SecretString" | jq -r ".e2e_password"`
# docker-compose --file ../docker-compose.yml rm -f
# docker-compose --file ../docker-compose.yml up --force-recreate --exit-code-from e2e
