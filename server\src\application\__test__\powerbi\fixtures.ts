export const tokenConfigRecordMock = {
    orgId: 2,
    datasetId: '1234-1234-1234-1234',
    profileId: '3456-3456-3456-3456',
    workspaceId: '4567-4567-4567-4567',
    embedUrl: 'https://embedurlfromazure.us',
    lifetimeInMinutes: 3600,
    pbixFilePath: 'PbixTemplateName.pbix'
};

export const tokenConfigNoRecordMock = null as any;

export const templateRecordMock = {
    id: 1,
    name: 'Template Name',
    description: 'Template description.',
    pbixFileName: 'PbixTemplateName.pbix',
    created: '2022-07-13 00:00:00.000 Z',
    modifed: '2022-07-13 00:00:00.000 Z',
};

export const notAdminPermissionMasksMock = [
    -4,
    255
];

export const adminPermissionMasksMock = [
    -2,
    255,
    1073741824,
    1275068416
];

export const contactPushDataRequestMock: any = {
    "stops": [
        {
            "data": [
                {
                    "recordId": "{{$guid}}",
                    "age": 85,
                    "city": "ANAHEIM",
                    "lgbt": false,
                    "gender": "Male",
                    "school": "",
                    "location": "BROOKHURST / crescent ",
                    "ethnicity": [
                        "White"
                    ],
                    "disability": [],
                    "k12_school": false,
                    "timeOfStop": "09:05",
                    "leaRecordId": "0000345523",
                    "personNumber": 1,
                    "resultOfStop": [
                        "Warning (verbal or written)"
                    ],
                    "ecSubdivision": "",
                    "basisForSearch": [],
                    "datetimeOfStop": "2022-11-07T17:05:00Z",
                    "durationOfStop": 5,
                    "limitedEnglish": false,
                    "reasonsForStop": "Traffic Violation",
                    "warningOffCode": [
                        "21461(A) - DRIVER, FAIL \"OBEY\" SIGN/ETC",
                        "test",
                        "234556(A) - DRIVER, FAIL \"OBEY\" 2/ETC"
                    ],
                    "citationOffCode": [],
                    "stopForAStudent": false,
                    "trafficViolation": "Moving",
                    "disciplineUnderEc": "",
                    "ethnicityExclusive": "White",
                    "genderNonconforming": false,
                    "reasonableSuspicion": [],
                    "contrabandOrEvidence": [],
                    "suspicionOffenseCode": "",
                    "typeOfPropertySeized": [],
                    "responseToServiceCall": false,
                    "actionsTakenDuringStop": [],
                    "custodialArrestOffCode": [],
                    "reasonForStopNarrative": "Regulatory Sign ",
                    "basisForPropertySeizure": [],
                    "basisForSearchNarrative": "",
                    "officerTypeOfAssignment": "Patrol, traffic, field operations",
                    "officerYearsOfExperience": 1,
                    "inFieldCiteAndReleaseCode": [
                        "test",
                        "test,test,test",
                        "12345678 \" , \""
                    ],
                    "officerOtherAssignmentType": "",
                    "trafficViolationOffenseCode": "21461(A) - DRIVER FAIL OBEY SIGN/ETC",
                    "raceOfOfficer": [
                        "Asian",
                        "Black/African American",
                        "Hispanic/Latine(x)",
                        "White"
                    ],
                    "sexualOrientation": "Straight/Heterosexual",
                    "typeOfStop": "Vehicular",
                    "officerWorksWithNonPrimaryAgency": false,
                    "reasonGivenStoppedPerson": [
                        "Matched suspect description",
                        "Matched description of suspect’s vehicle or vehicle observed at the scene of a crime",
                        "Witness or victim identified stopped person as a suspect of a crime"
                    ],
                    "stopDuringWellnessCheck": false,
                    "typeOfAssignmentOfficer": "Off Duty - Working Private Event",
                    "stoppedPassenger": false,
                    "stoppedInsideResidence": false,
                    "nonConforming": false,
                    "unhoused": false
                }
            ]
        },
        {
            "customQuestionData": [
                {
                    "recordId": "contact-stop-id-1", // stop record id
                    "personNumber": 1, // person number = 0 is for stop-wide answers,
                    "isStopQuestion": false, // the answer is for a stop-wide question (personNumber should be 0 if true)
                    "answer": [
                        "Option, 1",
                        "Option ,\",2\""
                    ], // string or string[]
                    "questionKey": "somethingRandom<k9ds22bjK>" // the result path the question is saved to,
                },
                {
                    "recordId": "contact-stop-id-1", // stop record id
                    "personNumber": 0, // person number = 0 is for stop-wide answers,
                    "isStopQuestion": true, // the answer is for a stop-wide question (personNumber should be 0 if true)
                    "answer": "Some text answer", // string or string[]
                    "questionKey": "enterSomeText<123567890>" // the result path the question is saved to,
                }
            ],
            "data": [
                {
                    "recordId": "{{$guid}}",
                    "age": 85,
                    "city": "ANAHEIM",
                    "lgbt": false,
                    "gender": "Male",
                    "school": "",
                    "location": "BROOKHURST / crescent ",
                    "ethnicity": [
                        "White"
                    ],
                    "disability": [],
                    "k12_school": false,
                    "timeOfStop": "09:05",
                    "leaRecordId": "0000345523",
                    "personNumber": 1,
                    "resultOfStop": [
                        "Warning (verbal or written)"
                    ],
                    "ecSubdivision": "",
                    "basisForSearch": [],
                    "datetimeOfStop": "2022-11-07T17:05:00Z",
                    "durationOfStop": 5,
                    "limitedEnglish": false,
                    "reasonsForStop": "Traffic Violation",
                    "warningOffCode": [
                        "21461(A) - DRIVER, FAIL \"OBEY\" SIGN/ETC",
                        "test",
                        "234556(A) - DRIVER, FAIL \"OBEY\" 2/ETC"
                    ],
                    "citationOffCode": [],
                    "stopForAStudent": false,
                    "trafficViolation": "Moving",
                    "disciplineUnderEc": "",
                    "ethnicityExclusive": "White",
                    "genderNonconforming": false,
                    "reasonableSuspicion": [],
                    "contrabandOrEvidence": [],
                    "suspicionOffenseCode": "",
                    "typeOfPropertySeized": [],
                    "responseToServiceCall": false,
                    "actionsTakenDuringStop": [],
                    "custodialArrestOffCode": [],
                    "reasonForStopNarrative": "Regulatory Sign ",
                    "basisForPropertySeizure": [],
                    "basisForSearchNarrative": "",
                    "officerTypeOfAssignment": "Patrol, traffic, field operations",
                    "officerYearsOfExperience": 1,
                    "inFieldCiteAndReleaseCode": [
                        "test",
                        "test,test,test",
                        "12345678 \" , \""
                    ],
                    "officerOtherAssignmentType": "",
                    "trafficViolationOffenseCode": "21461(A) - DRIVER FAIL OBEY SIGN/ETC",
                    "raceOfOfficer": [
                        "Asian",
                        "Black/African American",
                        "Hispanic/Latine(x)",
                        "White"
                    ],
                    "sexualOrientation": "Straight/Heterosexual",
                    "typeOfStop": "Vehicular",
                    "officerWorksWithNonPrimaryAgency": false,
                    "reasonGivenStoppedPerson": [
                        "Matched suspect description",
                        "Matched description of suspect’s vehicle or vehicle observed at the scene of a crime",
                        "Witness or victim identified stopped person as a suspect of a crime"
                    ],
                    "stopDuringWellnessCheck": false,
                    "typeOfAssignmentOfficer": "Off Duty - Working Private Event",
                    "stoppedPassenger": false,
                    "stoppedInsideResidence": false,
                    "nonConforming": false,
                    "unhoused": false
                },
                {
                    "recordId": "{{$guid}}",
                    "age": 85,
                    "city": "ANAHEIM",
                    "lgbt": false,
                    "gender": "Male",
                    "school": "",
                    "location": "BROOKHURST / crescent ",
                    "ethnicity": [
                        "White"
                    ],
                    "disability": [],
                    "k12_school": false,
                    "timeOfStop": "09:05",
                    "leaRecordId": "0000345523",
                    "personNumber": 1,
                    "resultOfStop": [
                        "Warning (verbal or written)"
                    ],
                    "ecSubdivision": "",
                    "basisForSearch": [],
                    "datetimeOfStop": "2022-11-07T17:05:00Z",
                    "durationOfStop": 5,
                    "limitedEnglish": false,
                    "reasonsForStop": "Traffic Violation",
                    "warningOffCode": [
                        "21461(A) - DRIVER, FAIL \"OBEY\" SIGN/ETC",
                        "test",
                        "234556(A) - DRIVER, FAIL \"OBEY\" 2/ETC"
                    ],
                    "citationOffCode": [],
                    "stopForAStudent": false,
                    "trafficViolation": "Moving",
                    "disciplineUnderEc": "",
                    "ethnicityExclusive": "White",
                    "genderNonconforming": false,
                    "reasonableSuspicion": [],
                    "contrabandOrEvidence": [],
                    "suspicionOffenseCode": "",
                    "typeOfPropertySeized": [],
                    "responseToServiceCall": false,
                    "actionsTakenDuringStop": [],
                    "custodialArrestOffCode": [],
                    "reasonForStopNarrative": "Regulatory Sign ",
                    "basisForPropertySeizure": [],
                    "basisForSearchNarrative": "",
                    "officerTypeOfAssignment": "Patrol, traffic, field operations",
                    "officerYearsOfExperience": 1,
                    "inFieldCiteAndReleaseCode": [
                        "test",
                        "test,test,test",
                        "12345678 \" , \""
                    ],
                    "officerOtherAssignmentType": "",
                    "trafficViolationOffenseCode": "21461(A) - DRIVER FAIL OBEY SIGN/ETC",
                    "raceOfOfficer": [
                        "Asian",
                        "Black/African American",
                        "Hispanic/Latine(x)",
                        "White"
                    ],
                    "sexualOrientation": "Straight/Heterosexual",
                    "typeOfStop": "Vehicular",
                    "officerWorksWithNonPrimaryAgency": false,
                    "reasonGivenStoppedPerson": [
                        "Matched suspect description",
                        "Matched description of suspect’s vehicle or vehicle observed at the scene of a crime",
                        "Witness or victim identified stopped person as a suspect of a crime"
                    ],
                    "stopDuringWellnessCheck": false,
                    "typeOfAssignmentOfficer": "Off Duty - Working Private Event",
                    "stoppedPassenger": false,
                    "stoppedInsideResidence": false,
                    "nonConforming": false,
                    "unhoused": false
                }
            ]
        }
    ]
}
