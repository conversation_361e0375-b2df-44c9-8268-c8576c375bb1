import { useState } from 'react';
import {
  Grid,
  TableHeaderRow,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui';
import { ConnectedProps, connect } from 'react-redux';
import Typography from '@mui/material/Typography';
import classNames from 'classnames/bind';
import { enginesSchemasRequired } from '../../state/modules/requiredEnginesAndSchemas';
import * as styles from './styles.scss';
const cx = classNames.bind(styles);
function EngineSchemaRequired({ enginesSchemasRequired }: PropsFromRedux) {
  const [columns] = useState([
    { name: 'id', title: 'engineID' },
    { name: 'name', title: 'Engine Name' },
    { name: 'type', title: 'Type' },
    { name: 'status', title: 'Status' },
  ]);
  const [tableColumnExtensions] = useState([
    { columnName: 'id', wordWrapEnabled: true },
    { columnName: 'name', wordWrapEnabled: true, align: 'left' as const },
    { columnName: 'type', wordWrapEnabled: true, align: 'left' as const },
    {
      columnName: 'status',
      wordWrapEnabled: true,
      align: 'center' as const,
    },
  ]);
  const dataRow =
    enginesSchemasRequired &&
    enginesSchemasRequired.map((item) => {
      const id = <Typography>{item.id}</Typography>;
      const name = <Typography>{item.name}</Typography>;
      const type = <Typography>{item.type}</Typography>;
      const status = (
        <span
          className={cx(styles.status, {
            // safe due to available exists in styles
            [styles.available!]: item.status === 'Available',
            // safe due to unavailable exists in styles
            [styles.unavailable!]: item.status === 'N/A',
          })}
        >
          {item.status}
        </span>
      );
      return {
        id,
        name,
        type,
        status,
      };
    });
  return (
    <div>
      <Typography className={styles.titleRequired}>
        Required engines and schema
      </Typography>
      <Grid rows={dataRow} columns={columns}>
        <VirtualTable height="auto" columnExtensions={tableColumnExtensions} />
        <TableHeaderRow />
      </Grid>
    </div>
  );
}

export interface EngineSchema {
  id: string;
  name: string;
  status: string;
  type: string;
}

const mapState = (state: any) => ({
  enginesSchemasRequired: enginesSchemasRequired(state),
});

const mapDispatch = {};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(EngineSchemaRequired);
