import React, { Fragment, KeyboardEvent } from 'react';
import TextField from '@mui/material/TextField';
import Chip from '@mui/material/Chip';
import makeStyles from '@mui/styles/makeStyles';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import styles from './styles';
const useStyles = makeStyles(styles);
const helperTextStyles = makeStyles(() => ({
  root: {
    '&.MuiFormHelperText-root': {
      color: '#ff9800',
    },
  },
}));
function TagsCustomize({
  onKeyPress,
  handleOnChangeTagsCustomize,
  tagsCustomizeName,
  tagsCustomize,
  handleRemoveTagsCustomize,
  onClickAddTags,
  type,
}: Props) {
  const classes = useStyles();
  const helperTextClasses = helperTextStyles();
  return (
    <Fragment>
      <Box display="inline-flex" data-testid="tags-customize">
        <TextField
          id={type}
          label="Tags"
          placeholder="Type here and press enter"
          onKeyPress={onKeyPress}
          onChange={handleOnChangeTagsCustomize}
          value={tagsCustomizeName}
          data-test="tags-upload"
          className={classes.tagsCustomize}
          helperText={`${
            tagsCustomizeName ? 'Press Enter or Add Tag to submit the tag' : ' '
          }`}
          slotProps={{
            inputLabel: { shrink: true },
            htmlInput: { 'data-testid': 'input-tags' },
            formHelperText: { classes: helperTextClasses },
          }}
          variant="standard"
        />
        <div>
          <Button
            variant="contained"
            color="primary"
            className={classes.buttonAddTags}
            onClick={onClickAddTags}
            data-type={type}
            disabled={
              !tagsCustomizeName ||
              (Array.isArray(tagsCustomize) &&
                tagsCustomize.some((item) => item.value === tagsCustomizeName))
            }
            data-testid="add-tag"
          >
            Add Tag
          </Button>
        </div>
        <div className={classes.listTagsCustomize}>
          {Array.isArray(tagsCustomize) &&
            tagsCustomize.map((item) => {
              return (
                <Chip
                  label={item.value}
                  key={item.value}
                  data-name={item.value}
                  onDelete={() => handleRemoveTagsCustomize(item.value, type)}
                  data-testid="chip"
                />
              );
            })}
        </div>
      </Box>
    </Fragment>
  );
}
interface Props {
  onKeyPress: (event: KeyboardEvent<HTMLInputElement>) => void;
  handleOnChangeTagsCustomize: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  tagsCustomizeName: string;
  tagsCustomize: { value: string }[];
  handleRemoveTagsCustomize: (name: string, type: string) => void;
  onClickAddTags: (event: React.MouseEvent<HTMLElement>) => void;
  type: string;
}
export default TagsCustomize;
