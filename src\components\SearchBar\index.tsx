import { Component } from 'react';
import { connect, ConnectedProps } from 'react-redux';
import getApiRoot from '../../helpers/getApiRoot';
import getGraphQLEndpoint from '../../helpers/getGraphQLEndpoint';
/* @ts-expect-error: Seems like common component types are missing something */
import { attachSearchBar } from '@veritone/glc-advanced-search-bar';
/* @ts-expect-error: Seems like common component types are missing something */
import { CSPToV3Query } from 'veritone-csp-generator';
import {
  ON_SEARCH_BAR_QUERY_CHANGE,
  DEFAULT_SEARCH_ALL_MEDIA_QUERY,
  getSentiment,
  UPDATE_SEARCH_PARAMETERS,
  Query,
  UpdateParamType,
} from 'state/modules/search';
import { FETCH_MEDIA_AGGREGATIONS } from 'state/modules/dashboard';
import { FETCH_SUNBURST_AGGREGATIONS } from 'state/modules/sunburst';
import getApiAuthToken from '../../helpers/getApiAuthToken';
import { ROUTE_TABS } from 'state/modules/routing';
import { TABS } from 'state/modules/tabs';
import { openTdoPreview } from 'state/modules/tdosTable';
import config from '../../../config.json';
import * as styles from './styles.scss';

const { searchCategories } = config;
const DEFAULT_SENTIMENT_SCORES = {
  positive: '0.5',
  negative: '0.5',
};

export class SearchBar extends Component<Props> {
  state = {
    paramSearch: this.props.paramSearch,
  };

  componentDidMount() {
    this.attachSearchBar();
  }

  attachSearchBar = () => {
    const { paramSearch } = this.state;
    const { apiRoot, apiAuthToken } = this.props;
    const api = apiRoot + '/';

    attachSearchBar({
      auth: apiAuthToken,
      mountPoint: 'entity-search',
      onSearch: this.onSearch,
      api: api,
      color: 'rgb(50, 84, 144)',
      enabledEngineCategories: searchCategories,
      csp: paramSearch,
      showLoadSavedSearch: true,
      disableSavedSearch: false,
      isEditor: true,
    });
  };

  onSearch = (param: UpdateParamType) => {
    const {
      onSearchBarQueryChange,
      fetchMediaAggregations,
      fetchSunburstAggregations,
      sentiment,
      updateSearchParameters,
      navigateToTab,
      openTdoPreview,
    } = this.props;
    if (!param.csp) {
      onSearchBarQueryChange(DEFAULT_SEARCH_ALL_MEDIA_QUERY);
      fetchMediaAggregations();
      fetchSunburstAggregations();
      updateSearchParameters(null);
      openTdoPreview({ isShowPreview: false, initFullScreen: false });
      navigateToTab(TABS.Files);
      return this.setState({
        paramSearch: undefined,
      });
    }
    const customizedSearch = {
      sentiment: sentiment || DEFAULT_SENTIMENT_SCORES,
    };
    param.csp.customizedSearch = customizedSearch;
    const cspQuery: {
      query: Query;
      select: string[];
    } = CSPToV3Query(param.csp);

    const searchQuery = { ...cspQuery, ...{ index: ['mine'] } };

    const getData = () => {
      onSearchBarQueryChange(searchQuery);
      fetchMediaAggregations();
      fetchSunburstAggregations();
      updateSearchParameters(param.csp);
      this.setState({
        paramSearch: param,
      });
    };
    getData();
    openTdoPreview({ isShowPreview: false, initFullScreen: false });
    navigateToTab(TABS.Files);
  };

  render() {
    return (
      <div
        data-test="main-entity-search"
        id="entity-search"
        className={styles.searchBar}
        data-testid="entity-search"
      />
    );
  }
}

type Props = PropsFromRedux & {
  paramSearch?: {
    engineCategoryId: string;
  };
};

const mapState = (state: any) => ({
  apiRoot: getApiRoot(state),
  graphQLEndpoint: getGraphQLEndpoint(state),
  apiAuthToken: getApiAuthToken(state),
  sentiment: getSentiment(state),
});

const mapDispatch = {
  onSearchBarQueryChange: (searchQuery: {
    index: string[];
    select: string[];
  }) => ON_SEARCH_BAR_QUERY_CHANGE({ searchQuery }),
  fetchMediaAggregations: () => FETCH_MEDIA_AGGREGATIONS(),
  fetchSunburstAggregations: () => FETCH_SUNBURST_AGGREGATIONS(),
  updateSearchParameters: (csp: any) => UPDATE_SEARCH_PARAMETERS({ csp }),
  navigateToTab: (tabName: string) => ROUTE_TABS({ tab: tabName }),
  openTdoPreview: openTdoPreview,
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(SearchBar);
