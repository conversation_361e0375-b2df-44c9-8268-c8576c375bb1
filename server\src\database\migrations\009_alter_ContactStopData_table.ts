import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
      if (tableExists) {
        const hasNonBinaryOfficerColumn = knex.schema.hasColumn('ContactStopData', 'nonBinaryOfficer')
        const hasGenderOfOfficerColumn = knex.schema.hasColumn('ContactStopData', 'genderOfOfficer')

        if (hasNonBinaryOfficerColumn || hasGenderOfOfficerColumn) {
          if (hasNonBinaryOfficerColumn) {
            await knex.schema.raw(
              `ALTER TABLE ContactStopData DROP COLUMN nonBinaryOfficer;`
            );
          }
          if (hasGenderOfOfficerColumn) {
            await knex.schema.raw(
              `ALTER TABLE ContactStopData DROP COLUMN genderOfOfficer;`
            );
          }
          return true;
        }
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('ContactStopData')
  ])
