// veritone icons
@use '../../resources/styles/static-veritone-com/veritone-ui/veritone-icons-26/style.css'
  as *;
@use '../../resources/styles/static-veritone-com/engines/assets/v001/style.css'
  as *;
@use '../../resources/styles/daterangepicker/daterangepicker.css' as *;

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  left: 0;
  top: 0;
  font-size: 100%;
}

html {
  color: #222;
  font-size: 16px;
  line-height: 1.4;
  box-sizing: border-box;
}

body {
  background-color: #fff;
  min-width: 375px;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

p {
  font-size: 1.125rem;
  font-weight: 200;
  line-height: 1.8;
}

// workaround https://github.com/callemall/material-ui/issues/283
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
}

:global {
  #root {
    height: 100%;
  }
}

/*
 * Remove text-shadow in selection highlight:
 * https://twitter.com/miketaylr/status/12228805301
 *
 * These selection rule sets have to be separate.
 * Customize the background color to match your design.
 */

::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}

::selection {
  background: #b3d4fc;
  text-shadow: none;
}

/*
 * Remove the gap between audio, canvas, iframes,
 * images, videos and the bottom of their containers:
 * https://github.com/h5bp/html5-boilerplate/issues/440
 */

audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

/*
 * Remove default fieldset styles.
 */

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

/*
 * Allow only vertical resizing of textareas.
 */

textarea {
  resize: vertical;
}

/*
 * A better looking default horizontal rule
 */

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar {
  width: 8px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  background-color: #555;
}
