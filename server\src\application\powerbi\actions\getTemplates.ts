import { DatabaseError } from '../errors';
import { Context } from '../../types';

const getTemplates = async (context: Context) => {
  const { log, queries, data } = context;

  try {
    const templates = await queries.getAllTemplates();

    data.templates = templates;

    return context;
  } catch (e) {
    log.error('getTemplates failed', e);
    throw new DatabaseError(e);
  }
};

export default getTemplates;
