import { createAction, createReducer } from '@reduxjs/toolkit';
import callGraph<PERSON><PERSON><PERSON>, {
  createGQLFailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraph<PERSON>Api';
import { get } from 'lodash';

export const BOOT = createAction<Record<string, any>>(
  'boot saga: sequence all the stuff needed to start the app'
);
export const BOOT_FINISHED = createAction('boot saga finished');
export const CHECK_ADMIN_RIGHT = createGQLRequestAction('check admin right');
export const CHECK_ADMIN_RIGHT_SUCCESS =
  createGQLSuccessAction<CheckAdminRightResponse>('check admin right success');
export const CHECK_ADMIN_RIGHT_FAILURE = createGQLFailureAction(
  'check admin right failure'
);

export const GET_ROLES = createGQLRequestAction('get roles');
export const GET_ROLES_SUCCESS =
  createGQLSuccessAction<GetRolesResponse>('get roles success');
export const GET_ROLES_FAILURE = createGQLFailureAction('get roles failure');

const defaultState = {
  isBooting: false,
  bootDidFinish: false,
  myRights: null as CheckAdminRightResponse['myRights']['operations'] | null,
  checkingRights: false,
  checkingRightErrors: null,
  roles: [] as GetRolesResponse['me']['roles'],
};
const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(BOOT, (state) => ({
      ...state,
      isBooting: true,
      bootDidFinish: false,
    }))
    .addCase(BOOT_FINISHED, (state) => ({
      ...state,
      isBooting: false,
      bootDidFinish: true,
    }))
    .addCase(CHECK_ADMIN_RIGHT, (state) => ({
      ...state,
      checkingRights: true,
    }))
    .addCase(CHECK_ADMIN_RIGHT_SUCCESS, (state, action) => ({
      ...state,
      checkingRights: false,
      myRights: action.payload?.myRights?.operations || [],
    }))
    .addCase(CHECK_ADMIN_RIGHT_FAILURE, (state, action) => ({
      ...state,
      checkingRights: false,
      checkingRightErrors: get(action, 'payload.errors'),
    }))
    .addCase(GET_ROLES_SUCCESS, (state, action) => ({
      ...state,
      roles: action.payload.me.roles,
    }));
});

interface CheckAdminRightResponse {
  myRights: {
    operations: string[];
  };
}
export const checkAdminRight = () => (dispatch: any, getState: any) => {
  const query = `{
    myRights{
      operations
    }
  }`;

  return callGraphQLApi<CheckAdminRightResponse>({
    actionTypes: [
      CHECK_ADMIN_RIGHT,
      CHECK_ADMIN_RIGHT_SUCCESS,
      CHECK_ADMIN_RIGHT_FAILURE,
    ],
    query,
    dispatch,
    getState,
  });
};

interface GetRolesResponse {
  me: {
    roles: {
      appName: string;
      id: string;
      name: string;
    }[];
  };
}
export const getRoles = () => (dispatch: any, getState: any) => {
  const query = `{
        me {
          roles {
            appName
            id
            name
          }
        }
     }`;
  return callGraphQLApi<GetRolesResponse>({
    actionTypes: [GET_ROLES, GET_ROLES_SUCCESS, GET_ROLES_FAILURE],
    query,
    dispatch,
    getState,
  });
};
export default reducer;
export const namespace = 'app';
export const local = (state: any) => state[namespace] as typeof defaultState;

export const boot = (options: Record<string, any> = {}) => BOOT(options);

export const bootFinished = () => BOOT_FINISHED();

export const isBooting = (state: any) => local(state).isBooting;
export const bootDidFinish = (state: any) => local(state).bootDidFinish;
export const getRights = (state: any) => local(state).myRights;
export const getMeRoles = (state: any) => local(state).roles;
