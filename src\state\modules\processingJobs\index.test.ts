import { DateTime } from 'luxon';
import { ProcessingJob } from './models';
import { fetchProcessingJobGroupedByTdoId } from './index';
import { GQLApi } from '../../../helpers/gqlApi';
import testData from './index.test.data.json';

const mockGetProcessingJobsByOffset = jest.fn();
jest.mock('../../../helpers/gqlApi', () => {
  return {
    GQLApi: jest.fn().mockImplementation(() => {
      return {
        getProcessingJobsByOffset: mockGetProcessingJobsByOffset,
      };
    }),
  };
});

describe('processingJobs', () => {
  beforeEach(() => {
    mockGetProcessingJobsByOffset.mockReset();
  });

  it('fetch 1 processing job', async () => {
    const data = {
      jobs: {
        records: testData.data.jobs.records.slice(0, 1),
      },
    };
    mockGetProcessingJobsByOffset.mockReturnValueOnce(
      Promise.resolve({
        data: data,
      })
    );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const limit = 10;
    const offset = 0;
    const status: string[] = [];
    const dateTimeRangeFilter = 2;
    const remainingProcessingJobs: ProcessingJob[] = [];

    const isShowCustomRange = false;
    const got = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );
    const want = {
      offset: 10,
      remainingProcessingJobs: [],
      results: [
        {
          jobs: [
            {
              id: 'job-id-01',
              target: {
                id: 'tdo-id-01',
                name: 'file-name-01.mp4',
                details: {
                  veritoneFile: {
                    filename: 'file-name-01.mp4',
                  },
                },
              },
              status: 'complete',
              tasks: {
                records: [
                  {
                    id: 'job-id-01-task-id-01',
                    status: 'complete',
                  },
                ],
              },
              createdDateTime: '2023-05-30T18:08:34.001Z',
              modifiedDateTime: '2023-05-30T18:08:37.001Z',
            },
          ],
          modifiedDateTime: '2023-05-30T18:08:37.001Z',
          name: 'file-name-01.mp4',
          targetId: 'tdo-id-01',
        },
      ],
    };

    expect(got).toEqual(want);
  });

  it('fetch 0 processing job', async () => {
    mockGetProcessingJobsByOffset.mockReturnValueOnce(
      Promise.resolve({
        data: { jobs: { records: [] } },
      })
    );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const limit = 10;
    const offset = 0;
    const status: string[] = [];
    const dateTimeRangeFilter = 2;
    const remainingProcessingJobs: ProcessingJob[] = [];

    const isShowCustomRange = false;
    const result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );

    expect(result.results.length).toBe(0);
    expect(result.remainingProcessingJobs.length).toBe(0);
  });

  it('fetch 10 processing jobs', async () => {
    const data = {
      jobs: {
        records: testData.data.jobs.records.slice(0, 10),
      },
    };
    mockGetProcessingJobsByOffset
      .mockReturnValueOnce(
        Promise.resolve({
          data: data,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: { jobs: { records: [] } },
        })
      );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const limit = 10;
    const offset = 0;
    const status: string[] = [];
    const dateTimeRangeFilter = 2;
    const remainingProcessingJobs: ProcessingJob[] = [];

    const isShowCustomRange = false;
    const result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );

    expect(result.results.length).toBe(10);
    expect(result.remainingProcessingJobs.length).toBe(0);
  });

  it('fetch 12 processing jobs', async () => {
    const data1 = {
      jobs: {
        records: testData.data.jobs.records.slice(0, 10),
      },
    };
    const data2 = {
      jobs: {
        records: testData.data.jobs.records.slice(10, 12),
      },
    };
    mockGetProcessingJobsByOffset
      .mockReturnValueOnce(
        Promise.resolve({
          data: data1,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: data2,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: { jobs: { records: [] } },
        })
      );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const limit = 10;
    const status: string[] = [];
    const dateTimeRangeFilter = 2;

    const isShowCustomRange = false;

    // 1st page
    let offset = 0;
    let remainingProcessingJobs: ProcessingJob[] = [];
    let result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );

    expect(result.results.length).toBe(10);
    expect(result.remainingProcessingJobs.length).toBe(2);

    // 2nd page
    offset = 10;
    remainingProcessingJobs = [...result.remainingProcessingJobs];
    result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );
    expect(result.results.length).toBe(2);
    expect(result.remainingProcessingJobs.length).toBe(0);
  });

  it('fetch 12 processing jobs with limit 20', async () => {
    const data = {
      jobs: {
        records: testData.data.jobs.records.slice(0, 12),
      },
    };
    mockGetProcessingJobsByOffset
      .mockReturnValueOnce(
        Promise.resolve({
          data: data,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: { jobs: { records: [] } },
        })
      );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const status: string[] = [];
    const dateTimeRangeFilter = 2;

    const isShowCustomRange = false;

    const limit = 20;
    const offset = 0;
    const remainingProcessingJobs: ProcessingJob[] = [];
    const result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );

    expect(result.results.length).toBe(12);
    expect(result.remainingProcessingJobs.length).toBe(0);
  });

  it('fetch processing jobs for grouping job by tdos', async () => {
    // the 1- 4th tdo each has 2 jobs.
    // the 5th tdo has 3 jobs.
    const data1 = {
      jobs: {
        records: testData.data2.jobs.records.slice(0, 10),
      },
    };
    const data2 = {
      jobs: {
        records: testData.data2.jobs.records.slice(10, 11),
      },
    };
    mockGetProcessingJobsByOffset
      .mockReturnValueOnce(
        Promise.resolve({
          data: data1,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: data2,
        })
      )
      .mockReturnValueOnce(
        Promise.resolve({
          data: { jobs: { records: [] } },
        })
      );
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    const status: string[] = [];
    const dateTimeRangeFilter = 2;
    const remainingProcessingJobs: ProcessingJob[] = [];

    const isShowCustomRange = false;
    const limit = 10;
    const offset = 0;
    const result = await fetchProcessingJobGroupedByTdoId(
      gql,
      limit,
      offset,
      status,
      dateTimeRangeFilter,
      remainingProcessingJobs,
      DateTime.now(),
      DateTime.now(),
      isShowCustomRange
    );
    // 5 tdo/file
    expect(result.results.length).toBe(5);
    // 1st tdo has 2 jobs.
    expect(result.results[0]?.jobs.length).toBe(2);
    // no duplicated jobs
    expect(result.results[0]?.jobs[0]?.id).not.toBe(
      result.results[0]?.jobs[1]?.id
    );
    // 5th tdo has 3 jobs.
    expect(result.results[4]?.jobs.length).toBe(3);
    // no duplicated jobs
    expect(result.results[4]?.jobs[0]?.id).not.toBe(
      result.results[4]?.jobs[1]?.id
    );
    expect(result.results[4]?.jobs[1]?.id).not.toBe(
      result.results[4]?.jobs[2]?.id
    );

    expect(result.remainingProcessingJobs.length).toBe(0);
  });
});
