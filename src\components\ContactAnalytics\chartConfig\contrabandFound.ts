import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    limit: 10000
                    name: "contrabandOrEvidence"
                    field: "contrabandOrEvidence"
                    operator: "term"
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'contrabandAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Contraband',
  filterTextType: 'Contraband by Type',
  filterType: 'contrabandOrEvidence',
  filterTerms: [
    'Firearm(s)',
    'Drugs/narcotics',
    'Drug Paraphernalia',
    'Ammunition',
    'Weapon(s) other than a firearm',
    'Alcohol',
    'Cell phone(s) or electronic device(s)',
    'Suspected Stolen property',
    'Money',
    'Other Contraband or evidence',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = data.contrabandAggregation
      .map((dateAgg) => {
        return {
          date: dateAgg.key_as_string,
          'Firearm(s)': 0,
          'Drugs/narcotics': 0,
          'Drug Paraphernalia': 0,
          Ammunition: 0,
          'Weapon(s) other than a firearm': 0,
          Alcohol: 0,
          'Cell phone(s) or electronic device(s)': 0,
          'Suspected Stolen property': 0,
          Money: 0,
          'Other Contraband or evidence': 0,
          ...dateAgg.contrabandOrEvidence.buckets.reduce(
            (acc: { [key: string]: number }, e) => {
              acc[e.key] = e.doc_count;
              return acc;
            },
            {} // this reduce need to return an object to update default values
          ),
        };
      })
      .reduce(
        (
          acc: ChartData[],
          e: { date: string; [key: string]: number | string }
        ) => {
          if (config.useFilter === 'filter') {
            acc.push({
              date: e.date,
              [`${config.filter}`]: e[`${config.filter}`],
            } as ChartData);
          } else {
            acc.push(e);
          }
          return acc;
        },
        []
      );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'Firearm(s)';
    series.dataFields.dateX = 'date';
    series.name = 'Firearm(s)';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series2 = chart.series.push(new am4charts.LineSeries());
    series2.strokeWidth = config.lineWidth;
    series2.dataFields.valueY = 'Drugs/narcotics';
    series2.dataFields.dateX = 'date';
    series2.name = 'Drugs/narcotics';
    series2.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series3 = chart.series.push(new am4charts.LineSeries());
    series3.strokeWidth = config.lineWidth;
    series3.dataFields.valueY = 'Drug Paraphernalia';
    series3.dataFields.dateX = 'date';
    series3.name = 'Drug Paraphernalia';
    series3.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series4 = chart.series.push(new am4charts.LineSeries());
    series4.strokeWidth = config.lineWidth;
    series4.dataFields.valueY = 'Ammunition';
    series4.dataFields.dateX = 'date';
    series4.name = 'Ammunition';
    series4.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series5 = chart.series.push(new am4charts.LineSeries());
    series5.strokeWidth = config.lineWidth;
    series5.dataFields.valueY = 'Weapon(s) other than a firearm';
    series5.dataFields.dateX = 'date';
    series5.name = 'Weapon(s) other than a firearm';
    series5.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series6 = chart.series.push(new am4charts.LineSeries());
    series6.strokeWidth = config.lineWidth;
    series6.dataFields.valueY = 'Alcohol';
    series6.dataFields.dateX = 'date';
    series6.name = 'Alcohol';
    series6.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series7 = chart.series.push(new am4charts.LineSeries());
    series7.strokeWidth = config.lineWidth;
    series7.dataFields.valueY = 'Cell phone(s) or electronic device(s)';
    series7.dataFields.dateX = 'date';
    series7.name = 'Cell phone(s) or electronic device(s)';
    series7.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series8 = chart.series.push(new am4charts.LineSeries());
    series8.strokeWidth = config.lineWidth;
    series8.dataFields.valueY = 'Suspected Stolen property';
    series8.dataFields.dateX = 'date';
    series8.name = 'Suspected Stolen property';
    series8.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series9 = chart.series.push(new am4charts.LineSeries());
    series9.strokeWidth = config.lineWidth;
    series9.dataFields.valueY = 'Money';
    series9.dataFields.dateX = 'date';
    series9.name = 'Money';
    series9.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series10 = chart.series.push(new am4charts.LineSeries());
    series10.strokeWidth = config.lineWidth;
    series10.dataFields.valueY = 'Other Contraband or evidence';
    series10.dataFields.dateX = 'date';
    series10.name = 'Other Contraband or evidence';
    series10.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  contrabandAggregation: {
    key: number;
    key_as_string: string;
    doc_count: number;
    contrabandOrEvidence: {
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
      buckets: { key: string; doc_count: number }[];
    };
  }[];
}

interface ChartData {
  [key: string]: number | string;
  date: string;
}
