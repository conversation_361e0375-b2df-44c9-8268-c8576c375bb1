// Copied from the veritone-dev-env package
import react from 'eslint-plugin-react';
import lodash from 'eslint-plugin-lodash';
import importPlugin from 'eslint-plugin-import';
import prettier from 'eslint-plugin-prettier';
import reactHooks from 'eslint-plugin-react-hooks';
import jambitTypedReduxSaga from '@jambit/eslint-plugin-typed-redux-saga';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import pluginCypress from 'eslint-plugin-cypress/flat'
// import testingLibrary from 'eslint-plugin-testing-library';
// import jestDom from 'eslint-plugin-jest-dom'
import eslintConfigPrettier from "eslint-config-prettier";
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import pluginPromise from 'eslint-plugin-promise'
import tseslint from 'typescript-eslint'
import cspellESLintPluginRecommended from '@cspell/eslint-plugin/recommended';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
export default [
  {
    ignores: [
    ],
  },
  js.configs.recommended,
  ...tseslint.configs.recommended,
  importPlugin.flatConfigs.recommended,
  react.configs.flat.recommended,
  pluginCypress.configs.recommended,
  pluginPromise.configs['flat/recommended'],
  reactHooks.configs['recommended-latest'],
  eslintConfigPrettier,
  eslintPluginPrettierRecommended,
  cspellESLintPluginRecommended,
  {
    plugins: {
      // lodash,
      '@jambit/typed-redux-saga': jambitTypedReduxSaga,
    },
    languageOptions: {
      globals: {
        // ...globals.browser,
        ...globals.amd,
        ...globals.node,
        ...globals.jest,
        React: 'readonly',
        Promise: 'readonly',
        WeakMap: 'readonly',
        JSX: true,
        module: true,
      },

      parser: tseslint.parser,
      ecmaVersion: 8,
      sourceType: 'module',

      parserOptions: {
        ecmaFeatures: {
          impliedStrict: true,
          jsx: true,
          experimentalObjectRestSpread: true,
          legacyDecorators: true,
        },
        projectService: true,
        projectService: {
          allowDefaultProject: [
            'src/components/ContactAnalytics/icons/Copy.js',
            'src/components/SDKComponent/FilePicker/story.js',
          ]
        }
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
      'import/resolver': {
        typescript: {},
        // node: {
        //   extensions: ['.js', '.jsx', '.ts', '.tsx', '.d.ts'],
        // }
      },
    },
    rules: {
      'no-param-reassign': 'warn',
      'react/sort-comp': 'warn',
      'react/prop-types': 'off',
      // TODO: Enable these and fix all the errors
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
          // TODO: Does this make sense?
          enforceForJSX: false,
        }
      ],
      // -----------------------------------------
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          "args": "all",
          "argsIgnorePattern": "^_",
          "caughtErrors": "all",
          "caughtErrorsIgnorePattern": "^_",
          "destructuredArrayIgnorePattern": "^_",
          "varsIgnorePattern": "^_",
          "ignoreRestSiblings": true
        }
      ],
      '@cspell/spellchecker': [
        'error',
        {
          checkIdentifiers: false,
          checkComments: false,
          ignoreImports: true,
        }
      ],
    }
  },
  {
    files: ['src/**/*.ts'],
    // ignores: ['./**/*.spec.ts', './**/*.test.ts'],
    // excludedFiles: ["./**/*.spec.ts"],
    rules: {
      '@jambit/typed-redux-saga/use-typed-effects': ['error', 'macro'],
      '@jambit/typed-redux-saga/delegate-effects': 'error',
    },
  },
  {
    files: ['server/src/database/**/*.ts'],
    rules: {
      'promise/always-return': 'off',
    }
  },
  {
    files: ['cypress/**/*'],
    rules: {
      // TODO: Enable and fix
      '@cspell/spellchecker': 'off',
    }
  },
  {
    files: [
      'server/src/application/__test__/**/*.ts',
      'server/src/database/migrations/*.ts',
      'server/src/application/powerbi/schemas.ts',
    ],
    rules: {
      'prettier/prettier': 'off',
    }
  },
  {
    files: ['**/fixtures.ts'],
    rules: {
      '@cspell/spellchecker': 'off',
    }
  }
]
