import { render, screen, fireEvent } from '@testing-library/react';
import { TdosTable } from './index';

describe('TdosTable', () => {
  const props = {
    items: [
      {
        id: 'id1',
        name: 'name1',
        createdDateTime: '2019-07-03T04:57:39.258Z',
        startDateTime: '2019-07-03T04:30:00.000Z',
        stopDateTime: '2019-07-03T04:30:58.043Z',
        details: {
          veritoneFile: {
            filename: 'filename1',
            mimetype: 'audio/mp4',
            segmented: true,
          },
          tags: [],
          name: 'name1',
        },
        icon: '',
      },
      {
        id: 'id2',
        name: 'name2',
        createdDateTime: '2019-07-03T04:57:39.258Z',
        startDateTime: '2019-07-03T04:30:00.000Z',
        stopDateTime: '2019-07-03T04:30:58.043Z',
        details: {
          veritoneFile: {
            filename: 'filename2',
            mimetype: 'audio/mp4',
            segmented: true,
          },
          tags: [],
          name: 'name2',
        },
        icon: '',
      },
    ],
    engineCategoriesIds: {
      '1000': { engineCategoriesIds: ['925e8039-5246-4ced-9f2b-b456d0b57ea1'] },
    },
    totalResults: 100,
    limit: 10,
    onNextPage: jest.fn(),
    onLimitChange: jest.fn(),
    onSelectionChange: jest.fn(),
    allRowsSelectedIndexes: [1, 2],
    engineCategories: {
      '581dbb32-ea5b-4458-bd15-8094942345e3': {
        id: '581dbb32-ea5b-4458-bd15-8094942345e3',
        iconClass: 'icon-engine-transcode',
        name: 'Transcode',
      },
    },
    onExportingJobToFlow: jest.fn(),
    currentPage: 1,
    updateCurrentPage: jest.fn(),
    updateSelectedAll: jest.fn(),
    isSelectedAll: false,
    indeterminate: false,

    updateTdoIdPreview: jest.fn(),
    isShowPreview: false,
    tdoIdPreview: 'tdoIdPreview',
    updateIsShowPreview: jest.fn(),
    getTranscriptPreview: jest.fn(),
    initFullScreen: false,
    openTdoPreview: jest.fn(),
    noData: false,
    appConfig: {},
    searchBarQuery: {},
    state: {},
    openModalReprocess: jest.fn(),
    navigateToFile: jest.fn(),
    navigateToTab: jest.fn(),
    redactUrl: '',
    enableRedactNavigation: '',
    searchParameters: [],
    onSortChange: jest.fn(),
    deletedTdos: [],
    movedTdos: [],
  };

  // set up screen object for TdosTable for test
  beforeEach(() => {
    return render(<TdosTable {...(props as any)} />);
  });

  it('should have tdo table columns', () => {
    screen.queryByText('Name');
    screen.queryByText('Type');
    screen.queryByText('ID');
    screen.queryByText('Duration');
    screen.queryByText('Creation Date');
    screen.queryByText('Engine Run');
    screen.queryByText('Tags');
  });

  it('should have tdo table rows', () => {
    screen.queryByText('filename1');
    screen.queryByText('filename2');
  });

  it('should have current page and page size', () => {
    screen.queryByText('1-10 of 100');
  });

  it('should be able to go to next page', () => {
    screen.queryByText('1-10 of 100');
    fireEvent.click(screen.getAllByRole('button')[1]!);
    screen.queryByText('11-20 of 100');
  });

  it('should be able to select all files', () => {
    fireEvent.click(screen.getByTestId('select-all'));
    screen.queryByText('Selected 100 Files');
  });

  it('should be able to select individual file', () => {
    fireEvent.click(screen.getAllByRole('checkbox')[0]!);
    screen.queryByText('Selected 1 Files');
  });
});
