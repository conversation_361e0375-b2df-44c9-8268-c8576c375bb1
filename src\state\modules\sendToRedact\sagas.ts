import {
  takeLatest,
  put,
  select,
  all,
  call,
  fork,
} from 'typed-redux-saga/macro';
import { showNotification } from '../snackbar';
import { getSearchResultTdosAll } from '../search';
import getRedactDataRegistryId from '../../../helpers/getRedactDataRegistryId';
import { getApiConnectionParameters } from '../../../helpers/apiHelper';
import { TDO_TAGS_UPDATED } from '../tags';
import {
  sendToRedact,
  dedupById,
  isAllAudioVideo,
  SEND_TO_REDACT,
  SEND_TO_REDACT_SUCCESS,
  SEND_TO_REDACT_FAILURE,
} from '.';
import { Action } from 'state/sagas';
import { AlertColor } from '@mui/material/Alert';
function* watchSendToRedact() {
  yield* all([
    takeLatest(SEND_TO_REDACT, handleSendToRedact),
    takeLatest(
      [SEND_TO_REDACT_SUCCESS, SEND_TO_REDACT_FAILURE],
      handleSendToRedactResults
    ),
  ]);
}

function* handleSendToRedactResults(
  action: Action<{ message: string; error: any }>
) {
  let status: AlertColor = 'success';
  if (action?.payload?.error) {
    status = 'error';
  }
  yield* put(showNotification(action.payload.message, status));
}

function* handleSendToRedact(action: Action<{ tdoIds: string[] }>) {
  const { tdoIds } = action.payload;
  if (!tdoIds || tdoIds.length === 0) {
    const message = 'No file selected.';
    const error = new Error(message);
    yield* put(SEND_TO_REDACT_FAILURE({ error, message }));
    return;
  }

  if (tdoIds.length > 10) {
    const message =
      'You can only send 10 files to Redact at a time. Please make a smaller selection and try again.';
    const error = new Error(message);
    yield* put(SEND_TO_REDACT_FAILURE({ error, message }));
    return;
  }

  const searchResultTdos = yield* select(getSearchResultTdosAll); // TODO - refactor
  const dedupedTdoList = dedupById(searchResultTdos);
  const tdosToSend = dedupedTdoList.filter(
    (tdo) => tdoIds.includes(tdo.id) && tdo.status !== 'error'
  );
  if (!isAllAudioVideo(tdosToSend)) {
    const message =
      'The Redact App currently supports audio/video files. Please remove any non-audio/video files from your selection and try again.';
    const error = new Error(message);
    yield* put(SEND_TO_REDACT_FAILURE({ error, message }));
    return;
  }

  try {
    const redactDataRegistryId = yield* select(getRedactDataRegistryId);
    const { endpoint, token, veritoneAppId } = yield* select(
      getApiConnectionParameters
    );
    const result = yield* call(
      sendToRedact,
      endpoint,
      token!,
      // The ! is safe because when the app booting at the first time, token is null
      // the app call function fetchUserWithStoredTokenOrCookie when the app is booting.
      // In generator fetchUserWithStoredTokenOrCookie function they try to call fetchUser function,
      // and received the payload from fetchUser response.
      // This non null assertion just allow running into that case without change so much in code
      veritoneAppId,
      redactDataRegistryId,
      tdosToSend
    );
    yield* put(TDO_TAGS_UPDATED({ ...result }));
    const message = `Sent ${tdosToSend.length} files to Redact.`;
    yield* put(SEND_TO_REDACT_SUCCESS({ message }));
  } catch (error) {
    const message = 'Fail to send to Redact.';
    yield* put(SEND_TO_REDACT_FAILURE({ error, message }));
    console.error('Fail to send to Redact.', error);
  }
}

export function* sendToRedactSaga() {
  yield* all([fork(watchSendToRedact)]);
}
