import { Knex } from 'knex';

exports.up = (knex: Knex) =>
    Promise.all([
        knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
            if (tableExists) {
                const hasConsentType = await knex.schema.hasColumn('ContactStopData', 'consentType');
                const hasProbableCause = await knex.schema.hasColumn('ContactStopData', 'probableCause');
                const hasProbableCauseCode = await knex.schema.hasColumn('ContactStopData', 'probableCauseCode');

                if (!hasConsentType) {
                    await knex.schema.raw(
                        `ALTER TABLE ContactStopData ADD consentType varchar(256);`
                    );
                }
                if (!hasProbableCause) {
                    await knex.schema.raw(
                        `ALTER TABLE ContactStopData ADD probableCause varchar(2048);`
                    );
                }
                if (!hasProbableCauseCode) {
                    await knex.schema.raw(
                        `ALTER TABLE ContactStopData ADD probableCauseCode varchar(256);`
                    );
                }
                return true;
            }
        })
    ])

exports.down = (knex: Knex) =>
    Promise.all([
        knex.schema.dropTable('ContactStopData')
    ])
