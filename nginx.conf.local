server {
    listen 443 ssl;
    server_name         local.veritone.com;
    ssl_certificate     local.veritone.com.pem;
    ssl_certificate_key local.veritone.com-key.pem;
    ssl_protocols       TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers         HIGH:!aNULL:!MD5;


    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    server_tokens off;
    # can't run this under we work out how to build nginx
    # more_clear_headers Server;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    location /api {
        client_max_body_size 5M;
        proxy_pass http://localhost:3002;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #error_page 404 and 403 to 200 and index.html
    error_page 404 =200 /index.html;
    error_page 403 =200 /index.html;
    location = /index.html {
        root /usr/share/nginx/html;
        add_header Cache-Control "must-revalidate";
        internal;
    }

    location ~* \.(css)$ {
        root /usr/share/nginx/html;
    }

    location ~* \.(mjs|js)$ {
        root /usr/share/nginx/html;
        types {
            text/javascript js mjs;
        }
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
