import {
  Before,
  Then,
  When,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
  mediaListPage.goToMediaListPage();
});

When('The user clicks on the search bar', () => {
  mediaListPage.clicksOnTheSearchBar();
});

When(
  'The user clicks the {string} button on the search bar pop-up',
  (buttonText: string) => {
    mediaListPage.clicksButtonOnSearchBarPopup(buttonText);
  }
);

When(
  'The user searches by keyword {string} to find file {string}',
  (keyword: string, fileName: string) => {
    mediaListPage.searchByKeyword(keyword, fileName);
  }
);

When(
  'The user searches by tag {string} to find file {string}',
  (tag: string, fileName: string) => {
    mediaListPage.searchByTag(tag, fileName);
  }
);

When(
  'The user searches by tag {string} to find file {string} with exclude option',
  (tag: string, fileName: string) => {
    mediaListPage.searchByTag(tag, fileName, { exclude: true });
  }
);

When(
  'The user searches by bookmark {string} to find file {string}',
  (bookmark: string, fileName: string) => {
    mediaListPage.searchByBookmark(bookmark, fileName);
  }
);

When(
  'The user searches by time from {string} to {string}',
  (startTime: string, endTime: string) => {
    mediaListPage.searchByTime(startTime, endTime);
  }
);

When('The user searches by excluding the tag {string}', (tagName: string) => {
  mediaListPage.searchByExcludingTag(tagName);
});

When('The user adds the following search criteria:', (dataTable: DataTable) => {
  mediaListPage.addSearchCriteria(dataTable);
});

When('The user removes the search for {string}', (keyword: string) => {
  mediaListPage.removeSearch(keyword);
});

When('The user clicks on the file {string}', (fileName: string) => {
  mediaListPage.clickOnFile(fileName);
});

When('The user selects the {string} category', (categoryName: string) => {
  mediaListPage.selectCategory(categoryName);
});

Then('The search bar pop-up is displayed', () => {
  mediaListPage.verifySearchBarPopupDisplayed();
});

Then('The search bar pop-up is no longer visible', () => {
  mediaListPage.verifySearchBarPopupNotVisible();
});

Then('The pop-up contains a list of searchable options', () => {
  mediaListPage.verifyPopupContainsSearchableOptions();
});

Then('The default focus is on {string}', (text: string) => {
  mediaListPage.verifyDefaultFocus(text);
});

Then('The text {string} is displayed', (text: string) => {
  mediaListPage.verifyTextDisplayed(text);
});

Then(
  'A text box to input phrase to search exists with placeholder {string}',
  (placeholder: string) => {
    mediaListPage.verifyTextBoxExists(placeholder);
  }
);

Then(
  'The {string} and {string} buttons are displayed',
  (closeButtonText: string, addButtonText: string) => {
    mediaListPage.verifyButtonsDisplayed(closeButtonText, addButtonText);
  }
);

Then('The file list is displayed', () => {
  mediaListPage.verifyFileListDisplayed();
});

Then('The file list should display {string}', (message: string) => {
  mediaListPage.verifyFileListMessage(message);
});

Then(
  /^The file "([^"]*)" is( not)? displayed in the list$/,
  (fileName: string, negation: string | undefined) => {
    mediaListPage.verifyFileInList(fileName, negation);
  }
);

Then('The media details page is displayed', () => {
  mediaListPage.verifyMediaDetailsPageDisplayed();
});

Then(
  'The transcript contains the following highlighted keywords:',
  (dataTable: DataTable) => {
    mediaListPage.verifyTranscriptHighlightedKeywords(dataTable);
  }
);

Then(
  'The transcript focuses on the first highlighted keyword {string}',
  (keyword: string) => {
    mediaListPage.verifyTranscriptFocusOnKeyword(keyword);
  }
);

Then(
  'The OCR results contain the highlighted text {string}',
  (ocrText: string) => {
    mediaListPage.verifyOCRResultsContainText(ocrText);
  }
);
