import { createReducer } from '@reduxjs/toolkit';
import { mean, isNaN, get, isArray, isEmpty, cloneDeep } from 'lodash';
import update from 'immutability-helper';
export const namespace = 'uploadFile';
import * as actions from './actions';
import { getDateTimeNow } from '../../utils/util';
import Handlebars from 'handlebars';
import {
  Engine,
  EnginesSelected,
  Template,
  Library,
  DagTemplate,
  EngineCategory,
  ContentTemplate,
  UploadResult,
  DagTemplatesByCategorySelected,
  DagTemplatesByCategory,
  EngineByCategories,
  UploadResultEdit,
  LibrariesByCategories,
} from './models';
import { Folder } from '../../../model';
import { arrayHasLength } from '@utils';

const makeEmptyUploadResultEdit = (): UploadResultEdit => {
  return {
    fileName: '',
    tagsEdit: [],
    programImage: '',
    getUrlProgramImage: '',
    programLiveImage: '',
    getUrlProgramLiveImage: '',
    dateTime: '',
    uploadResultId: [],
  };
};
type FileState = 'overview' | 'selecting' | 'uploading' | 'complete';
const defaultPickerState = {
  open: false,
  state: 'overview' as FileState, // selecting | uploading | complete
  progressPercentByFileKey: {} as {
    [key: string]: {
      name: string;
      type: string;
      size: number;
      percent: number;
    };
  },
  success: false as boolean | null,
  error: false as string | boolean | null,
  warning: false as string | boolean | null,
  uploadResult: [] as UploadResult[],
  checkedFile: [] as number[],
  currentEngineCategory: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
  uploadResultEdit: makeEmptyUploadResultEdit(),
  isOpenModalUpload: false,
  loadingSaveUpload: false,
  enginesSelected: [] as EnginesSelected[],
  contentTemplateSelected: [] as ContentTemplate[],
  tagsCustomize: [] as { value: string }[],
  isReprocess: false,
  tdoIdsNotSupported: [],
  tdoIdsSupported: [],
  enginesSelectedSupported: [] as EnginesSelected[],
  isShowConfirmSaveTemplate: false,
  templateName: '',
  templateSelected: '',
  loadingRemoveTemplate: false,
  percentageFilesUploaded: 0,
  showProcessByCategory: {} as { [key: string]: boolean },
  dagTemplateIdSelected: '',
};

const defaultState = {
  ...defaultPickerState,
  selectedCurrentFolder: undefined as undefined | Folder,
  enginesDefault: undefined as Engine[] | undefined,
  dagTemplateByCategorySelected: {} as DagTemplatesByCategorySelected,
  engineByCategories: {} as EngineByCategories,
  engineCategories: [] as EngineCategory[],
  templates: [] as Template[],
  contentTemplates: [] as ContentTemplate[],
  librariesByCategories: undefined as undefined | LibrariesByCategories,
  selectedFolder: undefined as undefined | Folder,
  dagTemplates: undefined as undefined | DagTemplate[],
  dagTemplatesByCategory: undefined as undefined | DagTemplatesByCategory,
};

const currentEngineCategoryDefault = '67cd4dd0-2f75-445d-a6f0-2f297d6cd182';

const makeLibrariesByCategories = (
  entityIdentifierTypes: string[],
  libraries: Library[]
) => {
  const availableLibraries = libraries
    .filter((library) =>
      (get(library, 'libraryType.entityIdentifierTypes', []) || []).some(
        ({ id }: { id: string }) => entityIdentifierTypes.includes(id)
      )
    )
    .reduce<Record<string, Library>>(
      (libraries, library) => ({
        ...libraries,
        [library.id]: library,
      }),
      {}
    );
  return isEmpty(availableLibraries) ? null : availableLibraries;
};

const makeDataEngine = (data: Engine[], engineId: string) => {
  return data.map((item) => {
    if (item.id === engineId) {
      return {
        ...item,
        isSelected: true,
      };
    }
    return {
      ...item,
    };
  });
};

const hasGlcFastIngestor = (
  standaloneJobTemplates: {
    type: string;
    template: { template: string };
  }[]
) => {
  try {
    const templateString =
      Array.isArray(standaloneJobTemplates) &&
      standaloneJobTemplates.length &&
      standaloneJobTemplates[1]?.template.template;
    if (templateString) {
      const template = convertTemplateStringToObject(templateString);
      // First engine in standaloneJobTemplates
      const engineId =
        template.tasks && template.tasks.length && template.tasks[0].engineId;
      if (actions.ENGINE_GLC_FAST_INGESTOR === engineId) {
        return true;
      }
    }
  } catch (error) {
    console.error('failed to check GlcFastIngestor', error);
    return false;
  }
};

const makeEngineByCategory = (
  value: Engine,
  supportedDefault: { [key: string]: string[] }
): Engine => {
  const { supportedInputFormats, id, standaloneJobTemplates } = value;
  const hasAudioTypes =
    Array.isArray(supportedInputFormats) &&
    supportedInputFormats.filter((item) => item.includes('audio')).length > 0;
  const newSupportedInputFormats = [];
  if (hasAudioTypes) {
    newSupportedInputFormats.push(
      ...actions.AUDIO_MIME_TYPES,
      ...actions.VIDEO_MIMES_TYPE
    );
  } else if (hasGlcFastIngestor(standaloneJobTemplates)) {
    newSupportedInputFormats.push(
      ...actions.AUDIO_MIME_TYPES,
      ...actions.VIDEO_MIMES_TYPE
    );
  } else {
    const supportedDefaultFormats = supportedDefault[id];
    if (supportedDefaultFormats) {
      newSupportedInputFormats.push(
        ...(supportedInputFormats || []),
        ...supportedDefaultFormats
      );
    }
  }

  return {
    ...value,
    supportedInputFormats: newSupportedInputFormats,
    isSelected: false,
    fields: makeFieldEngine(cloneDeep(value)),
  };
};

const convertTemplateStringToObject = (templateString: string) => {
  const handlebarsTemplate = Handlebars.compile(templateString, {
    strict: true,
  });
  const params = {
    ENGINE_ID: '',
    TARGET_ID: '',
    TDO_ID: '',
    UPLOAD_URL: '',
    pangeaMTSrcLang: '',
    pangeaMTTgtLang: '',
    libraryId: '',
  };
  const payloadString = handlebarsTemplate(params);
  return JSON.parse(payloadString);
};

const makeFieldEngine = (value: Engine) => {
  let fields =
    (Array.isArray(value.fields) &&
      value.fields.length &&
      value.fields.map((item) => {
        if (['MultiPicklist', 'Picklist'].includes(item.type)) {
          return {
            ...item,
            defaultValue:
              item.defaultValue || get(item, 'options[0].value', ''),
            options: item.options.sort((a, b) => a.key.localeCompare(b.key)),
          };
        }
        return item;
      })) ||
    [];
  if (value.id === actions.ENGINE_FILE_TRANSLATOR) {
    fields = fields
      .slice(1, 3)
      .sort((a: { label: string }, b: { label: string }) =>
        a.label.localeCompare(b.label)
      );
  }
  return fields;
};

const makeDagTemplateByCategory = (dagTemplates: DagTemplate[]) => {
  const results: DagTemplatesByCategory = {};
  for (const item of dagTemplates) {
    if (!results[item.cognitiveCategoryId]) {
      results[item.cognitiveCategoryId] = [];
    }
    results[item.cognitiveCategoryId]!.push(item); // Safe due to previous assignment
  }
  return results;
};
const makeDagTemplateByCategorySelected = (dagTemplates: DagTemplate[]) => {
  const results: DagTemplatesByCategorySelected = {};
  for (const item of dagTemplates) {
    if (!results[item.cognitiveCategoryId]) {
      results[item.cognitiveCategoryId] = { ...item };
    }
  }
  return results;
};
export default createReducer(defaultState, (builder) => {
  builder
    .addCase(actions.PICK_START, (state: any, { meta: { type } }) => {
      return {
        ...defaultPickerState,
        ...state,
        open: true,
        state: 'selecting',
        type,
      };
    })
    .addCase(actions.PICK_END, (state) => {
      return {
        ...state,
        open: false,
        state: state.uploadResult.length ? 'complete' : 'overview',
      };
    })
    .addCase(actions.ABORT_REQUEST, (state, { meta: { fileKey } }) => {
      let newProgressPercentByFileKey = get(
        state,
        'progressPercentByFileKey',
        {}
      );
      if (fileKey) {
        newProgressPercentByFileKey = update(newProgressPercentByFileKey, {
          [fileKey]: {
            aborted: { $set: 'aborted' },
          },
        });
      } else {
        Object.keys(get(state, 'progressPercentByFileKey', {})).forEach(
          (fileKey) => {
            newProgressPercentByFileKey = update(newProgressPercentByFileKey, {
              [fileKey]: {
                aborted: { $set: 'aborted' },
              },
            });
          }
        );
      }
      return {
        ...state,
        progressPercentByFileKey: newProgressPercentByFileKey,
      };
    })
    .addCase(actions.RETRY_REQUEST, (state) => {
      return {
        ...state,
        state: 'uploading',
        progressPercentByFileKey: {},
        success: null,
        error: null,
        warning: null,
      };
    })
    .addCase(actions.RETRY_DONE, (state) => {
      return {
        ...state,
        state: 'complete',
      };
    })
    .addCase(actions.UPLOAD_REQUEST, (state) => {
      // todo: status message
      return {
        ...state,
        state: 'uploading',
        progressPercentByFileKey: {},
        success: null,
        error: null,
        warning: null,
      };
    })
    .addCase(
      actions.UPLOAD_PROGRESS,
      (state, { payload, meta: { fileKey } }) => {
        // todo: status message
        return {
          ...state,
          progressPercentByFileKey: {
            ...state.progressPercentByFileKey,
            [fileKey]: {
              ...state.progressPercentByFileKey[fileKey],
              ...payload,
            },
          },
        };
      }
    )
    .addCase(
      actions.UPLOAD_COMPLETE,
      (state, { payload, meta: { warning, error } }) => {
        const errorMessage = get(error, 'message', ''); // Error or string
        // Extract failed files to be reuploaded
        const failedFiles = isArray(payload)
          ? payload
              .filter((result) => result.error)
              .map((result) => result.file)
          : [];
        // Combine existing uploadResult if any
        const prevUploadResult = (get(state, 'uploadResult') || []).filter(
          (result) => !result.error
        );
        const type = get(state, 'type', '') as string;
        const checkedFile = get(state, 'checkedFile', []);
        let uploadResultEdit = get(
          state,
          'uploadResultEdit',
          {}
        ) as UploadResultEdit;

        if (type === 'programImage') {
          uploadResultEdit = {
            ...uploadResultEdit,
            programImage: payload?.[0]?.unsignedUrl ?? '',
            getUrlProgramImage: payload?.[0]?.getUrl ?? '',
            uploadResultId: checkedFile,
          };
        }
        if (type === 'programLiveImage') {
          uploadResultEdit = {
            ...uploadResultEdit,
            programLiveImage: payload?.[0]?.unsignedUrl ?? '',
            getUrlProgramLiveImage: payload?.[0]?.getUrl ?? '',
            uploadResultId: checkedFile,
          };
        }
        return {
          ...state,
          success: !(warning || error) || null,
          error: error ? errorMessage : null,
          warning: warning || null,
          state: 'complete',
          uploadResult:
            type === 'uploadFile'
              ? prevUploadResult.concat(payload ?? [])
              : prevUploadResult,
          failedFiles,
          isShowListFile: true,
          uploadResultEdit,
        };
      }
    )
    .addCase(
      actions.ON_SELECTION_CHANGE,
      (state, { payload: { value, type } }) => {
        let newChecked = [...state.checkedFile];
        if (type === 'all') {
          if (newChecked.length < state.uploadResult.length) {
            newChecked = Object.keys([...state.uploadResult]).map(Number);
          } else {
            newChecked = [];
          }
        }
        if (type === 'single') {
          const currentIndex = newChecked.indexOf(value);
          if (currentIndex === -1) {
            newChecked.push(value);
          } else {
            newChecked.splice(currentIndex, 1);
          }
        }
        return {
          ...state,
          checkedFile: newChecked,
        };
      }
    )
    .addCase(actions.REMOVE_FILE_UPLOAD, (state, { payload: { value } }) => {
      const newCheckedFile = [...state.checkedFile].filter(
        (item) => !value.includes(item)
      );
      const newUploadResult = [...state.uploadResult].filter(
        (_item, key) => !value.includes(key)
      );
      return {
        ...state,
        checkedFile: newCheckedFile,
        uploadResult: newUploadResult,
        state: newUploadResult.length ? 'complete' : 'overview',
        isShowListFile: newUploadResult.length ? true : false,
      };
    })
    .addCase(actions.SHOW_EDIT_FILE_UPLOAD, (state) => {
      const uploadResult = get(state, 'uploadResult', []);
      const checkedFileId: number[] = get(state, 'checkedFile', []);

      let uploadResultEdit: UploadResultEdit = {
        ...state.uploadResultEdit,
        dateTime: getDateTimeNow(),
        uploadResultId: checkedFileId,
      };
      if (arrayHasLength(checkedFileId, 1)) {
        const uploadResultChecked = uploadResult[checkedFileId[0]];
        if (uploadResultChecked) {
          uploadResultEdit = {
            ...state.uploadResultEdit,
            fileName: uploadResultChecked.fileName,
            dateTime: uploadResultChecked.dateTime || getDateTimeNow(),
            programImage: uploadResultChecked.programImage || '',
            getUrlProgramImage: uploadResultChecked.getUrlProgramImage || '',
            programLiveImage: uploadResultChecked.programLiveImage || '',
            getUrlProgramLiveImage:
              uploadResultChecked.getUrlProgramLiveImage || '',
            uploadResultId: checkedFileId,
            tagsEdit: uploadResultChecked.tagsEdit ?? [],
          };
        }
      }
      return {
        ...state,
        isShowEditFileUpload: true,
        uploadResultEdit,
      };
    })
    .addCase(actions.HIDE_EDIT_FILE_UPLOAD, (state) => {
      return {
        ...state,
        isShowEditFileUpload: false,
        uploadResultEdit: makeEmptyUploadResultEdit(),
      };
    })
    .addCase(actions.FETCH_LIBRARIES_REQUEST, (state) => {
      return {
        ...state,
        loadingUpload: true,
      };
    })
    .addCase(
      actions.FETCH_ENGINE_CATEGORIES_SUCCESS,
      (state, { payload: { engineCategories } }) => {
        const newEngineCategories = engineCategories.filter(
          (item) =>
            !actions.CATEGORY_IDS_TO_EXCLUDE.includes(item.id) &&
            item.categoryType &&
            item.engines.records.filter(
              (engine) => engine.runtimeType === 'edge'
            ).length
        );
        const librariesCategory = [...newEngineCategories].filter(
          ({ libraryEntityIdentifierTypeIds, engines }) =>
            Array.isArray(libraryEntityIdentifierTypeIds) &&
            libraryEntityIdentifierTypeIds.length > 0 &&
            engines.records.filter((item) => item.libraryRequired).length > 0
        );
        const libraries = get(state, 'libraries', {});
        const librariesByCategories = [
          ...librariesCategory,
        ].reduce<LibrariesByCategories>(
          (acc, { id, libraryEntityIdentifierTypeIds }) => ({
            ...acc,
            [id]:
              makeLibrariesByCategories(
                libraryEntityIdentifierTypeIds ?? [],
                Object.values(libraries)
              ) || {},
          }),
          {}
        );
        return {
          ...state,
          engineCategories: newEngineCategories,
          librariesByCategories: librariesByCategories,
        };
      }
    )

    .addCase(
      actions.FETCH_LIBRARIES_SUCCESS,
      (state, { payload: { libraries } }) => {
        const newLibraries = libraries.reduce<Record<string, Library>>(
          (res, value) => {
            if (value.libraryId && value.version > 0) {
              return {
                ...res,
                [value.id]: {
                  ...value,
                },
              };
            }
            return res;
          },
          {}
        );
        return {
          ...state,
          libraries: newLibraries,
        };
      }
    )
    .addCase(
      actions.FETCH_ENGINES_SUCCESS,
      (state, { payload: { engines } }) => {
        // let engineCategories = get(state, 'engineCategories', []);
        let engineCategories = state?.engineCategories || [];
        const supportedDefault = actions.SUPPORTED_FORMATS_ENGINE;
        // remove filter item.edgeVersion === 3 on Azure
        const enginesFilter = engines.filter(
          (item) => item.runtimeType === 'edge' // && item.edgeVersion === 3
        );
        const engineByCategories = enginesFilter.reduce<EngineByCategories>(
          (res, value) => {
            const categoryId = value.category.id;
            const enginesByCategory = res[categoryId];
            if (!enginesByCategory) {
              res[categoryId] = [makeEngineByCategory(value, supportedDefault)];
            } else {
              res[categoryId] = [
                ...enginesByCategory,
                makeEngineByCategory(value, supportedDefault),
              ].sort((a, b) => {
                if (a.isConductor && b.isConductor) {
                  return 0;
                } else if (a.isConductor) {
                  return -1;
                } else if (b.isConductor) {
                  return 1;
                }
                return 0;
              });
            }
            return res;
          },
          {}
        );
        engineCategories = engineCategories.filter((item) =>
          Object.keys(engineByCategories).includes(item.id)
        );
        return {
          ...state,
          engineByCategories,
          enginesDefault: enginesFilter.map((item) => ({
            ...makeEngineByCategory(item, supportedDefault),
          })),
          loadingUpload: false,
          engineCategories,
        };
      }
    )
    .addCase(actions.ADD_ENGINE, (state, { payload: { engineId } }) => {
      const currentEngineCategory = get(
        state,
        'currentEngineCategory',
        currentEngineCategoryDefault
      );
      // const category = get(state, 'engineCategories', []).find(
      const category = (state?.engineCategories || []).find(
        (item) => item.id === currentEngineCategory
      ) ?? { name: '' };
      const enginesSelected = state.enginesSelected || [];
      const enginesDefault = state.enginesDefault || [];

      let engineSelected = enginesDefault.find((item) => item.id === engineId);
      let newEnginesSelected: EnginesSelected[] = [];
      if (engineSelected) {
        engineSelected = {
          ...engineSelected,
          expand: true,
        };

        newEnginesSelected = [...enginesSelected];
        if (!newEnginesSelected.length) {
          newEnginesSelected.push({
            categoryId: currentEngineCategory,
            categoryName: category.name,
            engineIds: [engineSelected],
          });
        } else {
          if (
            !newEnginesSelected.some(
              (item) => item.categoryId === currentEngineCategory
            )
          ) {
            newEnginesSelected.push({
              categoryId: currentEngineCategory,
              categoryName: category.name,
              engineIds: [engineSelected],
            });
          } else {
            const index = newEnginesSelected.findIndex(
              (item) => item.categoryId === currentEngineCategory
            );
            newEnginesSelected[index] = {
              ...newEnginesSelected[index]!,
              engineIds: [
                ...(newEnginesSelected[index]?.engineIds ?? []),
                engineSelected!,
              ],
            };
          }
        }
      }

      return {
        ...state,
        enginesSelected: newEnginesSelected,
        engineByCategories: {
          ...state.engineByCategories,
          [currentEngineCategory]: makeDataEngine(
            state.engineByCategories?.[currentEngineCategory] ?? [],
            engineId
          ),
        },
        templateSelected: '',
      };
    })
    .addCase(actions.CHANGE_ENGINE, (state, { payload: { engineId } }) => {
      return {
        ...state,
        currentEngineCategory: engineId,
      };
    })
    .addCase(actions.REMOVE_ENGINE, (state, { payload: { engineId } }) => {
      const enginesSelected = get(state, 'enginesSelected', []);
      const newEnginesSelected = [...enginesSelected]
        .map((item) => {
          if (item.engineIds.some((item) => item.id === engineId)) {
            return {
              ...item,
              engineIds: [
                ...item.engineIds.filter((item) => item.id !== engineId),
              ],
            };
          }
          return item;
        })
        .filter((item) => item.engineIds.length);
      return {
        ...state,
        enginesSelected: newEnginesSelected,
        templateSelected: '',
      };
    })
    .addCase(
      actions.SHOW_MODAL_SAVE_TEMPLATE,
      (state, { payload: { value } }) => {
        return {
          ...state,
          isShowModalSaveTemplate: value,
        };
      }
    )
    .addCase(
      actions.HIDE_MODAL_SAVE_TEMPLATE,
      (state, { payload: { value } }) => {
        return {
          ...state,
          isShowModalSaveTemplate: value,
          templateName: '',
        };
      }
    )
    .addCase(actions.SAVE_TEMPLATE_SUCCESS, (state) => {
      return {
        ...state,
        isShowModalSaveTemplate: false,
        isShowConfirmSaveTemplate: false,
        templateName: '',
      };
    })
    .addCase(
      actions.FETCH_TEMPLATES_SUCCESS,
      (state, { payload: { templates } }) => {
        const templatesFilter = templates
          .filter((item) =>
            item.taskList.some(
              (task) =>
                Array.isArray(task.engineIds) &&
                task.engineIds.length > 0 &&
                task.engineIds.some(
                  (engine) =>
                    Array.isArray(engine.standaloneJobTemplates) &&
                    engine.standaloneJobTemplates.length > 0
                )
            )
          )
          .map((item) => ({ ...item, loadingRemoveTemplate: false }));
        return {
          ...state,
          templates: templatesFilter.sort((a: Template, b: Template) =>
            a.name.localeCompare(b.name)
          ),
        };
      }
    )
    .addCase(actions.CHANGE_TEMPLATE, (state, { payload: { templateId } }) => {
      const templates = state.templates || [];
      const enginesSelectedFilter = [...templates].find(
        (item) => item.id === templateId
      );
      const newEnginesSelected = get(enginesSelectedFilter, 'taskList', []);
      return {
        ...state,
        enginesSelected: newEnginesSelected,
        templateSelected: templateId,
      };
    })
    .addCase(
      actions.FETCH_CONTENT_TEMPLATES_SUCCESS,
      (state, { payload: { contentTemplates } }) => {
        return {
          ...state,
          contentTemplates,
        };
      }
    )
    .addCase(
      actions.ADD_CONTENT_TEMPLATE,
      (state, { payload: { contentTemplateId } }) => {
        const contentTemplates = state.contentTemplates || [];
        const contentTemplate = contentTemplates.find(
          (item) => item.id === contentTemplateId
        );
        let contentTemplateSelected = state.contentTemplateSelected || [];
        const records = (contentTemplate?.schemas?.records || []).find(
          (item) => item.status === 'published'
        );
        const { properties = {} } = records?.definition || {};
        const { required = [] } = records?.definition || {};
        const data = Object.keys(properties).reduce<Record<string, string>>(
          (res, value) => ({
            ...res,
            [value]: '',
          }),
          {}
        );
        if (contentTemplate) {
          contentTemplateSelected = [
            ...contentTemplateSelected,
            {
              ...contentTemplate,
              data,
              validate: required,
            },
          ];
        }

        return {
          ...state,
          contentTemplateSelected,
        };
      }
    )
    .addCase(
      actions.REMOVE_CONTENT_TEMPLATE,
      (state, { payload: { contentTemplateId } }) => {
        const contentTemplateSelected = state?.contentTemplateSelected || [];
        const newContentTemplateSelected = contentTemplateSelected.filter(
          (item) => item.id !== contentTemplateId
        );
        return {
          ...state,
          contentTemplateSelected: newContentTemplateSelected,
        };
      }
    )
    .addCase(
      actions.ON_CHANGE_FORM_CONTENT_TEMPLATE,
      (state, { payload: { contentTemplateId, name, value } }) => {
        const contentTemplateSelected = state?.contentTemplateSelected || [];
        for (const contentTemplate of contentTemplateSelected) {
          if (contentTemplate.id === contentTemplateId) {
            if (contentTemplate.data) {
              contentTemplate.data[name] = value;
            }
            if (Array.isArray(contentTemplate.validate)) {
              const records = (contentTemplate?.schemas?.records || []).find(
                (item) => item.status === 'published'
              );
              const { required = [] } = records?.definition || {};
              const isRequiredField = required.includes(name);

              if (isRequiredField && value) {
                contentTemplate.validate = contentTemplate.validate.filter(
                  (element) => element !== name
                );
              } else if (
                isRequiredField &&
                !value &&
                !contentTemplate.validate.includes(name)
              ) {
                contentTemplate.validate.push(name);
              }
            }
            break;
          }
        }
      }
    )
    .addCase(
      actions.UPDATE_SELECTED_FOLDER_UPLOAD,
      (state, { payload: { selectedFolder } }) => {
        return {
          ...state,
          selectedFolder: {
            ...selectedFolder,
            name: selectedFolder.root ? 'My Organization' : selectedFolder.name,
          },
        };
      }
    )
    .addCase(
      actions.ADD_TAGS_CUSTOMIZE,
      (state, { payload: { value, type } }) => {
        if (type === 'editFileUpload') {
          const tagsEdit = state?.uploadResultEdit?.tagsEdit || [];
          const checkAvailableTags = tagsEdit.filter(
            (item) => item.value === value
          );
          if (checkAvailableTags.length) {
            return {
              ...state,
            };
          }
          return {
            ...state,
            uploadResultEdit: {
              ...state.uploadResultEdit,
              tagsEdit: [
                ...state.uploadResultEdit.tagsEdit,
                {
                  value,
                },
              ],
            },
          };
        } else {
          const tagsCustomize = state?.tagsCustomize || [];
          const checkAvailableTags = tagsCustomize.filter(
            (item) => item.value === value
          );
          if (checkAvailableTags.length) {
            return {
              ...state,
            };
          }
          return {
            ...state,
            tagsCustomize: [
              ...state.tagsCustomize,
              {
                value,
              },
            ],
          };
        }
      }
    )
    .addCase(
      actions.REMOVE_TAGS_CUSTOMIZE,
      (state, { payload: { value, type } }) => {
        if (type === 'editFileUpload') {
          const tagsEdit =
            state.uploadResultEdit.tagsEdit?.filter(
              (item) => item.value !== value
            ) ?? [];
          return {
            ...state,
            uploadResultEdit: {
              ...state.uploadResultEdit,
              tagsEdit,
            },
          };
        } else {
          const tagsCustomize = (state?.tagsCustomize || []).filter(
            (item) => item.value !== value
          );
          return {
            ...state,
            tagsCustomize,
          };
        }
      }
    )
    .addCase(actions.SAVE_UPLOAD_FILE, (state) => {
      return {
        ...state,
        loadingSaveUpload: true,
      };
    })
    .addCase(actions.FETCH_CREATE_JOB_SUCCESS, (state) => {
      const selectedCurrentFolder = get(state, 'selectedCurrentFolder');
      return {
        ...state,
        ...defaultPickerState,
        selectedFolder: selectedCurrentFolder,
      };
    })
    .addCase(
      actions.ON_CHANGE_FORM_ENGINE_SELECTED,
      (state, { payload: { engineId, name, value } }) => {
        const enginesSelected = get(state, 'enginesSelected', []);
        for (const engineInfo of enginesSelected) {
          for (const engine of engineInfo.engineIds) {
            if (engine.id === engineId) {
              for (const field of engine.fields) {
                if (field.name === name) {
                  field.defaultValue = value;
                }
              }
            }
          }
        }
      }
    )
    .addCase(
      actions.ON_CHANGE_JOB_PRIORITY_ENGINE_SELECTED,
      (state, { payload: { engineId, priority } }) => {
        const enginesSelected = get(state, 'enginesSelected', []);
        for (const engineInfo of enginesSelected) {
          for (const engine of engineInfo.engineIds) {
            if (engine.id === engineId) {
              engine.priority = priority;
              break;
            }
          }
        }
      }
    )
    .addCase(
      actions.ON_CHANGE_LIBRARIES_ENGINE_SELECTED,
      (state, { payload: { engineId, value } }) => {
        const enginesSelected = get(state, 'enginesSelected', []);
        for (const engineInfo of enginesSelected) {
          for (const engine of engineInfo.engineIds) {
            if (engine.id === engineId) {
              engine.librariesSelected = value;
              break;
            }
          }
        }
      }
    )
    .addCase(
      actions.ON_CHANGE_EXPAND,
      (state, { payload: { engineId, expand } }) => {
        const enginesSelected = get(state, 'enginesSelected', []);
        for (const engineInfo of enginesSelected) {
          for (const engine of engineInfo.engineIds) {
            if (engine.id === engineId) {
              engine.expand = expand;
              break;
            }
          }
        }
      }
    )
    .addCase(
      actions.ON_CHANGE_FILE_NAME_EDIT,
      (state, { payload: { value } }) => {
        return {
          ...state,
          uploadResultEdit: {
            ...state.uploadResultEdit,
            fileName: value,
          },
        };
      }
    )
    .addCase(
      actions.ON_CHANGE_DATE_TIME_EDIT,
      (state, { payload: { value } }) => {
        return {
          ...state,
          uploadResultEdit: {
            ...state.uploadResultEdit,
            dateTime: value,
          },
        };
      }
    )
    .addCase(actions.SAVE_EDIT_FILE_UPLOAD, (state) => {
      const uploadResultEdit = get(
        state,
        'uploadResultEdit',
        {}
      ) as UploadResultEdit;

      // For clarify, uploadResult does not have uploadResultId, while uploadResultEdit
      // has uploadResultId to store the selected uploadResult.
      // To exclude uploadResultId from uploadResultEdit for using object spread to update
      // uploadResult
      const { uploadResultId, ...restUploadResultEdit } = uploadResultEdit;

      // const uploadResult = get(state, 'uploadResult', []);
      const uploadResult = state?.uploadResult || [];
      const newUploadResult = uploadResult.map((item, index) => {
        if (uploadResultId.includes(index)) {
          return {
            ...item,
            ...restUploadResultEdit,
          };
        }
        return item;
      });
      return {
        ...state,
        uploadResult: newUploadResult,
        uploadResultEdit: makeEmptyUploadResultEdit(),
        isShowEditFileUpload: false,
      };
    })
    .addCase(
      actions.SET_OPEN_FOLDER_UPLOAD,
      (state, { payload: { folderId } }) => {
        return {
          ...state,
          openFolderIdUpload: folderId,
        };
      }
    )
    .addCase(actions.OPEN_MODAL_UPLOAD, (state) => {
      return {
        ...state,
        isOpenModalUpload: true,
      };
    })
    .addCase(actions.CLOSE_MODAL_UPLOAD, (state) => {
      return {
        ...state,
        ...defaultPickerState,
        isOpenModalUpload: false,
        state: 'overview',
        isReprocess: false,
      };
    })
    .addCase(actions.SET_CURRENT_FOLDER, (state, { payload: { folder } }) => {
      return {
        ...state,
        selectedFolder: {
          ...folder,
        },
        openFolderIdUpload: folder.id,
        selectedCurrentFolder: {
          ...folder,
        },
      };
    })
    .addCase(actions.OPEN_MODAL_REPROCESS, (state) => {
      return {
        ...state,
        isReprocess: true,
        isOpenModalUpload: true,
      };
    })
    .addCase(actions.SAVE_REPROCESS_FILE, (state) => {
      state.loadingSaveUpload = true;
    })
    .addCase(
      actions.UPDATE_DAG_TEMPLATE_ID_SELECTED,
      (state, { payload: { dagTemplateId } }) => {
        return {
          ...state,
          dagTemplateIdSelected: dagTemplateId,
        };
      }
    )
    .addCase(
      actions.SHOW_MODAL_PROCESS_LIMIT_EXCEEDED,
      (state, { payload: { value } }) => {
        return {
          ...state,
          isShowModalProcessLimitExceeded: value,
        };
      }
    )
    .addCase(
      actions.UPDATE_ENGINES_SELECTED,
      (state, { payload: { enginesSelected } }) => {
        return {
          ...state,
          enginesSelectedSupported: enginesSelected,
        };
      }
    )
    .addCase(actions.UPDATE_LOADING_SAVE_UPLOAD, (state) => {
      return {
        ...state,
        loadingSaveUpload: false,
        enginesSelected: [],
      };
    })
    .addCase(actions.SHOW_CONFIRM_SAVE_TEMPLATE, (state) => {
      return {
        ...state,
        isShowConfirmSaveTemplate: true,
      };
    })
    .addCase(actions.HIDE_CONFIRM_SAVE_TEMPLATE, (state) => {
      return {
        ...state,
        isShowConfirmSaveTemplate: false,
      };
    })
    .addCase(
      actions.ON_CHANGE_TEMPLATE_NAME,
      (state, { payload: { name } }) => {
        return {
          ...state,
          templateName: name,
        };
      }
    )
    .addCase(actions.SET_DEFAULT_STATE_UPLOAD_FILE, (state) => {
      const selectedCurrentFolder = get(state, 'selectedCurrentFolder');
      return {
        ...state,
        ...defaultPickerState,
        selectedFolder: selectedCurrentFolder,
      };
    })
    .addCase(actions.REMOVE_TEMPLATE_REQUEST, (state, { payload: { id } }) => {
      const templates = state?.templates || [];
      const newTemplates = templates.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            loadingRemoveTemplate: true,
          };
        }
        return item;
      });
      return {
        ...state,
        templates: newTemplates,
      };
    })
    .addCase(
      actions.UPDATE_PERCENTAGE_FILES_UPLOADED,
      (state, { payload: { percentage } }) => {
        return {
          ...state,
          percentageFilesUploaded: percentage,
        };
      }
    )
    .addCase(
      actions.FETCH_DAG_TEMPLATES_SUCCESS,
      (state, { payload: { dagTemplates } }) => {
        return {
          ...state,
          dagTemplates: dagTemplates,
          dagTemplatesByCategory: makeDagTemplateByCategory(dagTemplates),
          dagTemplateByCategorySelected:
            makeDagTemplateByCategorySelected(dagTemplates),
        };
      }
    )
    .addCase(
      actions.ON_CLICK_DAG_TEMPLATE,
      (state, { payload: { dagTemplateSelected, categoryId } }) => {
        return {
          ...state,
          dagTemplateByCategorySelected: {
            ...state.dagTemplateByCategorySelected,
            [categoryId]: dagTemplateSelected,
          },
        };
      }
    )
    .addCase(
      actions.UPDATE_SHOW_PROCESS_BY_CATEGORY,
      (state, { payload: { categoryId, value } }) => {
        if (categoryId) {
          state.showProcessByCategory[categoryId] = value;
        }
        state.loadingSaveUpload = true;
      }
    );
});

const local = (state: any) => state[namespace] as typeof defaultState;

export const isOpen = (state: any) => get(local(state), 'open');
export const state = (state: any) => get(local(state), 'state', 'overview');

// Keep this in case we want to go back to using mean percentage progresses
export const progressPercent = (state: any) => {
  const currentProgress = get(local(state), 'progressPercentByFileKey');
  if (!currentProgress) {
    return 0;
  }

  const meanProgress = mean(Object.values(currentProgress));
  const rounded = Math.round(meanProgress);
  return isNaN(rounded) ? 0 : rounded;
};
export const percentByFiles = (state: any) => {
  const currentFiles = get(local(state), 'progressPercentByFileKey', {});
  return Object.entries(currentFiles).map(([key, value]) => ({
    key,
    value,
  }));
};
export const failedFiles = (state: any) => {
  const failedFiles = get(local(state), 'failedFiles', []);
  return failedFiles;
};
export const uploadResult = (state: any) => local(state).uploadResult || [];
export const didSucceed = (state: any) => !!get(local(state), 'success');
export const didError = (state: any) => !!get(local(state), 'error');
export const didWarn = (state: any) => !!get(local(state), 'warning');
// todo: status message for normal cases
export const statusMessage = (state: any) =>
  (get(local(state), 'warning') || get(local(state), 'error') || '').toString();
export const isShowListFile = (state: any) =>
  get(local(state), 'isShowListFile', false);
export const checkedFile = (state: any) => local(state).checkedFile || [];
export const isShowEditFileUpload = (state: any) =>
  get(local(state), 'isShowEditFileUpload', false);
export const engineCategories = (state: any) =>
  local(state).engineCategories || [];
export const librariesByCategories = (state: any) =>
  local(state).librariesByCategories || {};
export const engineByCategories = (state: any) =>
  local(state).engineByCategories || {};
export const currentEngineCategory = (state: any) =>
  get(local(state), 'currentEngineCategory', currentEngineCategoryDefault);
export const enginesSelected = (state: any) =>
  local(state).enginesSelected || [];
export const isShowModalSaveTemplate = (state: any) =>
  get(local(state), 'isShowModalSaveTemplate', false);
export const templates = (state: any) => local(state).templates || [];
export const contentTemplates = (state: any) =>
  local(state).contentTemplates || [];
export const contentTemplateSelected = (state: any) =>
  local(state).contentTemplateSelected || [];
export const selectedFolder = (state: any) => local(state).selectedFolder;
export const tagsCustomize = (state: any) => local(state).tagsCustomize || [];
export const loadingUpload = (state: any) =>
  get(local(state), 'loadingUpload', false);
export const librariesSelected = (state: any) =>
  get(local(state), 'librariesSelected', {});
export const libraries = (state: any) => get(local(state), 'libraries', {});
export const uploadResultEdit = (state: any) =>
  local(state).uploadResultEdit || {};
export const openFolderIdUpload = (state: any) =>
  get(local(state), 'openFolderIdUpload', '');
export const isOpenModalUpload = (state: any) =>
  get(local(state), 'isOpenModalUpload', false);
export const loadingSaveUpload = (state: any) =>
  get(local(state), 'loadingSaveUpload', false);
export const enginesDefault = (state: any) => local(state).enginesDefault || [];
export const isReprocess = (state: any) =>
  get(local(state), 'isReprocess', false);
export const tdoIdsNotSupported = (state: any) =>
  get(local(state), 'tdoIdsNotSupported', []);
export const tdoIdsSupported = (state: any) =>
  get(local(state), 'tdoIdsSupported', []);
export const enginesSelectedSupported = (state: any) =>
  get(local(state), 'enginesSelectedSupported', []);
export const isShowConfirmSaveTemplate = (state: any) =>
  get(local(state), 'isShowConfirmSaveTemplate', false);
export const templateName = (state: any) =>
  get(local(state), 'templateName', '');
// TODO: Can we type this properly/safely?
export const defaultWorkflowSetting = (state: any) =>
  get(
    state,
    'user.user.organization.kvp.features.illuminate.defaultWorkflowSetting',
    'simple'
  );
export const templateSelected = (state: any) =>
  get(local(state), 'templateSelected', '');
export const loadingRemoveTemplate = (state: any) =>
  get(local(state), 'loadingRemoveTemplate', false);
export const percentageFilesUploaded = (state: any) =>
  get(local(state), 'percentageFilesUploaded', 0);
export const dagTemplates = (state: any) => local(state).dagTemplates || [];
export const dagTemplateByCategorySelected = (state: any) =>
  local(state).dagTemplateByCategorySelected || {};
export const engineCategoryIdSelected = (state: any) =>
  get(local(state), 'engineCategoryIdSelected', '');
export const dagTemplatesByCategory = (state: any) =>
  local(state).dagTemplatesByCategory || {};

// TODO: Can we properly type this?
export const reprocessLimit = (state: any): number =>
  get(
    state,
    'user.user.organization.kvp.features.illuminate.reprocessLimit',
    1000
  );
export const isShowModalProcessLimitExceeded = (state: any) =>
  get(local(state), 'isShowModalProcessLimitExceeded', false);
export const dagTemplateIdSelected = (state: any) =>
  get(local(state), 'dagTemplateIdSelected', '');
export const showProcessByCategory = (state: any) =>
  local(state).showProcessByCategory;
