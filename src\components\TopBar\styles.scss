@import 'src/variables';

$sidebarwidth: 260;

.container {
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 1;

  // copied from material-ui
  transition:
    margin 195ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    width 195ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;

  &.selected {
    background: #f6fbff !important;
  }
}

.mainMenu {
  top: 52px;
  left: 52px;
  width: 210px;
  right: 0;
  position: absolute;
  border-radius: 0;
  background: white;
  box-shadow:
    0 0 16px 0 $black-3,
    0 16px 16px 0 $black-2;
}

.item {
  justify-content: flex-start;
  padding-left: 0;
  padding-right: 15px;
}

.group-btn {
  // width: #{$sidebarwidth}px;
  // text-align: center;
  // line-height: 47px;

  button {
    width: 100%;
    background-color: #fafafa;
    border: none !important;
  }

  svg {
    // color: rgba(0, 0, 0, 0.54);
    // margin-left: 20px;
    color: #2196f3;
  }

  p {
    width: 158px;
    color: rgba(0, 0, 0, 0.54);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    text-align: left;
  }
}

.leftButtonContainer {
  flex-grow: 0;

  .highlight {
    background: grey;
    height: 100%;
    display: inline-block;
  }
}

.top-bar {
  &-container {
    display: flex;
    flex-grow: 1;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0;
  }

  &__icon {
    text-align: right;
    margin: 0 20px;

    svg {
      height: 22px;
      cursor: pointer;
    }
  }

  .selected {
    font-weight: 400;
    color: blue;
  }
}

.menu-item {
  min-width: 140px;

  a {
    display: flex;
    align-items: center;
    width: 100%;
    height: 47px;
    color: rgba(0, 0, 0, 0.87);
    font-size: 15px;
  }

  &:hover {
    a {
      text-decoration: none;
    }
  }

  svg {
    font-size: 21px;
  }

  span {
    font-size: 15px;
  }
}

.iconAdvertiser {
  width: 16px;
  height: 16px;
  margin: 0 18px 0 3px;
}

.create-new-folder {
  margin: 10px 0 0 10px;
}

.drawerRoot {
  > div {
    background-color: transparent !important;
  }
}

.modalRoot {
  margin-top: 118px;
  border: 0;
  box-shadow: none;
  max-height: calc(100vh - 118px);
}

.filters__button {
  width: 118px;
  font-size: 12px;
  padding: 5px 9px;
}

.breadcrumbs_container {
  display: flex;
  align-items: center;

  button {
    background-color: transparent !important;
  }
  button:first-child {
    display: none;
  }
  span {
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 1.75 !important;
  }

  div:first-child {
    min-width: 110px;
    background-color: rgb(250, 250, 250);
    margin-left: 0;
  }
}

.group-btn button:hover {
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.54);
  border: none;
  border-radius: 0;
  padding: 11px 16px;
}

.loading {
  visibility: hidden;
}

.breadcrumbs-loading {
  color: rgba(0, 0, 0, 0.54);
  font-weight: 500;
  line-height: 1.4em;
  font-size: 14px;
  min-width: 0 !important;
}

.upload-file {
  width: 118px;
  font-size: 12px;
  padding: 5px 9px;
  margin-right: 10px;
}
