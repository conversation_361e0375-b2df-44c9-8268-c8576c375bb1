import React, { Fragment, useState, useEffect } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { DateTime, Duration } from 'luxon';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Tooltip from '@mui/material/Tooltip';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Popover from '@mui/material/Popover';
import Button from '@mui/material/Button';
import ScheduleIcon from '@mui/icons-material/Schedule';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import ClearIcon from '@mui/icons-material/Clear';
import Cached from '@mui/icons-material/Cached';
import RefreshIcon from '@mui/icons-material/Refresh';
import Checkbox from '@mui/material/Checkbox';
import SaveAlt from '@mui/icons-material/SaveAlt';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Icon from '@mui/material/Icon';
import Box from '@mui/material/Box';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import EventIcon from '@mui/icons-material/Event';
import {
  PagingState,
  SelectionState,
  IntegratedSelection,
  IntegratedPaging,
  CustomPaging,
  IntegratedGrouping,
  RowDetailState,
  GroupingState,
} from '@devexpress/dx-react-grid';
import {
  Grid,
  TableHeaderRow,
  VirtualTable,
  PagingPanel,
  TableSelection,
  TableRowDetail,
  Table,
} from '@devexpress/dx-react-grid-material-ui';
import TablePagination from '@mui/material/TablePagination';
import { createTheme, ThemeProvider, Theme } from '@mui/material/styles';
import withStyles from '@mui/styles/withStyles';
import { get, uniq, isEmpty } from 'lodash';
import TableCell from '@mui/material/TableCell';
import { TABS } from 'state/modules/tabs';
import { getDisableAnalytics } from 'state/modules/tdosTable';
import {
  processingJobs,
  loadingProcessingJobs,
  noDataProcessing,
  currentPage,
  pageSize,
  statusFilter,
  dateTimeRangeFilter,
  getLoadingRetryJobs,
  getPercentageFailedJobsUploaded,
  getSelectedRows,
  getIndeterminate,
  getIsSelectedAllJobsFailed,
  getTotalProcessingJobs,
  getIsFilterJobsFailed,
  getShowCustomRange,
  getCustomStartDate,
  getCustomEndDate,
  getRemainingProcessingJobs,
  loadingChangePage,
  getExportingFailedJobs,
  getExportFailedJobsPercentage,
} from 'state/modules/processingJobs';
import { enginesDefault } from 'state/modules/uploadFile';
import {
  refreshProcessingJobs,
  STATUS_PROCESSING_JOBS,
  onChangePageProcessingJobs,
  onChangePageSizeProcessingJobs,
  onChangeStatusFilter,
  filterJobByStatus,
  filterJobByDateTimeRange,
  retryJobs,
  onSelectionJobsChange,
  updateCurrentPageProcessingJobs,
  setShowCustomRange,
  setCustomStartDate,
  setCustomEndDate,
  filterJobByCustomDateRange,
  clearCustomDateRange,
  exportFailedJobs,
  TASK_FAILED_STATUS,
  TASK_COMPLETE_STATUS,
  TASK_RUNNING_STATUS,
} from 'state/modules/processingJobs/actions';
import { openModalReprocess } from 'state/modules/uploadFile/actions';
import classNames from 'classnames/bind';
import * as styles from './styles.scss';
import { ROUTE_FILE } from 'state/modules/routing';
import { Job, Task } from '../../state/modules/processingJobs/models';
import copy from 'copy-to-clipboard';
import { Engine } from 'state/modules/uploadFile/models';
declare module '@mui/styles/defaultTheme' {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface DefaultTheme extends Theme {}
}

const cx = classNames.bind(styles);

const timePickerTheme = createTheme({
  palette: {
    primary: {
      main: '#1871E8',
    },
    secondary: {
      main: '#2A323C',
    },
  },
});
const customLabelStart = {
  okButtonLabel: 'Next',
};
const customLabelEnd = {
  okButtonLabel: 'Set Custom Range',
};
function getHoursFromTime(input: number, units: string) {
  return Duration.fromObject({ [units]: input }).as('hours');
}

export const DATE_TIME_RANGE_FILTER = [
  {
    name: '2 hours',
    value: 2,
  },
  {
    name: '12 hours',
    value: 12,
  },
  {
    name: '24 hours',
    value: 24,
  },
  {
    name: '3 days',
    value: getHoursFromTime(3, 'days'),
  },
  {
    name: '1 week',
    value: getHoursFromTime(1, 'weeks'),
  },
  {
    name: '2 weeks',
    value: getHoursFromTime(2, 'weeks'),
  },
  {
    name: '3 weeks',
    value: getHoursFromTime(3, 'weeks'),
  },
  {
    name: '1 month',
    value: getHoursFromTime(1, 'months'),
  },
  {
    name: '2 months',
    value: getHoursFromTime(2, 'months'),
  },
  {
    name: '3 months',
    value: getHoursFromTime(3, 'months'),
  },
] as const;

function ProcessingJobs({
  refreshProcessingJobs,
  processingJobs,
  loadingProcessingJobs,
  noDataProcessing,
  enginesDefault,
  currentPage,
  pageSize,
  onChangePageProcessingJobs,
  onChangePageSizeProcessingJobs,
  statusFilter,
  onChangeStatusFilter,
  filterJobByStatus,
  filterJobByDateTimeRange,
  dateTimeRangeFilter,
  navigateToFile,
  retryJobs,
  loadingRetryJobs,
  percentageFailedJobsUploaded,
  onSelectionJobsChange,
  selectedRows,
  indeterminate,
  isSelectedAll,
  totalProcessingJobs,
  updateCurrentPageProcessingJobs,
  isFilterJobsFailed,
  openModalReprocess,
  showCustomRange,
  customStartDate,
  customEndDate,
  setShowCustomRange,
  setCustomStartDate,
  setCustomEndDate,
  filterJobByCustomDateRange,
  clearCustomDateRange,
  remainingProcessingJobs,
  loadingChangePage,
  exportFailedJobs,
  exportingFailedJobs,
  exportFailedJobsPercentage,
}: PropsFromRedux) {
  const [columns] = useState([
    { name: 'name', title: 'Name', showBadge: false },
    { name: 'inQueue', title: 'In Queue', showBadge: true },
    { name: 'inProgress', title: 'In Progress', showBadge: true },
    { name: 'complete', title: 'Complete', showBadge: true },
    { name: 'errors', title: 'Errors', showBadge: true },
    { name: 'lastModifiedDate', title: 'Last Modified Date' },
  ]);
  const [tableColumnExtensions] = useState([
    { columnName: 'name', wordWrapEnabled: true },
    {
      columnName: 'inQueue',
      wordWrapEnabled: true,
      width: 200,
      align: 'center' as const,
    },
    {
      columnName: 'inProgress',
      wordWrapEnabled: true,
      width: 200,
      align: 'center' as const,
    },
    { columnName: 'complete', width: 200, align: 'center' as const },
    { columnName: 'errors', width: 330, align: 'center' as const },
    { columnName: 'lastModifiedDate', width: 300, align: 'center' as const },
  ]);

  const [anchorElStatusFilter, setAnchorElStatusFilter] = React.useState<
    (EventTarget & HTMLElement) | null
  >(null);
  const [anchorElDateTimeRangeFilter, setAnchorElDateTimeRangeFilter] =
    React.useState<(EventTarget & HTMLElement) | null>(null);
  const [isStartDateOpen, setIsStartDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);
  const [expandedRowIds, setExpandedRowIds] = React.useState<
    Array<number | string>
  >([]);

  const DATE_PICKER_FORMAT = 'MM/dd/yyyy HH:mm';

  useEffect(() => {
    refreshProcessingJobs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function myCustomDataCellComponent(value: NoDataCellProps) {
    return noDataProcessing ? <VirtualTable.NoDataCell {...value} /> : null;
  }
  function myCustomPagination(value: {
    totalCount: number;
    pageSize: number;
    currentPage: number;
  }) {
    const { pageSize } = value;
    return (
      <TablePagination
        rowsPerPageOptions={[10, 20, 100]}
        component="div"
        rowsPerPage={pageSize}
        page={currentPage}
        slotProps={{
          actions: {
            previousButton: {
              'aria-label': 'Previous Page',
              disabled: currentPage === 0 || loadingChangePage,
            },
            nextButton: {
              'aria-label': 'Next Page',
              disabled:
                ((currentPage + 1) * pageSize >= processingJobs.length &&
                  remainingProcessingJobs.length === 0) ||
                loadingChangePage,
            },
          },
        }}
        onPageChange={onChangePage}
        onRowsPerPageChange={onChangePageSize}
        style={{
          paddingRight: 68,
        }}
        count={totalProcessingJobs}
        labelDisplayedRows={({ from, to }) => `${from}-${to} `}
      />
    );
  }

  function cellHeader(
    row: CellProps,
    totalByStatus: {
      [key: string]: number;
    }
  ) {
    return (
      <td
        className={cx('aa', {
          'total-status-header': row.column.name !== 'name',
        })}
      >
        {row.column.name === 'name' && !isCheckboxDisabled && (
          <Checkbox
            indeterminate={indeterminate}
            checked={isSelectedAll}
            onChange={onSelectAllClick}
            data-testid="select-all"
            disabled={isCheckboxDisabled}
            className={styles.selectAll}
          />
        )}
        {row.column.showBadge && (
          <span
            className={cx(styles['badge'], styles[`status-${row.column.name}`])}
          >
            {totalByStatus[row.column.name]}
          </span>
        )}
        {row.column.title}
      </td>
    );
  }

  function clickToViewDetail(event: React.MouseEvent<HTMLElement>) {
    const tdoId = event.currentTarget.getAttribute('data-tdoid');
    const payload = {
      tab: TABS.Files,
      tdoId,
      initFullScreen: true,
    };
    navigateToFile(payload);
  }

  function isValidDatePickers() {
    if (customStartDate && customEndDate) {
      const currentDate = new Date();
      const start = customStartDate.toJSDate().getTime();
      const end = customEndDate.toJSDate().getTime();
      const dateNow = currentDate.getTime();
      const ninetyDaysAgo = new Date().setDate(currentDate.getDate() - 90);
      return (
        start < end &&
        start < dateNow &&
        start > ninetyDaysAgo &&
        DateTime.fromFormat(
          customStartDate.toFormat(DATE_PICKER_FORMAT),
          DATE_PICKER_FORMAT
        ).isValid &&
        DateTime.fromFormat(
          customEndDate.toFormat(DATE_PICKER_FORMAT),
          DATE_PICKER_FORMAT
        ).isValid
      );
    }
    return false;
  }

  let totalByStatus = {
    inQueue: 0,
    inProgress: 0,
    complete: 0,
    errors: 0,
  };

  const HtmlTooltip = withStyles(() => ({
    tooltip: {
      maxWidth: 600,
    },
  }))(Tooltip);

  const renderTooltip = (jobs?: Job[]) => {
    if (!jobs || jobs.length === 0) {
      return <Typography>0</Typography>;
    }
    const tasks = jobs.flatMap((job) => job.tasks.records);
    const taskEngines: { engine: Engine; status: string }[] = [];
    const dedupMap: { [key: string]: boolean } = {};
    for (const task of tasks) {
      if (dedupMap[task.engine.id]) {
        continue;
      }
      const engine = enginesDefault.find(
        (engine) => engine.id === task.engine?.id
      );
      if (!engine) {
        continue;
      }
      let status = 'queued';
      if (isTaskInStatus(task, TASK_RUNNING_STATUS)) {
        status = 'running';
      } else if (isTaskInStatus(task, TASK_COMPLETE_STATUS)) {
        status = 'complete';
      } else if (isTaskInStatus(task, TASK_FAILED_STATUS)) {
        status = 'failed';
      }
      taskEngines.push({
        engine,
        status,
      });
      dedupMap[engine.id] = true;
    }
    return (
      <HtmlTooltip
        title={
          taskEngines.length &&
          taskEngines.map((taskEngine) => {
            return (
              <Typography
                key={taskEngine.engine.id}
                className={cx(styles['title-tooltip'], {
                  queued: taskEngine.status === 'queued',
                  running: taskEngine.status === 'running',
                  failed: taskEngine.status === 'failed',
                })}
              >
                <i
                  className={get(taskEngine.engine, 'category.iconClass', '')}
                />{' '}
                {taskEngine.engine.name}
              </Typography>
            );
          })
        }
      >
        <Typography>{jobs.length}</Typography>
      </HtmlTooltip>
    );
  };

  const cutText = (text: string, start: number, end: number) => {
    if (!text) {
      return '';
    }
    return text.slice(start, end) + '...';
  };

  const renderError = (job?: Job) => {
    if (!job || !isJobInStatus(job, STATUS_PROCESSING_JOBS.errors.value)) {
      return (
        <div
          className={cx({
            failedJobs: false,
            doNotFailed: true,
          })}
        >
          {renderTooltip()}
        </div>
      );
    }
    const failureMessage = getFailureMessage(job);
    const errors = (
      <div
        className={cx({
          failedJobs: true,
          doNotFailed: false,
        })}
      >
        {renderTooltip([job])}
        <Box display="flex" alignItems="top">
          <HtmlTooltip
            title={
              <Typography className={styles['title-tooltip']}>
                {failureMessage}
              </Typography>
            }
          >
            <Typography className={styles.failureMessage}>
              {cutText(failureMessage, 0, 18)}
            </Typography>
          </HtmlTooltip>
          <IconButton
            onClick={() => copy(failureMessage)}
            style={{ padding: 4, marginTop: -4 }}
            size="large"
          >
            <Tooltip
              title={`Copy error details to clipboard`}
              placement="bottom"
            >
              <FileCopyIcon fontSize="small" />
            </Tooltip>
          </IconButton>
        </Box>
      </div>
    );
    return errors;
  };

  const renderChild = (jobs: Job[]) => {
    return jobs.map((job) => {
      const inQueue = isJobInStatus(job, STATUS_PROCESSING_JOBS.inQueue.value)
        ? [job]
        : undefined;
      const inProgress = isJobInStatus(
        job,
        STATUS_PROCESSING_JOBS.inProgress.value
      )
        ? [job]
        : undefined;
      const complete = isJobInStatus(job, STATUS_PROCESSING_JOBS.complete.value)
        ? [job]
        : undefined;
      const failed = isJobInStatus(job, STATUS_PROCESSING_JOBS.errors.value)
        ? job
        : undefined;

      const lastModifiedDate = (
        <>
          {DateTime.fromISO(job.modifiedDateTime).toFormat(
            'ccc MMM d yyyy HH:mm:ss'
          )}
        </>
      );
      return {
        name: job.id,
        inQueue: renderTooltip(inQueue),
        inProgress: renderTooltip(inProgress),
        complete: renderTooltip(complete),
        errors: renderError(failed),
        lastModifiedDate,
      };
    });
  };

  const dataRow =
    processingJobs &&
    processingJobs.map((processingJob) => {
      if (!processingJob.targetId) {
        return {
          name: '',
          inQueue: '',
          inProgress: '',
          complete: '',
          errors: '',
          lastModifiedDate: '',
          tasks: [],
          targetId: '',
        };
      }
      const name = (
        <div>
          <Typography>
            {processingJob.name || processingJob.targetId}
          </Typography>
        </div>
      );
      const jobs = processingJob.jobs ?? [];

      const inQueue = jobsByStatus(jobs, STATUS_PROCESSING_JOBS.inQueue.value);
      const inProgress = jobsByStatus(
        jobs,
        STATUS_PROCESSING_JOBS.inProgress.value
      );
      const complete = jobsByStatus(
        jobs,
        STATUS_PROCESSING_JOBS.complete.value
      );
      const failed = jobsByStatus(jobs, STATUS_PROCESSING_JOBS.errors.value);

      totalByStatus = {
        inQueue: totalByStatus.inQueue + inQueue.length,
        inProgress: totalByStatus.inProgress + inProgress.length,
        complete: totalByStatus.complete + complete.length,
        errors: totalByStatus.errors + failed.length,
      };

      const lastModifiedDate = (
        <>
          {DateTime.fromISO(processingJob.modifiedDateTime).toFormat(
            'ccc MMM d yyyy HH:mm:ss'
          )}
        </>
      );
      return {
        name: (
          <a
            className={styles['name-tdo-link-cell']}
            onClick={clickToViewDetail}
            data-tdoid={processingJob.targetId}
          >
            {name}
          </a>
        ),
        inQueue: renderTooltip(inQueue),
        inProgress: renderTooltip(inProgress),
        complete: renderTooltip(complete),
        errors: renderTooltip(failed),
        lastModifiedDate,
        tasks: renderChild(jobs),
        targetId: processingJob.targetId,
      };
    });

  const isCheckboxDisabled = !(isFilterJobsFailed && processingJobs.length);
  function onChangePage(
    _event: React.MouseEvent<HTMLElement> | null,
    currentPage: number
  ) {
    if (currentPage * pageSize === processingJobs.length) {
      onChangePageProcessingJobs(currentPage, pageSize);
    } else {
      updateCurrentPageProcessingJobs(currentPage);
    }
  }

  function onChangePageSize(event: React.ChangeEvent<HTMLInputElement>) {
    const pageSize = Number(event.target.value);
    onChangePageSizeProcessingJobs(0, pageSize);
  }
  function handleClickDateTimeRangeFilter(
    event: React.MouseEvent<HTMLElement>
  ) {
    setAnchorElDateTimeRangeFilter(event.currentTarget);
  }
  function handleFilterJobByDateTimeRange(
    event: React.MouseEvent<HTMLElement>
  ) {
    clearCustomDateRange();
    const value = Number(event.currentTarget.getAttribute('data-value'));
    filterJobByDateTimeRange(value);
    handleCloseDateTimeRangeFilter();
  }
  function handleClickStatusFilter(event: React.MouseEvent<HTMLElement>) {
    setAnchorElStatusFilter(event.currentTarget);
  }
  function handleChangeStatusFilter(
    event: React.ChangeEvent<HTMLInputElement>
  ) {
    const { name, checked } = event.target;
    onChangeStatusFilter({ name, checked });
  }
  function handleCloseStatusFilter() {
    setAnchorElStatusFilter(null);
  }
  function handleCloseDateTimeRangeFilter() {
    setAnchorElDateTimeRangeFilter(null);
  }
  function handleFilterJobByStatus() {
    filterJobByStatus();
    handleCloseStatusFilter();
  }
  function handleClearCustomRange() {
    clearCustomDateRange();
    filterJobByDateTimeRange(DATE_TIME_RANGE_FILTER[0].value);
  }
  function handleRefreshProcessingStatus() {
    refreshProcessingJobs();
  }
  const openStatusFilter = Boolean(anchorElStatusFilter);
  const openDateTimeRangeFilter = Boolean(anchorElDateTimeRangeFilter);
  function handleRetryJobs() {
    retryJobs();
  }
  function changeSelection(
    selection: Array<number | string>,
    targetId: string
  ) {
    const newSelectedRows: { [key: string]: Array<number | string> } = {
      ...selectedRows,
    };
    if (selection.length) {
      newSelectedRows[targetId] = selection;
    } else {
      delete newSelectedRows[targetId];
    }
    const count = Object.values(newSelectedRows).flatMap((item) => item).length;

    onSelectionJobsChange(
      newSelectedRows,
      count === totalProcessingJobs,
      count > 0 && count < totalProcessingJobs,
      targetId
    );
  }
  function onSelectAllClick(event: React.ChangeEvent<HTMLInputElement>) {
    const newSelectedRows: { [key: string]: Array<number | string> } = {};
    if (event.target.checked) {
      processingJobs
        .filter((processingJob) => processingJob.targetId)
        .forEach((processingJob) => {
          newSelectedRows[processingJob.targetId] = Object.keys(
            processingJob.jobs
          ).map((key) => Number(key));
        });
      onSelectionJobsChange(newSelectedRows, true, false, '');
    } else {
      onSelectionJobsChange(newSelectedRows, false, false, '');
    }
  }
  function getDateTimeRange(dateTimeRangeFilter: number) {
    const currentDateTime =
      DATE_TIME_RANGE_FILTER.find(
        (dateTime) => dateTime.value === dateTimeRangeFilter
      ) || DATE_TIME_RANGE_FILTER[0];

    return currentDateTime;
  }
  function handleReprocess() {
    openModalReprocess();
  }

  function handleExportError() {
    exportFailedJobs();
  }

  const gridDetailContainer = ({ row }: any) => (
    <Paper key={row.targetId}>
      <Grid rows={row.tasks} columns={columns}>
        <SelectionState
          selection={selectedRows[row.targetId]}
          onSelectionChange={(selection) =>
            changeSelection(selection, row.targetId)
          }
        />
        <IntegratedSelection />
        <Table columnExtensions={tableColumnExtensions} />
        <TableSelection showSelectionColumn={!isCheckboxDisabled} />
      </Grid>
    </Paper>
  );
  const onExpandedRowIdsChange = (row: Array<number | string>) => {
    setExpandedRowIds(row);
  };

  function cellComponent({ row, value, children, ...restProps }: any) {
    if (!row.targetId) {
      return (
        <Table.Cell {...restProps} style={{ border: 'none', height: 0 }} />
      );
    }
    return <Table.Cell {...restProps}>{value || children}</Table.Cell>;
  }

  function toggleCellComponent({
    row,
    onToggle,
    expanded,
  }: {
    row: any;
    onToggle: () => void;
    expanded: boolean;
  }) {
    if (!row.targetId) {
      return null;
    }
    return (
      <TableCell>
        {expanded ? (
          <IconButton onClick={(e) => onClickToggle(e, onToggle)} size="large">
            <ExpandLessIcon />
          </IconButton>
        ) : (
          <IconButton onClick={(e) => onClickToggle(e, onToggle)} size="large">
            <ExpandMoreIcon />
          </IconButton>
        )}
      </TableCell>
    );
  }
  function onClickToggle(
    e: React.MouseEvent<HTMLElement>,
    callback: () => void
  ) {
    e.stopPropagation();
    callback();
  }
  return (
    <Fragment>
      <div className={styles.mainFilter}>
        <Button
          variant="contained"
          color="primary"
          endIcon={<ArrowDropDownIcon />}
          onClick={handleClickStatusFilter}
          className={styles.filterType}
        >
          {`Status Filter`}
        </Button>
        <Popover
          open={openStatusFilter}
          anchorEl={anchorElStatusFilter}
          onClose={handleCloseStatusFilter}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <FormControl component="fieldset" className={styles.formControl}>
            <FormGroup>
              {statusFilter.map((status) => (
                <FormControlLabel
                  key={status.name}
                  control={
                    <Checkbox
                      checked={status.checked}
                      onChange={handleChangeStatusFilter}
                      name={status.name}
                    />
                  }
                  label={status.name}
                />
              ))}
            </FormGroup>
          </FormControl>
          <div className={styles.mainApplyFilter}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleFilterJobByStatus}
              className={styles.btnApplyFilter}
            >
              Apply Filter
            </Button>
          </div>
        </Popover>

        <Button
          variant="contained"
          color="primary"
          startIcon={<ScheduleIcon />}
          endIcon={<ArrowDropDownIcon />}
          onClick={handleClickDateTimeRangeFilter}
          className={styles.filterType}
        >
          {showCustomRange
            ? 'Custom Range'
            : `${getDateTimeRange(dateTimeRangeFilter).name} ago`}
        </Button>
        <Popover
          open={openDateTimeRangeFilter}
          anchorEl={anchorElDateTimeRangeFilter}
          onClose={handleCloseDateTimeRangeFilter}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <div style={{ width: 200 }}>
            {DATE_TIME_RANGE_FILTER.map((date) => (
              <MenuItem
                key={date.name}
                value={date.value}
                onClick={handleFilterJobByDateTimeRange}
                data-value={date.value}
              >
                {date.name}
              </MenuItem>
            ))}
            <MenuItem
              value="Custom Range"
              data-value="Custom Range"
              onClick={() => {
                setShowCustomRange(true);
                setAnchorElDateTimeRangeFilter(null);
              }}
            >
              Custom Range
            </MenuItem>
          </div>
        </Popover>
        {showCustomRange && (
          <ThemeProvider theme={timePickerTheme}>
            <Box className={styles['margin-left']}>
              <LocalizationProvider
                dateAdapter={AdapterLuxon}
                localeText={customLabelStart}
              >
                <MobileDateTimePicker
                  slotProps={{
                    textField: {
                      size: 'small',
                      InputProps: {
                        placeholder: 'Start Date/Time',
                        endAdornment: (
                          <InputAdornment position="end">
                            <EventIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  value={customStartDate}
                  onChange={(customStartDate: DateTime | null) => {
                    if (customStartDate !== null) {
                      setCustomStartDate(customStartDate);
                    }
                  }}
                  ampm={false}
                  disableFuture
                  open={isStartDateOpen}
                  onOpen={() => setIsStartDateOpen(true)}
                  onClose={() => setIsStartDateOpen(false)}
                  onAccept={() => {
                    setIsEndDateOpen(true);
                    if (isValidDatePickers()) {
                      filterJobByCustomDateRange();
                    }
                  }}
                  minDate={DateTime.now().minus({ days: 90 })}
                  maxDate={DateTime.now()}
                />
              </LocalizationProvider>
            </Box>
            <Box className={styles['margin-left']}>
              <LocalizationProvider
                dateAdapter={AdapterLuxon}
                localeText={customLabelEnd}
              >
                <MobileDateTimePicker
                  slotProps={{
                    textField: {
                      size: 'small',
                      InputProps: {
                        placeholder: 'End Date/Time',
                        endAdornment: (
                          <InputAdornment position="end">
                            <EventIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  value={customEndDate}
                  onChange={(customEndDate: DateTime | null) => {
                    if (customEndDate !== null) {
                      setCustomEndDate(customEndDate);
                    }
                  }}
                  ampm={false}
                  disableFuture
                  open={isEndDateOpen}
                  onOpen={() => setIsStartDateOpen(true)}
                  onClose={() => setIsEndDateOpen(false)}
                  onAccept={() => {
                    setIsEndDateOpen(false);
                    if (isValidDatePickers()) {
                      filterJobByCustomDateRange();
                    }
                  }}
                  minDate={customStartDate ?? DateTime.now().minus({ days: 1 })}
                  maxDate={DateTime.now()}
                />
              </LocalizationProvider>
            </Box>
          </ThemeProvider>
        )}
        {showCustomRange && (
          <IconButton
            data-test="clear-customRange"
            onClick={handleClearCustomRange}
            size="large"
          >
            <Tooltip title={`Clear custom date ranges`} placement="bottom">
              <ClearIcon />
            </Tooltip>
          </IconButton>
        )}
        <IconButton
          data-test="refresh-processing-status"
          onClick={handleRefreshProcessingStatus}
          size="large"
        >
          <Tooltip title={`Refresh`} placement="bottom">
            <Cached />
          </Tooltip>
        </IconButton>
        {!loadingRetryJobs ? (
          <IconButton
            onClick={handleRetryJobs}
            disabled={
              isCheckboxDisabled ||
              (!isCheckboxDisabled && isEmpty(selectedRows)) ||
              loadingRetryJobs ||
              exportingFailedJobs
            }
          >
            <Tooltip title={`Retry Jobs`} placement="bottom">
              <RefreshIcon />
            </Tooltip>
          </IconButton>
        ) : (
          <Box
            position="relative"
            display="inline-flex"
            top={10}
            left={10}
            sx={{ width: 48 }}
          >
            <CircularProgress
              size={28}
              variant="determinate"
              value={percentageFailedJobsUploaded}
            />
            <Box
              top={0}
              left={0}
              bottom={0}
              right={0}
              position="absolute"
              display="flex"
              alignItems="center"
              justifyContent="flex-start"
            >
              <Typography
                variant="caption"
                component="div"
                color="textSecondary"
              >{`${Math.round(percentageFailedJobsUploaded)}%`}</Typography>
            </Box>
          </Box>
        )}
        <IconButton
          onClick={handleReprocess}
          data-test="reprocess-file-icon-button"
          data-testid="icon-button"
          disabled={
            isCheckboxDisabled ||
            (!isCheckboxDisabled && isEmpty(selectedRows)) ||
            loadingRetryJobs ||
            exportingFailedJobs
          }
          size="large"
        >
          <Tooltip title={`Run process`} placement="bottom">
            <Icon className="icon-enginerunning" />
          </Tooltip>
        </IconButton>
        {!exportingFailedJobs ? (
          <IconButton
            onClick={handleExportError}
            data-test="export-error-icon-button"
            // data-type={isSelectedAll ? 'exportAll' : 'exportSingle'}
            data-testid="icon-button"
            disabled={
              isCheckboxDisabled ||
              (!isCheckboxDisabled && isEmpty(selectedRows)) ||
              loadingRetryJobs ||
              exportingFailedJobs
            }
          >
            <Tooltip title={`Export Error Details`} placement="bottom">
              <SaveAlt data-testid="save-alt" />
            </Tooltip>
          </IconButton>
        ) : (
          <Box
            position="relative"
            display="inline-flex"
            top={10}
            left={10}
            sx={{ width: 48 }}
          >
            <CircularProgress
              size={28}
              variant="determinate"
              value={exportFailedJobsPercentage}
            />
            <Box
              top={0}
              left={3}
              bottom={0}
              right={0}
              position="absolute"
              display="flex"
              alignItems="center"
              justifyContent="flex-start"
            >
              <Typography
                variant="caption"
                component="div"
                color="textSecondary"
              >{`${Math.round(exportFailedJobsPercentage)}%`}</Typography>
            </Box>
          </Box>
        )}
      </div>
      <Paper className={styles['table']}>
        <Grid rows={dataRow} columns={columns}>
          <GroupingState
            grouping={[]}
            expandedGroups={[]}
            columnGroupingEnabled={false}
          />
          <PagingState currentPage={currentPage} pageSize={pageSize} />

          <RowDetailState
            expandedRowIds={expandedRowIds}
            onExpandedRowIdsChange={onExpandedRowIdsChange}
          />

          <SelectionState />
          <IntegratedGrouping />
          <IntegratedSelection />
          <IntegratedPaging />
          <CustomPaging totalCount={totalProcessingJobs} />
          <VirtualTable
            columnExtensions={tableColumnExtensions}
            noDataCellComponent={myCustomDataCellComponent}
            cellComponent={cellComponent}
          />
          <TableHeaderRow
            cellComponent={(row: CellProps) => cellHeader(row, totalByStatus)}
          />

          <TableRowDetail
            contentComponent={gridDetailContainer}
            toggleCellComponent={toggleCellComponent}
          />
          <PagingPanel
            containerComponent={myCustomPagination}
            pageSizes={[10, 20, 100]}
          />
        </Grid>
        {loadingProcessingJobs && (
          <div className={styles['loadding-data']}>
            <CircularProgress size={30} variant="indeterminate" />
          </div>
        )}
      </Paper>
    </Fragment>
  );
}

function getFailureMessage(job: Job) {
  const failedTasks = job.tasks.records.filter((task) =>
    isTaskInStatus(task, TASK_FAILED_STATUS)
  );
  if (failedTasks.length === 0) {
    return '';
  }
  const failedMsg = failedTasks.map(
    (task) => task?.taskOutput?.failureMessage ?? task?.failureMessage ?? ''
  );
  const uniqMsg = uniq(failedMsg);
  return uniqMsg.join('|');
}

function jobsByStatus(jobs: Job[], status: readonly string[]) {
  return jobs.filter((job) => status.includes(job.status));
}

function isJobInStatus(job: Job, status: readonly string[]) {
  return status.includes(job?.status);
}

function isTaskInStatus(task: Task, status: readonly string[]) {
  return status.includes(task?.status);
}

interface Column {
  name: string;
  title?: string;
  showBadge?: boolean;
}
interface TableRow {
  key: string;
  type: symbol;
  rowId?: number | string;
  row?: any;
  height?: number;
}
interface TableColumn {
  key: string;
  type: symbol;
  column?: Column;
  width?: number | string;
  align?: 'left' | 'right' | 'center';
  fixed?: 'left' | 'right';
}
interface NoDataCellProps {
  getMessage: (messageKey: string) => string;
  tableRow: TableRow;
  tableColumn: TableColumn;
  colSpan?: number;
  rowSpan?: number;
}
type CellWidthGetter = () => number;

interface CellProps {
  column: Column;
  resizingEnabled: boolean;
  onWidthChange: (parameters: { shift: number }) => void;
  onWidthDraft: (parameters: { shift: number }) => void;
  onWidthDraftCancel(): void;
  getCellWidth: (getter: CellWidthGetter) => void;
  draggingEnabled: boolean;
  children: React.ReactNode;
  tableRow: TableRow;
  tableColumn: TableColumn;
}
// interface ColumnExtension {
//   columnName: string;
//   width?: number | string;
//   align?: 'left' | 'right' | 'center';
//   wordWrapEnabled?: boolean;
// }

// interface Props {
//   enginesDefault: {
//     id: string;
//     name: string;
//   }[];
//   statusFilter: {
//     name: string;
//     checked: boolean;
//   }[];
//   selectedRows: { [key: string]: number[] };
//   showCustomRange: boolean;
// }

const mapState = (state: any) => ({
  processingJobs: processingJobs(state),
  loadingProcessingJobs: loadingProcessingJobs(state),
  noDataProcessing: noDataProcessing(state),
  enginesDefault: enginesDefault(state),
  disableAnalytics: getDisableAnalytics(state),
  currentPage: currentPage(state),
  pageSize: pageSize(state),
  statusFilter: statusFilter(state),
  dateTimeRangeFilter: dateTimeRangeFilter(state),
  loadingRetryJobs: getLoadingRetryJobs(state),
  percentageFailedJobsUploaded: getPercentageFailedJobsUploaded(state),
  selectedRows: getSelectedRows(state),
  indeterminate: getIndeterminate(state),
  isSelectedAll: getIsSelectedAllJobsFailed(state),
  totalProcessingJobs: getTotalProcessingJobs(state),
  isFilterJobsFailed: getIsFilterJobsFailed(state),
  showCustomRange: getShowCustomRange(state),
  customStartDate: getCustomStartDate(state),
  customEndDate: getCustomEndDate(state),
  remainingProcessingJobs: getRemainingProcessingJobs(state),
  loadingChangePage: loadingChangePage(state),
  exportingFailedJobs: getExportingFailedJobs(state),
  exportFailedJobsPercentage: getExportFailedJobsPercentage(state),
});
const mapDispatch = {
  refreshProcessingJobs: refreshProcessingJobs,
  onChangePageProcessingJobs: onChangePageProcessingJobs,
  onChangePageSizeProcessingJobs: onChangePageSizeProcessingJobs,
  onChangeStatusFilter: onChangeStatusFilter,
  filterJobByStatus: filterJobByStatus,
  filterJobByDateTimeRange: filterJobByDateTimeRange,
  navigateToFile: (payload: {
    tab: string;
    tdoId: string | null;
    initFullScreen: boolean;
  }) => ROUTE_FILE(payload),
  retryJobs: retryJobs,
  onSelectionJobsChange: onSelectionJobsChange,
  updateCurrentPageProcessingJobs: updateCurrentPageProcessingJobs,
  openModalReprocess: openModalReprocess,
  setShowCustomRange: setShowCustomRange,
  setCustomStartDate: setCustomStartDate,
  setCustomEndDate: setCustomEndDate,
  filterJobByCustomDateRange: filterJobByCustomDateRange,
  clearCustomDateRange: clearCustomDateRange,
  exportFailedJobs: exportFailedJobs,
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(ProcessingJobs);
