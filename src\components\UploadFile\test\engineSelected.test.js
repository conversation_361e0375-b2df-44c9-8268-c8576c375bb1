import React from 'react';
import EngineSelected from '../engineSelected';
import configureStore from 'redux-mock-store';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('EngineSelected', () => {
  const props = {
    librariesByCategories: {
      '6faad6b7-0837-45f9-b161-2f6bf31b7a07': {
        '4e48925b-2cfc-43b3-8794-53e6e683651f': {
          coverImageUrl: '',
          createdDateTime: '2021-01-12T09:40:23.000Z',
          id: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          libraryId: '4e48925b-2cfc-43b3-8794-53e6e683651f',
          name: 'Quan_Test3',
          organizationId: '1',
          summary: { entityCount: 1, unpublishedEntityCount: 0 },
          version: 1,
        },
      },
    },
    enginesSelected: [
      {
        categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        categoryName: 'Transcription',
        engineIds: [
          {
            id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
            description: 'This engine converts US English speech to text.',
            edgeVersion: 3,
            mode: 'Chunk',
            name: 'Speechmatics Transcription - English (Global) V3',
            runtimeType: 'edge',
            standaloneJobTemplates: [],
            fields: [
              {
                defaultValue: 'true',
                info: 'diarise',
                label: 'diarise',
                max: null,
                min: null,
                name: 'diarise',
                step: null,
                type: 'Picklist',
                options: [
                  {
                    keys: 'false',
                    value: 'false',
                  },
                  {
                    keys: 'true',
                    value: 'true',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    engineByCategories: {
      '67cd4dd0-2f75-445d-a6f0-2f297d6cd182': [
        {
          id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
          isPublic: true,
          isSelected: false,
          name: 'Speechmatics Transcription - English (Global) V3',
          description: 'This engine converts US English speech to text.',
        },
      ],
    },
    templateSelected: '',
    handleChangeTemplates: jest.fn(),
    templates: [],
    handleShowModalSaveTemplate: jest.fn(),
    checkValidateLibrary: false,
    handleExpandClick: jest.fn(),
    handleRemoveEngine: jest.fn(),
    handleChangeLibrariesEngineSelected: jest.fn(),
    handleChangeFieldsEngine: jest.fn(),
    handleChangeJobPriority: jest.fn(),
    handleRemoveTemplate: jest.fn(),
    isReprocess: false,
  };

  it('renders a input label with content Your Selected Engines', () => {
    const { getByText } = render(<EngineSelected {...props} />);
    expect(getByText('Your Selected Engines')).toBeInTheDocument();
  });
  it('renders a template engine Select', () => {
    const { getByTestId } = render(<EngineSelected {...props} />);
    expect(getByTestId('select-template-engine')).toBeInTheDocument();
  });
  it('renders a save template IconButton', () => {
    const { getByTestId } = render(<EngineSelected {...props} />);
    expect(getByTestId('show-modal-save-template-engine')).toBeInTheDocument();
  });
  it('renders a list engine Paper', () => {
    const { getByTestId } = render(<EngineSelected {...props} />);
    expect(getByTestId('list-engine-paper')).toBeInTheDocument();
  });
});
