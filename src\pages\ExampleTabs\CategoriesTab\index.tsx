import React from 'react';
import { ConnectedProps, connect } from 'react-redux';
import Typography from '@mui/material/Typography';
import Table from '@mui/material/Table';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

import {
  engineCategoriesAreLoading,
  selectEngineCategories,
} from 'state/modules/engines-example';
import RouteLoadingScreen from 'components/RouteLoadingScreen';

class CategoriesTab extends React.Component<PropsFromRedux> {
  static defaultProps = {};

  render() {
    return (
      <div>
        <Typography variant="inherit" gutterBottom>
          Engine Categories:
        </Typography>
        <Typography variant="body1" gutterBottom>
          (fetched when this tab is loaded)
        </Typography>
        {this.props.loading ? (
          <RouteLoadingScreen delay={500} />
        ) : (
          <Paper>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Icon Class</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {this.props.categories.map((category) => {
                  return (
                    <TableRow key={category.id}>
                      <TableCell>{category.id}</TableCell>
                      <TableCell>{category.name}</TableCell>
                      <TableCell>
                        {JSON.stringify(category.type, null, '\t')}
                      </TableCell>
                      <TableCell>{category.iconClass}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </Paper>
        )}
      </div>
    );
  }
}

export interface Category {
  id: string;
  name: string;
  iconClass: string;
  type: {
    name: string;
    description: string;
  };
}

const mapState = (state: any) => ({
  loading: engineCategoriesAreLoading(state),
  categories: selectEngineCategories(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(CategoriesTab);
