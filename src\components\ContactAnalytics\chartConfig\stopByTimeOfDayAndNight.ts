import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { DateTime } from 'luxon';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "06:00",
                  lt: "19:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'dayTimeAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "or"
                    conditions: [
                      { field: "timeOfStop", operator: "range", lt: "06:00" }
                      { field: "timeOfStop", operator: "range", gte: "19:00" }
                  ]
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'nightTimeAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stops By Time of Day and Night',
  filterTextType: 'Stops By Time of Day and Night by Type',
  filterType: 'datetimeOfStop',
  filterTerms: {
    'Daytime Hours (06:00-19:00)': {
      queryFilter: `{
        field: "timeOfStop",
        operator: "range",
        gte: "06:00",
        lt: "19:00"
      }`,
      storageKey: 'dayTimeAggregation',
    },
    'Night Time (19:00-06:00)': {
      queryFilter: `{
        operator: "or"
          conditions: [
            { field: "timeOfStop", operator: "range", lt: "06:00" }
            { field: "timeOfStop", operator: "range", gte: "19:00" }
        ]
      }`,
      storageKey: 'nightTimeAggregation',
    },
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const chartData =
      data.dayTimeAggregation?.map((dateAgg) => {
        return {
          date: dateAgg.key_as_string,
          'Day Time': dateAgg.doc_count,
          'Night Time':
            data.nightTimeAggregation?.find(
              (agg) => agg.key_as_string === dateAgg.key_as_string
            )?.doc_count ?? 0,
        };
      }) ?? [];

    data.nightTimeAggregation?.forEach((dateAgg) => {
      if (!chartData?.find((d) => d.date === dateAgg.key_as_string)) {
        chartData?.push({
          date: dateAgg.key_as_string,
          'Day Time': 0,
          'Night Time': dateAgg?.doc_count,
        });
      }
    });

    chart.data = chartData?.sort(
      (a, b) =>
        DateTime.fromISO(a.date).toUnixInteger() -
        DateTime.fromISO(b.date).toUnixInteger()
    );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    ['Day Time', 'Night Time'].forEach((pr) => {
      // Create series
      const series = chart.series?.push(new am4charts.LineSeries());
      series.strokeWidth = config.lineWidth;
      series.dataFields.valueY = pr;
      series.dataFields.dateX = 'date';
      series.name = pr;
      series.tooltipText = '{name}: [bold]{valueY}[/]';
    });

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  dayTimeAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
  }[];
  nightTimeAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
  }[];
}
