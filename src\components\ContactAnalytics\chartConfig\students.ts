import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { AxisItemLocation, zeroAggregationsByDateRange } from './util';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  { field: "stopForAStudent", operator: "term", limit: 10000 }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'studentsAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'Students',
  filterTextType: 'Student by Type',
  filterType: 'stopForAStudent',
  filterTerms: { Student: ['true'], 'Non-Student': ['false'] },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = zeroAggregationsByDateRange(
      config,
      { Student: 0, 'Non-Student': 0 },
      data.studentsAggregation.map((dateAgg) => {
        return {
          date: dateAgg.key_as_string,
          ...dateAgg.stopForAStudent.buckets.reduce(
            (acc: { [key: string]: number }, b) => {
              if (b.key_as_string === 'true') {
                acc['Student'] = b.doc_count;
              }
              if (b.key_as_string === 'false') {
                acc['Non-Student'] = b.doc_count;
              }
              return acc;
            },
            {} // this reduce need to return an object to add 2 properties "Student" and "Non-Student"
          ),
        };
      })
    );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    ['Student', 'Non-Student'].forEach((pr) => {
      // Create series
      const series = chart.series.push(new am4charts.LineSeries());
      series.strokeWidth = config.lineWidth;
      series.dataFields.valueY = pr;
      series.dataFields.dateX = 'date';
      series.name = pr;
      series.tooltipText = '{name}: [bold]{valueY}[/]';
    });

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  studentsAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
    stopForAStudent: {
      buckets: { doc_count: number; key: number; key_as_string: string }[];
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
    };
  }[];
}
