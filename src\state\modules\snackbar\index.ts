import { AlertColor } from '@mui/material/Alert';
import { createAction, createReducer } from '@reduxjs/toolkit';

export const namespace = 'notification';

const SHOW_SNACKBAR = createAction<{
  message: string;
  variant: AlertColor | undefined;
}>('show snackbar');
const HIDE_SNACKBAR = createAction('hide snackbar');

const defaultState = {
  snackbar: {
    open: false,
    message: '',
    variant: '' as AlertColor | undefined,
  },
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(SHOW_SNACKBAR, (state, action) => {
      if (!action.payload.message) {
        return {
          ...state,
        };
      }
      return {
        ...state,
        snackbar: {
          open: true,
          message: action.payload.message,
          variant: action.payload.variant,
        },
      };
    })
    .addCase(HIDE_SNACKBAR, (state) => {
      return {
        ...state,
        snackbar: defaultState.snackbar,
      };
    });
});
export const showNotification = (
  message: string,
  variant: AlertColor | undefined
) =>
  SHOW_SNACKBAR({
    message,
    variant,
  });
export const hideSnackbarNotification = () => HIDE_SNACKBAR();

export default reducer;
export const local = (state: any) => state[namespace] as typeof defaultState;
export const selectSnackbarNotificationState = (state: any) =>
  local(state).snackbar;
