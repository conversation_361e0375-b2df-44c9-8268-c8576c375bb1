import axios from 'axios';
import { ApiError, ReportsError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface ReportsResponse {
  value: {
    '@odata.context': string;
    id: string;
    name: string;
    embedUrl: string;
  }[];
}

const getReportsInWorkspace = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const { data: resp } = await axios.get<ReportsResponse>(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/reports`,
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );
    const report = resp.value.find((v) => v.name === data.datasetDisplayName);

    if (!report) {
      log.error('Reports not available!');
      throw new ReportsError();
    }

    data.reportId = report.id;
    data.reportName = report.name;
    data.embedUrl = report.embedUrl;

    return context;
  } catch (e) {
    log.error('Reports API failed', e);
    throw new ApiError(e);
  }
};

export default getReportsInWorkspace;
