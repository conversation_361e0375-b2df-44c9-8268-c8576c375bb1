import { Response } from 'express';
import { RequestWithMeta } from '../types';
import { gitInfo } from './gitInfo';

function createHealthCheckApp() {
  const handlers = {
    get: {
      async handleHealthCheck(_req: RequestWithMeta, res: Response) {
        try {
          const info = await gitInfo();
          res.set('Content-Type', 'text/plain');
          res.send(info.shortCommit);
        } catch (e) {
          res.status(500).send(e.message);
        }
      },
    },
  };

  return { handlers };
}

export default createHealthCheckApp;
