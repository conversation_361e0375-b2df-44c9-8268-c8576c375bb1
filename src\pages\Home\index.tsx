import React, { Fragment } from 'react';
import { getSegment, getPendo } from '../../helpers/analyticsHelper';
import AppContainer from 'components/AppContainer';
import AppBar from 'components/AppBar';
import TopBar from 'components/TopBar';
import ContentContainer from 'components/ContentContainer';
import DashBoard from 'components/DashBoard';
import * as styles from './styles.scss';
import { ConnectedProps, connect } from 'react-redux';
import { lowerCase } from 'lodash';
import {
  getShowFormCreateFolder,
  HIDE_FORM_CREATE_FOLDER,
  FETCH_CREATE_FOLDER_REQUEST,
  FETCH_UPDATE_FOLDER_REQUEST,
  getFolders,
  getShowMoveFolder,
  HIDE_MOVE_FOLDER,
  FETCH_UPDATE_MOVE_FOLDER_REQUEST,
  getShowFolder,
  HIDE_FOLDER,
  SHOW_FORM_CREATE_FOLDER,
  getActionFolderId,
  getOpenFolderId,
  SET_SELECTED_FOLDER_ID,
  getWidthFolder,
  UPDATE_OPEN_FOLDER_IDS,
  getCaseOptions,
  getSelectedFolderId,
} from '../../state/modules/folders';
import CreateFolder from '../../components/CreateFolder';
import GlobalSnackbar from '../../components/GlobalSnackbar';
import MoveFolder from '../../components/MoveFolder';
import FolderModal from '../../components/FolderModal';
import {
  getConfirmNotificaions,
  FETCH_REMOVE_NOTIFICATION_REQUEST,
  FETCH_HIDE_CONFIRM_REMOVE_NOTIFICATION,
  getIsShowBulkExport,
  HIDE_BULK_EXPORT,
  INTERNAL_FOLDER_NAME,
} from '../../state/modules/tdosTable';
import { BULK_EXPORT_REQUEST } from '../../state/modules/bulkExport';
import { getMeRoles } from '../../state/modules/app';

import ConfirmNotificationModal from '../../components/ConfirmNotificationModal';
import { BulkExport } from '../../components/BulkExport';
import UploadFile from '../../components/UploadFile';
import { isShowConfirmSaveTemplate } from '../../state/modules/uploadFile';
import {
  hideConfirmSaveTemplate,
  updateTemplate,
} from '../../state/modules/uploadFile/actions';
import { navigateCurrentTab } from '../../state/modules/routing';
import { BulkExportOption, BulkExportCustomizedNames } from '../../model';
import {
  getExportTemplateData,
  FETCH_SAVE_EXPORT_TEMPLATE_DATA_REQUEST,
  FETCH_DELETE_EXPORT_TEMPLATE_DATA_REQUEST,
} from '../../state/modules/bulkExport/exportTemplate';
class Home extends React.Component<PropsFromRedux> {
  state = {
    folderName: '',
  };
  componentDidMount() {
    document.title = 'Veritone | Illuminate';
  }
  closeFormModalCreateFolder = () => {
    const { handleHideCreateFolder } = this.props;
    handleHideCreateFolder();
    this.setState({ folderName: '' });
  };

  handleDeletingExportTemplate = (id: string) => {
    const { handleDeleteExportTemplate } = this.props;

    handleDeleteExportTemplate(id);
  };

  handleSaveExportTemplate = (
    name: string,
    fields: BulkExportOption,
    customizedNames: BulkExportCustomizedNames,
    id: string
  ) => {
    const { handleCreateExportTemplate } = this.props;

    handleCreateExportTemplate(name, fields, customizedNames, id);
  };

  handleSubmitForm = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const { folderName } = this.state;
    const {
      handleCreateFolder,
      handleUpdateFolder,
      typeShowFormCreateFolder,
      actionFolderId,
      caseOptions,
      selectedFolderId,
    } = this.props;
    if (typeShowFormCreateFolder === 'rename') {
      const id = caseOptions === 'tab' ? selectedFolderId : actionFolderId;
      handleUpdateFolder(id, folderName);
    } else {
      handleCreateFolder(folderName);
    }
    this.closeFormModalCreateFolder();
  };
  closeMoveFolder = () => {
    const { handleHideMoveFolder } = this.props;
    handleHideMoveFolder();
  };
  handleMoveFolder = () => {
    const { handeMoveFolder, handleHideMoveFolder } = this.props;
    handeMoveFolder();
    handleHideMoveFolder();
  };
  handleCloseFolderModal = () => {
    const { handleHideFolderModal, updateOpenFolderId } = this.props;
    handleHideFolderModal();
    updateOpenFolderId();
  };
  handleConFirmFolderModal = () => {
    const { handleSelectedFolder, handleHideFolderModal, navigateCurrentTab } =
      this.props;
    navigateCurrentTab();
    handleSelectedFolder();
    handleHideFolderModal();
  };
  handleShowCreateFolder = () => {
    const { handleShowCreateFolder } = this.props;
    handleShowCreateFolder({ type: 'create' });
  };
  onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      folderName: event.target.value,
    });
  };
  handleConfirmRemoveNotification = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
    const {
      confirmNotificaions,
      fetchRemoveNotificationAction,
      fetchHideRemoveNotification,
    } = this.props;
    fetchRemoveNotificationAction(confirmNotificaions.notificationId);
    fetchHideRemoveNotification();
  };
  handleCancelRemoveNotification = () => {
    const { fetchHideRemoveNotification } = this.props;
    fetchHideRemoveNotification();
  };
  handleCloseBulkExportModal = () => {
    this.props.hideBulkExport();
  };
  handleConFirmBulkExport = (
    exportName: string,
    exportOption: BulkExportOption,
    password: string,
    customizedNames: BulkExportCustomizedNames
  ) => {
    const { createBulkExport } = this.props;
    const payload = {
      exportName,
      exportOption,
      password,
      customizedNames,
    };
    createBulkExport(payload);
    this.props.hideBulkExport();
  };
  handleConfirmSaveTemplate = () => {
    const { updateTemplate } = this.props;
    updateTemplate();
  };
  handleCancelSaveTemplate = () => {
    const { hideConfirmSaveTemplate } = this.props;
    hideConfirmSaveTemplate();
  };
  render() {
    const {
      typeShowFormCreateFolder,
      actionFolderId,
      folders,
      isShowMoveFolder,
      isShowFolder,
      openFolderId,
      widthFolder,
      confirmNotificaions,
      selectedFolderId,
      caseOptions,
      isShowBulkExport,
      isShowConfirmSaveTemplate,
      exportTemplateData,
      roles,
    } = this.props;
    const folderDetail = // safe due to folders = makeFolderRoot with openFolderId, selectedFolderId, actionFolderId = rootFolderId is the Key
      typeShowFormCreateFolder === 'create'
        ? folders[openFolderId]!
        : caseOptions === 'tab'
          ? folders[selectedFolderId]!
          : folders[actionFolderId]!;
    const { folderName } = this.state;
    const isExportTemplateEditEnabled = roles.some(
      (role) => role.name === 'Illuminate Admin'
    );
    return (
      <Fragment>
        <AppBar />
        <TopBar />
        <AppContainer>
          <ContentContainer>
            <DashBoard
              classes={{
                root: styles.tabs! /* safe due to tabs exists in styles */,
              }}
            />
          </ContentContainer>
        </AppContainer>
        {typeShowFormCreateFolder && (
          <CreateFolder
            open
            onClose={this.closeFormModalCreateFolder}
            onSubmit={this.handleSubmitForm}
            type={typeShowFormCreateFolder}
            folderDetail={folderDetail}
            onChange={this.onChange}
            pristine={
              !folderName ||
              (typeShowFormCreateFolder === 'rename' &&
                folderName === folderDetail.name) ||
              lowerCase(folderName) === lowerCase(INTERNAL_FOLDER_NAME)
            }
          />
        )}
        {isShowMoveFolder && (
          <MoveFolder
            open
            onClose={this.closeMoveFolder}
            onConfirm={this.handleMoveFolder}
          />
        )}
        <GlobalSnackbar />
        {isShowFolder && (
          <FolderModal
            open
            onClose={this.handleCloseFolderModal}
            onConfirm={this.handleConFirmFolderModal}
            handleCreateFolder={this.handleShowCreateFolder}
            widthFolder={widthFolder}
            disabled={!!folders[openFolderId]?.fetchingSubFolders}
          />
        )}
        {confirmNotificaions.isShowConfirm && (
          <ConfirmNotificationModal
            open
            title={'Remove Notifications'}
            onConfirm={this.handleConfirmRemoveNotification}
            description={`The export job is in processed, if you remove it from notifications, you cannot get its status anymore.
            Are you sure you want to remove this export job from notifications?`}
            onClose={this.handleCancelRemoveNotification}
            cancelButtonText={'Cancel'}
            confirmButtonText={'Remove'}
          />
        )}
        {isShowBulkExport && (
          <BulkExport
            open={isShowBulkExport}
            onClose={this.handleCloseBulkExportModal}
            onConfirm={this.handleConFirmBulkExport}
            exportTemplateData={exportTemplateData}
            onSaveTemplate={this.handleSaveExportTemplate}
            handleDeletingExportTemplate={this.handleDeletingExportTemplate}
            isExportTemplateEditEnabled={isExportTemplateEditEnabled}
          />
        )}
        {isShowConfirmSaveTemplate && (
          <ConfirmNotificationModal
            open
            title={'Template Name Is Already Taken'}
            onConfirm={this.handleConfirmSaveTemplate}
            description={`Would you like to override the currently saved template?`}
            onClose={this.handleCancelSaveTemplate}
            cancelButtonText={'Cancel'}
            confirmButtonText={'Override'}
          />
        )}
        <UploadFile />
      </Fragment>
    );
  }
}

const mapState = (state: any) => ({
  typeShowFormCreateFolder: getShowFormCreateFolder(state),
  folders: getFolders(state),
  isShowMoveFolder: getShowMoveFolder(state),
  segment: getSegment(state, null),
  pendo: getPendo(state),
  isShowFolder: getShowFolder(state),
  actionFolderId: getActionFolderId(state),
  openFolderId: getOpenFolderId(state),
  widthFolder: getWidthFolder(state),
  confirmNotificaions: getConfirmNotificaions(state),
  caseOptions: getCaseOptions(state),
  selectedFolderId: getSelectedFolderId(state),
  isShowBulkExport: getIsShowBulkExport(state),
  isShowConfirmSaveTemplate: isShowConfirmSaveTemplate(state),
  exportTemplateData: getExportTemplateData(state),
  roles: getMeRoles(state),
});

const mapDispatch = {
  handleCreateExportTemplate: (
    name: string,
    fields: BulkExportOption,
    customizedNames: BulkExportCustomizedNames,
    id: string
  ) =>
    FETCH_SAVE_EXPORT_TEMPLATE_DATA_REQUEST({
      name,
      fields,
      customizedNames,
      id,
    }),
  handleDeleteExportTemplate: (id: string) =>
    FETCH_DELETE_EXPORT_TEMPLATE_DATA_REQUEST({ id }),
  handleCreateFolder: (name: string) => FETCH_CREATE_FOLDER_REQUEST(name),
  handleHideCreateFolder: () => HIDE_FORM_CREATE_FOLDER(),
  handleUpdateFolder: (id: string, name: string) =>
    FETCH_UPDATE_FOLDER_REQUEST({
      id,
      name,
    }),
  handleHideMoveFolder: () => HIDE_MOVE_FOLDER(),
  handeMoveFolder: () => FETCH_UPDATE_MOVE_FOLDER_REQUEST(),
  handleHideFolderModal: () => HIDE_FOLDER(),
  handleSelectedFolder: () => SET_SELECTED_FOLDER_ID(),
  handleShowCreateFolder: (payload: { type: string }) =>
    SHOW_FORM_CREATE_FOLDER(payload),
  fetchRemoveNotificationAction: (payload: string) =>
    FETCH_REMOVE_NOTIFICATION_REQUEST(payload),
  fetchHideRemoveNotification: () => FETCH_HIDE_CONFIRM_REMOVE_NOTIFICATION(),
  updateOpenFolderId: () => ({
    type: UPDATE_OPEN_FOLDER_IDS,
  }),
  hideBulkExport: () => HIDE_BULK_EXPORT(),
  createBulkExport: (payload: {
    exportName: string;
    exportOption: BulkExportOption;
  }) => BULK_EXPORT_REQUEST(payload),
  hideConfirmSaveTemplate: hideConfirmSaveTemplate,
  updateTemplate: updateTemplate,
  navigateCurrentTab: navigateCurrentTab,
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Home);
