import Home from './index';
import * as Redux from 'redux';
import configureStore from 'redux-mock-store';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
// eslint-disable-next-line react/display-name
jest.mock('components/AppContainer', () => () => (
  <div data-testid="app-container" />
));
describe('Home Page', () => {
  const middlewares: Redux.Middleware[] = [];
  const mockStore = configureStore(middlewares);
  const initialState = {
    folders: {
      byId: {},
      typeShowFormCreateFolder: '',
      selectedFolderId: '',
      selectedFolderIds: [],
    },
    exportTemplate: {
      exportTemplateData: [],
      excportTemplateSchemaId: '',
    },
    tdosTable: {
      confirmNotification: {
        isShowConfirm: false,
        notificationId: '',
      },
    },
    config: {
      simpleCognitiveWorkflowDefaultEngineId: {},
      user: {},
      ApiToken: '49fbe26c-58f7-4250-a797-dafd45ac02ee',
      pendoKey: 'cefc4ec9-0826-4b42-7b3e-9b3538e741d0',
      segmentWriteKey: 'AT5e3mTyrTnb25FhOtJ3GDS54RRYbkA5',
    },
    notification: {
      snackbar: {
        open: false,
        message: '',
        variant: '',
      },
    },
    history: {
      totalCount: 0,
    },
    user: {
      user: {
        organization: {
          kvp: {
            features: {
              illuminate: {
                exportDestination: 'enable',
              },
            },
          },
        },
        groups: [
          {
            groupId: '77e53e23-d600-4bbf-ae09-dde5a0c88c5f',
            groupName: 'Root Admin',
            organizationGuid: '7f936a87-8e59-4206-9999-938b0e3c62ab',
            kvp: {
              groupType: 'organization',
              organizationId: '1',
              organizationName: 'Root Admin',
            },
          },
        ],
      },
    },
    location: {
      payload: {
        tab: 'analytics',
      },
    },
    sunburst: {
      analyticsData: {},
      fetchingAggregations: false,
    },
    filters: {
      entityType: '',
      fileNames: [],
      ids: [],
      enginesRun: [],
      duration: {},
      date: {},
      fileTypes: {},
      selectedTopics: [],
    },
    wordcloud: {
      isWordcloudRedrawn: false,
      fetchingWordcloudAggregation: false,
      colouredWords: {},
      wordsPerColour: {},
    },
    dashboard: {
      durPerEngineClass: [],
      totalMediaProcessedTime: '0',
      totalDuration: '0',
      fetchingMediaAggregations: false,
    },
    search: {
      totalResults: 0,
      sortQuery: [],
      searchParameters: [],
      searchResultTdos: [],
    },
    app: {
      roles: [],
    },
    uploadFile: {
      uploadResult: [],
    },
  };
  const store = mockStore(initialState);
  it('renders a AppContainer', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Home />
      </Provider>
    );
    expect(getByTestId('app-container')).toBeInTheDocument();
  });
});
