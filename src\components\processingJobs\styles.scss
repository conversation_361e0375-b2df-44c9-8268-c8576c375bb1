$color-button: #2196f3;

.loadding-data {
  position: absolute;
  top: 40%;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
}

.pointer {
  cursor: pointer;
}

.table {
  td {
    height: 58px;
    padding: 4px;

    p {
      font-size: 0.875rem;
      font-weight: 400;
    }
  }

  thead tr td {
    color: rgba(0, 0, 0, 0.54);
    font-size: 0.75rem;
    font-weight: 500;
    padding: 20px 0;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
  }
}

.margin-left {
  margin-left: 20px;
}

.name-tdo-link-cell {
  cursor: pointer;
}

.status-errors {
  background: #d32f2f;
}

.status-complete {
  background: #15c853;
}

.status-inProgress {
  background: #69b675;
}

.status-inQueue {
  background: #77a8d2;
}

.title-status {
  margin-left: 20px;
}

.title-tooltip {
  font-size: 0.8rem;
}

.queued {
  color: #b7ddfe;
}
.running {
  color: #aef5b8;
}
.failed {
  color: #fc9292;
}

.badge {
  align-items: center;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-weight: 500;
  line-height: 1;
  align-content: center;
  border-radius: 10px;
  flex-direction: row;
  justify-content: center;
  padding: 3px 10px;
  color: #fff;
  margin-right: 4px;
}

.total-status-header {
  text-align: center;
}

.formControl {
  margin: 16px;
}

.mainFilter {
  text-align: right;
  margin-right: 18px;
  margin-bottom: 10px;
  display: flex;
  justify-content: right;
  align-items: center;
}

.filterType {
  margin-top: 2px;
  margin-left: 20px;
  background-color: $color-button;
  padding: 4px 12px;
  text-transform: none;

  &:hover {
    background-color: $color-button;
  }
}

.mainApplyFilter {
  text-align: center;
}

.btnApplyFilter {
  background-color: $color-button;
  padding: 4px 12px;
  text-transform: none;
  margin-bottom: 10px;

  &:hover {
    background-color: $color-button;
  }
}

.selectionAll {
  padding-left: 8px;
}

.failedJobs {
  display: flex;
  justify-content: left;
  padding-left: 160px;
}

.doNotFailed {
  display: flex;
  justify-content: center;
}

.failureMessage {
  padding-left: 5px;
  color: red;
}

.selectAll {
  position: absolute;
  top: 8px;
  left: 8px;
}
