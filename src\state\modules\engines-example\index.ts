import { createReducer } from '@reduxjs/toolkit';
import callGraph<PERSON><PERSON><PERSON>, {
  createGQLFailureAction,
  createGQLRequestAction,
  createGQLSuccessAction,
} from '../../../helpers/callGraph<PERSON>Api';
import { without, union } from 'lodash';
import { set } from 'lodash/fp';
import { Category } from 'pages/ExampleTabs/CategoriesTab';

export const FETCH_ENGINE = createGQLRequestAction(
  'request to fetch an engine'
);
export const FETCH_ENGINE_SUCCESS = createGQLSuccessAction<FetchEngineResponse>(
  'engine fetched successfully'
);
export const FETCH_ENGINE_FAILURE = createGQLFailureAction(
  'engine fetch failed'
);

export const FETCH_ENGINE_CATEGORIES = createGQLRequestAction(
  `request to fetch the list of engine categories`
);
export const FETCH_ENGINE_CATEGORIES_SUCCESS =
  createGQLSuccessAction<FetchEngineCategoriesResponse>(
    'engine categories fetched successfully'
  );
export const FETCH_ENGINE_CATEGORIES_FAILURE = createGQLFailureAction(
  'engine categories fetch failed'
);

// fixme -- use some generic reducer to handle all this boilerplate,

const defaultState = {
  fetchingEngines: [] as string[],
  failedFetchingEngines: [] as string[],
  enginesById: {} as Record<
    string,
    {
      engineId: string;
      engineName: string;
    }
  >,
  fetchingEngineCategories: false,
  failedFetchingEngineCategories: false,
  engineCategories: [] as Category[],
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_ENGINE, (state, action) => ({
      ...state,
      fetchingEngines: union(state.fetchingEngines, [
        action.payload.variables.id,
      ]),
      failedFetchingEngines: without(
        state.fetchingEngines,
        action.payload.variables.id
      ),
    }))
    .addCase(FETCH_ENGINE_SUCCESS, (state, action) => ({
      ...set(
        `enginesById.${action.meta.variables.id}`,
        action.payload.engine,
        state
      ),
      fetchingEngines: without(state.fetchingEngines, action.meta.variables.id),
    }))
    .addCase(FETCH_ENGINE_FAILURE, (state, action) => ({
      ...state,
      fetchingEngines: without(state.fetchingEngines, action.meta.variables.id),
      failedFetchingEngines: union(state.fetchingEngines, [
        action.meta.variables.id,
      ]),
    }))
    .addCase(FETCH_ENGINE_CATEGORIES, (state, _action) => ({
      ...state,
      fetchingEngineCategories: true,
      failedFetchingEngineCategories: false,
    }))
    .addCase(FETCH_ENGINE_CATEGORIES_SUCCESS, (state, action) => ({
      ...state,
      engineCategories: action.payload.engineCategories.records,
      fetchingEngineCategories: false,
      failedFetchingEngineCategories: false,
    }))
    .addCase(FETCH_ENGINE_CATEGORIES_FAILURE, (state) => ({
      ...state,
      fetchingEngineCategories: false,
      failedFetchingEngineCategories: true,
    }));
});

export default reducer;
export const namespace = 'engines-example';
export const local = (state: any) => state[namespace] as typeof defaultState;

interface FetchEngineResponse {
  engine: {
    engineId: string;
    engineName: string;
  };
}
export const fetchEngine =
  (id: string) => async (dispatch: any, getState: any) => {
    const query = `
    query($id: ID!) {
      engine(id: $id) {
        engineId: id
        engineName: name
      }
    }
  `;

    return await callGraphQLApi<FetchEngineResponse>({
      actionTypes: [FETCH_ENGINE, FETCH_ENGINE_SUCCESS, FETCH_ENGINE_FAILURE],
      query,
      variables: { id },
      bailout: (state: any) => !!selectEngine(state, id),
      dispatch,
      getState,
    });
  };

interface FetchEngineCategoriesResponse {
  engineCategories: {
    records: {
      id: string;
      name: string;
      type: {
        name: string;
        description: string;
      };
      iconClass: string;
    }[];
  };
}
export const fetchEngineCategories =
  ({ type } = { type: undefined }) =>
  async (dispatch: any, getState: any) => {
    const query = `
    query($type: String) {
      engineCategories(type: $type) {
        records{
          id
          name
          type {
            name
            description
          }
          iconClass
        }
      }
    }`;

    return await callGraphQLApi<FetchEngineCategoriesResponse>({
      actionTypes: [
        FETCH_ENGINE_CATEGORIES,
        FETCH_ENGINE_CATEGORIES_SUCCESS,
        FETCH_ENGINE_CATEGORIES_FAILURE,
      ],
      query,
      variables: { type },
      dispatch,
      getState,
      bailout: (state: any) => engineCategoriesAreLoading(state),
    });
  };

export const selectEngine = (state: any, id: string) =>
  local(state).enginesById[id];
export const engineIsLoading = (state: any, id: string) =>
  local(state).fetchingEngines.includes(id);
export const engineFailedToFetch = (state: any, id: string) =>
  local(state).failedFetchingEngines.includes(id);

export const selectEngineCategories = (state: any) =>
  local(state).engineCategories;
export const engineCategoriesAreLoading = (state: any) =>
  local(state).fetchingEngineCategories;
export const engineCategoriesFailedToFetch = (state: any) =>
  local(state).failedFetchingEngineCategories;
