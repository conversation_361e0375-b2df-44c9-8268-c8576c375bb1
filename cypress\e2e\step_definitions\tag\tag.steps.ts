import { When, Before } from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
  mediaListPage.goToMediaListPage();
});

When(
  'The user adds tag to 1 tdo. In case of error, make sure no existing tag on this tdo',
  () => {
    mediaListPage.addsTagTo1TDO();
  }
);

When(
  'The user deletes tag from 1 tdo. In case of error, make sure "Add tag" run before this',
  () => {
    mediaListPage.deletesTagFrom1TDO();
  }
);
