import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{ field: "age", operator: "term", limit: 10000 }]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'ageAggregation',
      dataKey: 'age',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Ages',
  filterTextType: 'Ages by Range',
  filterType: 'age',
  filterTerms: {
    Minor: Array.from(Array(18).keys()).map((i) => `${i}`),
    '18-25': Array.from(Array(26).keys())
      .slice(18, 26)
      .map((i) => `${i}`),
    '26-35': Array.from(Array(36).keys())
      .slice(26, 36)
      .map((i) => `${i}`),
    '36-45': Array.from(Array(46).keys())
      .slice(36, 46)
      .map((i) => `${i}`),
    '46-55': Array.from(Array(56).keys())
      .slice(46, 56)
      .map((i) => `${i}`),
    '56-65': Array.from(Array(66).keys())
      .slice(56, 66)
      .map((i) => `${i}`),
    '66-75': Array.from(Array(76).keys())
      .slice(66, 76)
      .map((i) => `${i}`),
    '76-85': Array.from(Array(86).keys())
      .slice(76, 86)
      .map((i) => `${i}`),
    '86-95': Array.from(Array(96).keys())
      .slice(86, 96)
      .map((i) => `${i}`),
    '96-100': Array.from(Array(101).keys())
      .slice(96, 101)
      .map((i) => `${i}`),
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const setOrAdd = (a: { [key: string]: number }, k: string, c: number) => {
      const value = a[k];
      if (value) {
        return c + value;
      }
      return c;
    };

    const dataObj = {
      Minor: 0,
      '18-25': 0,
      '26-35': 0,
      '36-45': 0,
      '46-55': 0,
      '56-65': 0,
      '66-75': 0,
      '76-85': 0,
      '86-95': 0,
      '96-100': 0,
      ...data.ageAggregation.reduce((acc: { [key: string]: number }, b) => {
        if (b.key < 18) {
          acc['Minor'] = setOrAdd(acc, 'Minor', b.doc_count);
        }
        if (b.key >= 18 && b.key <= 25) {
          acc['18-25'] = setOrAdd(acc, '18-25', b.doc_count);
        }
        if (b.key >= 26 && b.key <= 35) {
          acc['26-35'] = setOrAdd(acc, '26-35', b.doc_count);
        }
        if (b.key >= 36 && b.key <= 45) {
          acc['36-45'] = setOrAdd(acc, '36-45', b.doc_count);
        }
        if (b.key >= 46 && b.key <= 55) {
          acc['46-55'] = setOrAdd(acc, '46-55', b.doc_count);
        }
        if (b.key >= 56 && b.key <= 65) {
          acc['56-65'] = setOrAdd(acc, '56-65', b.doc_count);
        }
        if (b.key >= 66 && b.key <= 75) {
          acc['66-75'] = setOrAdd(acc, '66-75', b.doc_count);
        }
        if (b.key >= 76 && b.key <= 85) {
          acc['76-85'] = setOrAdd(acc, '76-85', b.doc_count);
        }
        if (b.key >= 86 && b.key <= 95) {
          acc['86-95'] = setOrAdd(acc, '86-95', b.doc_count);
        }
        if (b.key >= 96 && b.key <= 100) {
          acc['96-100'] = setOrAdd(acc, '96-100', b.doc_count);
        }
        return acc;
      }, {}), // this reduce need to return an object to update default values
    };

    // Add data
    chart.data = Object.entries(dataObj).map((kvp) => {
      const [ag, v] = kvp;
      return { ageGroup: ag, count: v };
    });

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'ageGroup';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  ageAggregation: { key: number; doc_count: number }[];
}
