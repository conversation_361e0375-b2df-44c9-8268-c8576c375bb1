import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

// --- HELPER FUNCTION FOR WAITING FOR EXPORT COMPLETION ---

function waitForExportComplete() {
  // Wait for export dialog to close first
  cy.get('[data-test="panel-bulk-export"]').should('not.exist');

  // Wait for the export to complete by checking the notification panel
  // Using a simple polling approach with cy.wait() as requested
  cy.wait(10000); // Initial wait for export to start processing

  // Check notification panel for completed export
  cy.get('#notification-image').click({ force: true });

  // Wait for the notification panel to open and check for completed export
  cy.get('[role="dialog"]').should('be.visible');

  // Look for a completed export notification
  return cy.contains('li', 'complete', { timeout: 120000 }).then(() => {
    // Close the notification panel
    cy.get('body').click(0, 0, { force: true });
    cy.log('Export completed successfully');
    return null;
  });
}

Before(() => {
  cy.LoginLandingPage();
});

Given('The user is on the media list page', () => {
  mediaListPage.goToMediaListPage();
});

When('The user exports a file', () => {
  mediaListPage.exportFile();
});

When('The user exports all items', () => {
  mediaListPage.exportAllItems();
});

When('the user selects the file {string}', (fileName: string) => {
  cy.contains('[data-testid^="files-table-row-"]', fileName)
    .first()
    .find('input[type="checkbox"]')
    .check();
});

When('the user clicks the {string} toolbar button', (buttonName: string) => {
  const buttonSelectors: { [key: string]: string } = {
    tag: '[data-test="files-bulk-tag-icon-button"]',
    export: '[data-test="files-bulk-export-icon-button"]',
    'run process': '[data-test="reprocess-file-icon-button"]',
    move: '[data-test="files-bulk-move-icon-button"]',
    delete: '[data-test="files-bulk-delete-icon-button"]',
    'send to redact': '[data-test="files-send-to-redact-icon-button"]',
  };

  const selector = buttonSelectors[buttonName.toLowerCase()];
  if (!selector) {
    throw new Error(`Button "${buttonName}" is not defined`);
  }

  cy.get(selector).click();
});

When('the user checks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .check({ force: true });
});

When('the user unchecks the {string} export option', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .uncheck({ force: true });
});

When(
  'the user clicks the edit icon for the {string} field',
  (fieldName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .find(`label:has(p:contains("${fieldName}")) + [data-testid="EditIcon"]`)
      .click();
  }
);

When(
  'the user renames the field {string} to {string}',
  (originalName: string, newName: string) => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .clear();

      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .type(newName);
    });
  }
);

When(
  'the user clicks the {string} button in the dialog',
  (buttonName: string) => {
    cy.get('[role="dialog"]').contains('button', buttonName).click();
  }
);

When(
  'the user selects the export template {string}',
  (templateName: string) => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      cy.get('#export-template-selected').click();
    });
    cy.get('[role="option"]').contains(templateName).click();
  }
);

When(
  'the user clicks the {string} button on the export pop-up',
  (buttonName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  }
);

When('the user names the template {string}', (templateName: string) => {
  cy.contains('[role="dialog"]', 'Name Template').within(() => {
    cy.get('#export-template-name').clear();
    cy.get('#export-template-name').type(templateName);
  });
});

When(
  'the user clicks the {string} button in the new template dialog',
  (buttonName: string) => {
    cy.contains('[role="dialog"]', 'Name Template')
      .contains('button', buttonName)
      .click();
  }
);

When(
  'the user checks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .check({ force: true });
    });
  }
);

When(
  'the user unchecks the following export options:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .uncheck({ force: true });
    });
  }
);

When('the user navigates to the {string} tab', (tabName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .find('button[role="tab"]')
    .contains(tabName)
    .click();
});

When(
  'the user clicks the {string} button in option tab',
  (buttonName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  }
);

When('the user clears the Archive Name field', () => {
  cy.get('[data-testid="export-file-name"]').find('input').clear();
});

When('the user sets the Archive Name to {string}', (archiveName: string) => {
  cy.get('[data-testid="export-file-name"]').find('input').type(archiveName);
});

When('the user enters the password {string}', (password: string) => {
  cy.get('#encryption-password').type(password);
});

When(
  'the user clicks the {string} button in the password dialog',
  (buttonName: string) => {
    cy.get('div:has(#encryption-password)')
      .contains('button', buttonName)
      .click();
  }
);

When('the user opens the notifications panel', () => {
  cy.get('#notification-image').click();
});

When(
  'the user downloads the first completed export from the notifications panel',
  () => {
    cy.contains('li', 'complete', { timeout: 60000 })
      .first()
      .find('button[data-testid="SaveAltIcon"]')
      .click();
  }
);

When('the user closes the notifications panel', () => {
  cy.get('body').click(0, 0);
});

When(
  'the user navigates to the main application tab {string}',
  (tabName: string) => {
    cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
  }
);

Then('the "Advanced Export" pop-up is displayed', () => {
  cy.get('[data-test="panel-bulk-export"]')
    .should('be.visible')
    .within(() => {
      cy.get('h1').contains('advanced export', { matchCase: false });
    });
});

Then('the {string} tab should be selected', (tabName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .find('button[role="tab"]')
    .contains(tabName)
    .should('have.attr', 'aria-selected', 'true');
});

Then(
  'the following export options should be visible:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      options.forEach((optionName: string) => {
        cy.contains('p', optionName).should('be.visible');
      });
    });
  }
);

Then(
  'the following buttons should be visible on the export pop-up:',
  (dataTable: DataTable) => {
    const buttons = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];

    cy.get('[data-test="panel-bulk-export"]').within(() => {
      buttons.forEach((buttonName: string) => {
        cy.contains('button', buttonName).should('be.visible');
      });
    });
  }
);

When(
  'the user clicks the {string} button on the export pop-up and waits for the export to complete',
  (buttonName: string) => {
    cy.intercept('POST', '/v3/graphql', (req) => {
      if (req.body.query.includes('createTDOWithAsset')) {
        req.alias = 'createExportJob';
      }
      if (req.body.query.includes('getNotification')) {
        req.alias = 'exportStatus';
      }
    }).as('graphql');

    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();

    // Wait for the success message to appear
    cy.contains('Your export job has been submitted', { timeout: 10000 });

    return cy.wait('@createExportJob').then((interception) => {
      const exportTdoId =
        interception.response?.body?.data?.createTDOWithAsset?.id;
      if (exportTdoId) {
        return waitForExportComplete();
      } else {
        throw new Error('Could not get TDO ID from export job creation.');
      }
    });
  }
);

Then('the {string} export option should be checked', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.checked');
});

Then(
  'the {string} export option should not be checked',
  (optionName: string) => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('not.be.checked');
  }
);

Then('the {string} export option should be disabled', (optionName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('label', optionName)
    .find('input[type="checkbox"]')
    .should('be.disabled');
});

Then('the {string} dialog should appear', (name: string) => {
  cy.contains('[role="dialog"]', name).should('be.visible');
});

Then('the export option should be renamed to {string}', (newName: string) => {
  cy.get('[data-test="panel-bulk-export"]')
    .contains('p', newName)
    .should('be.visible');
});

Then(
  'the input for {string} should have the value {string}',
  (labelName: string, expectedValue: string) => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.get(`div:has(> label:contains("${labelName}"))`)
        .find('input[type="text"]')
        .should('have.value', expectedValue);
    });
  }
);

Then('a success message {string} is displayed', (message: string) => {
  cy.get('[role="alert"]').should('be.visible').and('contain', message);
});

Then(
  'the following export options should be checked:',
  (dataTable: DataTable) => {
    const options = dataTable
      .rows()
      .map((row: string[]) => row[0])
      .filter(Boolean) as string[];
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .should('be.checked');
    });
  }
);

Then(
  'the Archive Name field should show the error message {string}',
  (errorMessage: string) => {
    cy.get('#export-file-name-helper-text')
      .should('be.visible')
      .and('contain', errorMessage);
  }
);

Then('the "Export" button on the export pop-up should be disabled', () => {
  cy.get('[data-test="export-select-button"]').should('be.disabled');
});

Then('the Archive Name field should not show an error message', () => {
  cy.get('#export-file-name-helper-text').should('not.have.class', 'Mui-error');
});

Then('the "Export" button on the export pop-up should be enabled', () => {
  cy.get('[data-test="export-select-button"]').should('be.enabled');
});

Then('the "Set Password" dialog of option should appear', () => {
  cy.get('div:has(#encryption-password)').should('be.visible');
});

Then('the "Set Password" dialog should disappear', () => {
  cy.get('#encryption-password').should('not.exist');
});

Then('the user can download the export from the notifications panel', () => {
  cy.get('#notification-image').click();
  cy.contains('li', 'complete')
    .first()
    .find('[data-testid="SaveAltIcon"]')
    .should('be.enabled');
  cy.get('body').click(0, 0);
});

Then(
  'the user can download the first completed export from the exports table',
  () => {
    cy.contains('[data-testid="export-table-row"]', 'complete')
      .first()
      .find('[data-testid="SaveAltIcon"]')
      .should('be.enabled');
  }
);
