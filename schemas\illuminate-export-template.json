{"type": "object", "title": "illuminate-export-template", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["name", "userId"], "properties": {"name": {"type": "string", "description": "the template name."}, "fields": {"type": "object", "properties": {"hasTag": {"type": "boolean"}, "hasTtml": {"type": "boolean"}, "hasWord": {"type": "boolean"}, "hasTdoId": {"type": "boolean"}, "hasNative": {"type": "boolean"}, "hasBookmark": {"type": "boolean"}, "hasFilename": {"type": "boolean"}, "hasPlainText": {"type": "boolean"}, "hasSentiment": {"type": "boolean"}, "hasEngineName": {"type": "boolean"}, "hasFoldername": {"type": "boolean"}, "hasTranslation": {"type": "boolean"}, "filePathWindows": {"type": "boolean"}, "hasBaseFilename": {"type": "boolean"}, "hasObjectNotation": {"type": "boolean"}, "hasObjectDetection": {"type": "boolean"}}, "description": "fields included in template"}, "userId": {"type": "string", "description": "the id of the user that creates the template."}, "customizedNames": {"type": "object", "properties": {"tag": {"type": "string"}, "ttml": {"type": "string"}, "tdoId": {"type": "string"}, "native": {"type": "string"}, "bookmark": {"type": "string"}, "filename": {"type": "string"}, "plainText": {"type": "string"}, "sentiment": {"type": "string"}, "engineName": {"type": "string"}, "foldername": {"type": "string"}, "ttmlEdited": {"type": "string"}, "translation": {"type": "string"}, "baseFilename": {"type": "string"}, "exportstatus": {"type": "string"}, "objectNotation": {"type": "string"}, "objectDetection": {"type": "string"}, "plainTextEdited": {"type": "string"}, "speakerSeparation": {"type": "string"}, "objectNotationEdited": {"type": "string"}, "speakerSeparationEdited": {"type": "string"}, "speakerSeparationObjectNotation": {"type": "string"}, "speakerSeparationObjectNotationEdited": {"type": "string"}}, "description": "customized field names"}}, "description": "illuminate bulk export template"}