.action-more-less-btn {
  text-align: right;
  margin-top: 5px;
  margin-bottom: 5px;
  cursor: pointer;
  font-size: 12px;
  display: inline-block;
}

.same-width-tags {
  margin: 5px;
  display: inline-flex;

  span {
    width: 60px;
    min-width: 60px;
    height: 100%;
    line-height: 26px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
    display: inline-block;
    background: #9e9d9e;
    color: #fff;
    border-radius: 2px;
    padding: 0 4px;
  }
}

.tag-item {
  margin: 5px;
  display: inline-flex;

  span {
    line-height: 26px;
    height: 26px;
    display: inline-block;
    background: #9e9d9e;
    color: #fff;
    border-radius: 2px;
    width: auto !important;
    padding: 0 4px;
  }
}

.loadding-data {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
}

.icon-engine {
  display: flex;
  align-items: center;
  font-size: 20px;

  i,
  img {
    padding-left: 3px;
    padding-right: 3px;
  }
}

.selection-all {
  padding-left: 10px;
}

.preview-wrap {
  width: 50%;
  float: left;
  position: relative;
  padding-top: 30px;
  border-top: 1px solid #cccccc5c;
  padding-bottom: 50px;
}

.preview-hide {
  position: relative;
}

.preview-active {
  width: 50%;
  float: left;
  position: relative;
}

.table-clear {
  &::after {
    content: '';
    display: table;
    clear: both;
  }

  tr {
    &:hover {
      cursor: pointer;
    }
  }

  thead tr th {
    color: rgba(0, 0, 0, 0.54);
    font-size: 0.75rem;
    font-weight: 500;
  }
}

.active-row {
  background: #d8d8d8;
}

.other-mime-type {
  padding-left: 10px;
}

.title-content {
  font-size: 0.875rem;
}

.table-row {
  height: 48px;

  td {
    height: 48px;
    padding: 4px;
  }
}

.table-footer {
  color: rgba(0, 0, 0, 0.54);

  p {
    font-size: 0.75rem;
  }

  div > div > div {
    font-size: 0.75rem;
  }
}

.icon-redact {
  font-size: 19px;
  padding: 10px;
}

.searchIcon {
  font-size: 20px;
}

.searchInput {
  margin-bottom: 8px;
}

.searchIconButton {
  padding: 10px;
}

.disable-row {
  opacity: 0.5 !important;
}

// @media (min-width: 1400px){
//   .preview-active {
//     width: 65%;
//   }

//   .preview-wrap {
//     width: 35%;
//   }
// }
