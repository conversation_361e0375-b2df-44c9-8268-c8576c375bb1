import { get, isEmpty } from 'lodash';
import { fetchGraphQLApiThrowError } from '../../../helpers/apiHelper';
import { addTdoNewTag } from '../tags';
import { TDO } from '../universal/TDO';
import { createAction } from '@reduxjs/toolkit';
import { GQLApi } from '~helpers/gqlApi';

export const SEND_TO_REDACT = createAction<{
  tdoIds: string[];
}>('send to redact');
export const SEND_TO_REDACT_SUCCESS = createAction<{
  message: string;
}>('send to redact success');
export const SEND_TO_REDACT_FAILURE = createAction<{
  message: string;
  error: any;
}>('send to redact failure');

export const IN_REDACTION = 'in redaction';

export async function sendToRedact(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  redactDataRegistryId: string,
  tdos: TDO[]
) {
  const gql = new GQLApi(endpoint, token, veritoneAppId);
  const tdoIds = tdos.map((tdo) => tdo.id);
  const resp = await gql.getSDOSchemaId(redactDataRegistryId);
  if (!resp?.data?.dataRegistry.publishedSchema.id) {
    throw new Error('sendToRedact failed to get schema id');
  }
  const schemaId = resp.data.dataRegistry.publishedSchema.id;
  const redactSdos = await createRedactSdo(
    endpoint,
    token,
    veritoneAppId,
    schemaId,
    tdoIds
  );
  if (isEmpty(redactSdos)) {
    throw new Error('no redact sdo is created');
  }
  const newTags = [
    { label: 'in redaction', value: 'in redaction', redactionStatus: 'Draft' },
  ];
  const tagResults = await addTdoNewTag(
    endpoint,
    token,
    veritoneAppId,
    tdos,
    newTags
  );
  return tagResults;
}

export async function createRedactSdo(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  schemaId: string,
  tdoIds: string[]
) {
  const createStructuredDataQueries: string[] = [];
  tdoIds.forEach((tdoId) => {
    createStructuredDataQueries.push(`
      tdo_${tdoId}: createStructuredData(input: {
        id: null,
        schemaId: "${schemaId}",
        data: {
          tdoId: "${tdoId}",
          status: "Draft"
        }
      }) {
        id
        data
        createdDateTime
        modifiedDateTime
      }
    `);
  });

  const query = `
    mutation {
      ${createStructuredDataQueries.join('\n')}
  }`;

  const response = await fetchGraphQLApiThrowError<
    Record<
      string,
      {
        id: string;
        data: any;
        createdDateTime: string;
        modifiedDateTime: string;
      }
    >
  >({
    endpoint,
    query,
    token,
    veritoneAppId,
    variables: undefined,
  });
  return response.data;
}

export function isAllAudioVideo(tdos: TDO[]) {
  const result = tdos.every((tdo) => isAudioVideo(tdo));
  return result;
}

export function isAudioVideo(tdo: TDO) {
  const mimeType = get(tdo, 'details.veritoneFile.mimetype', '');
  return ['video', 'audio'].includes(mimeType.split('/')?.[0] ?? '');
}

export function dedupById<T extends { id: string }>(tdos: T[]) {
  const set = new Set();
  const newTdoList: T[] = [];
  tdos.forEach((tdo) => {
    if (!set.has(tdo.id)) {
      newTdoList.push(tdo);
      set.add(tdo.id);
    }
  });
  return newTdoList;
}
