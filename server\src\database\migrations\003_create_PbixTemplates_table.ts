import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('PbixTemplates').then(async (tableExists: boolean) => {
      if (!tableExists) {
        await knex.schema.raw(`CREATE TABLE PbixTemplates (
          id int IDENTITY(1,1) PRIMARY KEY,
          name varchar(512),
          description varchar(2048),
          pbixFileName varchar(512) UNIQUE,
          created DATETIME NOT NULL,
          modified DATETIME NOT NULL
        )`);
        return true;
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('PbixTemplates')
  ])
