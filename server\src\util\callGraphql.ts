import { request, Variables } from 'graphql-request';
import env from '../env';
import { Context, RequestHeader } from '../application/types';

type callGQLfn = <T>(
  context: Context,
  headers: RequestHeader,
  query: string,
  variables?: Variables
) => Promise<Awaited<T>>;

async function invokeG<PERSON><T>(
  context: Context,
  headers: RequestHeader,
  query: string,
  variables?: Variables
): Promise<Awaited<T>> {
  const { log } = context;
  const url = `${env.apiRoot}/${env.graphQLEndpoint}`;
  try {
    return await request({
      url: url,
      document: query,
      variables: variables,
      requestHeaders: headers,
    });
  } catch (err) {
    log.error(err);
    throw new Error(err);
  }
}
export const callGQL: callGQLfn = invokeGQL;
