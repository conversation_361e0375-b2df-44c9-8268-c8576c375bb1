import fetchGraphQL<PERSON>pi from '../../../helpers/fetchGraphQLApi';

export async function getFolderTreeObjectId(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  let parentTreeObjectId = await getCmsRootFolder(
    endpoint,
    token,
    veritoneAppId
  );
  // when folderName is null, return root folder
  if (!folderName) {
    return parentTreeObjectId;
  }
  const folderNameArray = folderName.split('/');
  for (const childFolderName of folderNameArray) {
    if (!parentTreeObjectId) {
      break;
    }
    parentTreeObjectId = await getChildFolders(
      endpoint,
      token,
      veritoneAppId,
      parentTreeObjectId,
      childFolderName
    );
  }
  return parentTreeObjectId;
}

export async function getCmsRootFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string
) {
  const query = `
    query rootFolders {
      rootFolders(type:cms) {
        id
        name
        treeObjectId
        description
      }
    }`;

  const response = await fetchGraph<PERSON><PERSON>pi<{
    rootFolders: {
      id: string;
      name: string;
      treeObjectId: string;
      description: string;
    }[];
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
  });

  for (const folder of response?.data?.rootFolders ?? []) {
    if (folder.name.includes('Root')) {
      return folder.treeObjectId;
    }
  }
  return null;
}

export async function getChildFolders(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  parentTreeObjectId: string,
  childFolderName: string
) {
  if (!parentTreeObjectId || !childFolderName) {
    return null;
  }
  let offset = 0;
  const pageSize = 1000;
  let isEnd = false;
  while (!isEnd) {
    const query = `
    query folder{
      folder(id:"${parentTreeObjectId}") {
        childFolders(offset: ${offset}, limit: ${pageSize}){
          records {
            id
            name
            treeObjectId
          }
        }
      }
    }`;
    const response = await fetchGraphQLApi<{
      folder: {
        childFolders: {
          records: {
            id: string;
            name: string;
            treeObjectId: string;
          }[];
        };
      };
    }>({
      endpoint,
      token,
      veritoneAppId,
      query,
    });
    for (const folder of response?.data?.folder?.childFolders?.records ?? []) {
      if (folder.name === childFolderName) {
        return folder.treeObjectId;
      }
    }
    offset = offset + pageSize;
    isEnd = pageSize !== response?.data?.folder?.childFolders?.records?.length;
  }
  return null;
}

export async function deleteFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  const treeObjectId = await getFolderTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  return await deleteFolderByTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    treeObjectId
  );
}

export async function deleteFolderByTreeObjectId(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  treeObjectId: string | null
) {
  if (!treeObjectId) {
    return null;
  }
  const query = `
    mutation deleteFolder{
      deleteFolder(input: {id: "${treeObjectId}", orderIndex: 0}) {
        id
      }
    }`;
  const response = await fetchGraphQLApi<{
    deleteFolder: {
      id: string;
    };
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
  });
  return response?.data?.deleteFolder?.id;
}
