import { DateTime } from 'luxon';
import { Config } from '../chartDefinitions';

export function getAggregationDateBuckets({
  lowerDateBound,
  upperDateBound,
  aggregationSize,
  TZ,
}: Config) {
  // eslint-disable-next-line no-param-reassign
  aggregationSize = aggregationSize.toLowerCase();

  let startDate;
  let curr = DateTime.fromISO(lowerDateBound, { zone: TZ });
  const last = DateTime.fromISO(upperDateBound, { zone: TZ });
  const dateBuckets: string[] = [];

  if (aggregationSize === '1w') {
    startDate = curr.set({ weekday: 1 });
  }
  if (aggregationSize === '1m') {
    startDate = curr.set({ day: 1 });
  }
  if (aggregationSize === '1y') {
    startDate = curr.set({ month: 1, day: 1 });
  }

  curr = startDate ?? curr;

  while (curr < last) {
    dateBuckets.push(curr.toISO()!); // lowerDateBound is inclusive

    if (aggregationSize === '1d') {
      curr = curr.plus({ days: 1 });
      continue;
    }
    if (aggregationSize === '1w') {
      curr = curr.plus({ weeks: 1 });
      continue;
    }
    if (aggregationSize === '1m') {
      curr = curr.plus({ months: 1 });
      continue;
    }
    if (aggregationSize === '1y') {
      curr = curr.plus({ years: 1 });
      continue;
    }
    break;
  }

  return dateBuckets;
}

export function zeroAggregationsByDateRange(
  config: Config,
  zeroFields: { Student: number; 'Non-Student': number },
  data: Data[]
) {
  const dateBuckets = getAggregationDateBuckets(config);
  const zeroedData: Data[] = [];
  dateBuckets.forEach((db) => {
    zeroedData.push({
      date: db,
      ...zeroFields,
      ...(data.find(
        (d) =>
          DateTime.fromISO(d.date).toUnixInteger() ===
          DateTime.fromISO(db as string).toUnixInteger()
      ) ?? {}),
    });
  });

  return zeroedData;
}

interface Data {
  date: string;
  [key: string]: number | string;
}

export enum AxisItemLocation {
  Start = 0,
  Middle = 0.5,
  End = 1,
}
