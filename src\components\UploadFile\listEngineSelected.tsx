import React, { Fragment } from 'react';
import makeStyles from '@mui/styles/makeStyles';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMore from '@mui/icons-material/ExpandMore';
import Security from '@mui/icons-material/Security';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import Input from '@mui/material/Input';
import MenuItem from '@mui/material/MenuItem';
import Tooltip from '@mui/material/Tooltip';
import { truncate, isEmpty } from 'lodash';
import styles from './styles';
import {
  Engine,
  LibrariesByCategories,
} from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: 48 * 4.5 + 8,
      width: 250,
    },
  },
};
function ListEngineSelected({
  item,
  librariesByCategories,
  handleRemoveEngine,
  handleChangeLibrariesEngineSelected,
  handleChangeFieldsEngine,
  handleChangeJobPriority,
  handleExpandClick,
  checkValidateLibrary,
}: Props) {
  const classes = useStyles();
  const renderLogoEngine = (src: string | null, showSecurity: boolean) => {
    return (
      <div className={classes.iconHeaderEngines}>
        {showSecurity && <Security />}
        {src && <img src={src} width="100" height="50" />}
      </div>
    );
  };
  const makeFieldEngineLabel = (label: string) => {
    if (label === 'pangeaMTTgtLang') {
      return 'Target Language';
    }
    if (label === 'pangeaMTSrcLang') {
      return 'Source Language';
    }
    return label;
  };

  return (
    <Fragment key={item.categoryId}>
      <Typography component="p" className={classes.titleCategorySelected}>
        {item.categoryName}
      </Typography>
      {item.engineIds.map((engine) => {
        const librariesSelected = engine.librariesSelected;
        return (
          <Card
            key={engine.id}
            className={classes.cardEngines}
            data-testid="engine-selected-card"
          >
            <CardHeader
              avatar={
                engine.logoPath ||
                engine.deploymentModel === 'FullyNetworkIsolated'
                  ? renderLogoEngine(
                      engine.logoPath,
                      engine.deploymentModel === 'FullyNetworkIsolated'
                    )
                  : null
              }
              action={
                <Fragment>
                  <IconButton
                    className={engine.expand ? classes.expandOpen : ''}
                    data-engine-id={engine.id}
                    data-expand={!engine.expand}
                    onClick={handleExpandClick}
                    data-testid="expand-click"
                    size="large"
                  >
                    <ExpandMore />
                  </IconButton>
                  <IconButton
                    data-id={engine.id}
                    data-categoryid={item.categoryId}
                    onClick={handleRemoveEngine}
                    data-testid="remove-engine-click"
                    size="large"
                  >
                    <CloseIcon />
                  </IconButton>
                </Fragment>
              }
              title={
                !engine.logoPath && (
                  <Typography
                    component="p"
                    className={classes.titleHeaderEngines}
                  >
                    {engine.name}
                  </Typography>
                )
              }
              className={classes.cardHeaderEngines}
              data-testid="engine-selected-card-header"
            />
            <Collapse in={engine.expand} timeout="auto" unmountOnExit>
              <CardContent
                className={classes.cardContentEngines}
                data-testid="engine-selected-card-content"
              >
                <Typography
                  component="p"
                  color="textSecondary"
                  className={classes.titleHeaderEngines}
                >
                  {engine.description}
                </Typography>
                {engine.libraryRequired &&
                  !isEmpty(librariesByCategories[item.categoryId]) && (
                    <div>
                      <FormControl
                        error={checkValidateLibrary}
                        variant="standard"
                      >
                        <InputLabel
                          variant="standard"
                          // className={classes.labelInput}
                        >
                          Choose Libraries
                        </InputLabel>
                        <Select
                          value={librariesSelected}
                          onChange={(event) =>
                            handleChangeLibrariesEngineSelected(
                              event as React.ChangeEvent<HTMLInputElement>,
                              engine.id
                            )
                          }
                          input={<Input />}
                          // renderValue={selected =>
                          //   selected.join(', ')
                          // }
                          MenuProps={MenuProps}
                          className={classes.selectLibraries}
                          data-test="select-libraries"
                          inputProps={{ 'data-testid': 'select-libraries' }}
                          variant="standard"
                        >
                          {Object.values(
                            librariesByCategories[item.categoryId]! // safe due to librariesByCategories[item.categoryId] has been checked
                          ).map((item) => (
                            <MenuItem
                              key={item.name}
                              value={item.name}
                              data-test={`list-libraries_${item.name}`}
                            >
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </div>
                  )}
                <div className={classes.formfieldsEngine}>
                  {engine.fields.length > 0 &&
                    engine.fields.map((item) => {
                      return (
                        <Fragment
                          key={`${item.label || item.name}_${
                            item.defaultValue
                          }`}
                        >
                          {['MultiPicklist', 'Picklist'].includes(item.type) ? (
                            <TextField
                              id={engine.id}
                              name={`${item.name}`}
                              select
                              label={
                                <Tooltip
                                  title={makeFieldEngineLabel(
                                    item.label || item.name
                                  )}
                                >
                                  <Typography>
                                    {truncate(
                                      makeFieldEngineLabel(
                                        item.label || item.name
                                      ),
                                      {
                                        length: 16,
                                        separator: ' ',
                                      }
                                    )}
                                  </Typography>
                                </Tooltip>
                              }
                              defaultValue={
                                item.defaultValue ||
                                (item.options.length && item.options[0]!.value) // Safe due to length check
                              }
                              onChange={(event) =>
                                handleChangeFieldsEngine(
                                  event as React.ChangeEvent<HTMLInputElement>,
                                  engine.id
                                )
                              }
                              data-test={`fields-engine_${item.name}`}
                              slotProps={{
                                htmlInput: { 'data-testid': 'fields-engine' },
                              }}
                              variant="standard"
                            >
                              {item.options.map((item) => {
                                return (
                                  <MenuItem
                                    key={item.value}
                                    value={item.value}
                                    data-test={item.value}
                                  >
                                    {item.key}
                                  </MenuItem>
                                );
                              })}
                            </TextField>
                          ) : (
                            <TextField
                              id={engine.id}
                              name={`${item.name}`}
                              label={
                                <Tooltip title={item.label || item.name}>
                                  <Typography>
                                    {truncate(item.label || item.name, {
                                      length: 16,
                                      separator: ' ',
                                    })}
                                  </Typography>
                                </Tooltip>
                              }
                              defaultValue={
                                item.defaultValue ? item.defaultValue : ''
                              }
                              onChange={(event) =>
                                handleChangeFieldsEngine(
                                  event as React.ChangeEvent<HTMLInputElement>,
                                  engine.id
                                )
                              }
                              slotProps={{
                                htmlInput: { 'data-testid': 'fields-engine' },
                              }}
                              variant="standard"
                            />
                          )}
                        </Fragment>
                      );
                    })}
                </div>
                <div className={classes.formfieldsEngine}>
                  <TextField
                    id={engine.id}
                    name="priority"
                    label={
                      <Tooltip title="Priority">
                        <Typography>Priority</Typography>
                      </Tooltip>
                    }
                    onChange={(event) =>
                      handleChangeJobPriority(
                        event as React.ChangeEvent<HTMLInputElement>,
                        engine.id
                      )
                    }
                    slotProps={{
                      htmlInput: { 'data-testid': 'engine-priority' },
                    }}
                    variant="standard"
                  />
                </div>
              </CardContent>
            </Collapse>
          </Card>
        );
      })}
    </Fragment>
  );
}
interface Props {
  item: {
    categoryId: string;
    categoryName: string;
    engineIds: Engine[];
  };
  librariesByCategories: LibrariesByCategories;
  handleRemoveEngine: (event: React.MouseEvent<HTMLElement>) => void;
  handleChangeLibrariesEngineSelected: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeFieldsEngine: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeJobPriority: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleExpandClick: (event: React.MouseEvent<HTMLElement>) => void;
  checkValidateLibrary: boolean;
}
export default ListEngineSelected;
