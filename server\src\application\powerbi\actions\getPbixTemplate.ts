import axios from 'axios';
import fs from 'fs';
import { DateTime } from 'luxon';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';
import { Stream } from 'stream';

export type AzureBlobResponse = Stream;

const getPbixTemplate = async (context: Context) => {
  const { log, data, req } = context;
  const { azureBlobStorageApiRoot, azureBlobStorageContainer } = env;

  try {
    const { data: resp } = await axios.get<AzureBlobResponse>(
      `${azureBlobStorageApiRoot}/${azureBlobStorageContainer}/PbixTemplates/${req.body.templatePbixFileName}`,
      {
        responseType: 'stream',
        headers: {
          Authorization: data.azBlobBearerToken,
          'x-ms-date': DateTime.now().toHTTP(),
          'x-ms-version': '2023-01-03',
          'x-ms-client-request-id': req.metadata.correlationId,
        },
      }
    );

    if (!fs.existsSync('./tmp')) {
      fs.mkdirSync('./tmp');
    }
    const pbixFilePath = `./tmp/${req.body.templatePbixFileName}`;
    const writer = fs.createWriteStream(pbixFilePath);

    data.pbixFilePath = pbixFilePath;

    return new Promise<Context>((resolve, reject) => {
      resp.pipe(writer);
      let error: Error;
      writer.on('error', (err: Error) => {
        error = err;
        writer.close();
        reject(err);
      });
      writer.on('close', () => {
        if (!error) {
          resolve(context);
        }
      });
    });
  } catch (e) {
    log.error('AzureBlob API failed', e);
    throw new ApiError(e);
  }
};

export default getPbixTemplate;
