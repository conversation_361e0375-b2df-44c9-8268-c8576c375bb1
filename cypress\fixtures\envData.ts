interface EnvConfig {
  baseUrl: string;
  apiRoot: string;
}
interface EnvDataMap {
  [key: string]: EnvConfig;
  dev: EnvConfig;
  stage: EnvConfig;
  prod: EnvConfig;
  'azure-stage': EnvConfig;
  'azure-prod': EnvConfig;
}

export const envData: EnvDataMap = {
  dev: {
    baseUrl: 'https://illuminate.dev.us-1.veritone.com',
    apiRoot: 'https://api.dev.us-1.veritone.com',
  },
  stage: {
    baseUrl: 'https://illuminate.stage.veritone.com',
    apiRoot: 'https://api.stage.veritone.com',
  },
  prod: {
    baseUrl: 'https://illuminate.veritone.com',
    apiRoot: 'https://api.veritone.com',
  },
  'azure-stage': {
    baseUrl: 'https://illuminate.stage.us-gov-2.veritone.com',
    apiRoot: 'https://api.stage.us-gov-2.veritone.com',
  },
  'azure-prod': {
    baseUrl: 'https://illuminate.us-gov-2.veritone.com',
    apiRoot: 'https://api.us-gov-2.veritone.com',
  },
};
