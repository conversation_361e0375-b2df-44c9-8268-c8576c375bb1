import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
const {
  user: { userIsAuthenticated, FETCH_USER_SUCCESS },
} = modules;

import {
  selectRouteType,
  selectCurrentRoutePayload,
  ROUTE_AUTH,
  // ROUTE_HOME,
  ROUTE_TABS,
} from 'state/modules/routing';
import { TABS } from 'state/modules/tabs';

function* redirectAwayIfAlreadyAuthenticated() {
  if (yield* select(userIsAuthenticated)) {
    yield* put(ROUTE_TABS({ tab: TABS.Analytics }));
  }
}

function* redirectAwayAfterUserLogin() {
  yield* takeLatest(FETCH_USER_SUCCESS, function* () {
    const routeType = yield* select(selectRouteType);
    const currentRoutePayload = yield* select(selectCurrentRoutePayload);
    const { nextType, nextPayload } = currentRoutePayload.query || {};
    if (routeType === ROUTE_AUTH.type) {
      // look for redirect information in the query string, and send the user
      // to their original destination if it exists.
      const redirectType = nextType || ROUTE_TABS.type;
      let parsedNextPayload;
      try {
        parsedNextPayload = JSON.parse(nextPayload);
      } catch (_e) {
        /* */
      }

      yield* put({ type: redirectType, payload: parsedNextPayload });
    }
  });
}

export function* loadAuthPage() {
  yield* all([
    fork(redirectAwayIfAlreadyAuthenticated),
    fork(redirectAwayAfterUserLogin),
  ]);
}
