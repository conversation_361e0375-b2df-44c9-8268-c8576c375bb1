import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface AddSuperUserToWorkspaceRequest {
  groupUserAccessRight: string;
  identifier: string | number;
  principalType: string;
  emailAddress: string;
}

export interface AddSuperUserToWorkspaceResponse {
  value: {
    '@odata.context': string;
    id: string;
    displayName: string;
    admins: string[];
    sku: string;
    state: string;
    capacityUserAccessRight: string;
    region: string;
  }[];
}

const addSuperUserToWorkspace = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg, azureAdSuEmail, azureAdSuId } =
    env;

  try {
    await axios.post<
      AddSuperUserToWorkspaceResponse,
      AxiosResponse<AddSuperUserToWorkspaceResponse>,
      AddSuperUserToWorkspaceRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/users`,
      {
        groupUserAccessRight: 'Admin',
        principalType: 'App',
        identifier: azureAdSuId,
        emailAddress: azureAdSuEmail,
      },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    return context;
  } catch (e) {
    log.error('AddSuperUserToWorkspace failed', e);
    throw new ApiError(e);
  }
};

export default addSuperUserToWorkspace;
