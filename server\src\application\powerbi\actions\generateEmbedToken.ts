import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface GenerateEmbedTokenRequest {
  datasets: {
    id: string;
  }[];
  reports: {
    id: string;
  }[];
  identities: {
    username: string;
    roles: string[];
    datasets: string[];
  }[];
  lifetimeInMinutes: number;
  accessLevel: string;
}

export interface GenerateEmbedTokenResponse {
  '@odata.context': string;
  token: string;
  tokenId: string;
  expiration: string;
}

const generateEmbedToken = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const { data: resp } = await axios.post<
      GenerateEmbedTokenResponse,
      AxiosResponse<GenerateEmbedTokenResponse>,
      GenerateEmbedTokenRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/reports/${data.reportId}/GenerateToken`,
      {
        accessLevel: 'Edit',
        datasets: [
          {
            id: data.datasetId,
          },
        ],
        reports: [
          {
            id: data.reportId,
          },
        ],
        identities: [
          {
            username: data.authorizedOrgId,
            roles: ['Org'],
            datasets: [data.datasetId],
          },
        ],
        lifetimeInMinutes: data.lifetimeInMinutes,
      },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    data.embedToken = resp.token;
    data.embedTokenId = resp.tokenId;
    data.embedTokenExp = resp.expiration;
    data.embedTokenHostname = 'https://app.high.powerbigov.us';

    return context;
  } catch (e) {
    log.error('GenerateEmbedToken failed', e);
    throw new ApiError(e);
  }
};

export default generateEmbedToken;
