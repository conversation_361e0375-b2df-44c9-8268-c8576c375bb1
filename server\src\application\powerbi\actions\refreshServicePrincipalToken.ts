import axios from 'axios';
import formurlencoded from 'form-urlencoded';
import { ServicePrincipalTokenError } from '../errors';
import { Context } from '../../types';
import { cacheKeys } from '../definitions';
import env from '../../../env';

export const minimumTtlMs = 60 * 1000;

interface AzureAdTokenResponse {
  access_token: string;
  expires_in: number;
}

const refreshServicePrincipalToken = async (context: Context) => {
  const { log, cache } = context;
  const {
    azureOAuthRoot,
    azureAdTenantId,
    azureAdTokenEndpoint,
    azureAdTokenClientId,
    azureAdTokenClientSecrt,
    azureAdTokenPowerBiScope,
  } = env;
  const ttl = cache.getTtl(cacheKeys.pbiToken);
  log.info(
    `Service Principal Token expires in ${ttl ? ttl - Date.now() : '<Expired>'}`
  );

  if (!ttl || Date.now() - ttl < minimumTtlMs) {
    try {
      const { data } = await axios.post<AzureAdTokenResponse>(
        `${azureOAuthRoot}/${azureAdTenantId}/${azureAdTokenEndpoint}`,
        formurlencoded({
          grant_type: 'client_credentials',
          client_id: azureAdTokenClientId,
          client_secret: azureAdTokenClientSecrt,
          scope: azureAdTokenPowerBiScope,
        })
      );
      cache.set(cacheKeys.pbiToken, data.access_token, data.expires_in);
      context.data.pbiBearerToken = `Bearer ${data.access_token}`;

      return context;
    } catch (e) {
      log.error('API PBI Token Validation failed', e);
      throw new ServicePrincipalTokenError(e);
    }
  } else {
    const pbiToken = cache.get(cacheKeys.pbiToken);
    context.data.pbiBearerToken = `Bearer ${pbiToken}`;
  }

  return context;
};

export default refreshServicePrincipalToken;
