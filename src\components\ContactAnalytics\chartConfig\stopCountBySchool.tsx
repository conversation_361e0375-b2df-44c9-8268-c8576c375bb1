import _ from 'lodash';
import { createRoot } from 'react-dom/client';
import * as styles from './styles.scss';
import { Config } from '../chartDefinitions';

export default {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "k12_school"
                  operator: "term"
                  value: true
                }
                {
                  field: "stopForAStudent"
                  operator: "term"
                  value: true
                }
              ]
            }
            aggregate: [
              {
                name: "school"
                field: "school"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'studentSchoolAggregation',
      dataKey: 'school',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "k12_school"
                  operator: "term"
                  value: true
                }
                {
                  field: "stopForAStudent"
                  operator: "term"
                  value: false
                }
              ]
            }
            aggregate: [
              {
                name: "school"
                field: "school"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'nonStudentSchoolAggregation',
      dataKey: 'school',
    },
  ],
  hasDemographics: false,
  configure: (
    chartContainerId: string,
    data: Data,
    _name: string,
    _title: string,
    _config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chartData = data.studentSchoolAggregation.map((agg) => {
      return {
        school: agg.key,
        studentsStopped: agg.doc_count,
        nonStudentsStopped:
          data.nonStudentSchoolAggregation?.find((b) => b.key === agg.key)
            ?.doc_count ?? 0,
      };
    });

    data.nonStudentSchoolAggregation.forEach((agg) => {
      if (!chartData.find((d) => d.school === agg.key)) {
        chartData.push({
          school: agg.key,
          studentsStopped: 0,
          nonStudentsStopped: agg.doc_count,
        });
      }
    });

    chartData.sort((a, b) => a.school.localeCompare(b.school));

    if (data) {
      const chartContainer = document.getElementById(chartContainerId)!;
      const chart = createRoot(chartContainer);
      chart.render(
        <table className={styles['data-table']}>
          <thead>
            <tr>
              <th className={styles['data-table-header-left']}>School</th>
              <th className={styles['data-table-header-center']}>
                Students Stopped
              </th>
              <th className={styles['data-table-header-center']}>
                Non-Students Stopped
              </th>
              <th className={styles['data-table-header-center']}>Total</th>
            </tr>
          </thead>
          <tbody>
            {chartData.map((cd) => {
              return (
                <tr key={`stopCountBySchool-${cd.school}`}>
                  <td>{cd.school}</td>
                  <td className={styles['data-table-cell-center']}>
                    {cd.studentsStopped}
                  </td>
                  <td className={styles['data-table-cell-center']}>
                    {cd.nonStudentsStopped}
                  </td>
                  <td className={styles['data-table-cell-center']}>
                    {cd.nonStudentsStopped + cd.studentsStopped}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      );
    }

    // Remove Export
    const exportButton = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    );
    if (exportButton) {
      exportButton.style.display = 'none';
    }

    return { dispose: _.constant(null) };
  },
};

interface Data {
  studentSchoolAggregation: {
    key: string;
    doc_count: number;
  }[];
  nonStudentSchoolAggregation: {
    key: string;
    doc_count: number;
  }[];
}
