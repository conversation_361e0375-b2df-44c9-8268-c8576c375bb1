import { render, fireEvent } from '@testing-library/react';
import DashboardCard from './DashboardCard';

describe('DashboardCard', () => {
  const props = {
    durPerEngineClass: [
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd181',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd183',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd184',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd185',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd186',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd187',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd188',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        duration: '100',
      },
    ],
    stylescard: 'stylescard',
    option: 1,
    data: '100',
  };
  it('renders a CardContent', () => {
    const { getByTestId } = render(<DashboardCard {...props} />);
    expect(getByTestId('card-content')).toBeInTheDocument();
  });

  it('renders a CardHeader', () => {
    const { getByTestId } = render(<DashboardCard {...props} />);
    expect(getByTestId('card-header')).toBeInTheDocument();
  });
  it('renders a Card', () => {
    const { getByTestId } = render(<DashboardCard {...props} />);
    expect(getByTestId('card')).toBeInTheDocument();
  });
  it('renders a Typography', () => {
    const { getByTestId } = render(<DashboardCard {...props} />);
    expect(getByTestId('typography')).toBeInTheDocument();
  });

  it('when set props option', async () => {
    const newProps = {
      ...props,
      option: 4,
    };
    const { queryByTestId, getByTestId, getAllByTestId, findByText } = render(
      <DashboardCard {...newProps} />
    );
    expect(queryByTestId('card-header')).not.toBeInTheDocument();
    fireEvent.mouseOver(
      getByTestId('engine-list-67cd4dd0-2f75-445d-a6f0-2f297d6cd181')
    );
    expect(await findByText('Transcription')).toBeInTheDocument();
    expect(getAllByTestId('grid')).toHaveLength(props.durPerEngineClass.length);
    expect(queryByTestId('Typography')).not.toBeInTheDocument();
  });
});
