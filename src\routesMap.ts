import { NOT_FOUND } from 'redux-first-router';
import {
  ROUTE_AUTH,
  ROUTE_HOME,
  ROUTE_FORBIDDEN,
  ROUTE_EXAMPLE_TAKEOVER,
  ROUTE_EXAMPLE_TABS,
  ROUTE_TABS,
  ROUTE_FILE,
} from 'modules/routing';

import { loadAuthPage } from 'modules/auth/saga';
import { loadExampleTabsPage } from 'modules/exampleTabs/saga';

export default {
  [ROUTE_AUTH.type]: {
    path: '/login',
    component: 'Auth',
    saga: loadAuthPage,
    requiresAuth: false,
  },
  [ROUTE_HOME.type]: {
    path: '/',
    component: 'Home',
    requiresAuth: true,
  },
  [ROUTE_EXAMPLE_TAKEOVER.type]: {
    path: '/example-takeover',
    modalOver: ROUTE_HOME.type,
    component: 'ExampleTakeoverModal',
    requiresAuth: true,
  },
  [ROUTE_EXAMPLE_TABS.type]: {
    path: '/tabs/:tab?',
    component: 'ExampleTabs',
    requiresAuth: true,
    saga: loadExampleTabsPage,
    redirects: [
      {
        test: (_getState: any, action: ReturnType<typeof ROUTE_EXAMPLE_TABS>) =>
          // /tabs or /tabs/invalidTab
          !['categories', 'tasks'].includes(action.payload.tab),
        to: ROUTE_EXAMPLE_TABS({ tab: 'categories' }),
      },
    ],
    returnTo: {
      label: 'Home',
      route: ROUTE_HOME(),
    },
  },
  [NOT_FOUND]: {
    path: '/not-found',
    component: 'NotFound',
    requiresAuth: true,
  },
  [ROUTE_FORBIDDEN.type]: {
    path: '/forbidden',
    component: 'Forbidden',
    requiresAuth: true,
  },
  [ROUTE_TABS.type]: {
    path: '/:tab?',
    component: 'Home',
    requiresAuth: true,
  },
  [ROUTE_FILE.type]: {
    path: '/:tab?/:tdoId?',
    component: 'Home',
    requiresAuth: true,
  },
};
