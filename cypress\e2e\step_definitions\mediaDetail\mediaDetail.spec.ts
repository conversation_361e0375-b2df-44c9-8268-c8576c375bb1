import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
  mediaListPage.goToMediaListPage();
});

When(
  'navigate to media detail audio page {string} and verify {string}',
  (audioName: string, audioTranscript: string) => {
    mediaListPage.verifyMediaDetailAudio(audioName, audioTranscript);
  }
);

When(
  'navigate to media detail video page {string} and verify {string}',
  (videoName: string, videoTranscript: string) => {
    mediaListPage.verifyMediaDetailVideo(videoName, videoTranscript);
  }
);
