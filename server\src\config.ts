import createHealthCheckApp from './application/health-check';
import createPowerbiApp from './application/powerbi';
import createLogger from './logger';
import createCache from './cache';
import database from './database';

export type Config = ReturnType<typeof createConfig>;

function createConfig({ env }: { env: any }) {
  const log = createLogger();
  const cache = createCache();

  log.info('Environment', env);
  log.info('Initializing config', { serviceName: env.serviceName });

  const dbConnectionPromise = database.createConnection({
    databaseName: env.mssqlDatabase,
    host: env.mssqlHost,
    password: env.mssqlPw,
    user: env.mssqlUser,
    port: env.mssqlPort,
    log,
  });

  const db = database.createBootstrap({
    dbConnectionPromise,
    log,
  });

  log.info(
    { databaseName: env.mssqlDatabase },
    'Database connection established'
  );

  const healthCheckApp = createHealthCheckApp();
  const powerbiApp = createPowerbiApp({ db, log, cache });

  return {
    healthCheckApp,
    powerbiApp,
    log,
    db,
  };
}

export default createConfig;
