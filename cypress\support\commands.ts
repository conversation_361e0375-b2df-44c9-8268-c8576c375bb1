// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
import { fileTabName } from '../fixtures/variables';
import 'cypress-file-upload';

Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo: Cypress.Response<LoginResponse>) => {
      Cypress.env('orgId', userInfo.body.organization.organizationId);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  cy.LoginToApp();
  cy.visit('/');
  // wait for initial load completed.
  // Ensure the dashboard element is visible to confirm successful login and page load.
  cy.get('[data-test="dashboard-card/render-card/Speaker Detection"]', {
    timeout: 60000,
  }).should('be.visible');
});

// Remove the old PreserveSession command as cy.session() will handle this.
// Cypress.Commands.add('PreserveSession', () => {
//   // azure prod, azure stage, aws prod : veritone-session-id
//   // aws dev :   dev-veritone-session-id
//   // aws stage : stage-veritone-session-id,
//   // uk prod :   uk-prod-veritone-session-id
//   Cypress.Cookies.preserveOnce(
//     'veritone-session-id',
//     'dev-veritone-session-id',
//     'stage-veritone-session-id',
//     'uk-prod-veritone-session-id'
//   );
// });

Cypress.Commands.add(
  'LoginAndSetupSession',
  (sessionId = 'defaultUserSession') => {
    cy.session(
      sessionId,
      () => {
        // This setup function runs once per session ID, or when validation fails.
        // It should contain the steps to log in and establish the session.
        cy.LoginLandingPage(); // Re-use your existing command that logs in and lands on the page
      },
      {
        validate() {
          // This function runs before restoring a cached session.
          // Check if one of the known session cookies exists.
          // cy.session() automatically preserves cookies set during its setup.
          const knownSessionCookieNames = [
            'veritone-session-id',
            'dev-veritone-session-id',
            'stage-veritone-session-id',
            'uk-prod-veritone-session-id',
          ] as const;
          cy.getCookie(knownSessionCookieNames[0]).should('exist');
        },
      }
    );
  }
);
// // New way using cy.session()
// beforeEach(() => {
//   cy.LoginAndSetupSession();
// });

// folder e2e/mediaDetail is used as test folder. it is seeded with 3 files:
// e2e_audio.mp3
// e2e_video.mp4
// bloomberg.mp4
Cypress.Commands.add('NavigateToTestFolder', () => {
  cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
  cy.get('[data-test="folder-modal-dialog-content"]').contains('e2e').click();
  cy.get('[data-test="folder-modal-dialog-content"]')
    .contains('mediaDetail')
    .click();
  cy.get('[data-test=folder-modal-select-folder-button]').click();
});

Cypress.Commands.add('GoToTestFolder', () => {
  cy.NavigateToTestFolder();
  cy.contains(fileTabName).click();
});

// folder e2e/upload is used for upload test. e2e/mediaDetail folder has seeded files, so
// the files created in upload test need to be a different folder to avoid interfere the
// other tests
Cypress.Commands.add('NavigateToUploadFolder', () => {
  cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
  cy.get('[data-test="folder-modal-dialog-content"]').contains('e2e').click();
  cy.get('[data-test="folder-modal-dialog-content"]')
    .contains('upload')
    .click();
  cy.get('[data-test=folder-modal-select-folder-button]').click();
});

// folder e2e/reprocess is used for reprocess test.
Cypress.Commands.add('NavigateToReprocessFolder', (folderName: string) => {
  cy.get('[data-test="top-bar-select-folder"]').click({ force: true });
  cy.get('[data-test="folder-modal-dialog-content"]').contains('e2e').click();
  cy.get('[data-test="folder-modal-dialog-content"]')
    .contains(folderName)
    .click();
  cy.get('[data-test=folder-modal-select-folder-button]').click();
});

// remove tag
Cypress.Commands.add('RemoveTag', (rowId: number, tagName: string) => {
  // check the tdo row
  cy.get('[data-test="files-table-row"]')
    .eq(rowId)
    .find('input[type="checkbox"]')
    .check();
  // clear the tag
  cy.get('[data-test="files-bulk-tag-icon-button"]').click();
  cy.get('[data-test="tag-autocomplete"]').contains(tagName).next().click();
  cy.get('[data-test="tag-dialog-save-button"]').click();

  // verify tag removed
  cy.contains('Successfully tagged files').next().click();
});

Cypress.Commands.add(
  'repeat',
  ({ action, times }: { action: unknown; times: number }) => {
    if (typeof action === 'function') {
      Array.from({ length: times }, () => action());
    }
  }
);

Cypress.Commands.add(
  'awaitNetworkResponseCode',
  ({
    alias,
    code,
    repeat = 1,
  }: {
    alias: string;
    code: number;
    repeat?: number;
  }) => {
    cy.repeat({
      action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
      times: repeat,
    });
    // cy.assertNoLoading();
  }
);

// Cypress.Commands.add('CreateNewFolder', anewFolder => {
//   cy.get('[data-test="top-bar-select-folder"]').should('be.visible');
//   cy.get('[data-test="top-bar-select-folder"]').click();
//   cy.get('[data-test="root-folder"]')
//     .contains('MY CASES')
//     .click({ timeout: 10000 });
//   cy.get('[data-test="folder-modal-create-new-folder-button"]').click();
//   cy.contains('Create Folder');
//   cy.contains('Create folder in My Cases');
//   cy.get('[data-test="create-folder-enter-folder-name"] input').type(
//     anewFolder
//   );
//   cy.get('[data-test="create-folder-submit-button"]').click();
// });

// Cypress.Commands.add('filterFile', fileType => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="FILE TYPES"]').click();
//   cy.get(`[id="${fileType}"]`).check({ force: true });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filterFileType', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="FILE TYPES"]').click();
//   cy.get('[id="video"]').check({ force: true });
//   cy.get('[id="audio"]').check({ force: true });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filterDateRange', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });

//   cy.get('[data-test="files-table-row"]').should('be.visible');
//   cy.contains('FILES');
//   cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="DATE RANGE"]').click();
//   cy.get('[data-test="filter-section-date-range"]')
//     .find('[id="startDate"]')
//     .type(startOfYear);
//   cy.get('[data-test="filter-section-date-range"]')
//     .find('[id="endDate"]')
//     .type(todaysDate);
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
//   cy.get('[data-test="files-table-row"]').should('be.visible');
// });

// Cypress.Commands.add('filter Enity Types', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });
//   cy.contains('FILES');
//   Cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="filter-section-entity-types"]', { timeout: 5000 })
//     .should('be.visible')
//     .then(() => {
//       cy.get(
//         'div:nth-child(1) > label:nth-child(2) input[type="radio"]'
//       ).click({ force: true });
//     });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
// });

Cypress.Commands.add(
  'verifyTableSort',
  (columnName: string, sortOrder: 'a-z' | 'z-a') => {
    cy.get('[data-testid^=files-table-row]').should('have.length.gt', 0);

    return cy
      .contains('th', columnName)
      .invoke('index')
      .then((columnIndex) => {
        const cellValues: string[] = [];

        return cy
          .get('[data-testid^=files-table-row]')
          .should('have.length.gt', 0)
          .then(($rows) => {
            $rows.each((_index, row) => {
              const cellText = Cypress.$(row)
                .find('td')
                .eq(columnIndex)
                .text()
                .trim();
              cellValues.push(cellText);
            });

            const sortedValues = [...cellValues].sort((a, b) =>
              a.localeCompare(b, undefined, { numeric: true })
            );

            if (sortOrder === 'z-a') {
              sortedValues.reverse();
            }

            expect(cellValues).to.deep.equal(sortedValues);
            return null;
          });
      });
  }
);

// Cypress.Commands.add('delete filter Enity Types', () => {
//   cy.contains('My Cases');
//   cy.get('[data-test="files-tab-button"]', { timeout: 10000 }).click({
//     force: true
//   });
//   cy.contains('FILES');
//   Cy.get('[data-test="top-bar-filters-button"]').click();
//   cy.contains('FILTERS');
//   cy.get('[data-test="filter-section-entity-types"]', { timeout: 5000 })
//     .should('be.visible')
//     .then(() => {
//       cy.get('div:nth-child(1) > label:nth-child(1) [data-test="NONE"]').click({
//         force: true
//       });
//     });
//   cy.get('[data-test="filter-apply-filter"]').click({ force: true });
// });
