# Example queries to set feature flag "contactAnalyticsPbi" in illuminate

````query organization {
  organization(id: 76) {
    id
    jsondata
	}
}```

```mutation organizationJson($input: JSONData) {
  updateOrganization(input: {
    id: 76,
    metadata: $input
  }) {
  	id
    jsondata
	}
}```

```{
  "input": {
    "platformType": "custom",
    "attribute": "disabled",
    "applicationIds": [
      "8b3eac1c-5150-448e-8d99-fb7b860e7e41",
      "8a37c1d0-3f3b-48d0-a84e-2b8e3646fbe5",
      "3c2e2d4c-0fe9-4ae7-905a-7ef44aa64252"
    ],
    "dataSources": [],
    "features": {
      "edgeVersion": 3,
      "enableMediaHierarchy": "disabled",
      "newFilePicker": "enabled",
      "enableRBACFeature": "disabled",
      "cmsFolders": "enabled",
      "media": "enabled",
      "mentionDetails": {
        "enableStarRating": "enabled",
        "sharing": {
          "facebook": true,
          "twitter": true,
          "email": true
        },
        "verifyMention": "enabled",
        "complianceStatus": "enabled",
        "canComment": "enabled",
        "editMention": "enabled",
        "spotType": "enabled",
        "editCollection": "enabled"
      },
      "mentionListing": {
        "comments": true,
        "edit": true,
        "ratings": true,
        "socialSharing": true
      },
      "notifications": {
        "email": true,
        "sms": true
      },
      "shareMentions": {
        "email": true,
        "twitter": true,
        "facebook": true,
        "link": true,
        "embed": true
      },
      "showGuestMentionDownloadOption": "disabled",
      "postToCollections": "enabled",
      "shareCollections": {
        "internal": "enabled"
      },
      "ratings": "enabled",
      "sendMentionsToSalesForce": "enabled",
      "sendMentionsToWebService": "enabled",
      "includeDataSources": "disabled",
      "includeNumeris": "disabled",
      "complianceStatus": "disabled",
      "mediaRights": {
        "shareWithPlatform": false,
        "shareWithSpecificAccounts": false
      },
      "termsAgreementNotRequired": false,
      "mentionApertureWindow": 120,
      "passwordExpirationDays": 90,
      "translate": {
        "providers": [
          {
            "name": "Pangeanic",
            "isEnabled": false,
            "api": {
              "key": "",
              "value": ""
            }
          }
        ]
      },
      "illuminate": {
        "exportDestination": [
          {
            "name": "Amazon S3",
            "isEnabled": false,
            "key": "awsS3"
          },
          {
            "name": "Azure Blob",
            "isEnabled": false,
            "key": "azure"
          },
          {
            "name": "Desktop",
            "isEnabled": false,
            "key": "desktop"
          }
        ],
        "contactAnalytics": "enabled",
        "contactAnalyticsPbi": "enabled"
      },
      "watchlistTypes": {
        "livePrerecorded": false
      },
      "attribute": {
        "displayAdSourcesFilter": "disabled",
        "displayCustomMarkets": "disabled",
        "restrictCampaignTypeToOther": "disabled",
        "advertiserInMultipleMarkets": "disabled",
        "hybridTimeZones": "disabled",
        "hideChannelFields": "disabled",
        "iHeartCustomEngineV3": "disabled",
        "mergeWideOrbitBookends": "enabled",
        "useWatchlistsAsAdSource": "disabled",
        "uploadLogs": "disabled",
        "webLogUploader": "disabled",
        "customReportBuilder": "disabled",
        "routeToCustomReportBuilder": "disabled",
        "customReportBuilderZeroState": "disabled",
        "keywordReport": "disabled",
        "cannedTemplatesCustomReportBuilder": "disabled",
        "customTemplatesCustomReportBuilder": "disabled",
        "newAdUsersMetric": "disabled",
        "combinedCampaignSetup": "disabled"
      }
    },
    "metadata": {
      "fields": []
    },
    "customCmsAndAclApplicationIds": [
      "3c2e2d4c-0fe9-4ae7-905a-7ef44aa64252"
    ],
    "billing": {
      "pausedProcessing": true
    }
  }
}```
````
