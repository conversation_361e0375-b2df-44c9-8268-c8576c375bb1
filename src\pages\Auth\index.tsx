import { ConnectedProps, connect } from 'react-redux';
// import { OAuthLoginButton } from 'veritone-widgets';
import { AppContainer } from '@veritone/glc-react';
import { modules } from '@veritone/glc-redux';
import * as styles from './styles.scss';

const {
  config: { getConfig },
  auth: { selectOAuthError },
} = modules;

const errorMessages = {
  unauthorized_client: 'Your account does not have access to this application.',
};

const genericErrorMessage = (error: string) =>
  `There was an error while trying to authenticate: ${error}.`;

const Auth = ({ OAuthErrorCode, OAuthErrorDescription }: PropsFromRedux) => (
  <AppContainer>
    <div className={styles.container}>
      {OAuthErrorCode &&
        (errorMessages[OAuthErrorCode] ||
          genericErrorMessage(
            OAuthErrorDescription ? OAuthErrorDescription : ''
          ))}
      {/* <OAuthLoginButton clientId={OAuthClientID} redirectUri={window.origin} /> */}
    </div>
  </AppContainer>
);

const mapState = (state: any) => {
  const error = selectOAuthError(state);
  const getConfigState = getConfig<Window['config']>(state);
  return {
    OAuthClientID: getConfigState.OAuthClientID,
    OAuthErrorCode: error.code,
    OAuthErrorDescription: error.description
      ? window.unescape(error.description)
      : null,
  };
};

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Auth);
