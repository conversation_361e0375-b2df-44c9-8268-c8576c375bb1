const path = require('path');
const moduleNameMapper = require('jest-module-name-mapper');

module.exports = {
  // Your normal jest config settings
  testPathIgnorePatterns: ['<rootDir>/cypress/', 'server', '.yalc/'],
  moduleNameMapper: {
    ...{
      '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
        '<rootDir>/test/__mocks__/fileMock.ts',
      '\\.(css|scss)$': 'identity-obj-proxy',
      '^resources(.*)$': '<rootDir>/resources$1',
      '^components(.*)$': '<rootDir>/src/components$1',
      '^pages(.*)$': '<rootDir>/src/pages$1',
      '^state(.*)$': '<rootDir>/src/state$1',
      '^modules(.*)$': '<rootDir>/src/state/modules$1',
      '^sagas(.*)$': '<rootDir>/src/state/sagas$1',
      '^~helpers(.*)$': '<rootDir>/src/helpers$1',
      '^@utils(.*)$': '<rootDir>/src/utils$1',
      // resolving tests from d3-color resolution upgrade to 3.1.0
      // https://stackoverflow.com/questions/69226759/jest-unexpected-token-export-when-using-d3
      'd3-color': '<rootDir>/node_modules/d3-color/dist/d3-color.min.js',
      // dnd issue https://github.com/ant-design/ant-design/pull/17607
      '^worker.ts$': '<rootDir>/test/__mocks__/workerMock.js',
      '^@veritone/glc-react$': '@veritone/glc-react/dist/bundle-cjs',
      '^@veritone/glc-redux$': '@veritone/glc-redux/dist/bundle-cjs',
      '^pdfjs-dist$': '<rootDir>/test/__mocks__/pdf.mjs',
      nanoid: '<rootDir>/node_modules/nanoid/index.cjs',
    },
    ...moduleNameMapper.default('./tsconfig.json'),
  },
  setupFilesAfterEnv: [
    '<rootDir>/test/setupTests.tsx',
    '<rootDir>/test/testSuitePolyfills.ts',
  ],
  verbose: true,
  testEnvironment: 'jsdom',
  transformIgnorePatterns: [
    '/node_modules/(?!(react-dnd|dnd-core|@react-dnd|react-dnd-html5-backend)/)',
  ],
};
