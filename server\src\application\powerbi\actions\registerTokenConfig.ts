import { DatabaseError } from '../errors';
import { Context } from '../../types';

const registerTokenConfig = async (context: Context) => {
  const { log, data, queries, req } = context;

  try {
    queries.upsertTokenConfig({
      reportId: data.reportId,
      profileId: data.profileId,
      workspaceId: data.workspaceId,
      datasetId: data.datasetId,
      embedUrl: data.embedUrl,
      pbixFilePath: data.pbixFilePath,
      orgId: req.body.orgId ?? data.orgId,
      lifetimeInMinutes: req.body.lifetimeInMinutes,
    });

    return context;
  } catch (e) {
    log.error('registerTokenConfig failed', e);
    throw new DatabaseError(e);
  }
};

export default registerTokenConfig;
