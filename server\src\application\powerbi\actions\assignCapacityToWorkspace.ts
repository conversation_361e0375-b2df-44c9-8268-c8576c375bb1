import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface AssignToCapacityRequest {
  capacityId: string;
}

const assignCapacityToWorkspace = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    await axios.post<never, AxiosResponse<never>, AssignToCapacityRequest>(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/AssignToCapacity`,
      { capacityId: data.capacityId },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    return context;
  } catch (e) {
    log.error('Workspace API failed', e);
    throw new ApiError(e);
  }
};

export default assignCapacityToWorkspace;
