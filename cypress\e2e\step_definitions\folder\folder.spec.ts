import { movedFolder, veritoneAppId } from '../../../fixtures/variables';
import { deleteFolder } from '../../../../src/state/modules/folders/folder';
import {
  After,
  Before,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';

Before(() => {
  cy.LoginLandingPage();
});

When('The user clicks the folder icon', () => {
  landingPage.clickFolderIcon();
});

Then('The folder list dialog is displayed', () => {
  landingPage.getFolderDialog().should('be.visible');
});

Then(
  'The {string} root folder is displayed with a counter',
  (folderName: string) => {
    landingPage
      .getRootFolder()
      .should('be.visible')
      .and(($element) => {
        const text = $element.text();
        expect(text).to.contain(folderName);
        expect(text).to.match(/\(\d+\)/);
      });
  }
);

When(
  'The user closes the folder dialog using the {string} button',
  (buttonType: 'X' | 'Cancel') => {
    landingPage.closeFolderDialog(buttonType);
  }
);

Then('The folder list dialog is no longer visible', () => {
  landingPage.getFolderDialog().should('not.exist');
});

Given('The user has opened the folder selection window', () => {
  landingPage.clickFolderIcon();
  landingPage.getFolderDialog().should('be.visible');
});

When('The user hovers over the {string} folder', (folderName: string) => {
  landingPage.hoverOverFolder(folderName);
});

Then('The folder tooltip should display with the correct text', () => {
  return landingPage
    .getRootFolder()
    .invoke('attr', 'aria-label')
    .then((folderAriaLabel) => {
      return landingPage
        .getTooltip()
        .should('be.visible')
        .and('contain.text', folderAriaLabel);
    });
});

When('The user selects the {string} folder', (folderName: string) => {
  landingPage.selectFolder(folderName);
});

Then('The {string} breadcrumb should be visible', (folderName: string) => {
  landingPage.getBreadcrumb(folderName).should('be.visible');
});

Given('The user deletes folder {string}', (folderPath: string) => {
  if (!folderPath || folderPath.trim() === '') {
    throw new Error(
      `Folder path cannot be empty or null. Received: '${folderPath}'`
    );
  }
  const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
  const token = Cypress.env('token');
  cy.wrap(deleteFolder(endpoint, token, veritoneAppId, folderPath));
});

When(
  'The user creates the {string} in the {string} parent directory',
  (folderName: string, parentFolderName: string) => {
    landingPage.createFolder(folderName, parentFolderName);
  }
);

When(
  'The user renames the folder {string} to {string} in the {string} parent directory',
  (folderName: string, renamedFolder: string, parentFolderName: string) => {
    landingPage.renameFolder(folderName, renamedFolder, parentFolderName);
  }
);

When(
  'The user moves the folder {string} from {string} to {string}',
  (
    folderToMoveName: string,
    sourceParentName: string,
    destinationParentName: string
  ) => {
    landingPage.moveFolder(
      folderToMoveName,
      sourceParentName,
      destinationParentName
    );
  }
);

After(() => {
  const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
  const token = Cypress.env('token');
  cy.wrap(deleteFolder(endpoint, token, veritoneAppId, movedFolder));
});
