import joi from 'joi';

const schemas = {
  post: {
    provisionOrg: joi.object().keys({
      orgName: joi.string().required(),
      orgId: joi.number().required(),
      templatePbixFileName: joi.string().required()
    }),
    generateEmbedToken: joi.object().keys({}),
    pushData: joi.object().keys({
      stops: joi.array().items(
        joi.object().keys({
          data: joi.array().items(
            joi.object({
              recordId: joi.string().required(),
              basisForPropertySeizure: joi.array().items(joi.string()).min(0).required(),
              officerYearsOfExperience: joi.number().required(),
              basisForSearchNarrative: joi.string().allow('').required(),
              ethnicity: joi.array().items(joi.string()).required(),
              city: joi.string().required(),
              disability: joi.array().items(joi.string()).required(),
              timeOfStop: joi.string().required(),
              reasonsForStop: joi.string().required(),
              personNumber: joi.number().required(),
              k12_school: joi.boolean().required(),
              basisForSearch: joi.array().items(joi.string()).min(0),
              custodialArrestOffCode: joi.array().items(joi.string()).min(0).required(),
              citationOffCode: joi.array().items(joi.string()).min(0),
              school: joi.string().allow('').required(),
              warningOffCode: joi.array().items(joi.string().min(0)),
              responseToServiceCall: joi.boolean().required(),
              officerTypeOfAssignment: joi.string().required(),
              inFieldCiteAndReleaseCode: joi.array().items(joi.string()),
              trafficViolationOffenseCode: joi.string().allow('').required(),
              trafficViolation: joi.string().allow('').required(),
              ecSubdivision: joi.string().allow('').required(),
              durationOfStop: joi.number().required(),
              reasonableSuspicion: joi.array().items(joi.string()).min(0).required(),
              suspicionOffenseCode: joi.string().allow('').required(),
              stopForAStudent: joi.boolean().required(),
              typeOfPropertySeized: joi.array().items(joi.string()).min(0).required(),
              location: joi.string().required(),
              genderNonconforming: joi.boolean().required(),
              reasonForStopNarrative: joi.string().required(),
              age: joi.number().required(),
              ethnicityExclusive: joi.string().required(),
              lgbt: joi.boolean().required(),
              raceOfOfficer: joi.array().items(joi.string()).min(0).required(),
              gender: joi.string().allow('').required(),
              nonConforming: joi.boolean().required(),
              sexualOrientation: joi.string().required(),
              typeOfStop: joi.string().required(),
              unhoused:  joi.boolean().required(),
              officerWorksWithNonPrimaryAgency:  joi.boolean().required(),
              reasonGivenStoppedPerson:  joi.array().items(joi.string()).min(0).required(),
              stopDuringWellnessCheck:  joi.boolean().required(),
              typeOfAssignmentOfficer: joi.string().required(),
              stoppedPassenger:  joi.boolean().required(),
              stoppedInsideResidence:  joi.boolean().required(),
              limitedEnglish: joi.boolean().required(),
              disciplineUnderEc: joi.string().allow('').required(),
              contrabandOrEvidence: joi.array().items(joi.string()).min(0).required(),
              officerOtherAssignmentType: joi.string().allow('').required(),
              resultOfStop: joi.array().items(joi.string()).min(0).required(),
              actionsTakenDuringStop: joi.array().items(joi.string()).min(0).required(),
              datetimeOfStop: joi.string().required(),
              consentType: joi.string().allow(''),
              probableCause: joi.array().items(joi.string().allow('')).min(0),
              probableCauseCode: joi.string().allow(''),
              datetimeOfStopPST: joi.string().allow(''),
            })
          ).min(1).required(),
          customQuestionData: joi.array().items(
            joi.object({
              recordId: joi.string().required(),
              personNumber: joi.number().required(),
              isStopQuestion: joi.boolean().required(),
              answer: joi.alternatives().try(joi.array().items(joi.string()).min(0), joi.string().allow(''), joi.number()),
              questionKey: joi.string().required(),
            })
          ).min(0)
        })
      ).min(1).required()
    }),   
    pushCustomQuestionDefinitions: joi.object().keys({
      data: joi.array().items(
        joi.object({
          contactId: joi.string().required(),
          component: joi.string().required(),
          title: joi.string().required(),
          options: joi.array().items(joi.string()),
          isMultiSelect: joi.boolean().required(),
          required: joi.boolean().required(),
          disabled: joi.boolean().required(),
          disabledDate: joi.string().allow(''),
        })
      )
    }),
    associateTemplate: joi.object().keys({
      orgId: joi.number().required(),
      templatePbixFileName: joi.string().required(),
    }),
    bulkAssociateTemplate: joi.object().keys({
      orgIds: joi.array().items(joi.number()).min(1).required(),
      templatePbixFileName: joi.string().required(),
    }),
  },
  patch: {
    template: joi.object().keys({
      name: joi.string().required(),
      description: joi.number(),
      pbixFileName: joi.string().required()
    }),
  },
  delete: {
    removeAllPowerBiData: joi.object().keys({
      code: joi.string().required()
    })
  }
}

export { schemas };
