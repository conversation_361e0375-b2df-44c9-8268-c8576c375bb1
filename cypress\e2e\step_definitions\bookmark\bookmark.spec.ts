import {
  Before,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
});

Given('The user is on the media list page', () => {
  mediaListPage.goToMediaListPage();
});

When(
  'The user highlights text in {string} for a new bookmark',
  (mediaName: string) => {
    mediaListPage.highlightsTextForBookmark(mediaName);
  }
);
Then(
  'The user verifies the saved bookmark and its note are visible for the {string} media file',
  (mediaName: string) => {
    mediaListPage.verifyBookmark(mediaName);
  }
);
