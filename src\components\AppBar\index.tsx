import React, { Fragment, useState, useEffect, useRef } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import Dialog from '@mui/material/Dialog';
import Portal from '@mui/material/Portal';
import cx from 'classnames';
import {
  ON_FETCH_URI_TO_EXPORT,
  getLoadingFetchUriExport,
} from 'state/modules/history';
import {
  getNotifications,
  fetchUpdateStatusReadNotification,
  fetchRemoveNotificationAction,
  fetchConfirmRemoveNotification,
  getTotalNotifications,
} from '../../state/modules/tdosTable';
import NotificationsIcon from 'resources/images/notifications.png';
import NotificationsList from './notificationsList';
import * as styles from './styles.scss';
import SearchBar from '../SearchBar';

const AppBar = ({
  notifications = [],
  fetchRemoveNotificationAction,
  fetchUpdateStatusReadNotification,
  fetchConfirmRemoveNotification,
  totalNotifications,
  onFetchUriToExport,
  loadingFetchUriExport,
}: PropsFromRedux) => {
  const [isOpenNotification, setIsOpenNotification] = useState(false);
  const [isShowSearchBar, setIsShowSearchBar] = useState(false);
  const [tdoIdDownload, setTdoIdDownload] = useState('');
  const widgetIdRef = useRef<string>();

  // Wait till the aiware.js has loaded to the window object
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (window.aiware && !window.isAiwareInitialized) {
        clearInterval(intervalId);
        aiwareInit(window.aiware);
      }
    }, 250);

    return () => {
      clearInterval(intervalId);
      if (widgetIdRef.current) {
        window.aiware.unmountWidget(widgetIdRef.current);
        widgetIdRef.current = undefined;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const notificationCount = document.getElementById('notification-count');
    if (notificationCount) {
      if (totalNotifications > 0) {
        notificationCount.style.display = 'block';
        notificationCount.innerHTML = totalNotifications.toString();
      } else {
        notificationCount.style.display = 'none';
      }
    }
  }, [totalNotifications]);

  function aiwareInit(aiware: any) {
    window.isAiwareInitialized = true;
    const apiRoot = window.config?.apiRoot || '';
    const graphQLEndpoint = window.config?.graphQLEndpoint || '';
    const applicationId =
      window.config?.veritoneAppId || '8b3eac1c-5150-448e-8d99-fb7b860e7e41';
    const knowledgeBaseUrl = 'https://support.veritone.com/s/article/000002133';
    aiware.init(
      {
        applicationId,
        baseUrl: `${apiRoot}/${graphQLEndpoint}`,
        // Specify both keys until SDK team cleans up the config
        knowledgeBaseUrl,
        knowledgeBaseURL: knowledgeBaseUrl,
        withAuth: true,
        betaFeatures: false,
      },
      () => {
        aiware.on('openSupport', async () => {
          try {
            const { initChatWithSupport, chatWithSupport } = await import(
              // eslint-disable-next-line import/no-unresolved
              /* webpackIgnore: true */ 'https://get.aiware.com/veritone-support/latest/2/index.js'
            );
            await initChatWithSupport();
            await chatWithSupport();
          } catch (ex) {
            console.error(
              'Our chat service is currently unavailable. Please contact <NAME_EMAIL> for assistance. Thank you for your understanding!',
              ex
            );
          }
        });

        widgetIdRef.current = mountWidget(aiware);
      }
    );
  }

  function mountWidget(aiware: any) {
    const widgetId: string = aiware.mountWidget(
      {
        name: 'APP_BAR',
        elementId: 'aiWareAppBar',
        config: {
          title: 'ILLUMINATE',
          backgroundColor: '#325491',
          help: true,
          zIndex: 1000,
          notification: false,
          rightActions: [
            {
              label: `
                <div title="Notifications">
                  <span class="${styles.notificationBtn}" >
                    <img id="notification-image" alt="notification" src=${NotificationsIcon} />
                    <span id="notification-count" class="${cx(
                      styles.notificationBadge,
                      {
                        [styles.showBadge!]: totalNotifications > 0, // safe due to showBadge exists in styles
                      }
                    )}">${totalNotifications}</span>
                  </span>
                <div>
              `,
              onClick: showNotifications,
            },
          ],
        },
      },
      () => {
        if (document.getElementById('search-bar-id')) {
          setIsShowSearchBar(true);
        }
      }
    );
    return widgetId;
  }

  const removeNotification = (event: React.MouseEvent<HTMLButtonElement>) => {
    const notificationId = event.currentTarget.getAttribute('data-id');
    if (!notificationId) {
      return;
    }
    const status = event.currentTarget.getAttribute('data-status');
    if (status && ['complete', 'failed'].includes(status)) {
      fetchRemoveNotificationAction(notificationId);
    } else {
      fetchConfirmRemoveNotification(notificationId);
    }
  };
  const showNotifications = () => {
    const totalNotifications = notifications.filter(
      (item) => !item.data.isRead
    ).length;
    setIsOpenNotification(true);
    if (totalNotifications > 0) {
      fetchUpdateStatusReadNotification();
    }
  };
  const hideNotifications = () => {
    setIsOpenNotification(false);
  };
  const handleDownload = (event: React.MouseEvent<HTMLButtonElement>) => {
    const tdoId = event.currentTarget.getAttribute('data-tdo');
    const exportName = event.currentTarget.getAttribute('data-name');
    if (tdoId && exportName) {
      setTdoIdDownload(tdoId);
      onFetchUriToExport(tdoId, exportName);
    }
  };
  return (
    <Fragment>
      <div id="aiWareAppBar" />
      {isShowSearchBar && (
        <Portal container={() => document.getElementById('search-bar-id')}>
          <SearchBar />
        </Portal>
      )}
      <Dialog open={isOpenNotification} onClose={hideNotifications}>
        <NotificationsList
          notifications={notifications}
          removeNotification={removeNotification}
          hideNotifications={hideNotifications}
          handleDownload={handleDownload}
          loadingFetchUriExport={loadingFetchUriExport}
          tdoIdDownload={tdoIdDownload}
        />
      </Dialog>
    </Fragment>
  );
};

const mapState = (state: any) => ({
  notifications: getNotifications(state),
  totalNotifications: getTotalNotifications(state),
  loadingFetchUriExport: getLoadingFetchUriExport(state),
});

const mapDispatch = {
  fetchUpdateStatusReadNotification: fetchUpdateStatusReadNotification,
  fetchRemoveNotificationAction: fetchRemoveNotificationAction,
  fetchConfirmRemoveNotification: fetchConfirmRemoveNotification,
  onFetchUriToExport: (tdoId: string, exportName: string) =>
    ON_FETCH_URI_TO_EXPORT({ tdoId, exportName }),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(AppBar);
