import {
  deleteTDO,
  deleteTDOByFilePath,
  deleteTDOInFolder,
  getTdoByFilePath,
} from './index';

xtest('deleteTDO', async () => {
  const result = await deleteTDO(
    'https://api.stage.us-gov-2.veritone.com/v3/graphql',
    'f3a7fb8a-3e58-43a7-b0ae-23103d1ab3f8',
    'veritoneAppId123',
    ['1790000008', '1790000009']
  );
  console.log(result);
  expect(result.length).toBeGreaterThan(0);
  console.log('done');
}, 600000);

xtest('delete tdo in in Folder', async () => {
  const result = await deleteTDOInFolder(
    'https://api.stage.us-gov-2.veritone.com/v3/graphql',
    'f3a7fb8a-3e58-43a7-b0ae-23103d1ab3f8',
    'veritoneAppId123',
    'folder1'
  );
  console.log(result);
  expect(result.length).toBeGreaterThan(0);
  console.log('done');
}, 600000);

xtest('get tdo by file path', async () => {
  const result = await getTdoByFilePath(
    'https://api.stage.us-gov-2.veritone.com/v3/graphql',
    '3f4b9d9c-5bf1-44fc-a1b3-3aa3fd5b7893',
    'veritoneAppId123',
    'test/bloomberg1.mp4'
  );
  console.log(result);
  expect(result).not.toBeNull();
  console.log('done');
}, 600000);

xtest('delete tdo in in Folder', async () => {
  const result = await deleteTDOByFilePath(
    'https://api.stage.us-gov-2.veritone.com/v3/graphql',
    'f3a7fb8a-3e58-43a7-b0ae-23103d1ab3f8',
    'veritoneAppId123',
    'test/mediaDetail/bloomberg10.mp4'
  );
  console.log(result);
  expect(result.length).toBeGreaterThan(0);
  console.log('done');
}, 600000);
