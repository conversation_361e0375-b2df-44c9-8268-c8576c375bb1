import React from 'react';
import UploadFile from '../';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('Upload File Component', () => {
  const middlewares = [];
  const mockStore = configureStore(middlewares);
  const initialState = {
    config: {
      simpleCognitiveWorkflowDefaultEngineId: {},
      // user: {},
      // ApiToken: '49fbe26c-58f7-4250-a797-dafd45ac02ee',
      // pendoKey: 'cefc4ec9-0826-4b42-7b3e-9b3538e741d0',
      // segmentWriteKey: 'AT5e3mTyrTnb25FhOtJ3GDS54RRYbkA5',
    },
    uploadFile: {
      open: true,
      state: 'overview', // selecting | uploading | complete
      progressPercentByFileKey: {},
      success: false,
      error: false,
      warning: false,
      uploadResult: [],
      checkedFile: [],
      currentEngineCategory: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
      uploadResultEdit: {
        getUrlProgramImage: '',
        programLiveImage: '',
        getUrlProgramLiveImage: '',
        uploadResultId: [],
        fileName: '',
        dateTime: '',
        tagsEdit: [],
      },
      isOpenModalUpload: true,
      loadingSaveUpload: false,
      enginesSelected: [],
      contentTemplateSelected: [],
      tagsCustomize: [],
      loadingProcessingJobs: false,
      noDataProcessing: false,
      isReprocess: false,
      tdoIdsNotSupported: [],
      tdoIdsSupported: [],
      enginesSelectedSupported: [],
      isShowConfirmSaveTemplate: false,
      templateName: '',
      templateSelected: '',
      loadingRemoveTemplate: false,
      countProcess: 0,
      totalReprocess: 0,
      openModalProcess: false,
      percentageFilesUploaded: 0,
      currentPage: 0,
      pageSize: 10,
      offsetPage: 0,
      statusFilter: [
        {
          name: 'In Queue',
          checked: false,
          value: ['pending', 'queued'],
        },
        {
          name: 'In Progress',
          checked: false,
          value: ['running'],
        },
        {
          name: 'Complete',
          checked: false,
          value: ['complete'],
        },
        {
          name: 'Errors',
          checked: false,
          value: ['failed'],
        },
      ],
      dateTimeRangeFilter: 2,
    },
  };
  const store = mockStore(initialState);

  it('renders a Stepper component', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <UploadFile {...initialState} />
      </Provider>
    );
    expect(getByTestId('stepper')).toBeInTheDocument();
  });
  it('renders a CloudUpload component', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <UploadFile {...initialState} />
      </Provider>
    );
    expect(getByTestId('cloud-upload')).toBeInTheDocument();
    expect(getByTestId('upload-media')).toBeInTheDocument();
  });
  it('renders a file picker dialog', () => {
    const newInitialState = {
      ...initialState,
      uploadFile: {
        ...initialState.uploadFile,
        state: 'selecting',
      },
    };
    const newStore = mockStore(newInitialState);
    const { getByTestId } = render(
      <Provider store={newStore}>
        <UploadFile {...initialState} />
      </Provider>
    );
    expect(getByTestId('file-picker-dialog')).toBeInTheDocument();
  });
  it('renders a file progress dialog', () => {
    const newInitialState = {
      ...initialState,
      uploadFile: {
        ...initialState.uploadFile,
        state: 'uploading',
      },
    };
    const newStore = mockStore(newInitialState);
    const { getByTestId } = render(
      <Provider store={newStore}>
        <UploadFile />
      </Provider>
    );
    expect(getByTestId('file-progress-dialog')).toBeInTheDocument();
  });
  it('renders a back Button component', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <UploadFile {...initialState} />
      </Provider>
    );
    expect(getByTestId('back-button')).toBeInTheDocument();
  });
  it('renders a next Button component', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <UploadFile {...initialState} />
      </Provider>
    );
    expect(getByTestId('next-button')).toBeInTheDocument();
  });
});
