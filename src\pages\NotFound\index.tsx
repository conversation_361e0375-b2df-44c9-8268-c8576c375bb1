import React from 'react';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Link from 'redux-first-router-link';

import AppContainer from 'components/AppContainer';
import errorLoading from 'resources/images/error-loading.svg';
import * as styles from './styles.scss';
import { ROUTE_HOME } from '../../state/modules/routing';

export default class NotFound extends React.Component {
  render() {
    return (
      <AppContainer>
        <div className={styles.container}>
          <img src={errorLoading} className={styles['big-image']} />
          <Typography variant="inherit" className={styles['head-line']}>
            Page Not Found
          </Typography>
          <Typography variant="inherit" color="textSecondary">
            {
              "The page you are looking for has been moved, deleted or doesn't exist."
            }
          </Typography>
          <Link to={ROUTE_HOME()}>
            <Button
              className={styles['action-button']}
              variant="contained"
              color="primary"
            >
              Home
            </Button>
          </Link>
        </div>
      </AppContainer>
    );
  }
}
