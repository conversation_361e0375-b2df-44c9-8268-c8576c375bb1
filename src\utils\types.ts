import { Action, AnyAction } from 'redux';

interface Flavoring<FlavorT> {
  _type?: FlavorT;
}
export type Flavor<T, FlavorT> = T & Flavoring<FlavorT>;

export interface ExtendedError extends Error {
  errors: any[];
}

type Tuple<T, N extends number> = N extends N
  ? number extends N
    ? T[]
    : _TupleOf<T, N, []>
  : never;
type _TupleOf<T, N extends number, R extends unknown[]> = R['length'] extends N
  ? R
  : _TupleOf<T, N, [T, ...R]>;

export function arrayHasContents<T>(
  arr: readonly T[] | undefined
): arr is [T, ...T[]] {
  return !!arr && arr.length > 0;
}

export function arrayHasLength<T, LENGTH extends number>(
  arr: readonly T[],
  i: LENGTH
): arr is Tuple<T, LENGTH> {
  return arr.length === i;
}

// TODO: should this be fixed?
// eslint-disable-next-line @jambit/typed-redux-saga/use-typed-effects
import { ChannelPutEffect, PutEffect } from 'redux-saga/effects';
import { ThunkAction } from 'redux-thunk';
import { END, PuttableChannel } from 'redux-saga';
import { BaseActionCreator } from '@reduxjs/toolkit/dist/createAction';

// export type GetActionCreatorPayloadT<C extends ActionCreatorWithPayload<any>> =
export type GetActionCreatorPayloadT<
  C extends BaseActionCreator<any, any, any, never>,
> = C extends BaseActionCreator<infer P, string, any, never> ? P : unknown;

export type AppThunk<T = Promise<any>> = ThunkAction<T, any, any, Action<any>>;

export type Thunk<P, R = any> = (
  payload: P,
  ...args: any[]
) => ThunkAction<Promise<R | undefined>, any, unknown, AnyAction>;
declare module 'typed-redux-saga/macro' {
  // Override default definition from typed-redux-saga
  // Ensure type parameter of action is a string
  export function put<A extends Action<string>>(
    action: A
  ): SagaGenerator<A, PutEffect<A>>;
  export function put<T>(
    channel: PuttableChannel<T>,
    action: T | END
  ): SagaGenerator<T, ChannelPutEffect<T>>;
  // -----------------------------
  export function put<A extends AppThunk<any>>(
    action: A
  ): SagaGenerator<A, PutEffect<any>>;
  // This is intended to cause an error if the `type` parameter of the action is not a string
  export function put<A extends Action<any>>(action: A): never;
}

export const typedObjectKeys = <T extends object>(obj: T) =>
  Object.keys(obj) as (keyof T)[];
