# illuminate-app change logs

## 08/04/2019

    Fix blank page
    - veritone-react-components PR:

## 06/04/2019

    Fix blank page
    - veritone-react-components PR: https://github.com/veritone/veritone-react-components/pull/75

## 03/04/2019

    Allow user to view either transcription and speaker separation bookmark in the same view
    - veritone-react-components PR: https://github.com/veritone/veritone-react-components/pull/73

## 31/03/2019

    Fix bookmark tab does not display transcript and search tag query
    - veritone-react-components branch: feature/GLC-281-boobmark-tab-does-not-display-transcript
    - clone CSP query from veritone-react-common to illuminate-app
        - Fix Tag query
        - Fix Structure Data Object query
