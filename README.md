# Veritone Illuminate

## Table of contents

- [Veritone Illuminate](#veritone-illuminate)
  - [Table of contents](#table-of-contents)
  - [Summary](#summary)
  - [Getting started](#getting-started)
    - [Dependencies](#dependencies)
    - [GitHub Packages](#github-packages)
    - [Set up for local development](#set-up-for-local-development)
    - [Run locally](#run-locally)
    - [Run https locally](#run-https-locally)
    - [Build docker image](#build-docker-image)

## Summary

    Veritone Legal App (Illuminate) helps organizations find what's relevant in audio, video and text-based data with the power of AI. Veritone Illuminate enables Legal teams to assess audio, video & text-based document evidence at the top of their case funnel before review - more cost effectively than ever before.

## Getting started

### Dependencies

    node version and yarn version:
      "node": "v22.12.0",
      "yarn": "4.8.1"

### GitHub Packages

    The GitHub Package is used. Please find .npmrc file in the project. To install package from GitHub Package, please create a personal access token. Set up following environment variable before running yarn install.

    export GITHUB_ACCESS_TOKEN=your personal access token

    Note: https://docs.github.com/en/packages/learn-github-packages/installing-a-package

### Set up for local development

    Make sure following in /etc/hosts
        127.0.0.1    localhost	local.veritone.com

### Run locally

    yarn install

    yarn start

    note: make sure to disable "Schemeful same-site" on Chrome 88 up.
        For anyone doing local app development that updates to Chrome 88 you will probably run into issues logging in to the environments (dev, stage, etc) when using your local app.
        This is because Chrome has turned on Schemeful same-site by default which means your http://local.veritone.com is no longer considered to be the same site as https://api.xxxxx.veritone.com .
        You can fix this by going to chrome://flags in your browser, searching for Schemeful same-site , disabling it, and then hitting the relaunch button.

        This set up is for running http only, and running https dose not need such setup.

### Run https locally

    yarn install

    yarn startssl

note: please generate local.veritone.com.pem and local.veritone.com-key.pem
brew install mkcert
mkcert -install
mkcert local.veritone.com
mkcert -install creates a local CA and adds it to chrome as a trusted CA
if you also want support for firefox do brew install nss after the brew install mkcert

### Build docker image

    docker build --build-arg GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN .

note: you might need to configure docker resources. The following is an example ( you might not need it ).

    cpus: 6
    memory: 12 GB
    swap: 2 GB
    disk image size: 200 GB

Copyright 2025, Veritone Inc.
