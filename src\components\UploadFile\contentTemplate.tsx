import React, { Fragment } from 'react';
import ListItemText from '@mui/material/ListItemText';
import ListItem from '@mui/material/ListItem';
import List from '@mui/material/List';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Add from '@mui/icons-material/Add';
import Grid from '@mui/material/Grid2';
import FormAddContentTemplate from './formContentTemplate';
import makeStyles from '@mui/styles/makeStyles';
import styles from './styles';
import { ContentTemplate as ContentTemplateProps } from '../../state/modules/uploadFile/models';
import { DateTime } from 'luxon';
import { ListItemButton } from '@mui/material';
const useStyles = makeStyles(styles);
function ContentTemplate({
  contentTemplates,
  contentTemplateSelected,
  handleAddContentTemplate,
  handleChangeContentTemplate,
  removeContentTemplate,
  checkValidateTemplate,
}: Props) {
  const classes = useStyles();
  return (
    <Grid container spacing={3}>
      {contentTemplates.length ? (
        <Fragment>
          <Grid
            size={{ xs: 4 }}
            className={classes.listContentTemplateLeft}
            data-testid="content-template-grid"
          >
            <List data-testid="content-template-list">
              {contentTemplates
                .filter(
                  (item) =>
                    !contentTemplateSelected.some(
                      (element) => element.id === item.id
                    )
                )
                .map((item) => {
                  return (
                    <ListItem
                      key={item.id}
                      dense
                      data-test="list-content-template"
                      data-testid="content-template-list-item"
                      secondaryAction={
                        <IconButton
                          data-id={item.id}
                          onClick={handleAddContentTemplate}
                          data-test={`add-content-template_${item.id}`}
                          data-testid="add-content-template"
                          size="large"
                        >
                          <Add />
                        </IconButton>
                      }
                      disablePadding
                    >
                      <ListItemButton>
                        <ListItemText primary={item.name} />
                      </ListItemButton>
                    </ListItem>
                  );
                })}
            </List>
          </Grid>
          <Grid
            size={{ xs: 8 }}
            className={classes.listContentTemplateRight}
            data-testid="content-template-selected-grid"
          >
            {contentTemplateSelected.length ? (
              contentTemplateSelected.map((item) => {
                return (
                  <FormAddContentTemplate
                    key={item.id}
                    contentTemplate={item}
                    onChange={handleChangeContentTemplate}
                    removeContentTemplate={removeContentTemplate}
                    checkValidateTemplate={checkValidateTemplate}
                  />
                );
              })
            ) : (
              <div className={classes.titleSelectContentTemplate}>
                <Typography component="p">
                  Select a content template to add
                </Typography>
              </div>
            )}
          </Grid>
        </Fragment>
      ) : (
        <Grid size={{ xs: 12 }} className={classes.listContentTemplateRight}>
          <div className={classes.titleSelectContentTemplate}>
            <Typography component="p">No content template</Typography>
          </div>
        </Grid>
      )}
    </Grid>
  );
}

interface Props {
  contentTemplates: ContentTemplateProps[];
  contentTemplateSelected: ContentTemplateProps[];
  handleAddContentTemplate: (event: React.MouseEvent<HTMLElement>) => void;
  handleChangeContentTemplate: ({
    value,
    contentTemplateId,
    name,
  }: {
    value: DateTime | null | string;
    contentTemplateId: string;
    name: string;
  }) => void;
  removeContentTemplate: (event: React.MouseEvent<HTMLElement>) => void;
  checkValidateTemplate: boolean;
}
export default ContentTemplate;
