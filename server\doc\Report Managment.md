# Workflow

- Client selects existing report, or creates a new one via a template

  - Ability to list existing reports (add a report table)
  - Ability to list templates

    - Need to add template scoping and availability per org, right now templates are global

  - If selecting an existing report

    - Use report id and org id to get a token configuration and generate a pbi token

  - If adding a new report based on a template

    - User enters name of report
    - Pull users workspace, profile, datasets, datasources, capacities
    - Download PBIX template
    - Upload to user's workspace PBIX template, set report name
    - Store in report table report id, org id, report name, date created
    - Store in token config table token info

  - New blank report ?

- Client loads a report
  - Add to path
  - Add report ID to generate token api
  - Return Token generated from report's token config
