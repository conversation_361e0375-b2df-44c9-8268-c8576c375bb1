import React from 'react';
import ContentTemplate from '../contentTemplate';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('ContentTemplate', () => {
  const props = {
    contentTemplates: [
      {
        description:
          "Stores data related to the user's Contact Analytics dashboard configuration in Illuminate",
        id: 'b4d5afc4-311b-4f0a-b905-d6f85672525c',
        name: 'Illuminate Contact Analytics User State',
        organizationId: 1,
      },
    ],
    contentTemplateSelected: [],
    handleAddContentTemplate: jest.fn(),
    handleChangeContentTemplate: jest.fn(),
    removeContentTemplate: jest.fn(),
    checkValidateTemplate: false,
  };

  it('renders a content template Card', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    expect(getByTestId('content-template-grid')).toBeInTheDocument();
  });
  it('renders a content template List', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    expect(getByTestId('content-template-list')).toBeInTheDocument();
  });
  it('renders a content template ListItem', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    expect(getByTestId('content-template-list-item')).toBeInTheDocument();
  });
  it('renders a title with content Illuminate Contact Analytics User State', () => {
    const { getByText } = render(<ContentTemplate {...props} />);
    expect(
      getByText('Illuminate Contact Analytics User State')
    ).toBeInTheDocument();
  });
  it('renders a add content template Button', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    expect(getByTestId('add-content-template')).toBeInTheDocument();
  });
  it('click add content template Button', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    fireEvent.click(getByTestId('add-content-template'));
    expect(props.handleAddContentTemplate).toHaveBeenCalled();
  });
  it('renders a content template selected Card', () => {
    const { getByTestId } = render(<ContentTemplate {...props} />);
    expect(getByTestId('content-template-selected-grid')).toBeInTheDocument();
  });
  it('renders a title with content Select a content template to add', () => {
    const { getByText } = render(<ContentTemplate {...props} />);
    expect(getByText('Select a content template to add')).toBeInTheDocument();
  });
  it('renders a title with content Select a content template to add', () => {
    const newProps = {
      ...props,
      contentTemplateSelected: [
        {
          description:
            "Stores data related to the user's Contact Analytics dashboard configuration in Illuminate",
          id: 'b4d5afc4-311b-4f0a-b905-d6f85672525c',
          name: 'Illuminate Contact Analytics User State',
          organizationId: 1,
          data: {
            useTitle: '',
            userId: '',
          },
        },
      ],
    };
    const { getByTestId } = render(<ContentTemplate {...newProps} />);
    expect(getByTestId('content-template-form')).toBeInTheDocument();
  });
});
