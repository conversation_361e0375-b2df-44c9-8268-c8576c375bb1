import React from 'react';
import AvailableEngine from '../availableEngine';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('AvailableEngine', () => {
  const props = {
    currentEngineCategory: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    handleChangeEngine: jest.fn(),
    engineCategories: [
      {
        id: '581dbb32-ea5b-4458-bd15-8094942345e3',
        name: 'Transcode',
        iconClass: 'icon-engine-transcode',
        description: 'Converts incoming data formats to another.',
      },
      {
        id: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        name: 'Transcription',
        iconClass: 'icon-transcription',
        description: 'Converts speech audio to text.',
      },
    ],
    handleSearchEngine: jest.fn(),
    engineByCategories: {
      '67cd4dd0-2f75-445d-a6f0-2f297d6cd182': [
        {
          id: 'c0e55cde-340b-44d7-bb42-2e0d65e98255',
          isPublic: true,
          isSelected: false,
          name: 'Speechmatics Transcription - English (Global) V3',
          description: 'This engine converts US English speech to text.',
          price: 10,
        },
      ],
    },
    enginesSelected: [],
    engineNameSearch: '',
    handleAddEngine: jest.fn(),
    isReprocess: false,
  };

  it('renders a input label with content Available Engines', () => {
    const { getByText } = render(<AvailableEngine {...props} />);
    expect(getByText('Available Engines')).toBeInTheDocument();
  });
  it('renders a Select component', () => {
    const { getByTestId } = render(<AvailableEngine {...props} />);
    expect(getByTestId('select-available-engine')).toBeInTheDocument();
  });
  it('renders a TextFiled Search by Engine name', () => {
    const { getByTestId } = render(<AvailableEngine {...props} />);
    expect(getByTestId('input-engine-name')).toBeInTheDocument();
  });
  it('renders a available engine Card', () => {
    const { getAllByTestId } = render(<AvailableEngine {...props} />);
    expect(getAllByTestId('available-engine-card')).toHaveLength(
      props.engineByCategories[props.currentEngineCategory].length
    );
  });
  it('renders a available engine Card', () => {
    const { getAllByTestId } = render(<AvailableEngine {...props} />);
    expect(getAllByTestId('available-engine-card')).toHaveLength(
      props.engineByCategories[props.currentEngineCategory].length
    );
  });
  it('renders a available engine CardHeader', () => {
    const { getAllByTestId } = render(<AvailableEngine {...props} />);
    expect(getAllByTestId('available-engine-card-header')).toHaveLength(
      props.engineByCategories[props.currentEngineCategory].length
    );
  });
  it('renders a icon add available engine CardHeader', () => {
    const { getAllByTestId } = render(<AvailableEngine {...props} />);
    expect(getAllByTestId('add-available-engine-card-header')).toHaveLength(
      props.engineByCategories[props.currentEngineCategory].length
    );
  });
  it('renders a title with content Speechmatics Transcription - English (Global) V3', () => {
    const { getByText } = render(<AvailableEngine {...props} />);
    expect(
      getByText('Speechmatics Transcription - English (Global) V3')
    ).toBeInTheDocument();
  });
  it('renders a available engine CardContent', () => {
    const { getAllByTestId } = render(<AvailableEngine {...props} />);
    expect(getAllByTestId('available-engine-card-content')).toHaveLength(
      props.engineByCategories[props.currentEngineCategory].length
    );
  });
  it('renders a description with content This engine converts US English speech to text.', () => {
    const { getByText } = render(<AvailableEngine {...props} />);
    expect(
      getByText('This engine converts US English speech to text.')
    ).toBeInTheDocument();
  });
  it('click add vailable engine', () => {
    const { getByTestId } = render(<AvailableEngine {...props} />);
    fireEvent.click(getByTestId('add-available-engine-card-header'));
    expect(props.handleAddEngine).toHaveBeenCalled();
  });
});
