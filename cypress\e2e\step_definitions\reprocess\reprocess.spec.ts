import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';

Before(() => {
  landingPage.loginLandingPage();
});

When(
  'The user reprocesses an MP3 file for Spanish to English translation',
  () => {
    const fileName = 'spanish-mp3.mp3';
    landingPage.reprocessesMP3FileForSpanishToEnglishTranslation(fileName);
  }
);

When(
  'The user reprocesses an EML file for Spanish to English translation',
  () => {
    const fileName = 'spanish-email.eml';
    landingPage.reprocessesEMLFileForSpanishToEnglishTranslation(fileName);
  }
);

When(
  'The user reprocesses a PDF file for Spanish to English translation',
  () => {
    const fileName = 'spanish-pdf.pdf';
    landingPage.reprocessesPDFFileForSpanishToEnglishTranslation(fileName);
  }
);

When('The user reprocesses a video file for Amazon Translate', () => {
  landingPage.reprocessesVideoFileForAmazonTranslate();
});
