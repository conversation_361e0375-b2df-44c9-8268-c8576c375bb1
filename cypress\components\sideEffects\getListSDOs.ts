import promisify from 'cypress-promise';

export const getListSDOs = (
  schemaId: string,
  organizationId: string,
  userId = {}
) => {
  const query = `
  query listSDOs {
    structuredDataObjects(
      schemaId: "${schemaId}",
      limit: 10,
      offset: 0,
      orderBy: [{
        field: createdDateTime,
        direction: desc
      }],
      filter: {
        organizationId: "${organizationId}",
        applicationKey: "illuminate",
        userId: "${userId}",
        jobId: {
          neq: ""
        }
      },
      owned: true) {
      records {
        id
        data
        createdDateTime
        modifiedDateTime
      }
    }
  }`;
  return promisify(cy.Graphql(query));
};
