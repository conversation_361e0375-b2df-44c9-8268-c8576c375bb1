import { Knex } from 'knex';
import { TableRow } from '../application/types';

const customQuery = (knex: Knex, tableName: string) => ({
  upsert: (row: TableRow) =>
    knex.transaction((trx) =>
      trx(tableName)
        .insert(row)
        .catch((err) => {
          if (!err.message.includes('Duplicate')) {
            throw err;
          }
          return trx(tableName).update(row).where({ id: row.id });
        })
    ),
});

export default customQuery;
