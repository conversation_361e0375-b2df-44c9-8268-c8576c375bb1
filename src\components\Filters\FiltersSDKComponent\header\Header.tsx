import React, { ReactNode } from 'react';
import * as styles from './styles.scss';

interface Props {
  rightIconButtonElement: ReactNode;
}

export default class FiltersHeader extends React.Component<Props> {
  render() {
    return (
      <div className={styles.container} data-testid="header">
        {
          <div className={styles.singleTabContainer}>
            <div className={styles.tabLabel}>Filters</div>
          </div>
        }
        <div className={styles.rightIconButtonContainer}>
          {this.props.rightIconButtonElement}
        </div>
      </div>
    );
  }
}
