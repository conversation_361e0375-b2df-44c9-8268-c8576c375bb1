import React from 'react';
import EditFileUpload from '../editFile';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('EditFileUpload', () => {
  const props = {
    open: true,
    title: 'Edit Media',
    handleClose: jest.fn(),
    data: [
      {
        key: '1',
        bucket: 'api',
        expiresInSeconds: 86400,
        fileName: 'bloomberg-edit.mp4',
        size: 1740192,
        type: 'video/mp4',
        error: false,
        unsignedUrl: 'unsignedUrl',
        getUrl: 'getUrl',
      },
    ],
    onChangeDateTime: jest.fn(),
    handlePick: jest.fn(),
    onChangeFileName: jest.fn(),
    uploadResultEdit: {
      getUrlProgramImage: 'getUrlProgramImage',
      programLiveImage: 'programLiveImage',
      getUrlProgramLiveImage: 'getUrlProgramLiveImage',
      uploadResultId: [0, 1],
      fileName: 'bloomberg-edit.mp4',
      dateTime: 'dateTime',
      tagsEdit: [],
    },
    handleSave: jest.fn(),
    onKeyPress: jest.fn(),
    handleOnChangeTagsCustomize: jest.fn(),
    tagsEditFileUpload: '',
    handleRemoveTagsCustomize: jest.fn(),
    onClickAddTags: jest.fn(),
  };

  it('renders a Edit File Dialog component', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('edit-file-dialog')).toBeInTheDocument();
  });
  it('renders dialog title with content Edit Media', () => {
    const { getByText } = render(<EditFileUpload {...props} />);
    expect(getByText('Edit Media')).toBeInTheDocument();
  });
  it('renders a DialogContent component', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('dialog-content')).toBeInTheDocument();
  });
  it('renders a Tabs component', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('tabs-edit')).toBeInTheDocument();
  });
  it('renders a Tab component', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('general-edit')).toBeInTheDocument();
  });
  it('renders a Tab component', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('general-edit')).toBeInTheDocument();
  });
  it('renders 2 p with content Upload Image', () => {
    const { getAllByText } = render(<EditFileUpload {...props} />);
    expect(getAllByText('Upload Image')).toHaveLength(2);
  });
  it('renders a File Name Input', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('input-name')).toBeInTheDocument();
  });
  it('renders a Date Time Input', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    expect(getByTestId('input-date')).toBeInTheDocument();
  });
  it('onchange File Name Input', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    fireEvent.change(getByTestId('input-name'), {
      target: { value: 'bloomberg-edit.mp4' },
    });
    expect(getByTestId('input-name')).toHaveValue('bloomberg-edit.mp4');
  });
  it('onchange Date Time Input', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    fireEvent.change(getByTestId('input-name'), {
      target: { value: '2022-07-12T15:60' },
    });
    expect(getByTestId('input-name')).toHaveValue('2022-07-12T15:60');
  });

  it('click Tags Tab', () => {
    const { getByTestId, getByText } = render(<EditFileUpload {...props} />);
    fireEvent.click(getByTestId('tags-edit'));
    expect(getByText('Tagging Media')).toBeInTheDocument();
    expect(getByTestId('tags-customize')).toBeInTheDocument();
  });

  it('click save edit file', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    fireEvent.click(getByTestId('save-edit-file'));
    expect(props.handleSave).toHaveBeenCalled();
  });
  it('click cancel edit file', () => {
    const { getByTestId } = render(<EditFileUpload {...props} />);
    fireEvent.click(getByTestId('cancel-edit-file'));
    expect(props.handleClose).toHaveBeenCalled();
  });
});
