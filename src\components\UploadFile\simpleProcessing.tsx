import React, { Fragment, KeyboardEvent } from 'react';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import makeStyles from '@mui/styles/makeStyles';
import { isEmpty } from 'lodash';
import ListEngine from './listEngine';
import TagsCustomize from './tagsCustomize';
import styles from './styles';
import {
  EngineCategory,
  DagTemplatesByCategory,
  DagTemplatesByCategorySelected,
} from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);

const unavailablEngineCategory: EngineCategory = {
  id: '',
  name: 'unavailable engine category',
  description: 'Please update cognitiveCategoryId',
  iconClass: '',
  unavailablEngineCategory: true,
  engines: {
    records: [],
  },
  libraryEntityIdentifierTypeIds: [],
};
function SimpleProcessing({
  handleShowAdvancedCognitive,
  engineCategories,
  handleClickDagTemplate,
  dagTemplateByCategorySelected,
  dagTemplatesByCategory,
  handleRunJobTemplate,
  handleOpenFolder,
  selectedFolder,
  onKeyPress,
  handleOnChangeTagsCustomize,
  tagsCustomizeName,
  tagsCustomize,
  handleRemoveTagsCustomize,
  onClickAddTags,
  isReprocess,
  showProcessByCategory,
  percentageFilesUploaded,
}: Props) {
  const classes = useStyles();
  const categoryIds = Object.keys(dagTemplatesByCategory);
  return (
    <Fragment>
      <Grid container>
        {!isReprocess && (
          <Grid size={{ xs: 12 }}>
            <TextField
              label="Folder"
              slotProps={{
                input: { readOnly: true },
                htmlInput: { 'data-testid': 'input-folder' },
              }}
              onClick={handleOpenFolder}
              value={selectedFolder?.name ?? ''}
              style={{ marginLeft: 10, marginRight: 20 }}
              variant="standard"
            />
            <TagsCustomize
              type="simpleCognitiveWorkflow"
              onKeyPress={onKeyPress}
              handleOnChangeTagsCustomize={handleOnChangeTagsCustomize}
              tagsCustomizeName={tagsCustomizeName}
              tagsCustomize={tagsCustomize}
              handleRemoveTagsCustomize={handleRemoveTagsCustomize}
              onClickAddTags={onClickAddTags}
            />
          </Grid>
        )}
        <Grid size={{ xs: 8 }}>
          <Typography component="p" className={classes.titleProcessing}>
            Simple Cognitive Workflow
          </Typography>
        </Grid>
        <Grid size={{ xs: 4 }}>
          <Typography
            component="p"
            data-test="show-advanced-cognitive-workflow"
            className={classes.showAdvanced}
            onClick={handleShowAdvancedCognitive}
            data-testid="show-advanced-cognitive-workflow"
          >
            Show Advanced Cognitive Workflow
          </Typography>
        </Grid>
      </Grid>

      <Typography color="textSecondary" gutterBottom>
        Build a workflow by selecting from the classes of cognition below to
        extract, analyze and discovery valuable insight on your ingested files.
      </Typography>
      <Grid container spacing={3} style={{ marginTop: 30 }}>
        {categoryIds.length ? (
          categoryIds.map((categoryId) => {
            const category: EngineCategory =
              engineCategories.find((item) => item.id === categoryId) ||
              unavailablEngineCategory;
            return (
              <Grid
                key={categoryId}
                size={{ xs: 6 }}
                data-id={category?.id}
                data-test={`click-engine-category_${category?.id}`}
                data-testid={`engine-category`}
              >
                <ListEngine
                  title={category?.name}
                  des={category?.description}
                  icon={category?.iconClass}
                  categoryId={category?.id}
                  handleClickDagTemplate={handleClickDagTemplate}
                  dagTemplateByCategorySelected={
                    (!isEmpty(dagTemplateByCategorySelected) &&
                      dagTemplateByCategorySelected[category?.id]) ||
                    (!isEmpty(dagTemplatesByCategory) &&
                      dagTemplatesByCategory[category?.id]?.[0]) ||
                    (!isEmpty(dagTemplatesByCategory) &&
                      dagTemplatesByCategory[categoryId]?.[0]) ||
                    {}
                  }
                  dagTemplates={
                    (!isEmpty(dagTemplatesByCategory) &&
                      dagTemplatesByCategory[category?.id]) ||
                    dagTemplatesByCategory[categoryId] ||
                    []
                  }
                  handleRunJobTemplate={handleRunJobTemplate}
                  unavailablEngineCategory={category?.unavailablEngineCategory}
                  showProcessByCategory={showProcessByCategory}
                  percentageFilesUploaded={percentageFilesUploaded}
                />
              </Grid>
            );
          })
        ) : (
          <Typography className={classes.noAvailableEngine}>
            No available engines.
          </Typography>
        )}
      </Grid>
    </Fragment>
  );
}
interface Props {
  handleShowAdvancedCognitive: () => void;
  engineCategories: EngineCategory[];
  handleClickDagTemplate: (id: string | null, categoryId: string) => void;
  handleRunJobTemplate: (event: React.MouseEvent<HTMLElement>) => void;
  dagTemplateByCategorySelected: DagTemplatesByCategorySelected;
  dagTemplatesByCategory: DagTemplatesByCategory;
  handleOpenFolder: () => void;
  selectedFolder?: {
    name: string;
    treeObjectId: string;
  };
  onKeyPress: (event: KeyboardEvent<HTMLInputElement>) => void;
  handleOnChangeTagsCustomize: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  tagsCustomizeName: string;
  tagsCustomize: { value: string }[];
  handleRemoveTagsCustomize: (name: string, type: string) => void;
  onClickAddTags: (event: React.MouseEvent<HTMLElement>) => void;
  isReprocess: boolean;
  showProcessByCategory: { [key: string]: boolean };
  percentageFilesUploaded: number;
}
export default SimpleProcessing;
