import promisify from 'cypress-promise';

export const getMediaHoursProcessed = (additionConditions: any) => {
  const query = {
    index: ['mine'],
    select: ['veritone-job', 'veritone-file'],
    aggregate: [
      {
        name: 'count',
        field: 'fileDuration',
        operator: 'sum',
      },
    ],
    query: {
      operator: 'and',
      conditions: [
        {
          field: 'veritone-job.engineCategories',
          operator: 'terms',
          values: [
            '581dbb32-ea5b-4458-bd15-8094942345e3',
            '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
            'f2554098-f14b-4d81-9be1-41d0f992a22f',
            '17d62b84-8b49-465b-a6be-fe3ea3bc8f05',
            '6faad6b7-0837-45f9-b161-2f6bf31b7a07',
            '088a31be-9bd6-4628-a6f0-e4004e362ea0',
            '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923',
            'c6e07fe3-f15f-48a7-8914-951b852d54d0',
            'c96b5d0e-3ce1-4fd7-9c38-d25ddef87a5f',
            '203ad7c2-3dbd-45f9-95a6-855f911563d0',
            '892960cb-14c7-4743-a6e2-d6e437d6c5bb',
            '935c4838-dcf6-415c-99c4-5ceb0a8944be',
            '3b4ac603-9bfa-49d3-96b3-25ca3b502325',
            '5a511c83-2cbd-4f2d-927e-cd03803a8a9c',
            'a70df3f6-84a7-4570-b8f4-daa122127e37',
            'a856c447-1030-4fb0-917f-08179f949c4e',
            'c5458876-43d2-41e8-a340-f734702df04a',
            'ba2a423e-99c9-4422-b3a5-0b188d8388ab',
            'a9fa3b20-96ab-44ce-86bc-7694a3b01d82',
            '0481ff76-00c5-4e2d-b73d-2e3aaeef79eb',
            '6c772d3b-6f40-4d85-a672-ddb75dbae0a4',
            '975f846e-79e6-4ead-8a49-23f02a5d068a',
            '24ffd12d-fcda-4f47-8843-b2e5dbfbb01f',
            '70e54f46-7586-4ff1-876c-5f918357aec6',
            'c1e5f177-ca10-433a-a155-bb5e4872cf9a',
            '0b10da6b-3485-496c-a2cb-aabf59a6352d',
            '925e8039-5246-4ced-9f2b-b456d0b57ea1',
            '4b150c85-82d0-4a18-b7fb-63e4a58dfcce',
            ...additionConditions,
          ],
        },
      ],
    },
  };
  return promisify(cy.SearchAggregate(query));
};
