import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function ProcessLimitExceededAlertDialog({
  open,
  processLimit,
  handleProcess,
  handleClose,
}: Props) {
  const onCancel = () => {
    handleClose();
  };
  const onProceed = () => {
    handleProcess();
    handleClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {'Process File Limit Exceeded'}
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          {`The file process limit of ${processLimit} files has been exceeded, only the first ${processLimit} files will be processed.`}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel} color="primary">
          Cancel
        </Button>
        <Button onClick={onProceed} color="primary" autoFocus>
          Proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}

interface Props {
  open: boolean;
  processLimit: number;
  handleProcess: () => void;
  handleClose: () => void;
}
