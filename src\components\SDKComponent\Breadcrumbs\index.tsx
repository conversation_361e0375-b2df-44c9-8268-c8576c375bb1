import React from 'react';
import cx from 'classnames';
import MenuList from '@mui/material/MenuList';
import Popover from '@mui/material/Popover';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import { ChevronRight, MoreHoriz, Work } from '@mui/icons-material';
import { withStyles, ClassNameMap } from '@mui/styles';
import BreadcrumbItem from './BreadcrumbItem';
import styles, { lightBlack } from './styles';

class Breadcrumbs extends React.Component<Props> {
  static defaultProps = {
    maxItems: 5,
    seperator: <ChevronRight style={{ color: lightBlack }} />,
  };

  state = {
    anchorEl: null,
  };

  onSpreadClick = (event: React.MouseEvent<HTMLElement>) => {
    this.setState({
      anchorEl: event.currentTarget,
    });
  };

  clickAway = () => {
    this.setState({
      anchorEl: null,
    });
  };

  onCrumbClick = (event: React.MouseEvent<HTMLElement>) => {
    const elementData = Object.assign({}, event.currentTarget.dataset);
    this.props.onCrumbClick(elementData);
  };

  handleClose = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
  };

  render() {
    const {
      pathList,
      maxItems,
      seperator,
      isStream,
      classes,
      isEnableSuiteCase = true,
      isEnableBackground = true,
      defaultRootTitle = 'My Files',
      loading = false,
    } = this.props;

    const { anchorEl } = this.state;

    const { 0: firstCrumb, [pathList.length - 1]: lastCrumb } = pathList;
    const hiddenCrumbs = pathList.slice(1, pathList.length - 1);
    const icon = isStream ? (
      <div className={cx('icon-streams', classes['rootIcon'])} />
    ) : (
      <Work className={classes['rootIcon']} />
    );
    if (loading) {
      return (
        <div
          className={cx(
            classes.breadcrumbContainer,
            isEnableBackground ? classes.greyBackround : {}
          )}
        >
          <CircularProgress size={20} />
        </div>
      );
    }
    return (
      <div
        className={cx(
          classes.breadcrumbContainer,
          isEnableBackground ? classes.greyBackround : {}
        )}
      >
        {isEnableSuiteCase && (
          <React.Fragment key={'root'}>
            <BreadcrumbItem
              id={'root'}
              icon={icon}
              index={0}
              key={'root'}
              name={pathList.length ? '' : defaultRootTitle}
              onClick={this.onCrumbClick}
              countLevel={pathList.length}
            />
            {!!pathList.length && seperator}
          </React.Fragment>
        )}
        {maxItems && pathList.length < maxItems ? (
          pathList.map((crumb, index) => (
            <React.Fragment key={crumb.id}>
              {index === 0 && (
                <BreadcrumbItem
                  {...crumb}
                  index={index + 1}
                  key={crumb.id}
                  onClick={this.onCrumbClick}
                  countLevel={pathList.length}
                />
              )}
              {index > 0 && seperator}
              {index > 0 && (
                <BreadcrumbItem
                  {...crumb}
                  index={index + 1}
                  key={crumb.id}
                  onClick={this.onCrumbClick}
                  countLevel={pathList.length}
                />
              )}
            </React.Fragment>
          ))
        ) : (
          <React.Fragment>
            <BreadcrumbItem
              {...firstCrumb}
              index={1}
              key={firstCrumb?.id}
              onClick={this.onCrumbClick}
              countLevel={pathList.length}
            />
            {seperator}
            <Button
              className={classes['crumbItem']}
              onClick={this.onSpreadClick}
            >
              <MoreHoriz className={classes['iconColor']} />
            </Button>
            {seperator}
            <BreadcrumbItem
              {...lastCrumb}
              index={pathList.length}
              key={lastCrumb?.id}
              onClick={this.onCrumbClick}
              countLevel={pathList.length}
            />
            <Popover
              open={Boolean(anchorEl)}
              anchorEl={anchorEl}
              onClick={this.clickAway}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
            >
              <MenuList role="menu">
                {hiddenCrumbs.map(({ id, name }, index) => (
                  <BreadcrumbItem
                    key={id}
                    isHidden
                    id={id}
                    index={index + 2}
                    name={name}
                    onClick={this.onCrumbClick}
                    countLevel={pathList.length}
                  />
                ))}
              </MenuList>
            </Popover>
          </React.Fragment>
        )}
      </div>
    );
  }
}
interface Props {
  pathList: { id?: string; name?: string }[];
  maxItems?: number;
  seperator?: string | React.ReactNode;
  onCrumbClick: (event: any) => void;
  isStream?: boolean;
  classes: ClassNameMap;
  isEnableSuiteCase?: boolean;
  isEnableBackground?: boolean;
  defaultRootTitle?: string;
  loading?: boolean;
}
export default withStyles(styles)(Breadcrumbs);
