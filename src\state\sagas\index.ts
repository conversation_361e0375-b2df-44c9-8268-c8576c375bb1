import { fork, all } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
const {
  auth: { authRootSaga },
} = modules;
import routeSaga from 'modules/routing/sagas';
import appSaga from 'modules/app/sagas';
import foldersSaga from 'modules/folders/sagas';
import searchSaga from 'modules/search/sagas';
import tdosTableSaga from 'modules/tdosTable/sagas';
import sunburstSaga from 'modules/sunburst/sagas';
import dashboardSaga from 'modules/dashboard/sagas';
import wordcloudSaga from 'modules/wordcloud/sagas';
import filterSaga from 'modules/filters/sagas';
import historySaga from 'modules/history/sagas';
import contactAnalyticsSaga from 'modules/contactAnalytics/sagas';
// import {uploadFileSaga} from 'veritone-widgets';
import uploadRootSaga from 'modules/uploadFile/uploadFileSaga';
import { sendToRedactSaga } from 'modules/sendToRedact/sagas';
import { tagSaga } from '../modules/tags/sagas';
import { bulkExportSaga } from '../modules/bulkExport/saga';
import processingJobsSaga from 'modules/processingJobs/sagas';
export interface Action<P, M = any> {
  type: any;
  payload: P;
  meta: M;
}

export interface FsaAction<P = any, M = any> {
  payload: P;
  meta: M;
}
// these are sagas that should run on *all* routes, at app startup.
// define route-specific sagas on the routesMap
export default function* sagas() {
  yield* all([
    fork(routeSaga),
    fork(appSaga),
    fork(authRootSaga),
    fork(foldersSaga),
    fork(searchSaga),
    fork(tdosTableSaga),
    fork(sunburstSaga),
    fork(dashboardSaga),
    fork(wordcloudSaga),
    fork(filterSaga),
    fork(historySaga),
    fork(uploadRootSaga),
    fork(sendToRedactSaga),
    fork(contactAnalyticsSaga),
    fork(tagSaga),
    fork(bulkExportSaga),
    fork(processingJobsSaga),
  ]);
}
