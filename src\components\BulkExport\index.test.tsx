import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BulkExport, formatDateTimeForExportName } from '.';

describe('BulkExport', () => {
  it('work correctly with default selection', () => {
    const onClose = jest.fn();
    const onConfirm = jest.fn();
    const onSaveTemplate = jest.fn();
    const handleDeletingExportTemplate = jest.fn();
    render(
      <BulkExport
        defaultExportFileName={'newExportName'}
        onClose={onClose}
        onConfirm={onConfirm}
        onSaveTemplate={onSaveTemplate}
        handleDeletingExportTemplate={handleDeletingExportTemplate}
        open
        isExportTemplateEditEnabled
      />
    );

    // screen.debug(undefined, Infinity);
    screen.getByText('advanced export');
    fireEvent.click(screen.getByText('Export'));
    const expectedExportOption = {
      hasTdoId: true,
      hasFilename: false,
      hasBaseFilename: false,
      hasFoldername: false,
      hasTag: false,
      hasPlainText: true,
      hasTtml: false,
      hasWord: false,
      hasObjectNotation: false,
      hasBookmark: false,
      hasTranslation: false,
      hasNativeTranslation: false,
      hasObjectDetection: false,
      hasSentiment: false,
      hasEngineName: false,
      hasNative: false,
      filePathWindows: false,
      hasClosedCaption: false,
    };
    expect(onConfirm.mock.calls.length).toBe(1);
    // arguments: exportFileName, bulkExportOption, password
    expect(onConfirm.mock.calls[0][0]).toBe('newExportName');
    expect(onConfirm.mock.calls[0][1]).toEqual(expectedExportOption);
    expect(onConfirm.mock.calls[0][2]).toBe('');
  });

  it('select export option correctly', () => {
    const onClose = jest.fn();
    const onConfirm = jest.fn();
    const onSaveTemplate = jest.fn();
    const handleDeletingExportTemplate = jest.fn();
    render(
      <BulkExport
        defaultExportFileName={'newExportName'}
        onClose={onClose}
        onConfirm={onConfirm}
        onSaveTemplate={onSaveTemplate}
        handleDeletingExportTemplate={handleDeletingExportTemplate}
        open
        isExportTemplateEditEnabled
      />
    );

    // screen.debug(undefined, Infinity);
    screen.getByText('advanced export');
    fireEvent.click(screen.getByText('File ID'));
    fireEvent.click(screen.getByText('Filename'));
    fireEvent.click(screen.getByText('Base Filename'));
    fireEvent.click(screen.getByText('Folder Name'));
    fireEvent.click(screen.getByText('Tags'));
    fireEvent.click(screen.getByText('Plain Text (TXT)'));
    fireEvent.click(screen.getByText('Time Text Markup Language (TTML)'));
    fireEvent.click(screen.getByText('Word Document'));
    fireEvent.click(screen.getByText('AI Object Notation (JSON)'));
    fireEvent.click(screen.getByText('Bookmarks'));
    fireEvent.click(screen.getByText('Transcription as Closed Caption'));
    fireEvent.click(screen.getByText('Translation (TXT)'));
    fireEvent.click(screen.getByText('Translation (Native File)'));
    fireEvent.click(screen.getByText('Object Detection'));
    fireEvent.click(screen.getByText('Sentiment Analysis'));
    // Select Options Tab
    fireEvent.click(screen.getByText('Options'));
    // screen.debug(undefined, Infinity);
    fireEvent.click(screen.getByText('Include Original Native Files'));
    fireEvent.click(screen.getByText('Engine Name (Language)'));
    fireEvent.click(screen.getByText('\\ (Windows File System)'));
    // set export name
    fireEvent.click(screen.getByText('Save Template'));
    fireEvent.change(screen.getByLabelText('Template label'), {
      target: { value: 'export template name' },
    });
    fireEvent.click(screen.getByText('Save template'));
    // set password
    fireEvent.click(screen.getByText('Set Password ...'));
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'aPassWord' },
    });
    fireEvent.click(screen.getByText('Save'));

    fireEvent.click(screen.getByText('Export'));
    const expectedExportOption = {
      hasTdoId: true,
      hasFilename: true,
      hasBaseFilename: true,
      hasFoldername: true,
      hasTag: true,
      hasPlainText: false,
      hasTtml: true,
      hasWord: true,
      hasObjectNotation: true,
      hasBookmark: true,
      hasTranslation: true,
      hasNativeTranslation: true,
      hasObjectDetection: true,
      hasSentiment: true,
      hasEngineName: true,
      hasNative: true,
      filePathWindows: true,
      hasClosedCaption: true,
    };
    expect(onConfirm.mock.calls.length).toBe(1);
    // arguments: exportFileName, bulkExportOption, password
    expect(onConfirm.mock.calls[0][0]).toBe('newExportName');
    expect(onConfirm.mock.calls[0][1]).toEqual(expectedExportOption);
    expect(onConfirm.mock.calls[0][2]).toBe('aPassWord');
  });
});

describe('formatDateTimeForExportName', () => {
  it('returns correct morning format for export name', () => {
    // 11 is monthIndex for Nov
    const now = new Date(2022, 11, 15, 22, 20, 46);
    const got = formatDateTimeForExportName(now);
    const want = '20221215_22_20_46';
    expect(got).toBe(want);
  });

  it('returns correct afternoon format for export name', () => {
    // 5 is monthIndex for Jun
    const now = new Date(2023, 5, 2, 8, 7, 6);
    const got = formatDateTimeForExportName(now);
    const want = '20230602_08_07_06';
    expect(got).toBe(want);
  });
});
