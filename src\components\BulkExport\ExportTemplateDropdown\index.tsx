import { TextField, TextFieldProps } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';

export default function ExportTemplateDropdown(
  props: ExportTemplateDropdownProps
) {
  const { dropdownOptions, onDropdownChange, selectedTemplate } = props;

  const options = [...dropdownOptions];
  if (
    selectedTemplate.label !== '' &&
    !options.find((e) => e.label === selectedTemplate.label)
  ) {
    options.push(selectedTemplate);
  }
  return (
    <Autocomplete
      id="export-template-selected"
      options={options}
      fullWidth
      getOptionLabel={(option) => option.label || ''}
      isOptionEqualToValue={(option, value) => option.label === value.label}
      onChange={onDropdownChange}
      value={
        selectedTemplate.label === '' && selectedTemplate.value === ''
          ? null
          : selectedTemplate
      }
      // value={selectedTemplate}
      renderInput={(params: TextFieldProps) => (
        <TextField
          {...params}
          variant="outlined"
          placeholder="Export Templates"
        />
      )}
    />
  );
}

interface ExportTemplateDropdownProps {
  dropdownOptions: { label: string; value: string }[];
  onDropdownChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: { label: string; value: string } | null
  ) => void;
  selectedTemplate: { label: string; value: string };
}
