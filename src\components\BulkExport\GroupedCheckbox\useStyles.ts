import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles(() => ({
  checkboxContainer: {
    marginLeft: '30px',
  },
  title: {
    userSelect: 'none',
    fontStyle: 'normal',
    fontSize: '18px',
    lineHeight: '40px',
    textTransform: 'uppercase',
    color: 'rgba(0, 0, 0, 0.6)',
  },
  formControl: {
    display: 'flex',
    maxWidth: 'fit-content',
  },
  formControlTitle: {
    fontWeight: 'bold',
    fontSize: '14px',
    cursor: 'default',
  },
  formControlLabel: {
    fontWeight: 'normal',
    fontSize: '14px',
    cursor: 'default',
    color: 'rgba(0, 0, 0, 0.6)',
  },
}));
