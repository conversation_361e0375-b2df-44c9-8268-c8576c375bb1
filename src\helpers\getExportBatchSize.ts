import { modules } from '@veritone/glc-redux';
const {
  config: { getConfig },
} = modules;

const defaultExportBatchSize = {
  hasNative: 5000,
  noNative: 50000,
};

const getExportBatchSize = (state: any) => {
  const config = getConfig<Window['config']>(state);
  if (!config.exportBatchSize) {
    return defaultExportBatchSize;
  }
  const batchSizes = config.exportBatchSize;
  if (!batchSizes.hasNative) {
    batchSizes.hasNative = defaultExportBatchSize.hasNative;
  }
  if (!batchSizes.noNative) {
    batchSizes.noNative = defaultExportBatchSize.noNative;
  }
  return batchSizes;
};
export default getExportBatchSize;
