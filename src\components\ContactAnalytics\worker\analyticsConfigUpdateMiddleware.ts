import throttle from 'lodash.throttle';
import reducer from './reducer';
import {
  updateChartUserConfig,
  SET_CHART_SETTINGS,
  SET_CHARTS,
  SET_ANALYTICS_DATE_RANGE,
  ADD_REPORT,
  REMOVE_REPORT,
  SET_CURRENT_REPORT,
} from './actions';

const sendUpdate = throttle(
  (action: any, dispatch: any, state: any) => {
    const { currentReportIndex, reports } = reducer(state, action);
    dispatch(
      updateChartUserConfig({
        currentReportIndex,
        reports: reports.map((r) => ({
          ...r,
          queriesPending: 0,
          chartData: {},
        })),
      })
    );
  },
  1000,
  { trailing: true, leading: false }
);

const analyticsConfigUpdateMiddleware =
  ({ dispatch, getState }: any) =>
  (next: any) =>
  (action: any) => {
    const sensitivityList: string[] = [
      SET_CHART_SETTINGS,
      SET_CHARTS,
      SET_ANALYTICS_DATE_RANGE,
      ADD_REPORT,
      REMOVE_REPORT,
      SET_CURRENT_REPORT,
    ].map((a) => a.type);
    if (sensitivityList.includes(action.type)) {
      sendUpdate(action, dispatch, getState());
    }
    next(action);
  };

export default analyticsConfigUpdateMiddleware;
