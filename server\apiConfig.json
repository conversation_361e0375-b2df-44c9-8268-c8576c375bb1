{"startApi": "true", "serviceName": "illuminate-backend", "nodeEnv": "development", "port": 3002, "prettyPrintLogs": "true", "mssqlHost": "powerbi-contact.database.usgovcloudapi.net", "mssqlDatabase": "powerbi-contact", "mssqlUser": "powerbiadmin", "mssqlPw": "yYJgX2aMMb95G2W", "mssqlSql": "true", "mssqlPort": 1433, "apiRoot": "https://api.stage.us-gov-2.veritone.com", "graphQLEndpoint": "v3/graphql", "azureOAuthRoot": "https://login.microsoftonline.us", "azureAdTenantId": "2b6ba87b-e3cb-4547-bd91-e4160125db25", "azureAdTokenEndpoint": "oauth2/v2.0/token", "azureAdTokenClientId": "aaf1f70d-c139-4b99-9d53-9a20585f4958", "azureAdTokenClientSecrt": "UgkX6SGJR-x.w03WtmtN095xke~.zifpvY", "azureAdTokenBlobStorageScope": "https://vtstorcoredev.blob.core.usgovcloudapi.net/.default", "azureAdTokenPowerBiScope": "https://high.analysis.usgovcloudapi.net/powerbi/api/.default", "azureAdServicePrincipalObjectId": "d6a4b83d-f329-4953-9b62-e8778cace6d1", "azureAdSuId": "1913c56e-53e8-4f0a-8161-a03e9bf0ca8d", "azureAdSuEmail": "<EMAIL>", "azureBlobStorageApiRoot": "https://vtstorcoredev.blob.core.usgovcloudapi.net", "azureBlobStorageContainer": "powerbi-contact", "powerbiApiRoot": "https://api.high.powerbigov.us", "powerbiApiVersionOrg": "v1.0/myorg"}