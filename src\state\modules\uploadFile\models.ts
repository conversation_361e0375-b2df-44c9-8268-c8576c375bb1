import { DateTime } from 'luxon';
export interface UploadProgress {
  name: string;
  type: string;
  size: number;
  error?: string;
  aborted?: string;
  percent: number;
}
export interface EngineCategory {
  id: string;
  name: string;
  description: string;
  iconClass: string;
  unavailablEngineCategory?: boolean;
  categoryType?: string;
  engines: {
    records: {
      id: string;
      name: string;
      libraryRequired: boolean;
      runtimeType: string;
    }[];
  };
  libraryEntityIdentifierTypeIds: string[] | null;
}
export interface Library {
  id: string;
  libraryId: string;
  name: string;
  engineModels: {
    records: {
      id: string;
      trainStatus: string;
      libraryVersion: number;
      engineId: string;
    }[];
  };
  libraryType: {
    id: string;
    label: string;
    entityIdentifierTypes: {
      id: string;
    }[];
  };
  version: number;
}
export interface EngineField {
  label: string;
  defaultValue: string;
  name: string;
  type: string;
  options: {
    value: string;
    key: string;
  }[];
}
export interface Engine {
  id: string;
  name: string;
  description: string;
  librariesSelected: string;
  logoPath: string;
  deploymentModel: string | null;
  expand: boolean;
  libraryRequired: boolean;
  category: {
    id: string;
  };
  fields: EngineField[];
  priority?: number;
  standaloneJobTemplates: {
    type: string;
    template: {
      template: string;
    };
  }[];
  runtimeType: string;
  supportedInputFormats: string[] | null;
  isPublic: boolean;
  price: number;
  isSelected?: boolean;
  isConductor: boolean;
}
export interface EnginesSelected {
  id?: string;
  categoryId: string;
  categoryName: string;
  engineIds: Engine[];
  category?: {
    id: string;
  };
}

interface TaskList {
  categoryId: string;
  categoryName: string;
  engineIds: Engine[];
}
export interface Template {
  id: string;
  name: string;
  taskList: TaskList[];
  loadingRemoveTemplate: boolean;
}
export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  data?: { [key: string]: DateTime | null | string };
  schemas: {
    records: {
      status: string;
      definition: {
        title?: string;
        type?: string;
        required?: string[];
        properties?: { [key: string]: { type: string } };
      };
    }[];
  };
  validate?: string[];
}
export interface DagTemplate {
  id: string;
  name: string;
  description: string;
  tags: string[];
  cognitiveCategoryId: string;
}
export interface DagTemplatesByCategory {
  [key: string]: DagTemplate[];
}
export interface DagTemplatesByCategorySelected {
  [key: string]: DagTemplate;
}
export interface DefaultEngineId {
  webstreamAdapter: string;
  chunk: string;
  outPutWriter: string;
  playbackOther: string;
  glcIngestor: string;
}
export interface EngineByCategories {
  [key: string]: Engine[];
}
export interface TagsCustomize {
  value: string;
}

export interface UploadResult {
  fileName: string;
  dateTime?: string;
  tagsEdit?: { value: string }[];
  programImage?: string;
  getUrlProgramImage?: string;
  programLiveImage?: string;
  getUrlProgramLiveImage?: string;

  type: string;
  key: string;
  engines?: Engine[];
  getUrl: string | null;
  size: number;
  error: string | boolean | null;
  file: File;
  unsignedUrl: string | null;
  bucket: string;
  expiresInSeconds: number;
  aborted?: string;
}

export interface UploadResultEdit {
  fileName: string;
  dateTime: string;
  tagsEdit: { value: string }[];
  programImage: string;
  getUrlProgramImage: string;
  programLiveImage: string;
  getUrlProgramLiveImage: string;
  // the selected UploadResult id
  uploadResultId: number[];
}

export interface LibrariesByCategories {
  [key: string]: {
    [key: string]: Library;
  };
}

export interface SignedWritableUrl {
  data: {
    getSignedWritableUrl: UploadDescriptor;
  };
  errors?: { message: string }[];
}
export interface UploadDescriptor {
  bucket: string;
  expiresInSeconds: number;
  getUrl: string;
  key: string;
  unsignedUrl: string;
  url: string;
}
export interface Records {
  jsondata: any;
  engineId: string;
  assetId: string;
  tdoId: string;
}
export interface EnginesResult {
  records: Records[];
}

export interface ProcessTDO {
  id: string;
  uploadUrl?: string;
  engines?: Engine[];
  primaryAsset: {
    id: string;
    signedUri: string;
    contentType: string;
  };
}

const UploadFileSelectionTypeOptions = ['all', 'single'] as const;
export type UploadFileSelectionType =
  (typeof UploadFileSelectionTypeOptions)[number];
export function isUploadFileSelectionType(
  type: string
): type is UploadFileSelectionType {
  return UploadFileSelectionTypeOptions.includes(
    type as UploadFileSelectionType
  );
}
