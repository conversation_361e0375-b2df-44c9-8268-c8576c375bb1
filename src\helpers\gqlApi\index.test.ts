import { GQLApi } from '../gqlApi';
import config from '../../../config.json';

describe('getSDOCount', () => {
  xit('getSDOCountByOffset', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = 'b402a4b1-53ec-411f-bb65-36365119eda3';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const offset = 0;
    const limit = 10;
    const filter = { organizationId: '1', applicationKey: 'illuminate' };
    const result = await gql.getSDOCountByOffset({
      schemaId,
      offset,
      limit,
      filter,
    });
    console.log('result =====>', result);
    expect(result.data?.structuredDataObjects?.count).toBeGreaterThan(0);
  });

  xit('getSDOCount', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = 'b402a4b1-53ec-411f-bb65-36365119eda3';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const filter = { organizationId: 1, applicationKey: 'illuminate' };
    const result = await gql.getSDOCount(schemaId, filter);
    console.log('result =====>', result);
    expect(result.errors).toBeUndefined();
    expect(result.count).toBeGreaterThan(0);
  });

  it('get sdo count 0', async () => {
    const data = {
      structuredDataObjects: {
        count: 0,
      },
    };
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    jest.spyOn(gql, 'getSDOCountByOffset').mockImplementation(() => {
      return Promise.resolve({
        data: data,
      });
    });
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const filter = { organizationId: '1', applicationKey: 'illuminate' };
    const got = await gql.getSDOCount(schemaId, filter);

    const want = { count: 0 };

    expect(got).toEqual(want);
  });

  it('get sdo count 1', async () => {
    const data = {
      structuredDataObjects: {
        count: 1,
      },
    };
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    jest.spyOn(gql, 'getSDOCountByOffset').mockImplementation(() => {
      return Promise.resolve({
        data: data,
      });
    });
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const filter = { organizationId: '1', applicationKey: 'illuminate' };
    const got = await gql.getSDOCount(schemaId, filter);

    const want = { count: 1 };

    expect(got).toEqual(want);
  });

  it('get sdo count 10001', async () => {
    const data1 = {
      structuredDataObjects: {
        count: 10000,
      },
    };
    const data2 = {
      structuredDataObjects: {
        count: 1,
      },
    };
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    jest
      .spyOn(gql, 'getSDOCountByOffset')
      .mockImplementation(
        ({
          offset,
        }: {
          schemaId: string;
          offset: number;
          limit: number;
          filter?: object;
        }) => {
          return Promise.resolve({
            data: offset === 0 ? data1 : data2,
          });
        }
      );
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const filter = { organizationId: '1', applicationKey: 'illuminate' };
    const got = await gql.getSDOCount(schemaId, filter);

    const want = { count: 10001 };

    expect(got).toEqual(want);
  });

  it('get sdo count error', async () => {
    const data = {
      structuredDataObjects: null,
    };
    const gql = new GQLApi('endpoint', 'token', 'veritoneAppId');
    jest.spyOn(gql, 'getSDOCountByOffset').mockImplementation(() => {
      return Promise.resolve({
        data: data,
        errors: [{ message: 'error1' }],
      });
    });
    const schemaId = '9e761e07-3cb9-48d1-bd53-d209b5ae4728';
    const filter = { organizationId: '1', applicationKey: 'illuminate' };
    const got = await gql.getSDOCount(schemaId, filter);

    const want = { errors: [{ message: 'error1' }] };

    expect(got).toEqual(want);
  });
});
