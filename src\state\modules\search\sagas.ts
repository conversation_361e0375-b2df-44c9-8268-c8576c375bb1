import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import { get } from 'lodash';
import {
  dispatchSearchMediaAction,
  searchMedia,
  updateEntityTypesQuery,
  createGetTdosRequest,
  getTotalResults,
  getFullSearchQuery,
  fetchTagSuggestions,
  ON_SEARCH_BAR_QUERY_CHANGE,
  ON_FOLDER_QUERY_CHANGE,
  SEARCH_MEDIA,
  SEARCH_MEDIA_SUCCESS,
  ON_SEARCH_LIMIT_CHANGE,
  ON_ENTITY_TYPES_QUERY_CHANGE,
  SAVE_QUERY_FOR_EXPORT_ALL,
  TAGS_AUTOCOMPLETE,
  TAG_SUGGESTIONS,
  updateTop<PERSON>Query,
  ON_TOPICS_QUERY_CHANGE,
  ON_CLICK_FILES_TAB,
  ON_SORT_CHANGE,
  updateSortQuery,
  QUERY_TYPE,
  FetchSearchMediaResponse,
  ON_CURRENT_PAGE_CHANGE,
} from '.';

import {
  SET_SELECTED_FOLDER_ID,
  getSelectedFolderId,
  fetchChildFolders,
} from '../folders';

import {
  UPDATE_SELECTED_ENTITY,
  SEARCH_PATH_ENTITY_TYPE,
  SEARCH_PATH_ENTITY_ENTITY,
} from '../sunburst';

import { SEARCH_PATH_TOPIC, ON_SELECTED_TOPIC_CHANGE } from '../wordcloud';

import { Action } from 'state/sagas';
import { PayloadAction } from '@reduxjs/toolkit';
import { ROUTE_TABS } from '../routing';
import { TABS } from '../tabs';

// TODO: Should this be fixed?
// eslint-disable-next-line @jambit/typed-redux-saga/use-typed-effects
import { PutEffect } from 'redux-saga/effects';

function* watchSearchMedia() {
  yield* takeLatest(SEARCH_MEDIA, doSearchMedia);
}

function* doSearchMedia(action: Action<number>) {
  const searchQuery = yield* select(getFullSearchQuery);
  yield* put(searchMedia(searchQuery, action.payload));
}

function* watchSearchMediaSuccess() {
  yield* takeLatest(SEARCH_MEDIA_SUCCESS, doGetTdosById);
}

function* doGetTdosById(action: PayloadAction<FetchSearchMediaResponse>) {
  const searchResults = action?.payload?.result?.records || [];
  const searchResultTdoIds = searchResults.map(
    // mediaId is number, but tdo id is string
    (searchResult) => get(searchResult, 'mediaId') + ''
  );
  // TODO: getFullSearchQuery needs better typing
  const searchQuery = yield* select(getFullSearchQuery);
  searchQuery.limit = yield* select(getTotalResults);

  // save query for exporting tdos
  yield* put(SAVE_QUERY_FOR_EXPORT_ALL(searchQuery));

  if (searchResultTdoIds.length) {
    yield* put(createGetTdosRequest(searchResultTdoIds));
  }
}

function* watchOnSearchBarQueryChange() {
  yield* takeLatest(ON_SEARCH_BAR_QUERY_CHANGE, function* () {
    yield* doDispatchSearchMediaAction();
  });
}

function* watchSelectedFolder() {
  yield* takeLatest(SET_SELECTED_FOLDER_ID, onFolderChange);
}

function* onFolderChange() {
  const selectedFolderId = yield* select(getSelectedFolderId);
  yield* put(fetchChildFolders(selectedFolderId));
}

function* watchOnFolderQueryChange() {
  yield* takeLatest(ON_FOLDER_QUERY_CHANGE, function* () {
    yield* doDispatchSearchMediaAction();
  });
}

function* watchOnCurrentPageChange() {
  yield* takeLatest(ON_CURRENT_PAGE_CHANGE, onCurrentPage);
}

function* onCurrentPage(action: Action<{ currentPage: number }>) {
  const currentPage = action.payload.currentPage;
  yield* put(dispatchSearchMediaAction(currentPage));
}

function* watchOnLimitChange() {
  yield* takeLatest(ON_SEARCH_LIMIT_CHANGE, function* () {
    yield* doDispatchSearchMediaAction();
  });
}

function* watchEntityChange() {
  yield* takeLatest(UPDATE_SELECTED_ENTITY, function* (action) {
    // The first element of selectedEntity is the entity type
    // the second element of selectedEntity is the entity name
    const selectedEntity = get(action, 'payload.selectedEntity', []);
    const queryType = get(action, 'payload.queryType', '');

    const fieldPath = [];
    let propertyValueParent: string | undefined = undefined;
    let propertyValue: string | undefined = undefined;
    if (selectedEntity.length > 0) {
      propertyValueParent = selectedEntity[0];
      if (selectedEntity.length === 1) {
        fieldPath.push(SEARCH_PATH_ENTITY_TYPE);
        propertyValue = selectedEntity[0];
      } else {
        const path = [SEARCH_PATH_ENTITY_TYPE, SEARCH_PATH_ENTITY_ENTITY];
        fieldPath.push(...path);
        propertyValue = selectedEntity[1];
      }
    }
    const values = { propertyValue, propertyValueParent };
    yield* put(updateEntityTypesQuery(fieldPath, values, queryType));
  });
}

function* watchEntityTypesQueryChange() {
  yield* takeLatest(ON_ENTITY_TYPES_QUERY_CHANGE, function* (action) {
    yield* doDispatchSearchMediaAction(action.payload.queryType);
  });
}

function* watchTopicsChange() {
  yield* takeLatest(ON_SELECTED_TOPIC_CHANGE, function* (action) {
    const topics = get(action, 'payload', null);
    yield* put(
      updateTopicsQuery(SEARCH_PATH_TOPIC, topics, QUERY_TYPE.ANALYTICS)
    );
  });
}

function* watchTopicsQueryChange() {
  yield* takeLatest(ON_TOPICS_QUERY_CHANGE, function* (action) {
    yield* doDispatchSearchMediaAction(action.payload.queryType);
  });
}

function* doDispatchSearchMediaAction(queryType?: string) {
  if (queryType !== QUERY_TYPE.APPLY_FILTER) {
    yield* put(dispatchSearchMediaAction(0));
  }
}

function* watchTagAutocomplete() {
  yield* takeLatest(TAGS_AUTOCOMPLETE, function* (action): Generator<
    PutEffect | ReturnType<typeof fetchTagSuggestions>,
    any,
    {
      fields: {
        'tags.displayName': {
          doc_count: number;
          key: string;
        }[];
      };
    }
  > {
    const sendPackage = get(action, 'payload', '');
    const data = yield* put(fetchTagSuggestions(sendPackage));
    const payload = yield data;
    yield* put(TAG_SUGGESTIONS(payload));
  });
}

function* watchOnclickFilesTab() {
  yield* takeLatest(ON_CLICK_FILES_TAB, function* () {
    yield* put(ROUTE_TABS({ tab: TABS.Files }));
    yield* put(dispatchSearchMediaAction(0));
  });
}

function* watchOnSortChange() {
  yield* takeLatest(ON_SORT_CHANGE, function* (action) {
    const { sortQuery = [] } = action.payload;
    yield* put(updateSortQuery(sortQuery));
    yield* put(dispatchSearchMediaAction(0));
  });
}
export default function* folders() {
  yield* all([
    fork(watchSearchMedia),
    fork(watchSearchMediaSuccess),
    fork(watchSelectedFolder),
    fork(watchOnSearchBarQueryChange),
    fork(watchOnFolderQueryChange),
    fork(watchOnLimitChange),
    fork(watchEntityTypesQueryChange),
    fork(watchEntityChange),
    fork(watchTagAutocomplete),
    fork(watchTopicsChange),
    fork(watchTopicsQueryChange),
    fork(watchOnclickFilesTab),
    fork(watchOnSortChange),
    fork(watchOnCurrentPageChange),
  ]);
}
