import { constant, isFunction, isString } from 'lodash';
import { guid } from './guid';
import { modules } from '@veritone/glc-redux';
import fetchGraphQLApi from './fetchGraphQLApi';
import getApiAuthToken from './getApiAuthToken';
import { ExtendedError } from '@utils';
import { GQLActions } from '~helpers/callGraphQLApi';

const {
  config: { getConfig },
} = modules;

async function callGraphQLApi<T>({
  actionTypes: [requestType, successType, failureType],
  query,
  variables,
  operationName,
  bailout = constant(false),
  dispatch,
  getState,
  // optional requestId which can be given to the selectors generated by
  // handleApiCall to track state of an individual request (by default only the
  // latest request is tracked)
  requestId,
}: Props<T>) {
  if (!isFunction(dispatch) || !isFunction(getState)) {
    throw new Error('callGraphQLApi requires dispatch and getState functions');
  }
  const state = getState();
  const config = getConfig<Window['config']>(state);
  const endpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
  const token = getApiAuthToken(state);
  const veritoneAppId = config.veritoneAppId;
  const shouldBail = bailout(state);
  if (shouldBail) {
    return;
  }

  // attach an ID so the handleApiCall reducer can track this request across
  // its multiple actions
  const _internalRequestId = requestId || guid();

  if (isString(requestType)) {
    dispatch({
      type: requestType,
      meta: {
        variables,
        operationName,
        query,
        _internalRequestId,
        _shouldTrackRequestsIndividually: !!requestId,
      },
    });
  } else {
    dispatch(
      requestType({
        variables,
        operationName,
        query,
        _internalRequestId,
        _shouldTrackRequestsIndividually: !!requestId,
      })
    );
  }

  const enableTimeWarnings = endpoint.includes('stage');
  const reqStartTime = Date.now();

  let response;
  try {
    response = await fetchGraphQLApi<T>({
      endpoint,
      query,
      variables,
      operationName,
      token,
      veritoneAppId,
    });
  } catch (e) {
    if (isString(failureType)) {
      dispatch({
        type: failureType,
        error: true,
        payload: e,
        meta: {
          response,
          variables,
          operationName,
          query,
          _internalRequestId,
          _shouldTrackRequestsIndividually: !!requestId,
        },
      });
    } else {
      dispatch(
        failureType({
          payload: e,
          meta: {
            response,
            variables,
            operationName,
            query,
            _internalRequestId,
            _shouldTrackRequestsIndividually: !!requestId,
          },
        })
      );
    }

    const error = new Error('API call failed') as ExtendedError;
    // wrap this single error for consistency with graphQL errors, which are always
    // wrapped.
    error.errors = [e];
    throw error;
  }

  const reqEndTime = Date.now();
  if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
    console.error(
      `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
      { endpoint, query: JSON.stringify({ query, variables }) }
    );
  }

  if (response.errors) {
    if (isString(failureType)) {
      dispatch({
        type: failureType,
        error: true,
        payload: response.errors,
        meta: {
          response,
          variables,
          operationName,
          query,
          _internalRequestId,
          _shouldTrackRequestsIndividually: !!requestId,
        },
      });
    } else {
      dispatch(
        failureType({
          payload: response.errors,
          meta: {
            response,
            variables,
            operationName,
            query,
            _internalRequestId,
            _shouldTrackRequestsIndividually: !!requestId,
          },
          error: true,
        })
      );
    }
    const error = new Error('API response included errors') as ExtendedError;
    error.errors = response.errors;
    throw error;
  }

  if (isString(successType)) {
    dispatch({
      type: successType,
      payload: response.data,
      meta: {
        response,
        variables,
        operationName,
        query,
        _internalRequestId,
        _shouldTrackRequestsIndividually: !!requestId,
      },
    });
  } else {
    dispatch(
      successType({
        payload: response.data,
        meta: {
          response,
          variables,
          operationName,
          query,
          _internalRequestId,
          _shouldTrackRequestsIndividually: !!requestId,
        },
      })
    );
  }
  return response.data;
}

interface Props<T> {
  actionTypes: GQLActions<T>;
  query: string;
  variables?: object;
  operationName?: string;
  bailout?: (state: any) => boolean;
  dispatch: any;
  getState: any;
  requestId?: string;
}

export default callGraphQLApi;
