import { pipe } from '../state/utils/pipe';

interface SearchQuery {
  field?: string;
  value?: string;
  values?: string[];
  operator: string;
  distance?: number;
  conditions?: SearchQuery[];
}

function deriveKeywordsFromQuery(
  searchQueries: SearchQuery[],
  keywordList: string[] = []
) {
  searchQueries.map((searchQuery) => {
    if (
      (searchQuery.field === 'transcript.transcript' ||
        searchQuery.field === 'text-document.text') &&
      (searchQuery.value || searchQuery.values)
    ) {
      if (searchQuery.operator === 'word_proximity' && searchQuery.values) {
        const searchTerms = searchQuery.values.join(
          ` w/${searchQuery.distance} `
        );
        keywordList.push(searchTerms);
      }
      if (searchQuery.operator !== 'word_proximity' && searchQuery.value) {
        keywordList.push(searchQuery.value);
      }
    }

    if (searchQuery.conditions) {
      deriveKeywordsFromQuery(searchQuery.conditions, keywordList);
    }
  });
  return keywordList;
}

function detectProximityAndRemoveSimilarTerms(terms: string[]) {
  const proximityWords = terms.filter((term) => term.includes('w/'));
  const duplicatedWords = proximityWords.map((word) => {
    const holder = word.split(' ');
    return holder[0] + ' ' + holder[2];
  });
  return terms.reduce<string[]>((acc, term) => {
    if (!duplicatedWords.includes(term)) {
      acc.push(term);
    }
    return acc;
  }, []);
}

function configureKeywordsBooleanSearch(terms: string[]) {
  return terms.map((term) => {
    const hasNotKeyword = ['-', 'not', 'NOT'].some((sign) =>
      term.includes(sign)
    );
    if (hasNotKeyword) {
      const normalWord = term.toLowerCase();
      const wordIndex = normalWord.indexOf('and');
      return normalWord.substring(0, wordIndex).trim();
    }
    return term;
  });
}

export function getSearchKeywords(query: SearchQuery[]) {
  return pipe(
    deriveKeywordsFromQuery,
    detectProximityAndRemoveSimilarTerms,
    configureKeywordsBooleanSearch
  )(query);
}
