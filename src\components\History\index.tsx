import HistoryItem from './Item';
import { ConnectedProps, connect } from 'react-redux';
import { getHistory } from 'state/modules/history';

const History = ({ data }: PropsFromRedux) => <HistoryItem data={data} />;

const mapState = (state: any) => ({
  data: getHistory(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(History);
