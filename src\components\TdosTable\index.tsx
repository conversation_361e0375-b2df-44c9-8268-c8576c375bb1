import React, { Fragment } from 'react';
import { DateTime } from 'luxon';
import Paper from '@mui/material/Paper';
import * as Sentry from '@sentry/react';
import {
  SelectionState,
  PagingState,
  IntegratedSelection,
  CustomPaging,
  IntegratedPaging,
  SortingState,
} from '@devexpress/dx-react-grid';
import {
  Grid,
  TableHeaderRow,
  TableSelection,
  PagingPanel,
  DragDropProvider,
  TableColumnReordering,
  VirtualTable,
  Table,
  TableColumnVisibility,
} from '@devexpress/dx-react-grid-material-ui';
import TablePagination from '@mui/material/TablePagination';
import { ConnectedProps, connect } from 'react-redux';
import {
  getSearchResultTdos,
  getTotalResults,
  getSearchError,
  isSearchingMedia,
  getLimit,
  getFullSearchQuery,
  // ON_SEARCH_OFFSET_CHANGE,
  ON_SEARCH_LIMIT_CHANGE,
  getEngineCategoriesIds,
  getCurrentPage,
  UPDATE_CURRENT_PAGE,
  getNoData,
  getSearchBarQuery,
  getSearchParameters,
  OBJECT_CONDITION_TYPE,
  onSortChange,
  UPDATE_TDO,
  ON_CURRENT_PAGE_CHANGE,
  getSearchResultTdosAll,
} from 'state/modules/search';
import {
  isSelectedAll,
  getSelectedRows,
  HIDE_CONFIRMATION_DIALOG,
  ON_SELECTION_CHANGE,
  getEngineCategories,
  SELECTED_ALL,
  getIndeterminate,
  getIsShowPreview,
  getInitFullScreen,
  getTdoIdPreview,
  getAllState,
  openTdoPreview,
  getEnableRedactNavigation,
  getDeletedTdos,
  getMovedTdos,
  getSelectedTdos,
  getIndexesByTdos,
} from 'state/modules/tdosTable';

import get from 'lodash/get';
import MicIcon from '@mui/icons-material/Mic';
import CamIcon from '@mui/icons-material/Videocam';
import DocIcon from '@mui/icons-material/Description';
import InsertPhoto from '@mui/icons-material/InsertPhoto';
import EmailIcon from '@mui/icons-material/Email';
import * as styles from './styles.scss';
import classNames from 'classnames/bind';
import getApiAuthToken from '../../helpers/getApiAuthToken';
import CircularProgress from '@mui/material/CircularProgress';
import Checkbox from '@mui/material/Checkbox';
import TableCell from '@mui/material/TableCell';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { getExcludeEngineCategoryIds } from '../../helpers/common';
/* @ts-expect-error: the type from shared component is wrong */
import { MediaDetailsWindow } from '@veritone/react-components';
import { modules } from '@veritone/glc-redux';
import { getSearchKeywords } from '../../helpers/getSearchKeywords';
import { openModalReprocess } from 'state/modules/uploadFile/actions';
import { ROUTE_FILE, ROUTE_TABS } from 'state/modules/routing';
import { TABS } from 'state/modules/tabs';
import { TDO } from 'state/modules/universal/TDO';
import getRedactUrl from '../../helpers/getRedactUrl';
import { IN_REDACTION } from 'state/modules/sendToRedact';
import mediaSummaryIcon from '../../../resources/images/media-summary.svg';

const {
  config: { getConfig },
} = modules;
const MimeTypeIcon: { [key: string]: React.ReactElement } = {
  video: <CamIcon />,
  audio: <MicIcon />,
  application: <DocIcon />,
  image: <InsertPhoto />,
  text: <DocIcon />,
  message: <EmailIcon />,
  other: <Typography className={styles['other-mime-type']}>-</Typography>,
};
const cx = classNames.bind(styles);

const columnExtensions = [
  { columnName: 'name', sortingEnabled: true },
  {
    columnName: 'mediaLengthMinutes',
    sortingEnabled: false,
  },
  {
    columnName: 'createdDateTime',
    sortingEnabled: true,
  },
  {
    columnName: 'enginesRun',
    sortingEnabled: false,
  },
  {
    columnName: 'redact',
    sortingEnabled: false,
  },
  {
    columnName: 'tagsForDisplay',
    sortingEnabled: false,
  },
];
declare type SortingDirection = 'asc' | 'desc';
interface Sorting {
  columnName: string;
  direction: SortingDirection;
}

export class TdosTable extends React.Component<PropsFromRedux> {
  static defaultProps = {};
  state = {
    redactDialogOpen: false,
    mimeTypeIcon: null,
    tags: null,
    arrIsShowMore: [] as string[],
    pageSize: 10,
    currentPage: 0,
    checkedAll: false,
    columns: [
      { name: 'name', title: 'Name' },
      { name: 'mimeType', title: 'Type' },
      { name: 'id', title: 'ID' },
      { name: 'mediaLengthMinutes', title: 'Duration' },
      { name: 'createdDateTime', title: 'Creation Date' },
      { name: 'enginesRun', title: 'Engines Run' },
      { name: 'redact', title: ' ' },
      { name: 'tagsForDisplay', title: 'Tags' },
    ],
    tableColumnExtensions: [
      { columnName: 'name', wordWrapEnabled: true },
      { columnName: 'mimeType', width: 100 },
      { columnName: 'id', width: 100 },
      { columnName: 'mediaLengthMinutes', width: 100 },
      { columnName: 'createdDateTime', wordWrapEnabled: true },
      { columnName: 'enginesRun', wordWrapEnabled: true },
      { columnName: 'redact', width: 50 },
      { columnName: 'tagsForDisplay', wordWrapEnabled: true },
    ],
    defaultHiddenColumnNames: ['enginesRun', 'tagsForDisplay'],
    heightRow: 70,
    sort: [{ columnName: 'id', direction: 'desc' }] as Sorting[],
    indexByTdoIds: [] as number[],
  };
  componentDidUpdate(prevProps: any) {
    if (
      prevProps.items !== this.props.items ||
      prevProps.indexesByTdos !== this.props.indexesByTdos
    ) {
      this.setState({
        indexByTdoIds: this.props.indexesByTdos[this.props.currentPage] ?? [],
      });
    }
  }

  changeSelection = (selection: Array<number | string>) => {
    const { items, navigateToTab, isShowPreview, currentPage } = this.props;
    const tdoIdsByIndex = [] as string[];
    for (const index of selection) {
      const tdoId = items[Number(index)]?.id;
      if (tdoId) {
        tdoIdsByIndex.push(tdoId);
      }
    }
    if (tdoIdsByIndex.length && this.isTdoDeletedOrMoved(tdoIdsByIndex)) {
      return;
    }
    if (isShowPreview) {
      navigateToTab(TABS.Files, true);
    }
    this.props.onSelectionChange(
      selection,
      selection.length === items.length,
      items,
      selection.length > 0 && selection.length < items.length,
      currentPage
    );
  };
  changeCurrentPage = (_event: any, currentPage: number) => {
    const { onNextPage } = this.props;
    onNextPage(currentPage);
  };
  onPageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { navigateToTab, isShowPreview } = this.props;
    if (isShowPreview) {
      navigateToTab(TABS.Files, true);
    }
    this.props.onLimitChange(Number(event.target.value));
    this.setState({ heightRow: 70, arrIsShowMore: [] });
  };

  actionExplainTags = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const idElm: string = event.currentTarget.getAttribute('data-id') || '';
    const dataLength = Number(event.currentTarget.getAttribute('data-length'));
    const { heightRow } = this.state;
    const limit = Number(this.props.limit);
    this.setState({
      heightRow: (dataLength * heightRow + heightRow * limit) / limit,
    });
    this.setState((prevState: { arrIsShowMore: string[] }) => {
      let currentArrIsShowMore = [...prevState.arrIsShowMore];
      currentArrIsShowMore.includes(idElm)
        ? (currentArrIsShowMore = currentArrIsShowMore.filter(
            (item) => item !== idElm
          ))
        : currentArrIsShowMore.push(idElm);
      return { arrIsShowMore: currentArrIsShowMore };
    });
  };
  onToggleFullText = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const target = event.target as HTMLElement;
    if (target.style.width !== 'auto') {
      target.style.width = 'auto';
    } else {
      target.style.width = '60px'; // set width for tag, change this if we need show more than text, but sync with style.css value.
    }
  };
  onClosePreview = () => {
    const { navigateToTab } = this.props;
    openTdoPreview({ isShowPreview: false, initFullScreen: false });
    navigateToTab(TABS.Files, true);
  };
  myCustomPagination = (props: { totalCount: number; pageSize: number }) => {
    const { items, currentPage, isLoading } = this.props;
    return (
      <TablePagination
        data-testid="file-paging"
        rowsPerPageOptions={[10, 20, 100]}
        component="div"
        count={props.totalCount}
        rowsPerPage={props.pageSize}
        page={currentPage}
        onPageChange={this.changeCurrentPage}
        onRowsPerPageChange={this.onPageSizeChange}
        style={{
          paddingRight: 68,
        }}
        className={styles['table-footer']}
        slotProps={{
          actions: {
            nextButton: {
              'aria-label': 'Next Page',
              disabled: isLoading || items.length < props.pageSize,
            },
            previousButton: {
              'aria-label': 'Next Page',
              disabled: isLoading || currentPage === 0,
            },
          },
        }}
      />
    );
  };
  onSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { updateSelectedAll, items, searchResultTdosAll, limit } = this.props;
    const keySelected = Object.keys(items).map((item) => parseInt(item));
    if (event.target.checked) {
      updateSelectedAll(
        event.target.checked,
        keySelected,
        items,
        false,
        searchResultTdosAll,
        limit
      );
    } else {
      updateSelectedAll(event.target.checked, [], [], false, [], limit);
    }
  };
  myCustomHeaderCellComponent = () => {
    const { isSelectedAll, indeterminate } = this.props;
    return (
      <TableCell
        padding="checkbox"
        classes={{ paddingCheckbox: styles['selection-all'] }}
      >
        <Checkbox
          indeterminate={indeterminate}
          checked={isSelectedAll ?? false}
          onChange={this.onSelectAllClick}
          data-testid="select-all"
        />
      </TableCell>
    );
  };
  TableRow = ({ row, ...restProps }: Table.DataRowProps) => {
    return (
      <Table.Row
        row={undefined}
        {...restProps}
        data-testid={`files-table-row-${row.id}`}
        onClick={this.clickToViewDetail(row.id)}
        className={cx(styles['table-row'], {
          'active-row':
            row.id === this.props.tdoIdPreview && this.props.isShowPreview,
          'disable-row': this.isTdoDeletedOrMoved(row.id),
        })}
      />
    );
  };

  clickToViewDetail = (id: string) => () => {
    const { navigateToFile } = this.props;
    const payload = {
      tab: TABS.Files,
      tdoId: id,
      initFullScreen: false,
    };
    navigateToFile(payload);
  };

  myCustomDataCellComponent = (props: NoDataCellProps) => {
    const { noData } = this.props;
    return noData ? <VirtualTable.NoDataCell {...props} /> : null;
  };

  handleReprocess = () => {
    const { openModalReprocess } = this.props;
    openModalReprocess();
  };
  navigateToRedact = (event: React.MouseEvent<HTMLElement>) => {
    const { enableRedactNavigation } = this.props;
    if (enableRedactNavigation) {
      event.stopPropagation();
      const { redactUrl } = this.props;
      const tdoId = event.currentTarget.getAttribute('data-id');
      const url = `${redactUrl}/files/${tdoId}`;
      window.open(url, '_blank');
    }
  };
  onSortingChange = (value: { columnName: string; direction: string }[]) => {
    const { onSortChange } = this.props;
    onSortChange(value);
    this.setState({ sort: value });
  };
  handleUpdateTdo = ({
    tdoId,
    name,
    tags,
  }: {
    tdoId: string;
    name?: string;
    tags?: { value: string }[];
  }) => {
    this.props.updateTdo({ tdoId, name, tags });
  };
  isTdoDeletedOrMoved = (tdoId: string | string[]) => {
    const { deletedTdos, movedTdos } = this.props;
    if (Array.isArray(tdoId)) {
      return (
        deletedTdos.some((item) => tdoId.includes(item.id)) ||
        movedTdos.some((item) => tdoId.includes(item.id))
      );
    }
    return (
      deletedTdos.some((item) => item.id === tdoId) ||
      movedTdos.some((item) => item.id === tdoId)
    );
  };
  getIndexByTdoIds = () => {
    const { selectedTdos, items, currentPage } = this.props;
    const indices: number[] = [];
    for (let i = 0; i <= items.length; i++) {
      const selectedTdosIds = selectedTdos?.[currentPage]?.map(
        (item: any) => item.id
      );

      const item = items[i];
      if (item && selectedTdosIds?.includes(item.id)) {
        indices.push(i);
      }
    }
    return indices;
  };

  render() {
    const {
      items,
      // allRowsSelectedIndexes,
      totalResults,
      engineCategoriesIds,
      engineCategories,
      limit,
      currentPage,
      isShowPreview,
      tdoIdPreview,
      appConfig,
      searchBarQuery,
      state,
      initFullScreen,
      enableRedactNavigation,
      searchParameters,
      isLoading,
    } = this.props;
    const kvp = get(state, 'user.user.organization.kvp', {});
    const sessionToken = get(state, 'auth.sessionToken', '');
    // Check api then remove these if the api have them
    const conditions = get(searchBarQuery, 'query.conditions', []);
    const keywordsFromQuery = getSearchKeywords(conditions);
    const keywordLists = keywordsFromQuery;
    const objectDetection = searchParameters.filter(
      (item: { conditionType: string }) =>
        item.conditionType === OBJECT_CONDITION_TYPE
    );

    const searchValue = objectDetection.map(
      (item: { value: { id: string; label: string } }) =>
        item.value.label || item.value.id
    );

    const data = items.map((item) => {
      if (item.status === 'error') {
        return {
          name: 'N/A',
          mimeType: 'N/A',
          id: item.id,
          mediaLengthMinutes: 'N/A',
          createdDateTime: 'N/A',
          enginesRun: 'N/A',
          tagsForDisplay: 'N/A',
        };
      }
      // Get duration of each TDO
      const duration = (
        (new Date(item.stopDateTime).getTime() -
          new Date(item.startDateTime).getTime()) /
        60000
      ).toFixed(1);

      let mimeType = get(item, 'primaryAsset.contentType', 'other')
        .toString()
        .split('/')[0];

      if (!mimeType || !MimeTypeIcon[mimeType]) {
        mimeType = 'other';
      }

      let detailsTags = item?.details?.tags || [];
      if (!Array.isArray(detailsTags)) {
        Sentry.captureMessage(`tag should be an array - ${item.id}`);
        console.error('tag should be an array.', item.id, detailsTags);
        detailsTags = [];
      }

      const isItemHasShowMore = this.state.arrIsShowMore.includes(item.id);
      detailsTags = detailsTags.filter(
        (tag) => tag.value && !(tag?.key === 'batchId')
      );
      const tagsTotalNumber = detailsTags.length ? detailsTags.length > 3 : 0;
      const cloneDetailsTags = isItemHasShowMore
        ? [...detailsTags]
        : [...detailsTags].slice(0, 3);
      const tagsForDisplay = cloneDetailsTags.map((tag, index) => (
        <Fragment key={tag.value}>
          <div
            className={cx({
              'same-width-tags': !isItemHasShowMore,
              'tag-item': isItemHasShowMore,
            })}
            key={tag.value}
            onClick={this.onToggleFullText}
          >
            <span>{tag.value}</span>
          </div>
          {index + 1 === detailsTags.length && isItemHasShowMore ? (
            <div
              className={styles['action-more-less-btn']}
              data-id={item.id}
              onClick={this.actionExplainTags}
              data-length={detailsTags.length}
            >
              less
            </div>
          ) : (
            ''
          )}
          {tagsTotalNumber && index + 1 === 3 && !isItemHasShowMore ? (
            <div
              className={styles['action-more-less-btn']}
              data-id={item.id}
              onClick={this.actionExplainTags}
              data-length={detailsTags.length}
            >
              + {detailsTags.length - 3} more
            </div>
          ) : (
            ''
          )}
        </Fragment>
      ));

      const categoriesId =
        engineCategoriesIds?.[item.id]?.engineCategoriesIds ?? [];

      const excludeEnginCategoryIds =
        getExcludeEngineCategoryIds(engineCategories);
      const iconClassName: { icon: string; name: string }[] = [];
      categoriesId
        .filter((item) => !excludeEnginCategoryIds.includes(item))
        .forEach((item) => {
          const category = engineCategories[item];
          const icon = category ? category.iconClass : '';
          const name = category ? category.name : '';
          if (
            !iconClassName.some(
              (item: { icon: string; name: string }) => item.icon === icon
            ) &&
            icon !== ''
          ) {
            iconClassName.push({ icon: icon, name: name });
          }
        });
      const inRedaction = detailsTags.filter(
        (tag) => tag.value === IN_REDACTION
      );
      const redact =
        (inRedaction.length && enableRedactNavigation && (
          <IconButton
            onClick={this.navigateToRedact}
            className={styles['icon-redact']}
            data-id={item.id}
            size="large"
          >
            <i className="icon-redaction-app" />
          </IconButton>
        )) ||
        '';
      const tooltipMessages: { [key: string]: string } = {
        deleting: 'The process of deleting files may take about 20 seconds',
        moving: 'The process of moving files may take about 20 seconds',
      };

      return {
        name: (
          <Tooltip
            title={
              item.status === 'deleting'
                ? tooltipMessages[item.status]
                : item.status === 'moving'
                  ? tooltipMessages[item.status]
                  : get(item, 'details.veritoneFile.filename', item.id)
            }
            placement="bottom"
            key={item.id}
          >
            <Typography className={styles['title-content']}>
              {item.status === 'deleting'
                ? 'Deleting...'
                : item.status === 'moving'
                  ? 'Moving...'
                  : get(item, 'details.veritoneFile.filename', null) ||
                    get(item, 'details.veritoneFile.fileName', item.id)}
            </Typography>
          </Tooltip>
        ),
        mimeType: (
          <Tooltip title={mimeType} placement="bottom" key={item.icon}>
            {MimeTypeIcon[mimeType]!}
          </Tooltip>
        ),
        id: item.id,
        mediaLengthMinutes: duration + ' m',
        createdDateTime: (
          <>
            {DateTime.fromISO(item.createdDateTime).toFormat(
              'ccc MMM d yyyy HH:mm:ss'
            )}
          </>
        ),
        enginesRun: (
          <div className={styles['icon-engine']}>
            {iconClassName
              .sort((a: { name: string }, b: { name: string }) =>
                a.name.localeCompare(b.name)
              )
              .map((item) => {
                return (
                  <Tooltip title={item.name} placement="bottom" key={item.icon}>
                    {item.name === 'Summarization' ? (
                      <img src={mediaSummaryIcon} alt="Media Summary" />
                    ) : (
                      <i className={item.icon} />
                    )}
                  </Tooltip>
                );
              })}
          </div>
        ),
        redact,
        tagsForDisplay: <div data-testid="tag-cell">{tagsForDisplay}</div>,
      };
    });

    return (
      <div className={styles['table-clear']}>
        <Paper
          className={cx({
            'preview-hide': !isShowPreview,
            'preview-active': isShowPreview,
          })}
        >
          <Grid rows={data} columns={this.state.columns}>
            <DragDropProvider />
            <PagingState currentPage={currentPage} pageSize={Number(limit)} />
            <SelectionState
              selection={this.state.indexByTdoIds}
              onSelectionChange={this.changeSelection}
            />
            <SortingState
              sorting={this.state.sort}
              onSortingChange={this.onSortingChange}
              columnExtensions={columnExtensions}
            />
            <IntegratedSelection />
            <IntegratedPaging />
            <CustomPaging totalCount={totalResults} />
            <VirtualTable
              rowComponent={this.TableRow}
              columnExtensions={this.state.tableColumnExtensions}
              height={data.length > 0 ? window.innerHeight - 302 : 175} // calc screen height minus with header and ...
              estimatedRowHeight={this.state.heightRow}
              noDataCellComponent={this.myCustomDataCellComponent}
            />
            <TableColumnReordering
              defaultOrder={this.state.columns.map((column) => column.name)}
            />
            {isShowPreview && (
              <TableColumnVisibility
                defaultHiddenColumnNames={this.state.defaultHiddenColumnNames}
              />
            )}
            <TableHeaderRow
              showSortingControls
              cellComponent={(props) => (
                <TableHeaderRow.Cell
                  {...props}
                  className={styles['table-row']}
                />
              )}
            />
            <TableSelection
              showSelectAll
              headerCellComponent={this.myCustomHeaderCellComponent}
            />
            <PagingPanel
              containerComponent={this.myCustomPagination}
              pageSizes={[10, 20, 30, 100]}
            />
            {/* <PagingPanel pageSizes={[10, 20, 30, 100]} /> */}
          </Grid>
          {isLoading && (
            <div className={styles['loadding-data']}>
              <CircularProgress size={30} variant="indeterminate" />
            </div>
          )}
        </Paper>
        {isShowPreview && tdoIdPreview && items.length > 0 && (
          <MediaDetailsWindow
            key={tdoIdPreview}
            mediaId={tdoIdPreview}
            config={appConfig}
            token={sessionToken}
            initFullScreen={initFullScreen}
            onClose={this.onClosePreview}
            appName="illuminate"
            kvp={kvp}
            keywordLists={keywordLists}
            handleRunProcessIlluminate={this.handleReprocess}
            searchValue={searchValue}
            onUpdateTdo={this.handleUpdateTdo}
          />
        )}
      </div>
    );
  }
}
interface Column {
  name: string;
  title?: string;
  showBadge?: boolean;
}
interface TableRow {
  key: string;
  type: symbol;
  rowId?: number | string;
  row?: any;
  height?: number;
}
interface TableColumn {
  key: string;
  type: symbol;
  column?: Column;
  width?: number | string;
  align?: 'left' | 'right' | 'center';
  fixed?: 'left' | 'right';
}
interface NoDataCellProps {
  getMessage: (messageKey: string) => string;
  tableRow: TableRow;
  tableColumn: TableColumn;
  colSpan?: number;
  rowSpan?: number;
}
export interface EngineCategoriesIds {
  [key: string]: {
    engineCategoriesIds: string[];
  };
}
export interface engineCategories {
  [key: string]: {
    id: string;
    iconClass: string;
    name: string;
  };
}
// interface Props {
//   limit: number;
//   engineCategoriesIds: EngineCategoriesIds;
//   engineCategories: engineCategories;
//   appConfig: object;
//   searchBarQuery: object;
//   state: object;
// }

const mapState = (state: any) => ({
  items: getSearchResultTdos(state),
  totalResults: getTotalResults(state),
  isLoading: isSearchingMedia(state),
  limit: getLimit(state),
  isSelectedAll: isSelectedAll(state),
  allRowsSelectedIndexes: getSelectedRows(state),
  error: getSearchError(state),
  apiAuthToken: getApiAuthToken(state),
  fullSearchQuery: getFullSearchQuery(state),
  engineCategories: getEngineCategories(state),
  engineCategoriesIds: getEngineCategoriesIds(state),
  currentPage: getCurrentPage(state),
  indeterminate: getIndeterminate(state),
  isShowPreview: getIsShowPreview(state),
  initFullScreen: getInitFullScreen(state),
  tdoIdPreview: getTdoIdPreview(state),
  noData: getNoData(state),
  appConfig: getConfig(state as any),
  searchBarQuery: getSearchBarQuery(state),
  state: getAllState(state),
  redactUrl: getRedactUrl(state),
  enableRedactNavigation: getEnableRedactNavigation(state),
  searchParameters: getSearchParameters(state),
  deletedTdos: getDeletedTdos(state),
  movedTdos: getMovedTdos(state),
  selectedTdos: getSelectedTdos(state),
  indexesByTdos: getIndexesByTdos(state),
  searchResultTdosAll: getSearchResultTdosAll(state),
});

const mapDispatch = {
  onNextPage: (currentPage: number) => ON_CURRENT_PAGE_CHANGE({ currentPage }),
  onLimitChange: (limit: number) => ON_SEARCH_LIMIT_CHANGE({ limit }),
  onSelectionChange: (
    selectedRows: Array<number | string>,
    isSelectedAll: boolean,
    allTdos: TDO[],
    indeterminate: boolean,
    currentPage: number
  ) =>
    ON_SELECTION_CHANGE({
      selectedRows,
      isSelectedAll,
      allTdos,
      indeterminate,
      currentPage,
    }),
  hideConfirmationDialog: () => HIDE_CONFIRMATION_DIALOG({}),
  updateCurrentPage: (currentPage: number) => UPDATE_CURRENT_PAGE(currentPage),
  updateSelectedAll: (
    selected: boolean,
    keySelected: number[],
    tdos: TDO[],
    indeterminate: boolean,
    searchResultTdosAll: any,
    limit: number
  ) =>
    SELECTED_ALL({
      selected,
      keySelected,
      tdos,
      indeterminate,
      searchResultTdosAll,
      limit,
    }),
  openTdoPreview: openTdoPreview,
  openModalReprocess: openModalReprocess,
  navigateToFile: (payload: {
    tab: string;
    tdoId: string;
    initFullScreen: boolean;
  }) => ROUTE_FILE(payload),
  navigateToTab: (tabName: string, isClosePreview?: boolean) =>
    ROUTE_TABS({ tab: tabName, isClosePreview }),
  onSortChange: onSortChange,
  updateTdo: ({
    tdoId,
    name,
    tags,
  }: {
    tdoId: string;
    name?: string;
    tags?: { value: string }[];
  }) => UPDATE_TDO({ tdoId, name, tags }),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(TdosTable);
