import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import get from 'lodash/get';
import { Notification } from '../../../model';
import {
  setSelection,
  isSelectedAll,
  fetchEngineCategories,
  fetchSchemasNotification,
  getSchemaIdNotification,
  fetchGetNotification,
  FETCH_UPDATE_IS_READ_NOTIFICATION_REQUEST,
  getNotifications,
  FETCH_REMOVE_NOTIFICATION_REQUEST,
  CREATE_ILLUMINATE_APP_FOLDER,
  CREATE_ILLUMINATE_APP_FOLDER_SUCCESS,
  CREATE_ILLUMINATE_APP_FOLDER_FAILURE,
  FETCH_UPDATE_IS_READ_NOTIFICATION,
  FETCH_UPDATE_IS_READ_NOTIFICATION_SUCCESS,
  FETCH_UPDATE_IS_READ_NOTIFICATION_FAILURE,
  <PERSON>ETCH_SCHEMAS_NOTIFICATIONS_SUCCESS,
  fetchNotifications,
  FET<PERSON>_UPDATE_HIDE_NOTIFICATION,
  FET<PERSON>_UPDATE_HIDE_NOTIFICATION_SUCCESS,
  FETCH_UPDATE_HIDE_NOTIFICATION_FAILURE,
  updateIlluminateAppFolder,
  INTERNAL_FOLDER_NAME,
  updateTdoIdPreview,
  openTdoPreview,
  RESET_DATA_PREVIEW,
  UPDATE_INDEXES_BY_TDOS,
  UPDATE_EXPORT_AND_REPROCESS_TDOS,
} from '.';

import {
  ON_SEARCH_BAR_QUERY_CHANGE,
  ON_FOLDER_QUERY_CHANGE,
  ON_SEARCH_LIMIT_CHANGE,
  GET_SEARCH_TDOS_BY_ID_SUCCESS,
  getCurrentPage,
} from '../search';
import { CREATE_BULK_EXPORT_SUCCESS } from '../bulkExport';

import { BOOT_FINISHED } from '../app';
import { modules } from '@veritone/glc-redux';
import {
  FETCH_FOLDERS_SUCCESS,
  fetchCreateFolder,
  SET_SELECTED_FOLDER_ID,
} from '../folders';
import {
  ROUTE_FILE,
  ROUTE_TABS,
  selectRouteType,
  selectCurrentRoutePayload,
} from '../routing';
import { TABS } from 'state/modules/tabs';
import { Action } from 'state/sagas';
import { TDO } from '../universal/TDO';
import { UPDATE_EXPORT_HISTORY_SUCCESS } from '../history';
const {
  user: { selectUser },
} = modules;

function* watchSearchQueryChange() {
  // intentionally not resetting selection ON_ANALYTICS_QUERY_CHANGE and  ON_FILE_TYPE_QUERY_CHANGE
  // talk to Product/UX
  yield* all([
    takeLatest(ON_SEARCH_BAR_QUERY_CHANGE, resetTableSelection),
    takeLatest(ON_FOLDER_QUERY_CHANGE, resetTableSelection),
    takeLatest(ON_SEARCH_LIMIT_CHANGE, resetTableSelection),
  ]);
}

function* resetTableSelection() {
  yield* put(setSelection([], false));
}

function* watchNextPageTdosFetched() {
  yield* all([takeLatest(GET_SEARCH_TDOS_BY_ID_SUCCESS, adjustSelectedRows)]);
}

function* adjustSelectedRows(action: Action<{ data: TDO[] }>) {
  const currentPage = yield* select(getCurrentPage);
  const selectedAll = yield* select(isSelectedAll);
  const data = action.payload.data ?? [];

  if (!selectedAll) {
    return;
  }

  yield* put(UPDATE_INDEXES_BY_TDOS({ data, currentPage }));
  yield* put(UPDATE_EXPORT_AND_REPROCESS_TDOS({ data, currentPage }));
}

function* fetchDataEngineCategories() {
  yield* put(fetchEngineCategories());
}

function* fetchSchemasIdNotification() {
  yield* takeLatest(BOOT_FINISHED, function* (_action) {
    yield* put(fetchSchemasNotification());
  });
}

function* updateIsReadNotification() {
  yield* takeLatest(
    FETCH_UPDATE_IS_READ_NOTIFICATION_REQUEST,
    function* (_action) {
      const notifications: Notification[] = yield* select(getNotifications);
      const schemaIdNotification = yield* select(getSchemaIdNotification);

      const dataSdoUpdate = notifications
        .filter((item) => !item.data.isRead)
        .map((item) => {
          return {
            id: item.id,
            schemaId: schemaIdNotification,
            data: {
              ...item.data,
              isRead: true,
            },
          };
        });
      // update isRead for Notifications
      if (dataSdoUpdate) {
        yield* put(
          fetchNotifications(dataSdoUpdate, [
            FETCH_UPDATE_IS_READ_NOTIFICATION,
            FETCH_UPDATE_IS_READ_NOTIFICATION_SUCCESS,
            FETCH_UPDATE_IS_READ_NOTIFICATION_FAILURE,
          ])
        );
      }
    }
  );
}

function* fetchRemoveNotifications() {
  yield* takeLatest(FETCH_REMOVE_NOTIFICATION_REQUEST, function* (action) {
    const schemaIdNotification = yield* select(getSchemaIdNotification);
    const notificationId = action.payload;
    // yield put(fetchRemoveNotification(action.payload, schemaIdNotification));
    const notifications = yield* select(getNotifications);
    const dataSdoUpdate = notifications
      .filter((item) => item.id === notificationId)
      .map((item) => {
        return {
          id: item.id ?? undefined,
          schemaId: schemaIdNotification ?? undefined,
          data: {
            ...item.data,
            hide: true,
          },
        };
      });
    // update hide for Notifications
    if (dataSdoUpdate) {
      yield* put(
        fetchNotifications(dataSdoUpdate, [
          FETCH_UPDATE_HIDE_NOTIFICATION,
          FETCH_UPDATE_HIDE_NOTIFICATION_SUCCESS,
          FETCH_UPDATE_HIDE_NOTIFICATION_FAILURE,
        ])
      );
    }
  });
}

function* watchFetchFolders() {
  yield* takeLatest(FETCH_FOLDERS_SUCCESS, function* (action) {
    const subFolder = action.payload.rootFolder.childFolders?.records || [];

    const illuminateAppFolders = subFolder.filter(
      (item) => item.name === INTERNAL_FOLDER_NAME
    );
    const rootFolderId = get(action, 'payload.rootFolder.id', '');
    if (illuminateAppFolders.length) {
      return yield* put(updateIlluminateAppFolder(illuminateAppFolders));
    }
    return yield* put(
      fetchCreateFolder(INTERNAL_FOLDER_NAME, rootFolderId, [
        CREATE_ILLUMINATE_APP_FOLDER,
        CREATE_ILLUMINATE_APP_FOLDER_SUCCESS,
        CREATE_ILLUMINATE_APP_FOLDER_FAILURE,
      ])
    );
  });
}

function* watchSelectedFolder() {
  yield* takeLatest(SET_SELECTED_FOLDER_ID, function* (action) {
    const routePayload: { tab?: string } = yield* select(
      selectCurrentRoutePayload
    );
    // reset files view when folder is selected
    if (
      routePayload?.tab === TABS.Files &&
      action.payload?.type !== 'initialPageLoad'
    ) {
      yield* put(ROUTE_TABS({ tab: TABS.Files }));
    }
  });
}

function* watchCreateIlluminateAppFolderSuccess() {
  yield* takeLatest(CREATE_ILLUMINATE_APP_FOLDER_SUCCESS, function* (action) {
    const { id, treeObjectId } = action.payload?.createFolder || {
      id: '',
      treeObjectId: '',
    };
    yield* put(updateIlluminateAppFolder([{ id, treeObjectId }]));
  });
}

function* watchFetchNotification() {
  // yield takeLatest(FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS, function*(action) {
  //   const user = yield select(selectUser);
  //   const userId = user.userId;
  //   const organizationId = user.organization.organizationId;
  //   const schemaId = get(action, 'payload.schemas.records[0].id', null);
  //   yield put(fetchGetNotification(schemaId, userId, organizationId));
  // });
  // yield takeLatest(FETCH_UPDATE_NOTIFICATION_BY_ID_SUCCESS, function* (action){
  //   yield put(fetchGetNotification(schemaId, userId, organizationId));

  // })

  yield* all([
    takeLatest(FETCH_SCHEMAS_NOTIFICATIONS_SUCCESS, function* (action) {
      const { userId, organizationId } = yield getUser();
      const schemaId = get(action, 'payload.schemas.records[0].id', '');
      yield* put(fetchGetNotification(schemaId, userId, organizationId));
    }),
    takeLatest(CREATE_BULK_EXPORT_SUCCESS, function* () {
      const { userId, organizationId } = yield getUser();
      const schemaId = yield* select(getSchemaIdNotification);
      if (schemaId) {
        yield* put(fetchGetNotification(schemaId, userId, organizationId));
      }
    }),
    takeLatest(UPDATE_EXPORT_HISTORY_SUCCESS, function* () {
      const { userId, organizationId } = yield getUser();
      const schemaId = yield* select(getSchemaIdNotification);
      if (schemaId) {
        yield* put(fetchGetNotification(schemaId, userId, organizationId));
      }
    }),
  ]);
}

function* getUser(): any {
  const user = yield* select(selectUser);
  const userId = user?.userId;
  const organizationId = user?.organization?.organizationId;
  return {
    userId,
    organizationId,
  };
}

function* loadRouteData() {
  yield* takeLatest(GET_SEARCH_TDOS_BY_ID_SUCCESS, function* () {
    // load data when reload page
    yield doUpdateTdoPreview({ isShowPreview: true, initFullScreen: true });
  });
}

function* watchRouteTabs() {
  yield* takeLatest(ROUTE_TABS, function* (action) {
    // reset data preview when back url or click tabs
    const { isClosePreview } = action.payload;

    yield* put(RESET_DATA_PREVIEW({ isClosePreview }));
  });
}

function* watchRouteFile() {
  yield* takeLatest(ROUTE_FILE, function* (action) {
    const { initFullScreen = false } = action.payload;
    yield doUpdateTdoPreview({ isShowPreview: true, initFullScreen });
  });
}

function* doUpdateTdoPreview({
  isShowPreview,
  initFullScreen,
}: {
  isShowPreview: boolean;
  initFullScreen: boolean;
}) {
  const routeType = yield* select(selectRouteType);
  const routePayload = yield* select(selectCurrentRoutePayload);
  if (routeType === ROUTE_FILE.type) {
    const { tdoId } = routePayload;
    yield* put(openTdoPreview({ isShowPreview, initFullScreen }));
    yield* put(updateTdoIdPreview(tdoId.toString()));
  }
}

export default function* tdosTable() {
  yield* all([
    fork(watchSearchQueryChange),
    fork(watchNextPageTdosFetched),
    fork(fetchDataEngineCategories),
    fork(fetchSchemasIdNotification),
    fork(fetchRemoveNotifications),
    fork(updateIsReadNotification),
    fork(watchFetchFolders),
    fork(watchSelectedFolder),
    fork(watchFetchNotification),
    fork(watchCreateIlluminateAppFolderSuccess),
    fork(loadRouteData),
    fork(watchRouteTabs),
    fork(watchRouteFile),
  ]);
}
