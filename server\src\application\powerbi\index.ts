import NodeCache from 'node-cache';
import { Knex } from 'knex';
import createQueries from './queries';
import createHandlers from './handlers';
import actions from './actions';

const createPowerbiApp = ({
  db,
  log,
  cache,
}: {
  db: Promise<Knex>;
  log: Logger;
  cache: NodeCache;
}) => {
  const queries = createQueries({ db });
  const handlers = createHandlers({ queries, log, cache });

  return {
    actions,
    handlers,
    queries,
  };
};

export default createPowerbiApp;
