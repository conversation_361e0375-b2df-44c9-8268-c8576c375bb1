import config from '../../config.json';

const { excludedEngineNames } = config;

export const getExcludeEngineCategoryIds = (
  engineCategories: EngineCategories
) => {
  // Object engineCategories to array engineCategories
  const arrayOfEngineCategories = Object.values(engineCategories);
  const excludedEngineIds: string[] = [];
  excludedEngineNames.forEach((engineName) => {
    arrayOfEngineCategories.forEach((engineCategory) => {
      if (engineCategory.name === engineName) {
        excludedEngineIds.push(engineCategory.id);
      }
    });
  });
  return excludedEngineIds;
};

export const AUDIO_MIME_TYPES = [
  'audio/aac',
  'audio/flac',
  'audio/midi',
  'audio/mp4',
  'audio/mp3',
  'audio/mpeg',
  'audio/wav',
  'audio/x-wav',
  'audio/webm',
];
export const VIDEO_MIME_TYPES = [
  'video/3gpp',
  'video/mp4',
  'video/mpeg',
  'video/ogg',
  'video/quicktime',
  'video/webm',
  'video/x-m4v',
  'video/x-ms-wmv',
  'video/x-msvideo',
];
export const MEDIA_TYPES = VIDEO_MIME_TYPES.concat(AUDIO_MIME_TYPES);
export const IMAGES_MIME_TYPES = [
  'image/jpeg',
  'image/gif',
  'image/png',
  'image/bmp',
  'image/tiff',
];
export const DOCS_MIME_TYPES = [
  'text/csv',
  'text/html',
  'text/plain',
  'application/json',
  'application/msword',
  'application/pdf',
  'application/rtf',
  'application/smil+xml',
  'application/ttml+xml',
  'application/vnd.ms-outlook',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.oasis.opendocument.text',
  'application/vnd.oasis.opendocument.spreadsheet',
  'application/x-flv',
  'application/xml',
  'application/x-www-form-urlencoded',
  'message/rfc822',
];

interface EngineCategories {
  [key: string]: {
    id: string;
    iconClass: string;
    name: string;
  };
}
