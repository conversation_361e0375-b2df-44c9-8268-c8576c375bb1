// import { MaterialUiPickersDate } from '@material-ui/pickers/typings/date';

import { createAction } from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from '~helpers/apiHelper';
import { ProcessingJob } from './models';

export const namespace = 'processingJobs';
export const FETCH_PROCESSING_JOBS = createAsyncFuncRequestAction(
  `${namespace}_FETCH_PROCESSING_JOBS`
);

export const FETCH_PROCESSING_JOBS_SUCCESS = createAsyncFuncSuccessAction<{
  results: ProcessingJob[];
  remainingProcessingJobs: ProcessingJob[];
  offset: number;
}>(`${namespace}_FETCH_PROCESSING_JOBS_SUCCESS`);

export const FETCH_PROCESSING_JOBS_FAILURE = createAsyncFuncFailureAction(
  `${namespace}_FETCH_PROCESSING_JOBS_FAILURE`
);

export const REFRESH_PROCESSING_JOBS = createAction(
  `${namespace}_REFRESH_PROCESSING_JOBS`
);

export const ON_CHANGE_PAGE_PROCESSING_JOBS = createAction<{
  currentPage: number;
  pageSize: number;
}>(`${namespace}_ON_CHANGE_PAGE_PROCESSING_JOBS`);
export const ON_CHANGE_PAGE_SIZE_PROCESSING_JOBS = createAction<{
  currentPage: number;
  pageSize: number;
}>(`${namespace}_ON_CHANGE_PAGE_SIZE_PROCESSING_JOBS`);

export const ON_CHANGE_STATUS_FILTER = createAction<{
  value: {
    name: string;
    checked: boolean;
  };
}>(`${namespace}_ON_CHANGE_STATUS_FILTER`);

export const FILTER_JOB_BY_STATUS = createAction(
  `${namespace}_FILTER_JOB_BY_STATUS`
);
export const FILTER_JOB_BY_DATE_TIME_RANGE = createAction<{
  value: number;
}>(`${namespace}_FILTER_JOB_BY_DATE_TIME_RANGE`);

export const RETRY_JOBS = createAction(`${namespace}_RETRY_JOBS`);
export const RETRY_JOBS_SUCCESS = createAction(
  `${namespace}_RETRY_JOBS_SUCCESS`
);
export const RETRY_JOBS_FAILED = createAction(`${namespace}_RETRY_JOBS_FAILED`);

export const EXPORT_FAILED_JOBS = createAction(
  `${namespace}_EXPORT_FAILED_JOBS`
);
export const EXPORT_FAILED_JOBS_SUCCESS = createAction(
  `${namespace}_EXPORT_FAILED_JOBS_SUCCESS`
);
export const EXPORT_FAILED_JOBS_FAILED = createAction(
  `${namespace}_EXPORT_FAILED_JOBS_FAILED`
);
export const EXPORT_FAILED_JOBS_PERCENTAGE = createAction<{
  percentage: number;
}>(`${namespace}_EXPORT_FAILED_JOBS_PERCENTAGE`);

export const UPDATE_PERCENTAGE_FAILED_JOBS_UPLOADED = createAction<{
  percentage: number;
}>(`${namespace}_UPDATE_PERCENTAGE_FAILED_JOBS_UPLOADED`);
export const ON_SELECTION_JOBS_CHANGE = createAction<{
  selectedRows: { [key: string]: Array<number | string> };
  isSelectedAll: boolean;
  indeterminate: boolean;
  tdoId: string;
}>(`${namespace}_ON_SELECTION_JOBS_CHANGE`);

export const UPDATE_TOTAL_PROCESSING_JOBS = createAsyncFuncRequestAction(
  `${namespace}_UPDATE_TOTAL_PROCESSING_JOBS`
);
export const UPDATE_TOTAL_PROCESSING_JOBS_SUCCESS =
  createAsyncFuncSuccessAction<{ total: number }>(
    `${namespace}_UPDATE_TOTAL_PROCESSING_JOBS_SUCCESS`
  );
export const UPDATE_TOTAL_PROCESSING_JOBS_FAILED = createAsyncFuncFailureAction(
  `${namespace}_UPDATE_TOTAL_PROCESSING_JOBS_FAILED`
);

export const UPDATE_CURRENT_PAGE_PROCESSING_JOBS = createAction<number>(
  `${namespace}_UPDATE_CURRENT_PAGE_PROCESSING_JOBS`
);

export const SET_SHOW_CUSTOM_RANGE = createAction<boolean>(
  `${namespace}_SET_SHOW_CUSTOM_RANGE`
);

export const SET_CUSTOM_START_DATE = createAction<DateTime | null>(
  `${namespace}_SET_CUSTOM_START_DATE`
);

export const SET_CUSTOM_END_DATE = createAction<DateTime | null>(
  `${namespace}_SET_CUSTOM_END_DATE`
);

export const FILTER_JOB_BY_CUSTOM_DATE_RANGE = createAction(
  `${namespace}_FILTER_JOB_BY_CUSTOM_DATE_RANGE`
);
export const CLEAR_CUSTOM_DATE_RANGE = createAction(
  `${namespace}_CLEAR_CUSTOM_DATE_RANGE`
);

// export const STATUS_PROCESSING_JOBS: {
//   [key: string]: { name: string; value: string[] };
// } = {
export const STATUS_PROCESSING_JOBS = {
  inQueue: {
    name: 'In Queue',
    value: ['pending', 'queued'],
  },
  inProgress: {
    name: 'In Progress',
    value: ['running'],
  },
  complete: {
    name: 'Complete',
    value: ['complete'],
  },
  errors: {
    name: 'Errors',
    value: ['failed'],
  },
} as const;

export const TASK_QUEUED_STATUS = [
  'pending',
  'queued',
  'accepted',
  'standby_pending',
  'waiting',
  'resuming',
  'paused',
];
export const TASK_RUNNING_STATUS = ['running'];
export const TASK_COMPLETE_STATUS = ['complete'];
export const TASK_FAILED_STATUS = ['failed', 'cancelled', 'aborted'];

export const refreshProcessingJobs = () => REFRESH_PROCESSING_JOBS();

export const onChangePageProcessingJobs = (
  currentPage: number,
  pageSize: number
) => ON_CHANGE_PAGE_PROCESSING_JOBS({ currentPage, pageSize });

export const onChangePageSizeProcessingJobs = (
  currentPage: number,
  pageSize: number
) => ON_CHANGE_PAGE_SIZE_PROCESSING_JOBS({ currentPage, pageSize });

export const onChangeStatusFilter = (value: {
  name: string;
  checked: boolean;
}) => ON_CHANGE_STATUS_FILTER({ value });

export const filterJobByStatus = () => FILTER_JOB_BY_STATUS();

export const filterJobByDateTimeRange = (value: number) =>
  FILTER_JOB_BY_DATE_TIME_RANGE({ value });

export const retryJobs = () => RETRY_JOBS();

export const retryJobsSuccess = () => RETRY_JOBS_SUCCESS();

export const retryJobsFailed = () => RETRY_JOBS_FAILED();

export const updatePercentageFailedJobsUploaded = (percentage: number) =>
  UPDATE_PERCENTAGE_FAILED_JOBS_UPLOADED({ percentage });

export const onSelectionJobsChange = (
  selectedRows: { [key: string]: Array<number | string> },
  isSelectedAll: boolean,
  indeterminate: boolean,
  tdoId: string
) =>
  ON_SELECTION_JOBS_CHANGE({
    selectedRows,
    isSelectedAll,
    indeterminate,
    tdoId,
  });

export const updateTotalProcessingJobs = (total: number) =>
  UPDATE_TOTAL_PROCESSING_JOBS_SUCCESS({ total });

export const updateCurrentPageProcessingJobs = (currentPage: number) =>
  UPDATE_CURRENT_PAGE_PROCESSING_JOBS(currentPage);

export const setShowCustomRange = (showCustomRange: boolean) =>
  SET_SHOW_CUSTOM_RANGE(showCustomRange);

export const setCustomStartDate = (customStartDate: DateTime | null) =>
  SET_CUSTOM_START_DATE(customStartDate);

export const setCustomEndDate = (customEndDate: DateTime | null) =>
  SET_CUSTOM_END_DATE(customEndDate);

export const filterJobByCustomDateRange = () =>
  FILTER_JOB_BY_CUSTOM_DATE_RANGE();

export const clearCustomDateRange = () => CLEAR_CUSTOM_DATE_RANGE();

export const exportFailedJobs = () => EXPORT_FAILED_JOBS();

export const exportJobsSuccess = () => EXPORT_FAILED_JOBS_SUCCESS();

export const exportJobsFailed = () => EXPORT_FAILED_JOBS_FAILED();

export const exportFailedJobsPercentageUpdate = (percentage: number) =>
  EXPORT_FAILED_JOBS_PERCENTAGE({ percentage });
