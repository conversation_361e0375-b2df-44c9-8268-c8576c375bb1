import { Knex } from 'knex';

exports.up = (knex: Knex) =>
    Promise.all([
        knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
            if (tableExists) {
                const datetimeOfStopPST = await knex.schema.hasColumn('ContactStopData', 'datetimeOfStopPST');

                if (!datetimeOfStopPST) {
                    await knex.schema.raw(
                        `ALTER TABLE ContactStopData ADD datetimeOfStopPST datetimeoffset;`
                    );
                }

                return true;
            }
        })
    ])

exports.down = (knex: Knex) =>
    Promise.all([
        knex.schema.dropTable('ContactStopData')
    ])
