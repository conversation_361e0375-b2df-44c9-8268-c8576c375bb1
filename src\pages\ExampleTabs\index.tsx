import React, { Fragment } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import Grid from '@mui/material/Grid2';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import CircularProgress from '@mui/material/CircularProgress';

import {
  selectCurrentRoutePayload,
  ROUTE_EXAMPLE_TABS,
} from 'state/modules/routing';
import { engineIsLoading, selectEngine } from 'state/modules/engines-example';
import AppContainer from 'components/AppContainer';
import AppBar from 'components/AppBar';
import TopBar from 'components/TopBar';
import SideBar from 'components/SideBar';
import ContentContainer from 'components/ContentContainer';

import CategoriesTab from './CategoriesTab';
import TasksTab from './TasksTab';
import * as styles from './styles.scss';
import Analytics from 'components/Tabs/Analytics';
import Files from 'components/Tabs/Files';
import Exports from 'components/Tabs/Exports';
import ContactAnalyticsTab from 'components/Tabs/ContactAnalytics';
import ProcessingStatus from 'components/Tabs/ProcessingStatus';
import ContactAnalyticsPowerbi from 'components/Tabs/ContactAnalyticsPowerbi';

class ExampleTabs extends React.Component<PropsFromRedux> {
  handleChangeTab = (_event: React.ChangeEvent<object>, tabName: string) => {
    this.props.navigateToTab(tabName);
  };

  render() {
    const tabClasses = {
      root: styles.tab,
    };

    return (
      <Fragment>
        <SideBar />
        <AppBar />
        <TopBar />
        <AppContainer appBarOffset topBarOffset sideBarOffset>
          <ContentContainer>
            <Grid container>
              <Grid size={{ xs: 12 }}>
                <p>
                  This is the tabbed example page. The following data is fetched
                  for all tabs:
                </p>
                {this.props.engineExampleDataLoading ? (
                  <CircularProgress />
                ) : (
                  <pre>
                    {JSON.stringify(this.props.engineExampleData, null, '\t')}
                  </pre>
                )}

                <Tabs
                  indicatorColor="primary"
                  value={this.props.currentTab}
                  onChange={this.handleChangeTab}
                >
                  <Tab
                    label="Categories"
                    value="categories"
                    classes={tabClasses}
                  />
                  <Tab label="Tasks" value="tasks" classes={tabClasses} />
                </Tabs>
                {
                  {
                    categories: <CategoriesTab />,
                    tasks: <TasksTab />,
                    analytics: <Analytics />,
                    files: <Files />,
                    exports: <Exports />,
                    'contact-analytics': <ContactAnalyticsTab />,
                    'contact-analytics-powerbi': <ContactAnalyticsPowerbi />,
                    'processing-status': <ProcessingStatus />,
                  }[this.props.currentTab]
                }
              </Grid>
            </Grid>
          </ContentContainer>
        </AppContainer>
      </Fragment>
    );
  }
}
const mapStateToProps = (state: any) => ({
  currentTab: selectCurrentRoutePayload(state).tab,
  engineExampleData: selectEngine(
    state,
    '18502331-1a02-4261-8350-9f36bbabf9cf'
  ),
  engineExampleDataLoading: engineIsLoading(
    state,
    '18502331-1a02-4261-8350-9f36bbabf9cf'
  ),
});

const mapDispatchToProps = (_dispatch: any) => ({
  navigateToTab: (tabName: string) => ROUTE_EXAMPLE_TABS({ tab: tabName }),
});

const connector = connect(mapStateToProps, mapDispatchToProps);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(ExampleTabs);
