import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    name: "actionsTakenDuringStop"
                    field: "actionsTakenDuringStop"
                    operator: "term"
                    limit: 10000
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'actionsTakenDuringStopAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stop Actions Taken',
  filterTextType: 'Stop Action Taken by Type',
  filterType: 'actionsTakenDuringStop',
  filterTerms: {
    VEHICLE: [
      'Person removed from vehicle by order',
      'Person removed from vehicle by physical contact',
      'Field sobriety test conducted',
      'Vehicle impounded',
    ],
    DETENTION: [
      'Curbside detention',
      'Handcuffed or flex cuffed',
      'Patrol car detention',
    ],
    WEAPON: [
      'Firearm pointed at person',
      'Firearm discharged or used',
      'Electronic control device used',
      'Impact projectile discharged or used (EG: blunt impact projectile, rubber bullets, or bean bags)',
      'Baton or other impact weapon used',
      'Chemical spray used (EG: pepper spray, mace, tear gas, or other chemical irritants)',
    ],
    CANINE: [
      'Canine removed from vehicle or used to search',
      'Canine bit or held person',
    ],
    SEARCH: [
      'Search of person was conducted',
      'Search of property was conducted',
      'Asked for consent to search person',
      'Asked for consent to search property',
      'Property was seized',
    ],
    OTHER: ['Other Physical or Vehicle contact', 'Person photographed'],
    K12: ['Admission or written statement obtained from student'],
    // 'No Action Taken': [''] TODO FIX THIS - NO DATA
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = data.actionsTakenDuringStopAggregation.map((dateAgg) => {
      return {
        date: dateAgg.key_as_string,
        VEHICLE: 0,
        DETENTION: 0,
        WEAPON: 0,
        CANINE: 0,
        SEARCH: 0,
        OTHER: 0,
        K12: 0,
        'No Action Taken': 0,
        ...dateAgg.actionsTakenDuringStop.buckets.reduce(
          (acc: { [key: string]: number }, b) => {
            if (
              [
                'Person removed from vehicle by order',
                'Person removed from vehicle by physical contact',
                'Field sobriety test conducted',
                'Vehicle impounded',
              ].includes(b.key)
            ) {
              acc['VEHICLE'] = acc['VEHICLE']
                ? acc['VEHICLE'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Curbside detention',
                'Handcuffed or flex cuffed',
                'Patrol car detention',
              ].includes(b.key)
            ) {
              acc['DETENTION'] = acc['DETENTION']
                ? acc['DETENTION'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Firearm pointed at person',
                'Firearm discharged or used',
                'Electronic control device used',
                'Impact projectile discharged or used (EG: blunt impact projectile, rubber bullets, or bean bags)',
                'Baton or other impact weapon used',
                'Chemical spray used (EG: pepper spray, mace, tear gas, or other chemical irritants)',
              ].includes(b.key)
            ) {
              acc['WEAPON'] = acc['WEAPON']
                ? acc['WEAPON'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Canine removed from vehicle or used to search',
                'Canine bit or held person',
              ].includes(b.key)
            ) {
              acc['CANINE'] = acc['CANINE']
                ? acc['CANINE'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Asked for consent to search person',
                'Property was seized',
                'Search of person was conducted',
                'Asked for consent to search property',
                'Search of property was conducted',
                'Property was seized',
              ].includes(b.key)
            ) {
              acc['SEARCH'] = acc['SEARCH']
                ? acc['SEARCH'] + b.doc_count
                : b.doc_count;
            }
            if (
              ['Admission or written statement obtained from student'].includes(
                b.key
              )
            ) {
              acc['K12'] = acc['K12'] ? acc['K12'] + b.doc_count : b.doc_count;
            }
            if (
              [
                'Other Physical or Vehicle contact',
                'Person photographed',
              ].includes(b.key)
            ) {
              acc['OTHER'] = acc['OTHER']
                ? acc['OTHER'] + b.doc_count
                : b.doc_count;
            }
            return acc;
          },
          {} // this reduce need to return an object to update default values
        ),
      };
    });

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    // dateAxis.renderer.minGridDistance = 50;
    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'VEHICLE';
    series.dataFields.dateX = 'date';
    series.name = 'Vehicle';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series2 = chart.series.push(new am4charts.LineSeries());
    series2.strokeWidth = config.lineWidth;
    series2.dataFields.valueY = 'DETENTION';
    series2.dataFields.dateX = 'date';
    series2.name = 'Detention';
    series2.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series3 = chart.series.push(new am4charts.LineSeries());
    series3.strokeWidth = config.lineWidth;
    series3.dataFields.valueY = 'WEAPON';
    series3.dataFields.dateX = 'date';
    series3.name = 'Weapon';
    series3.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series4 = chart.series.push(new am4charts.LineSeries());
    series4.strokeWidth = config.lineWidth;
    series4.dataFields.valueY = 'CANINE';
    series4.dataFields.dateX = 'date';
    series4.name = 'Canine';
    series4.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series6 = chart.series.push(new am4charts.LineSeries());
    series6.strokeWidth = config.lineWidth;
    series6.dataFields.valueY = 'SEARCH';
    series6.dataFields.dateX = 'date';
    series6.name = 'Search';
    series6.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series8 = chart.series.push(new am4charts.LineSeries());
    series8.strokeWidth = config.lineWidth;
    series8.dataFields.valueY = 'K12';
    series8.dataFields.dateX = 'date';
    series8.name = 'K12';
    series8.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series9 = chart.series.push(new am4charts.LineSeries());
    series9.strokeWidth = config.lineWidth;
    series9.dataFields.valueY = 'OTHER';
    series9.dataFields.dateX = 'date';
    series9.name = 'Other';
    series9.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  actionsTakenDuringStopAggregation: {
    key: number;
    key_as_string: string;
    doc_count: number;
    actionsTakenDuringStop: {
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
      buckets: { key: string; doc_count: number }[];
    };
  }[];
}
