------------------------------------------------
---------- DO NOT TRUST THIS DOCUMENT ----------
-------------- MAY BE OUT OF DATE --------------
------------------------------------------------

USE master


CREATE USER templateadmin WITH PASSWORD = 'MBBKWfneGXY48uX';
ALTER ROLE [db_datareader] ADD MEMBER templateadmin; 


UPDATE [powerbi-contact].dbo.ContactStopData
SET orgId=76 where orgId=79


select * from  [powerbi-contact].dbo.ContactStopData where nonBinaryOfficer is null


select * from  [powerbi-contact].dbo.ContactStopData  ORDER BY id DESC

 ---------- Test find how many columns have data
declare @table_name as varchar(128) = 'ContactStopData'; 
declare @column_name as varchar(128);
declare @sql as nvarchar(4000);
declare @count_column int;
declare @tmp_table_result as table (
    id int identity(1,1),
    table_object_id bigint,
    column_name varchar(128),
    non_null_records int
    );
declare csr cursor for 
select name
from sys.columns where object_id = OBJECT_ID(@table_name);
open csr;
fetch next from csr into @column_name;
WHILE @@FETCH_STATUS = 0 
BEGIN
set @sql = 'select @cnt_col = count(' + @column_name + ') from ' + @table_name;
print @sql
execute sp_executesql @sql, N'@cnt_col int output', @cnt_col=@count_column OUTPUT;

insert into @tmp_table_result (table_object_id, column_name, non_null_records)
values (
    object_id(@table_name),
    @column_name,
    @count_column
    );
fetch next from csr into @column_name;
END
close csr;
deallocate csr;
select * 
from @tmp_table_result
 ----------


select count(distinct recordId)  from  [powerbi-contact].dbo.ContactStopData

select * from  [powerbi-contact].dbo.TokenConfiguration

select * from  [powerbi-contact].dbo.PbixTemplates

--DROP TABLE TokenConfiguration;

--DROP TABLE PbixTemplates;

--DELETE FROM [powerbi-contact].dbo.PbixTemplates WHERE id=5;


CREATE TABLE TokenConfiguration (
	id int IDENTITY(1,1) PRIMARY KEY,
	orgId smallint UNIQUE,
	reportId varchar(64),
	datasetId varchar(64),
	profileId varchar(64),
	workspaceId varchar(64),
	embedUrl varchar(1024),
	lifetimeInMinutes smallint,
	pbixFilePath varchar(512),
)

CREATE TABLE PbixTemplates (
  id int IDENTITY(1,1) PRIMARY KEY,
  name varchar(512),
  description varchar(2048),
  pbixFileName varchar(512) UNIQUE,
  created DATETIME NOT NULL,
  modified DATETIME NOT NULL
)


SELECT id, name, batch, migration_time
FROM [powerbi-contact].dbo.knex_migrations;


--DELETE FROM [powerbi-contact].dbo.knex_migrations WHERE id=17;

--DELETE FROM [powerbi-contact].dbo.CustomQuestionDefinitions WHERE id>0;

--DELETE FROM [powerbi-contact].dbo.CustomQuestionAnswers WHERE id>0;

--DELETE FROM [powerbi-contact].dbo.ContactStopData WHERE id>0;


select * from [glc-powerbi-contact].dbo.CustomQuestionDefinitions

select * from [glc-powerbi-contact].dbo.CustomQuestionAnswers

--- DROP TABLE CustomQuestionAnswers;


CREATE TABLE CustomQuestionAnswers (
  	id int IDENTITY(1,1) PRIMARY KEY,
  	orgId smallint,
  	recordId varchar(64),
    personNumber tinyint,
    isStopQuestion: bit,
	questionDefinitionId int,
	answer varchar(MAX),
	questionKey varchar(2028),
	FOREIGN KEY (questionDefinitionId) REFERENCES CustomQuestionDefinitions(id)
)

---- DROP TABLE CustomQuestionDefinitions 

CREATE TABLE CustomQuestionDefinitions (
  	id int IDENTITY(1,1) PRIMARY KEY,
  	contactId varchar(64),
  	orgId smallint,
  	component varchar(128),
	title varchar(2048),
	resultPath varchar(2048),
	options varchar(MAX),
	isMultiSelect bit,
	required bit,
	disabled bit,
	disabledDate DATETIME,
)

---ALTER TABLE ContactStopData ADD 
--  	leaRecordId varchar(512)
--  	
--  	ALTER TABLE ContactStopData DROP COLUMN leaRecordId;

	
CREATE TABLE ContactStopData (
  	id int IDENTITY(1,1) PRIMARY KEY,
  	recordId varchar(64),
    orgId smallint,
    basisForPropertySeizure varchar(512),
    officerYearsOfExperience tinyint,
    basisForSearchNarrative varchar(512),
    gender varchar(256),
    ethnicity varchar(2048),
    city varchar(256),
    disability varchar(512),
    timeOfStop time,
    reasonsForStop varchar(512),
    personNumber tinyint,
    k12_school bit,
    basisForSearch varchar(512),
    custodialArrestOffCode varchar(512),
    citationOffCode varchar(512),
    school varchar(512),
    warningOffCode varchar(512),
    responseToServiceCall bit,
	officerTypeOfAssignment varchar(512),
	inFieldCiteAndReleaseCode varchar(512),
	trafficViolationOffenseCode varchar(512),
	trafficViolation varchar(512),
	ecSubdivision varchar(512),
	durationOfStop smallint,
	reasonableSuspicion varchar(1024),
	suspicionOffenseCode varchar(512),
  	limitedEnglish bit,
  	disciplineUnderEc varchar(512),
  	contrabandOrEvidence varchar(512),
  	officerOtherAssignmentType varchar(512),
  	resultOfStop varchar(1024),
  	actionsTakenDuringStop varchar(2048),
  	datetimeOfStop datetime,
  	stopForAStudent bit,
  	typeOfPropertySeized varchar(512),
  	location varchar(256),
  	genderNonconforming bit,
  	reasonForStopNarrative varchar(512),
  	age tinyint,
  	ethnicityExclusive varchar(2048),
  	lgbt bit
);



