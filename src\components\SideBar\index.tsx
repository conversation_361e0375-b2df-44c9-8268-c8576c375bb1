import React from 'react';
import { ConnectedProps, connect } from 'react-redux';
import Item from './Item';
import Grid from '@mui/material/Grid2';
import {
  getFolders,
  getRootFolderId,
  foldersFailedToFetch,
  getOpenFolderIds,
  getWidthFolder,
} from 'state/modules/folders';
class SideBar extends React.Component<PropsFromRedux> {
  render() {
    const { folders, rootFolderId, error, openFolderIds, widthFolder } =
      this.props;
    if (error || !rootFolderId) {
      return <div>Error: {error}</div>;
    }
    return (
      <React.Fragment>
        <Grid size={6} style={{ width: `${100 / widthFolder}%` }}>
          <Item
            folder={folders[rootFolderId]!} // safe due to folders = makeFolderRoot with rootFolderId is the Key
            allFolders={folders}
            type={'root'}
            selectedFolderIds={openFolderIds}
          />
        </Grid>
        {openFolderIds.map((item) => (
          <Grid
            size={6}
            key={item.id}
            style={{ width: `${100 / widthFolder}%` }}
          >
            <Item
              folder={folders[item.id]!} // safe due to folders = makeFolderRoot with rootFolderId is the Key
              allFolders={folders}
              selectedFolderIds={openFolderIds}
            />
          </Grid>
        ))}
      </React.Fragment>
    );
  }
}

const mapState = (state: any) => ({
  folders: getFolders(state),
  rootFolderId: getRootFolderId(state),
  error: foldersFailedToFetch(state),
  openFolderIds: getOpenFolderIds(state),
  widthFolder: getWidthFolder(state),
});

const mapDispatch = {};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(SideBar);
