import { Folder } from './FolderItem';
import ArrowRight from '../../../resources/images/baseline_arrow_right_black_18dp.png';
import ArrowDown from '../../../resources/images/baseline_arrow_drop_down_black_18dp.png';
import { render, fireEvent } from '@testing-library/react';

describe('Move folder', () => {
  const props = {
    selectedFolderId: '0a8a0348-7ddf-4dd8-9238-8c9e6b8a21fb',
    handleListItemClick: jest.fn(),
    setStatusExpanded: jest.fn(),
    setSelectedFolderId: jest.fn(),
    fetchMoveSubFolder: jest.fn(),
    setStatusMoveExpanded: jest.fn(),
    setSelectedMoveFolderId: jest.fn(),
    isShowConfirmModal: false,
    inSidebar: false,
    folder: {
      id: '4f2b2457-d86e-47fe-ba75-67d4f51c41c1',
      name: 'My Case<PERSON>',
      treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
      expanded: true,
      fetchingSubFolders: false,
      subfolders: ['81f28961-4fef-497f-b98f-23fbaa3b460'],
      count: 1,
      root: true,
      parentId: '',
      level: 1,
      description: '',
      childFolders: {
        count: 1,
        records: [],
      },
      fetchingMoveSubFolders: false,
      parent: {
        id: '1',
      },
      folderPath: [
        {
          id: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
          name: 'My Cases',
          treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
        },
      ],
    },
    fetchSubFolder: jest.fn(),
    allFolders: {
      '4f2b2457-d86e-47fe-ba75-67d4f51c41c1': {
        id: '4f2b2457-d86e-47fe-ba75-67d4f51c41c1',
        name: 'My Cases',
        treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
        expanded: true,
        fetchingSubFolders: false,
        subfolders: ['81f28961-4fef-497f-b98f-23fbaa3b460'],
        count: 1,
        root: true,
        parentId: '',
        level: 1,
        description: '',
        childFolders: {
          count: 1,
          records: [],
        },
        fetchingMoveSubFolders: false,
        parent: {
          id: '1',
        },
        folderPath: [
          {
            id: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
            name: 'My Cases',
            treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
          },
        ],
      },
    },
    handleShowMoveFolder: jest.fn(),
    handleShowCreateFolder: jest.fn(),
    handleShowConfirmModal: jest.fn(),
    fetchMoveFolder: jest.fn(),
    rootFolderId: '437f72f3-bfdd-44fa-9973-bbb1a6019ed1',
    type: 'type',
    setOpenFolderUpload: jest.fn(),
  };

  it('renders 2 List', () => {
    const { getAllByTestId } = render(<Folder {...props} />);
    expect(getAllByTestId('list')).toHaveLength(2);
  });
  it('renders a ListItem', () => {
    const { getByTestId } = render(<Folder {...props} />);
    expect(getByTestId('list-item')).toBeInTheDocument();
  });
  it('renders a ListItemIcon', () => {
    const { getByTestId } = render(<Folder {...props} />);
    expect(getByTestId('list-item-icon')).toBeInTheDocument();
  });
  it('renders a ListItemText', () => {
    const { getByTestId } = render(<Folder {...props} />);
    expect(getByTestId('list-item-text')).toBeInTheDocument();
  });
  it('renders a Tooltip', async () => {
    const { getByTestId, findByText } = render(<Folder {...props} />);
    fireEvent.mouseOver(getByTestId('list-item-text'));
    expect(await findByText('My Cases')).toBeInTheDocument();
  });
  it('renders a ArrowDown image when expanded === true', () => {
    const { getByTestId } = render(<Folder {...props} />);
    expect(getByTestId('arrow-down-img')).toBeInTheDocument();
    expect(getByTestId('arrow-down-img')).toHaveAttribute('src', ArrowDown);
  });
  it('renders a ArrowRight image when expanded === false', () => {
    const newProps = {
      ...props,
      folder: {
        ...props.folder,
        expanded: false,
      },
    };
    const { getByTestId, queryByTestId } = render(<Folder {...newProps} />);
    expect(getByTestId('arrow-right-img')).toBeInTheDocument();
    expect(queryByTestId('arrow-down-img')).not.toBeInTheDocument();
    expect(getByTestId('arrow-right-img')).toHaveAttribute('src', ArrowRight);
  });
  it('renders a WorkIcon when root === true', () => {
    const { getByTestId } = render(<Folder {...props} />);
    expect(getByTestId('work-icon')).toBeInTheDocument();
  });
  it('renders a FolderOpenIcon when expanded === true || selectedFolderId === folder.id', () => {
    const newProps = {
      ...props,
      selectedFolderId: '0a8a0348-7ddf-4dd8-9238-8c9e6b8a21fb',
      folder: {
        ...props.folder,
        id: '0a8a0348-7ddf-4dd8-9238-8c9e6b8a21fb',
        root: false,
      },
    };
    const { getByTestId } = render(<Folder {...newProps} />);
    expect(getByTestId('folder-open-icon')).toBeInTheDocument();
  });
  it('renders a FolderIcon when expanded === false || selectedFolderId !== folder.id', () => {
    const newProps = {
      ...props,
      selectedFolderId: '0a8a0348-7ddf-4dd8-9238-8c9e6b8a21fb',
      folder: {
        ...props.folder,
        id: 'a8387523-40c0-4e36-8c55-7900be9451f7',
        root: false,
        expanded: false,
      },
    };
    const { getByTestId } = render(<Folder {...newProps} />);
    expect(getByTestId('folder-icon')).toBeInTheDocument();
  });
  it('does not render a Menu', () => {
    const { queryByTestId } = render(<Folder {...props} />);
    expect(queryByTestId('menu')).not.toBeInTheDocument();
  });

  it('renders a Menu', () => {
    const newProps = {
      ...props,
      inSidebar: true,
      folder: {
        ...props.folder,
        root: false,
      },
    };
    const { getByTestId, getAllByTestId } = render(<Folder {...newProps} />);
    fireEvent.click(getByTestId('icon-button'));

    expect(getByTestId('menu')).toBeInTheDocument();
    expect(getAllByTestId('menu-item')).toHaveLength(2);
  });
});
