import { Knex } from 'knex';
import {
  tables,
  TokenConfigurationRow,
  TokenConfigurationRows,
  ContactStopDataRow,
  PbixTemplateRow,
  CustomQuestionAnswersRow,
  CustomQuestionDefinitionsRow,
} from './definitions';

const createQueries = ({ db }: { db: Promise<Knex> }) => ({
  upsertTokenConfig: async (row: TokenConfigurationRow) => {
    const client = await db;
    await client.transaction(async (trx) => {
      try {
        await trx(tables.TokenConfiguration).insert(row);
      } catch (err) {
        if (!err.message.includes('duplicate key')) {
          throw err;
        }
        await trx(tables.TokenConfiguration)
          .update(row)
          .where({ orgId: row.orgId });
      }
    });
  },
  getTokenConfig: (orgId: number): Promise<TokenConfigurationRow> =>
    db.then((client) =>
      client.table(tables.TokenConfiguration).where('orgId', orgId).first()
    ),
  getTokenConfigs: (orgIds: number[]): Promise<TokenConfigurationRows> =>
    db.then((client) =>
      client.table(tables.TokenConfiguration).whereIn('orgId', orgIds)
    ),
  insertContactDataRows: ({
    stopDataRows,
    customQuestionStopDataRows,
  }: {
    stopDataRows: ContactStopDataRow[];
    customQuestionStopDataRows: CustomQuestionAnswersRow[];
  }) =>
    db.then((client) =>
      client.transaction(async (trx) => {
        if (stopDataRows.length > 0) {
          await trx(tables.ContactStopData).insert(stopDataRows);
        }
        if (customQuestionStopDataRows.length > 0) {
          await trx(tables.CustomQuestionAnswers).insert(
            customQuestionStopDataRows
          );
        }
      })
    ),
  insertQuestionDefinitionsRows: (rows: CustomQuestionDefinitionsRow[]) =>
    db.then((client) =>
      client.table(tables.CustomQuestionDefinitions).insert(rows)
    ),
  findQuestionDefinition: ({
    resultPath,
    orgId,
  }: {
    resultPath: string;
    orgId: number;
  }) =>
    db.then((client) =>
      client
        .select()
        .table(tables.CustomQuestionDefinitions)
        .where({ resultPath, orgId })
        .first()
    ),
  getAllTemplates: (): Promise<PbixTemplateRow[]> =>
    db.then((client) => client.select().table(tables.PbixTemplates)),
  updateTemplate: (row: PbixTemplateRow) =>
    db.then((client) =>
      client
        .where('pbixFileName', '=', row.pbixFileName)
        .table(tables.PbixTemplates)
        .update(row)
    ),
  insertTemplate: (row: PbixTemplateRow) =>
    db.then((client) => client.table(tables.PbixTemplates).insert(row)),
  getTemplateRecord: (templatePbixFileName: string) =>
    db.then((client) =>
      client
        .table(tables.PbixTemplates)
        .where('pbixFileName', templatePbixFileName)
        .first()
    ),
});

export default createQueries;
