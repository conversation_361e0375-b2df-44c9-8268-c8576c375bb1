import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{ field: "ethnicity", operator: "term", limit: 10000 }]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'ethnicityAggregation',
      dataKey: 'ethnicity',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Race or Ethnicity Distribution',
  filterTextType: 'Race or Ethnicity Distribution by Type',
  filterType: 'ethnicity',
  filterTerms: [
    'Asian',
    'White',
    'Black/African American',
    'Hispanic/Latino(a)',
    'Middle Eastern or South Asian',
    'Pacific Islander',
    'Native American',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataObj = {
      Asian: 0,
      White: 0,
      'Black/African American': 0,
      'Hispanic/Latino(a)': 0,
      'Middle Eastern or South Asian': 0,
      'Pacific Islander': 0,
      'Native American': 0,
      ...data.ethnicityAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          acc[b.key] = b.doc_count;
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = Object.entries(dataObj).map((kvp) => {
      const [ag, v] = kvp;
      return { race: ag, count: v };
    });

    // Create axes
    const categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = 'race';
    categoryAxis.title.text = 'Stop Type';

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Number of People';

    // Create series
    const series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = 'count';
    series.dataFields.categoryX = 'race';
    series.name = 'Number of People';
    series.columns.template.tooltipText =
      'Series: {name}\nCategory: {categoryX}\nValue: {valueY}';
    series.columns.template.fill = am4core.color('#104547');

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  ethnicityAggregation: {
    key: string;
    doc_count: number;
  }[];
}
