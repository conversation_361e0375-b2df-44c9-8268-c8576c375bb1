import React from 'react';
import { ConnectedProps, connect } from 'react-redux';
import withStyles from '@mui/styles/withStyles';
import Item from './Item';
import Grid from '@mui/material/Grid2';
import {
  getFolders,
  getRootFolderId,
  foldersFailedToFetch,
  getSelectedFolderIds,
} from 'state/modules/folders';

const customStyles = {
  subFolder: {
    marginLeft: 20,
    display: 'block',
    padding: 0,
  },
  folder: {
    padding: 2,
    cursor: 'pointer',
  },
  rootFolder: {
    paddingLeft: 20,
    backgroundColor: 'lightgrey',
  },
  rootFolderActive: {
    paddingLeft: 15,
    borderLeft: '5px solid #2196F3',
  },
  iconShare: {
    color: '#2196F3',
  },
  folderLabel: {
    fontWeight: 500,
  },
};

class FolderTree extends React.Component<PropsFromRedux> {
  render() {
    const { folders, rootFolderId, error, selectedFolderIds } = this.props;
    if (error) {
      return <div>Error: {error}</div>;
    }
    return (
      <React.Fragment>
        <Grid size={{ xs: 2, sm: 2 }}>
          <Item
            folder={folders[rootFolderId]!} // safe due to folders = makeFolderRoot with rootFolderId is the Key
            allFolders={folders}
            // inSidebar inSidebar only exists in FolderItem
            type={'root'}
            selectedFolderIds={selectedFolderIds}
          />
        </Grid>
        {selectedFolderIds.map((item) => (
          <Grid size={{ xs: 2, sm: 2 }} key={item.id}>
            {/* <Paper> */}
            <Item
              folder={folders[item.id]!} // safe due to folders = makeFolderRoot with rootFolderId is the Key
              allFolders={folders}
              // inSidebar inSidebar only exists in FolderItem
              selectedFolderIds={selectedFolderIds}
            />
            {/* </Paper> */}
          </Grid>
        ))}
      </React.Fragment>
    );
  }
}

// interface Props {
//   rootFolderId: string;
//   error: string;
//   selectedFolderId: SelectedFolderId;
// }

const mapStateToProps = (state: any) => ({
  folders: getFolders(state),
  rootFolderId: getRootFolderId(state),
  error: foldersFailedToFetch(state),
  // selectedFolderId: getSelectedFolderId(state),
  selectedFolderIds: getSelectedFolderIds(state),
});

const connector = connect(mapStateToProps, {});

type PropsFromRedux = ConnectedProps<typeof connector>;

export default withStyles(customStyles)(connector(FolderTree));
