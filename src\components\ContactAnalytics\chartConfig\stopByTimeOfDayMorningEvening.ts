import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { DateTime } from 'luxon';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "00:00",
                  lt: "12:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'morningStopAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "12:00",
                  lt: "24:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'eveningStopAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'Morning and Evening Stops',
  filterTextType: 'Stop Time by Type',
  filterType: 'datetimeOfStop',
  filterTerms: {
    'Morning Hours (00:00-12:00)': {
      queryFilter: `{
        field: "timeOfStop",
        operator: "range",
        gte: "00:00",
        lt: "12:00"
      }`,
      storageKey: 'morningStopAggregation',
    },
    'Night Time (12:00-24:00)': {
      queryFilter: `{
        field: "timeOfStop",
        operator: "range",
        gte: "12:00",
        lt: "24:00"
      }`,
      storageKey: 'eveningStopAggregation',
    },
  },

  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataAry =
      data.morningStopAggregation?.map((dateAgg) => {
        return {
          date: dateAgg.key_as_string,
          'Morning Count': dateAgg.doc_count,
          'Evening Count':
            data.eveningStopAggregation?.find(
              (agg) => dateAgg.key_as_string === agg.key_as_string
            )?.doc_count ?? 0,
        };
      }) ?? [];
    const addEveningStops: {
      date: string;
      'Morning Count': number;
      'Evening Count': number;
    }[] = [];
    data.eveningStopAggregation?.forEach((dateAgg) => {
      const morningStopsAtDate = dataAry.find(
        (dataPointObj) => dateAgg.key_as_string === dataPointObj.date
      );
      if (!morningStopsAtDate) {
        addEveningStops.push({
          date: dateAgg.key_as_string,
          'Morning Count': 0,
          'Evening Count': dateAgg.doc_count,
        });
      }
    });
    addEveningStops.forEach((es) => dataAry.push(es));

    chart.data = dataAry.sort(
      (a, b) =>
        DateTime.fromISO(a.date).toUnixInteger() -
        DateTime.fromISO(b.date).toUnixInteger()
    );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    ['Morning Count', 'Evening Count'].forEach((pr) => {
      // Create series
      const series = chart.series.push(new am4charts.LineSeries());
      series.strokeWidth = config.lineWidth;
      series.dataFields.valueY = pr;
      series.dataFields.dateX = 'date';
      series.name = pr;
      series.tooltipText = '{name}: [bold]{valueY}[/]';
    });

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  morningStopAggregation: {
    doc_count: number;
    key_as_string: string;
    key: number;
  }[];
  eveningStopAggregation: {
    doc_count: number;
    key_as_string: string;
    key: number;
  }[];
}
