import React from 'react';
import { AppContainer as LibAppContainer } from '@veritone/glc-react';
import * as sideBarStyles from 'components/SideBar/styles.scss';

const AppContainer = ({ sideBarOffset, ...props }: Props) => (
  <LibAppContainer
    {...props}
    leftOffset={sideBarOffset ? Number(sideBarStyles.sidebarwidth) : 0}
  />
);

interface Props {
  readonly sideBarOffset?: boolean;
  readonly appBarOffset?: boolean;
  readonly topBarOffset?: boolean;
  readonly children?: React.ReactNode;
}

export default AppContainer;
