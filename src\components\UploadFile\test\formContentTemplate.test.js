import React from 'react';
import FormAddContentTemplate from '../formContentTemplate';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('FormAddContentTemplate', () => {
  const props = {
    contentTemplate: {
      id: 'b4d5afc4-311b-4f0a-b905-d6f85672525c',
      name: 'Illuminate Contact Analytics User State',
      schemas: {
        records: [
          {
            id: 'dfde3fcf-74f1-453d-ab81-a33aebf91c39',
            definition: {
              properties: {
                userId: {
                  type: 'string',
                },
              },
            },
            status: 'published',
          },
        ],
      },
      data: {
        userName: '',
      },
    },
    onChange: jest.fn(),
    removeContentTemplate: jest.fn(),
    checkValidateTemplate: false,
  };

  it('renders a title with content Illuminate Contact Analytics User State', () => {
    const { getByText } = render(<FormAddContentTemplate {...props} />);
    expect(
      getByText('Illuminate Contact Analytics User State')
    ).toBeInTheDocument();
  });
  it('renders a remove content template Button', () => {
    const { getByTestId } = render(<FormAddContentTemplate {...props} />);
    expect(getByTestId('remove-content-template-button')).toBeInTheDocument(1);
  });
  it('click add content template Button', () => {
    const { getByTestId } = render(<FormAddContentTemplate {...props} />);
    fireEvent.click(getByTestId('remove-content-template-button'));
    expect(props.removeContentTemplate).toHaveBeenCalled();
  });
  it('renders a title with content Illuminate Contact Analytics User State', () => {
    const { getAllByTestId } = render(<FormAddContentTemplate {...props} />);
    expect(getAllByTestId('content-template-field')).toHaveLength(1);
  });

  it('onchange content template', () => {
    const { getByTestId } = render(<FormAddContentTemplate {...props} />);
    fireEvent.change(getByTestId('content-template-field'), {
      target: { value: 'test' },
    });
    expect(props.onChange).toHaveBeenCalled();
  });
});
