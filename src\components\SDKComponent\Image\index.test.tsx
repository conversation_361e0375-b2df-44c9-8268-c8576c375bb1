import { render, screen } from '@testing-library/react';

import Image from '.';
describe('Image', () => {
  const TEST_IMG =
    'https://pp.userapi.com/c624631/v624631866/38602/miSZV1RlzEY.jpg';
  it('Image should be rendered in a div tag.', () => {
    render(<Image src={TEST_IMG} />);
    const image = screen.getByTestId('image');
    expect(image).toBeInTheDocument();
    expect(image?.style.backgroundImage).toBe(`url(${TEST_IMG})`);
  });
  it('Image Height/Width should have default values if undefined.', () => {
    render(<Image src={TEST_IMG} />);
    const image = screen.getByTestId('image');
    expect(image?.style.height).toBe('100px');
    expect(image?.style.width).toBe('100px');
  });
  it('Image Height/Width can be set to custom sizes.', () => {
    const HEIGHT = '123px',
      WIDTH = '321px';
    render(<Image src={TEST_IMG} height={HEIGHT} width={WIDTH} />);
    const image = screen.getByTestId('image');
    expect(image?.style.height).toBe(HEIGHT);
    expect(image?.style.width).toBe(WIDTH);
  });
  it('Image Border can be enabled', () => {
    render(<Image src={TEST_IMG} border />);
    const image = screen.getByTestId('image');
    expect(image?.style.border).toBe('1px solid #e4e4e4');
  });
});
