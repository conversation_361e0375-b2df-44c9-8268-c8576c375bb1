ARG app_name=illuminate-app

FROM node:22 AS builder
ENV APPLICATION=$app_name

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq libpango1.0-dev && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
ADD . /app
WORKDIR /app

RUN ls -a && \
    chmod +x /app/*.sh && \
    yarn && \
    yarn build

ENV APPLICATION=illuminate-app
RUN echo '### /app/buildinfo.sh...' && /app/buildinfo.sh

FROM node:22 AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
RUN mkdir -p /app/api
WORKDIR /app/api
COPY server/package.json .
COPY server/.yarnrc.yml .
COPY server/yarn.lock .
RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .
RUN yarn
RUN yarn build
COPY server/package.json ./dist
COPY server/yarn.lock ./dist
WORKDIR /app/api/dist
RUN yarn workspaces focus --production

FROM registry.central.aiware.com/fed-nginx:1.27.3 AS final
USER root
RUN if command -v apk > /dev/null; then \
        apk update && \
        apk add --update nginx envsubt && \
        mkdir -p /etc/nginx/conf.d && \
        apk add jq curl bash nodejs npm && \
        apk add pcre libjpeg-turbo libxml2 ncurses curl && \
        apk del tar; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf install -y nginx gettext && \
        mkdir -p /etc/nginx/conf.d && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo libxml2 ncurses curl && \
        dnf remove -y tar && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi

ENV NGINX_PORT=9000

EXPOSE ${NGINX_PORT}/tcp

COPY --from=builder /app/build-manifest.yml /build-manifest.yml
COPY --from=builder /app/build-manifest.yml /opt/build-manifest.yml
COPY --from=builder /app/configWhitelist.json /configWhitelist.json
COPY --from=builder /app/configWhitelist.json /usr/share/nginx/html/aiware-config.json
COPY --from=builder /app/dynamicConfig-index-html-k8s.sh /dynamicConfig-index-html-k8s.sh
COPY --from=builder /app/entrypoint-k8s.sh /entrypoint.sh
COPY --from=builder /app/static /usr/share/nginx/html
COPY --from=builder /app/build-local /usr/share/nginx/html
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /app/nginx.k8s.conf /etc/nginx.conf.template
COPY --from=backend /app/api/dist /api
COPY --from=backend /app/api/apiConfigWhitelist.json /api/apiConfigWhitelist.json

COPY --from=registry.central.aiware.com/aiware-spa:2dd03df0f646a7687b5871542bc71fd328167dbc /usr/share/nginx/config /usr/share/nginx/html/config
COPY --from=registry.central.aiware.com/aiware-spa:2dd03df0f646a7687b5871542bc71fd328167dbc /usr/share/nginx/config /api/config
RUN chmod +x /usr/share/nginx/html/config
RUN chmod +x /api/config
RUN chmod +x /entrypoint.sh
ENTRYPOINT '/entrypoint.sh'
