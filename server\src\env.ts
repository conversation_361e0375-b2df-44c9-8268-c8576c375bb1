import colors from 'colors/safe';
import apiConfig from '../apiConfig.json';

type Env = {
  [index: string]: any;
  nodeEnv: string;
  serviceName: string;
  mssqlHost: string;
  mssqlDatabase: string;
  mssqlUser: string;
  mssqlPw: string;
  mssqlPort: number;
  port: number;
  apiRoot: string;
  graphQLEndpoint: string;
  azureOAuthRoot: string;
  azureAdTenantId: string;
  azureAdTokenEndpoint: string;
  azureAdServicePrincipalObjectId: string;
  azureAdTokenClientId: string;
  azureAdTokenClientSecrt: string;
  azureAdTokenBlobStorageScope: string;
  azureAdTokenPowerBiScope: string;
  powerbiApiRoot: string;
  powerbiApiVersionOrg: string;
  azureBlobStorageApiRoot: string;
  azureBlobStorageContainer: string;
  azureAdSuEmail: string;
  azureAdSuId: string;
};

const env: Env = apiConfig;

function requireFromEnv(key: string) {
  if (typeof env[key] === 'undefined') {
    console.error(`${colors.red('[ERROR] Missing env variable:')} ${key}`);
    return process.exit(1);
  }

  return env[key];
}

const environment: Env = {
  nodeEnv: requireFromEnv('nodeEnv'),
  serviceName: requireFromEnv('serviceName'),
  mssqlHost: requireFromEnv('mssqlHost'),
  mssqlDatabase: requireFromEnv('mssqlDatabase'),
  mssqlUser: requireFromEnv('mssqlUser'),
  mssqlPw: requireFromEnv('mssqlPw'),
  mssqlPort: requireFromEnv('mssqlPort'),
  port: requireFromEnv('port'),
  apiRoot: requireFromEnv('apiRoot'),
  graphQLEndpoint: requireFromEnv('graphQLEndpoint'),
  azureOAuthRoot: requireFromEnv('azureOAuthRoot'),
  azureAdTenantId: requireFromEnv('azureAdTenantId'),
  azureAdTokenEndpoint: requireFromEnv('azureAdTokenEndpoint'),
  azureAdServicePrincipalObjectId: requireFromEnv(
    'azureAdServicePrincipalObjectId'
  ),
  azureAdTokenClientId: requireFromEnv('azureAdTokenClientId'),
  azureAdTokenClientSecrt: requireFromEnv('azureAdTokenClientSecrt'),
  azureAdTokenBlobStorageScope: requireFromEnv('azureAdTokenBlobStorageScope'),
  azureAdTokenPowerBiScope: requireFromEnv('azureAdTokenPowerBiScope'),
  powerbiApiRoot: requireFromEnv('powerbiApiRoot'),
  powerbiApiVersionOrg: requireFromEnv('powerbiApiVersionOrg'),
  azureBlobStorageApiRoot: requireFromEnv('azureBlobStorageApiRoot'),
  azureBlobStorageContainer: requireFromEnv('azureBlobStorageContainer'),
  azureAdSuEmail: requireFromEnv('azureAdSuEmail'),
  azureAdSuId: requireFromEnv('azureAdSuId'),
};

export default environment;
