Feature: Files Tab

  Background:
    Given The user is on the Files tab

  @e2e @tabFiles
  Scenario: Verify files tab elements
    Then The following columns should be visible in the files table:
      | Name          |
      | Type          |
      | ID            |
      | Duration      |
      | Creation Date |
      | Engines Run   |
      |               |
      | Tags          |
    Then Each file row should have the correct data
    Then The pagination control should be visible

  @e2e @tabFiles
  Scenario: Verify total files counter
    Then The total files counter should display "3"

  @e2e @tabFiles
  Scenario Outline: Verify user can sort files by <column>
    When The user sorts the "<column>" column by "<sortedBy>"
    Then The table is sorted by the "<column>" column in "<sortedBy>" order

    Examples:
      | column | sortedBy |
      | Name   | z-a      |
      | Name   | a-z      |
