import { createAction, createReducer } from '@reduxjs/toolkit';
import { EngineSchema } from 'pages/EngineSchemaRequired';

export const CHECK_REQUIRED_ENGINES_SCHEMAS_FAILURE = createAction<
  EngineSchema[]
>('CHECK_REQUIRED_ENGINES_SCHEMAS_FAILURE');

const defaultState = {
  enginesSchemasRequired: [] as EngineSchema[],
};
const reducer = createReducer(defaultState, (builder) => {
  builder.addCase(CHECK_REQUIRED_ENGINES_SCHEMAS_FAILURE, (state, action) => {
    const enginesSchemasRequired = action.payload;
    return {
      ...state,
      enginesSchemasRequired,
    };
  });
});

export const checkRequiredEnginesAndSchemasFailure = (
  payload: EngineSchema[]
) => CHECK_REQUIRED_ENGINES_SCHEMAS_FAILURE(payload);

export default reducer;
export const namespace = 'requiredEnginesAndSchemas';
const local = (state: any) => state[namespace] as typeof defaultState;
export const enginesSchemasRequired = (state: any) =>
  local(state).enginesSchemasRequired || [];
