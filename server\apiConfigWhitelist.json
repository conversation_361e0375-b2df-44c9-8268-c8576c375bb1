["startApi", "serviceName", "nodeEnv", "port", "prettyPrintLogs", "mssqlHost", "mssqlDatabase", "mssqlUser", "mssqlPw", "mssqlSql", "mssqlPort", "apiRoot", "graphQLEndpoint", "azureOAuthRoot", "azureAdTokenEndpoint", "azureAdTokenClientId", "azureAdTokenClientSecrt", "azureAdTenantId", "azureAdTokenBlobStorageScope", "azureAdTokenPowerBiScope", "azureAdServicePrincipalObjectId", "azureAdSuId", "azureAdSuEmail", "azureBlobStorageApiRoot", "azureBlobStorageContainer", "powerbiApiRoot", "powerbiApiVersionOrg"]