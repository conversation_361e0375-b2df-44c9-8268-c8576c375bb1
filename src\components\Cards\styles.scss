@import 'src/variables';

.material-icons.md-light {
  color: rgba(255, 255, 255, 1);
}

.material-icons.md-light.md-inactive {
  color: rgba(255, 255, 255, 0.3);
}

.material-icons.md-36 {
  font-size: 200%;
}

.container {
  width: 100%;
}

.row {
  display: flex;
  flex-wrap: wrap;

  // min-width: 950px;
}

.col-sm {
  // margin: 10px;
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
  position: relative;
  border-radius: 0;
  box-shadow: none;
}

.card-total-file {
  background-color: #638dd7;
  box-shadow: 0 1px 4px 0 $black-1;
  max-width: 25%;
}

.card-total-hours {
  background-color: #46518e;
  box-shadow: 0 1px 4px 0 $black-1;
  max-width: 25%;
}

.card-total-process {
  background-color: $blue-2;
  box-shadow: 0 1px 4px 0 $black-1;
  max-width: 22%;
}

.card-total-process-detail {
  background-color: $blue-2;
  box-shadow: 0 1px 4px 0 $black-1;
  max-width: 28%;
}

.range-button {
  width: 2rem;
  height: 2rem;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.title {
  color: rgba(255, 255, 255, 0.54);
  font-family: Roboto, sans-serif;
  font-size: 14px;
  line-height: 14px;
  padding-left: 16px;
  padding-top: 14px;
}

.content {
  padding-left: 16px;
  line-height: 38px;
  font-size: 34px;
  font-weight: 400;
  color: white;
  font-family: Roboto, sans-serif;
}

.card-title {
  padding-bottom: 0;
}

.card-traffic-from-ads {
  width: 100%;
  color: white;
  height: 128px;
}

.card-total-ads {
  background-color: $blue-1;
  width: 100%;
  color: white;
  height: 100px;
}

.card-total-visits {
  background-color: $green-1;
  width: 100%;
  color: white;
  height: 100px;
}

.card-ad-visits {
  background-color: $orange-1;
  width: 100%;
  color: white;
  height: 100px;
}

.card-new-ad-users {
  background-color: $red-1;
  width: 100%;
  color: white;
  height: 100px;
}

.icon-info {
  color: rgba(255, 255, 255, 0.2);
  font-size: 30px;
  margin-top: -5px;
}

.content-title {
  font-size: 14px;
  line-height: 30px;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.87);
  font-family: Roboto, sans-serif;
}

.content-icon {
  color: #00c75d;
  margin-top: 10px;
}

.card-content {
  display: flex;
}

.card-botton {
  color: white;
  margin-top: 4px;
  margin-left: auto;
}

.card-right {
  border-left: 3px solid #5e677f;
  position: relative;
  height: 63px;
  top: 32px;
}

.content-process {
  display: flex;
  margin-bottom: 18px;
  margin-right: 5px;
  align-items: center;
}

.spinner {
  color: white;
}

.total-process {
  color: white;
  font-size: 14px;
  padding-top: 2px;
  padding-left: 4px;
  font-family: Roboto, sans-serif;
}

.card-content-process {
  position: relative;
  bottom: 46px;
  display: flex;
  left: 0;
  justify-content: space-around;
}
