import AppBar from './index';
import configureStore from 'redux-mock-store';
import * as Redux from 'redux';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
const middlewares: Redux.Middleware[] = [];
const mockStore = configureStore(middlewares);
const initialState = {
  tdosTable: {
    notifications: [],
  },
  history: {
    loadingFetchUriExport: false,
  },
};
const store = mockStore(initialState);
describe('AppBar', () => {
  it('renders correctly AppBar', () => {
    const { asFragment } = render(
      <Provider store={store}>
        <AppBar />
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
