$color-button: #2196f3;
$color-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2);

.btn-select {
  height: 36px;
  width: 90px;
  background-color: $color-button;
  box-shadow: $color-box-shadow;
  margin: 0 10px;

  span {
    color: #fff;
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    text-align: center;
  }

  &:hover {
    background-color: $color-button;
  }
}

.btn-cancel {
  height: 36px;
  width: 90px;
  border-radius: 2px;
  background-color: #e0e0e0;
  box-shadow: $color-box-shadow;
  margin: 0 10px;

  span {
    color: rgba(0, 0, 0, 0.87);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    text-align: center;
  }

  &:hover {
    background-color: #e0e0e0;
  }
}

.form-name {
  margin-top: 38px;
}

.title {
  color: rgba(0, 0, 0, 0.87);
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: 300;
  line-height: 24px;
}

.dialog-content {
  padding: 0;
  box-sizing: border-box;
  border-bottom: 1px solid #dcdcdc;
}

.dialog-action {
  margin: 14px 10px;
}

.icon-close {
  position: absolute;
  right: 0;
  margin-right: 6px;
  padding: 6px;
}

.dialog-header {
  box-sizing: border-box;
  border: 1px solid #dcdcdc;
  padding-left: 8px;
  min-height: 48px;
}

.btn-new-folder {
  box-sizing: border-box;
  height: 30px;
  width: 86px;
  border: 1px solid $color-button;
  border-radius: 2px;
  background-color: #fafafa;
  box-shadow: $color-box-shadow;

  span {
    height: 16px;
    width: 48px;
  }

  svg {
    color: $color-button;
  }

  &:hover {
    background-color: #fafafa;
  }
}

.title-new-folder {
  height: 14px;
  width: 48px;
  color: $color-button;
  font-family: Roboto, sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 14px;
  text-align: center;
  padding: 0 12px;
}

.btn-select-disabled {
  height: 36px;
  width: 90px;
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.26);
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.12);
}
