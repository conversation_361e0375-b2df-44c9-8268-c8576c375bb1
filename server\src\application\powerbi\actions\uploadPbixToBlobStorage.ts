import axios, { AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import { Stream } from 'stream';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export type AzureBlobRequest = Stream;

const uploadPbixToBlobStorage = async (context: Context) => {
  const { log, data, req } = context;
  const { azureBlobStorageApiRoot, azureBlobStorageContainer } = env;

  try {
    await axios.put<void, AxiosResponse<void>, AzureBlobRequest>(
      `${azureBlobStorageApiRoot}/${azureBlobStorageContainer}/PbixTemplates/${req.params.fileName}`,
      req,
      {
        headers: {
          Authorization: data.azBlobBearerToken,
          'x-ms-date': DateTime.now().toHTTP(),
          'Content-Length': req.get('Content-Length'),
          'x-ms-client-request-id': req.metadata.correlationId,
          'x-ms-version': '2022-11-02',
          'x-ms-blob-type': 'BlockBlob',
          'Content-Type': 'application/octet-stream',
        },
      }
    );

    return context;
  } catch (e) {
    log.error('Upload PBIX API failed', e.response.data);
    throw new ApiError(e);
  }
};

export default uploadPbixToBlobStorage;
