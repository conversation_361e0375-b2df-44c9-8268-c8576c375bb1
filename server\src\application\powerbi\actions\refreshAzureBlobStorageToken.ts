import axios from 'axios';
import formurlencoded from 'form-urlencoded';
import { UnauthorizedError } from '../errors';
import { Context } from '../../types';
import { cacheKeys } from '../definitions';
import env from '../../../env';

export const minimumTtlMs = 60 * 1000;

interface AzureAdTokenResponse {
  access_token: string;
  expires_in: number;
}

const refreshAzureBlobStorageToken = async (context: Context) => {
  const { log, cache } = context;
  const {
    azureOAuthRoot,
    azureAdTenantId,
    azureAdTokenEndpoint,
    azureAdTokenClientId,
    azureAdTokenClientSecrt,
    azureAdTokenBlobStorageScope,
  } = env;
  const ttl = cache.getTtl(cacheKeys.azBlobToken);

  log.info(`AD Blob Token expires in ${ttl ? ttl - Date.now() : '<Expired>'}`);

  if (!ttl || Date.now() - ttl < minimumTtlMs) {
    try {
      const { data } = await axios.post<AzureAdTokenResponse>(
        `${azureOAuthRoot}/${azureAdTenantId}/${azureAdTokenEndpoint}`,
        formurlencoded({
          grant_type: 'client_credentials',
          client_id: azureAdTokenClientId,
          client_secret: azureAdTokenClientSecrt,
          scope: azureAdTokenBlobStorageScope,
        })
      );
      cache.set(cacheKeys.azBlobToken, data.access_token, data.expires_in);
      context.data.azBlobBearerToken = `Bearer ${data.access_token}`;

      return context;
    } catch (e) {
      log.error('API Blob Token Validation failed', e);
      throw new UnauthorizedError(e);
    }
  } else {
    const azBlobToken = cache.get(cacheKeys.azBlobToken);
    context.data.azBlobBearerToken = `Bearer ${azBlobToken}`;
  }

  return context;
};

export default refreshAzureBlobStorageToken;
