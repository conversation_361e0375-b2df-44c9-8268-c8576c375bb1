import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
      if (tableExists) {
        const hasAllColumns = await Promise.allSettled([
          knex.schema.hasColumn('ContactStopData', 'raceOfOfficer'),
          knex.schema.hasColumn('ContactStopData', 'sexualOrientation'),
          knex.schema.hasColumn('ContactStopData', 'nonConforming'),
          knex.schema.hasColumn('ContactStopData', 'typeOfStop'),
          knex.schema.hasColumn('ContactStopData', 'unhoused'),
          knex.schema.hasColumn('ContactStopData', 'officerWorksWithNonPrimaryAgency'),
          knex.schema.hasColumn('ContactStopData', 'reasonGivenStoppedPerson'),
          knex.schema.hasColumn('ContactStopData', 'stopDuringWellnessCheck'),
          knex.schema.hasColumn('ContactStopData', 'typeOfAssignmentOfficer'),
          knex.schema.hasColumn('ContactStopData', 'stoppedPassenger'),
          knex.schema.hasColumn('ContactStopData', 'stoppedInsideResidence')
        ]);

        const hasAllColumnsResult = hasAllColumns.every((result) => result.status === 'fulfilled' && result.value);

        if (!hasAllColumnsResult) {
          await knex.schema.raw(`ALTER TABLE ContactStopData ADD 
            raceOfOfficer varchar(2048),
            sexualOrientation varchar(256),
            nonConforming bit,
            typeOfStop varchar(256),
            unhoused bit,
            officerWorksWithNonPrimaryAgency bit,
            reasonGivenStoppedPerson varchar(2048),
            stopDuringWellnessCheck bit,
            typeOfAssignmentOfficer varchar(256),
            stoppedPassenger bit,
            stoppedInsideResidence bit;`
          );

          return true;
        }
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('ContactStopData')
  ])
