import { applyMiddleware, compose, createStore } from 'redux';
import { apiMiddleware } from 'redux-api-middleware';
// imports for router
import { redirect, connectRoutes } from 'redux-first-router';
import routesMap from '../routesMap';

import createSagaMiddleware from 'redux-saga';
import queryString from 'query-string';
import { find } from 'lodash';

// redux sagas, reducers, actions
import sagas from './sagas';
import composeReducers from './rootReducer';
import { boot } from 'modules/app';

import * as Sentry from '@sentry/react';
import { thunk } from 'redux-thunk';

const sentryReduxEnhancer = Sentry.createReduxEnhancer({
  // Optionally pass options listed below
});
const {
  reducer: routerReducer,
  middleware: routerMiddleware,
  enhancer: routerEnhancer,
  initialDispatch,
} = connectRoutes(routesMap, {
  querySerializer: queryString,
  initialDispatch: false,
  onBeforeChange: (dispatch, getState, { action }: any) => {
    const routeDefinition = routesMap[action.type as keyof typeof routesMap];

    if (routeDefinition && routeDefinition.redirects) {
      const matchedRedirect = find(
        routeDefinition.redirects,
        ({ test }) => !!test(getState, action)
      );

      matchedRedirect && dispatch(redirect(matchedRedirect.to));
    }
  },
});

const sagaMiddleware = createSagaMiddleware();

const middlewares = [routerMiddleware, apiMiddleware, sagaMiddleware, thunk];

const isDev = process.env.NODE_ENV === 'development';
if (isDev) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { createLogger } = require('redux-logger');
  middlewares.push(
    createLogger({
      collapsed: true,
    })
  );
}

const composeMiddlewares = applyMiddleware(...middlewares);
const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

export default function configureStore() {
  const store = createStore(
    composeReducers({ location: routerReducer }),
    composeEnhancers(routerEnhancer, composeMiddlewares, sentryReduxEnhancer)
  ) as any;

  sagaMiddleware.run(sagas);
  initialDispatch?.();
  store.dispatch(boot());

  return store;
}
