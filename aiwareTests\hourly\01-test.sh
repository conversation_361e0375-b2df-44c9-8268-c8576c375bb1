#!/bin/sh
echo 'Running hourly illuminate-e2e'
REGION=us-east-1
export CYPRESS_username=`aws secretsmanager --region=us-east-1 get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --output json | jq -r ".SecretString" | jq -r ".e2e_user"`
export CYPRESS_password=`aws secretsmanager --region=us-east-1 get-secret-value --secret-id arn:aws:secretsmanager:us-east-1:************:secret:illuminate_app_test_accounts-az3UZA --output json | jq -r ".SecretString" | jq -r ".e2e_password"`
if [ "$ENVIRONMENT" = "azure-stage" ]; then
  echo 'Start hourly illuminate-e2e on azure-stage'
	docker build . -f e2e.Dockerfile -t illuminate-e2e && docker run -e ENVIRONMENT=azure-stage -e CYPRESS_username -e CYPRESS_password illuminate-e2e
fi
if [ "$ENVIRONMENT" = "stage" ]; then
  echo 'Start hourly illuminate-e2e on stage'
	docker build . -f e2e.Dockerfile -t illuminate-e2e && docker run -e ENVIRONMENT=stage -e CYPRESS_username -e CYPRESS_password illuminate-e2e
fi
