export interface TopicsBucket {
  doc_count: number;
  key: string;
  recordingId: { doc_count: number; recordingId: { value: number } };
}

// export interface WordsPerColourTuple extends Array<string | number> {
//   0: string;
//   1: number;
//   2: string;
// }
export type WordsPerColourTuple = [string, number, string];

export interface ColouredWords {
  [key: string]: string[];
}

export interface Wordcloud {
  aggregations: {
    topic: {
      topic: {
        doc_count: number;
        topic: {
          doc_count_error_upper_bound: number;
          sum_other_doc_count: number;
          buckets: {
            doc_count: number;
            key: string;
            recordingId: {
              doc_count: number;
              recordingId: {
                value: number;
              };
            };
          }[];
        };
      };
    };
  };
}
