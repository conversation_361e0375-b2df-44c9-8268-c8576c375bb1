import { Component, Fragment } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { cloneDeep, isEmpty } from 'lodash';
// import { TopBar as LibTopBar } from '@veritone/glc-react';
import { navigateCurrentTab } from 'state/modules/routing';
import Breadcrumbs from '../SDKComponent/Breadcrumbs';
import cx from 'classnames';
import { Paper } from '@mui/material';
import * as styles from './styles.scss';
import Filters from 'components/Filters';
import Drawer from '@mui/material/Drawer';
import Button from '@mui/material/Button';
import FilterList from '@mui/icons-material/FilterList';
import CircularProgress from '@mui/material/CircularProgress';
import Work from '@mui/icons-material/Work';
import Tooltip from '@mui/material/Tooltip';
import CloudUpload from '@mui/icons-material/CloudUpload';
import {
  getSelectedFolderId,
  getFolders,
  SHOW_FOLDER,
  getFetchingFolderRoot,
  getSelectedFolderIds,
  FETCH_SUB_FOLDERS_REQUEST,
  getisFetchSubFolder,
} from 'state/modules/folders';
import {
  getFilterState,
  getFileNames,
  getIds,
  getEnginesRun,
  getDuration,
} from 'state/modules/filters';
import {
  getIsExportHistoryOpen,
  getSignedUris,
  getModalName,
  CLOSE_EXPORT_MODAL,
} from 'state/modules/history';
import SignedUrisDialog from 'components/History/SignedUrisDialog';
import { openModalUpload } from 'state/modules/uploadFile/actions';
import { getSelectedEntity } from 'state/modules/sunburst';
export const topBarHeight = 52;
export class TopBar extends Component<Props> {
  state = {
    anchorEl: null,
    values: {
      age: '',
      name: '',
    },
    right: false,
  };
  toggleDrawer = (side: string, open: boolean) => () => {
    this.setState({
      [side]: open,
    });
  };
  makeNameFolder = (name: string) => {
    if (name.length > 14) {
      return `${name.substring(0, 14)}...`;
    }
    return name;
  };
  handleShowFolder = () => {
    const { handleShowFolder } = this.props;
    handleShowFolder();
  };

  handleCrumbClick = (item: { id: string; index: string }) => {
    const { fetchSubFolder, navigateCurrentTab } = this.props;
    navigateCurrentTab();
    fetchSubFolder(item.id, false, 'breadcrumbs');
  };

  handleOpenModalUpload = () => {
    const { openModalUpload } = this.props;
    openModalUpload();
  };
  render() {
    const {
      selectedFolderId,
      folders,
      filters,
      fetchingFolderRoot,
      selectedFolderIds,
      isFetchSubFolder,
      isExportModalOpened,
      signedUris,
      modalName,
      onCloseModal,
      searchByFileNames,
      searchByIds,
      searchByEnginesRun,
      searchByDuration,
      elevation = 2,
      leftOffset = 0,
      selectedEntity,
    } = this.props;
    let selectedFolders: { id: string; level?: number; name?: string }[] =
      cloneDeep(selectedFolderIds);

    if (folders[selectedFolderId]) {
      selectedFolders[0]!.name = 'My Cases'; // safe due to after fetch folder success openFolderIds = rootFolder and selectedFolderIds = openFolderIds
    } else {
      selectedFolders = [{ id: '', name: 'Loading...' }];
    }
    const { date, entityType, fileTypes, selectedTopics } = filters;
    const { startDate, endDate } = date;
    const { video = [], audio = [], image = [], doc = [] } = fileTypes;
    const fileSubTypes = [...video, ...audio, ...image, ...doc];
    let dateCount = 0;
    if (startDate || endDate) {
      dateCount = 1;
    }
    let entityCount = 0;
    if (!isEmpty(selectedEntity) || entityType) {
      entityCount = 1;
    }
    let fileNames = 0;
    if (searchByFileNames.length) {
      fileNames = searchByFileNames.length;
    }
    let ids = 0;
    if (searchByIds.length) {
      ids = searchByIds.length;
    }
    let enginesRun = 0;
    if (searchByEnginesRun.length) {
      enginesRun = searchByEnginesRun.length;
    }
    let duration = 0;
    const lte =
      searchByDuration.hoursLte * 3600 +
      searchByDuration.minutesLte * 60 +
      searchByDuration.secondsLte;
    const gt =
      searchByDuration.hoursGt * 3600 +
      searchByDuration.minutesGt * 60 +
      searchByDuration.secondsGt;
    if (lte > 0) {
      duration += 1;
    }
    if (gt > 0) {
      duration += 1;
    }
    const totalFiltersCount =
      dateCount +
      entityCount +
      fileSubTypes.length +
      selectedTopics.length +
      fileNames +
      ids +
      enginesRun +
      duration;

    return (
      <Fragment>
        <Paper
          style={{
            top: '55px',
            height: topBarHeight,
            marginLeft: leftOffset,
            background: '#fafafa',
          }}
          className={styles.container}
          square
          elevation={elevation}
        >
          <div className={cx(styles['group-btn'])}>
            <Button
              data-test="top-bar-select-folder"
              variant="outlined"
              onClick={this.handleShowFolder}
              disabled={fetchingFolderRoot}
              data-testid="topbar-button"
              aria-label="folder-button"
            >
              <Tooltip title={'Select folder'} placement="bottom">
                <Work />
              </Tooltip>
            </Button>
          </div>
          <Drawer
            anchor="right"
            classes={{
              paper: styles.modalRoot,
            }}
            className={styles['drawerRoot']}
            open={this.state.right}
            onClose={this.toggleDrawer('right', false)}
          >
            <Filters onFiltersClose={this.toggleDrawer('right', false)} />
          </Drawer>
          <div className={styles['top-bar-container']}>
            <div
              data-test="top-bar-breadcrumbs"
              className={styles['breadcrumbs_container']}
            >
              <Breadcrumbs
                // className={styles['breadcrumbs']}
                pathList={selectedFolders}
                onCrumbClick={this.handleCrumbClick}
              />
              {isFetchSubFolder && (
                <CircularProgress size={20} variant="indeterminate" />
              )}
            </div>
            <div className={styles['top-bar__icon']}>
              <Button
                className={styles['upload-file']}
                variant="outlined"
                data-test="upload-file"
                onClick={this.handleOpenModalUpload}
                data-testid="topbar-button"
              >
                <CloudUpload style={{ fontSize: '15px' }} /> &nbsp; UPLOAD
              </Button>

              <Button
                className={styles['filters__button']}
                variant="outlined"
                onClick={this.toggleDrawer('right', true)}
                data-test="top-bar-filters-button"
                data-testid="topbar-button"
              >
                {' '}
                <FilterList style={{ fontSize: '15px' }} /> &nbsp; FILTERS (
                {totalFiltersCount})
              </Button>
            </div>
          </div>
        </Paper>
        {isExportModalOpened && (
          <SignedUrisDialog
            isOpened={isExportModalOpened}
            signedUris={signedUris}
            modalName={modalName}
            onClose={onCloseModal}
          />
        )}
      </Fragment>
    );
  }
}

export interface SignedUri {
  id: string;
  name: string;
  uri: string;
  signedUri: string;
  totalBatch: number;
  numberBatch: number;
}
type Props = PropsFromRedux & {
  elevation?: number;
  leftOffset?: number;
  // selectedFolderId: string;
  // filters: {
  //   entityType: string;
  //   date: {
  //     startDate: string;
  //     endDate: string;
  //   };
  //   fileTypes: {
  //     video: string[];
  //     audio: string[];
  //     image: string[];
  //     doc: string[];
  //   };
  //   selectedTopics: string[];
  // };
};

const mapState = (state: any) => ({
  selectedFolderId: getSelectedFolderId(state),
  folders: getFolders(state),
  filters: getFilterState(state),
  selectedEntity: getSelectedEntity(state),
  fetchingFolderRoot: getFetchingFolderRoot(state),
  selectedFolderIds: getSelectedFolderIds(state),
  isFetchSubFolder: getisFetchSubFolder(state),
  isExportModalOpened: getIsExportHistoryOpen(state),
  signedUris: getSignedUris(state),
  modalName: getModalName(state),
  searchByFileNames: getFileNames(state),
  searchByIds: getIds(state),
  searchByEnginesRun: getEnginesRun(state),
  searchByDuration: getDuration(state),
});

const mapDispatch = {
  navigateCurrentTab: navigateCurrentTab,
  handleShowFolder: () => SHOW_FOLDER(),
  fetchSubFolder: (subFolderId: string, expanded: boolean, from: string) =>
    FETCH_SUB_FOLDERS_REQUEST({ folderId: subFolderId, expanded, from }),
  onCloseModal: () => CLOSE_EXPORT_MODAL(),
  openModalUpload: openModalUpload,
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(TopBar);
