import { stringify } from 'csv-stringify/sync';
import { ActionError, NoQuestionDefError } from '../errors';
import { Context } from '../../types';
import { forEachSeries } from 'p-iteration';
import { DateTime } from 'luxon';

interface CustomQuestionData {
  recordId: string;
  personNumber: number;
  title: string;
  isStopQuestion: boolean;
  answer: string | string[];
  questionKey: string;
}

type StopData = Record<string, string | string[] | number | boolean>;

type Stop = {
  data: StopData[];
  customQuestionData: CustomQuestionData[];
};

const isValidDate = (date: unknown): date is string | Date => {
  return (
    (typeof date === 'string' || date instanceof Date) &&
    !isNaN(new Date(date).getTime())
  );
};

const findQuestionDefinitions = async (stopIndex: number, context: Context) => {
  const { req, queries, data } = context;
  const questionDefinitionIds: number[] = [];
  if (
    !req.body.stops[stopIndex]?.customQuestionData ||
    req.body.stops[stopIndex]?.customQuestionData?.length === 0
  ) {
    return questionDefinitionIds;
  }
  await forEachSeries(
    req.body.stops[stopIndex].customQuestionData,
    async (
      questionAnswer: {
        recordId: number;
        personNumber: number;
        answer: string;
        questionKey: string;
      },
      index: number
    ) => {
      const def = await queries.findQuestionDefinition({
        orgId: data.authorizedOrgId,
        resultPath: questionAnswer.questionKey,
      });

      if (!def?.id) {
        throw new NoQuestionDefError(
          `Question ${index} has no question definition for orgId: ${data.authorizedOrgId} and questionKey: ${questionAnswer.questionKey}`
        );
      }

      questionDefinitionIds.push(def.id);
    }
  );

  return questionDefinitionIds;
};

const processStopData = (dataRows: Stop['data'], context: Context) => {
  const {
    data: { authorizedOrgId },
  } = context;

  return dataRows.map((dataRow) => {
    dataRow.orgId = authorizedOrgId;
    const dataKeys = Object.keys(dataRow);

    return dataKeys.reduce<StopData>((acc, key) => {
      if (Array.isArray(dataRow[key])) {
        acc[key] = stringify([dataRow[key]]).replace('\n', '');
      } else {
        acc[key] = dataRow[key];
      }
      return acc;
    }, {});
  });
};

const processCustomQuestionStopData = (
  dataRows: Stop['customQuestionData'],
  questionDefinitionIds: number[],
  context: Context
) => {
  const { data } = context;
  return (
    dataRows?.map((row, index) => ({
      ...row,
      answer: Array.isArray(row?.answer)
        ? stringify([row.answer]).replace('\n', '')
        : (row?.answer?.toString() ?? ''),
      questionDefinitionId: questionDefinitionIds[index],
      orgId: data.authorizedOrgId,
    })) ?? []
  );
};

const pushContactDataRows = async (context: Context) => {
  const { log, req, queries, data } = context;
  try {
    data.errors = [];
    data.success = 0;
    await forEachSeries(
      req.body.stops,
      async ({ data: stopData, customQuestionData }: Stop, index: number) => {
        try {
          const questionDefinitionIds = await findQuestionDefinitions(
            index,
            context
          );
          const dateTimeConvertedStopData = stopData.map((dataRow) => {
            if (isValidDate(dataRow.datetimeOfStop)) {
              return {
                ...dataRow,
                dateTimeOfStopPST: DateTime.fromISO(dataRow.datetimeOfStop, {
                  zone: 'utc',
                })
                  .setZone('America/Los_Angeles')
                  .toFormat('yyyy-MM-dd HH:mm:ss.SSS ZZ'),
              };
            }
            return dataRow;
          });

          const stopDataRows = processStopData(
            dateTimeConvertedStopData,
            context
          );
          const customQuestionStopDataRows = processCustomQuestionStopData(
            customQuestionData,
            questionDefinitionIds,
            context
          );
          await queries.insertContactDataRows({
            stopDataRows,
            customQuestionStopDataRows,
          });
          data.success++;
        } catch (e) {
          log.error('pushContactDataRows insert failed', e);
          data.errors.push({ index, message: e.message });
        }
      }
    );
    if (data.errors.length > 0) {
      context.returnCode = 207;

      if (data.success === 0) {
        context.returnCode = 422;
      }
    }
  } catch (e) {
    log.error('pushContactDataRows failed', e);
    throw new ActionError(e);
  }
};

export default pushContactDataRows;
