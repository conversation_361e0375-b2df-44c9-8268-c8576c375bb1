import { all, fork, put, select } from 'typed-redux-saga/macro';
import { selectCurrentRoutePayload } from 'state/modules/routing';
import {
  fetchEngine,
  fetchEngineCategories,
} from 'state/modules/engines-example';

interface TabLoader {
  categories: any[];
  tasks: any[];
}

function* loadRouteData() {
  const { tab } = yield* select(selectCurrentRoutePayload);
  const tabLoader = {
    categories: loadCategoriesTabData,
    tasks: loadTasksTabData,
  }[tab as keyof TabLoader];

  yield* all([loadSharedData(), tabLoader && tabLoader()]);
}

function* loadSharedData() {
  yield* put(fetchEngine('********-1a02-4261-8350-9f36bbabf9cf'));
}

function* loadCategoriesTabData() {
  yield* put(fetchEngineCategories());
}

function* loadTasksTabData() {
  // yield put(fetchEngineTasks());
}

export function* loadExampleTabsPage() {
  yield* all([fork(loadRouteData)]);
}
