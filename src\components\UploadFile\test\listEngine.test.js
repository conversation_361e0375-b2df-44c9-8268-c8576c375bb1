import React from 'react';
import ListEngine from '../listEngine';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('ListEngine', () => {
  const props = {
    title: 'Transcription',
    des: 'Converts speech audio to text.',
    icon: 'icon-transcription',
    categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    dagTemplates: [
      {
        cognitiveCategoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        description: 'English (Global)',
        id: '02b4ee91-2d35-448d-a9f1-043b243fafd5',
        name: 'Transcription - English (Global)',
        tags: ['illuminate-simple-workflow'],
      },
      {
        cognitiveCategoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
        description: 'English Transcription for Video/Audio',
        id: '70883d62-7dc3-4da8-90dc-34a3cfe40446',
        name: 'illuminate_transcription_1',
        tags: ['illuminate-simple-workflow'],
      },
    ],
    handleClickDagTemplate: jest.fn(),
    dagTemplateByCategorySelected: {
      cognitiveCategoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
      description: 'English (Global)',
      id: '02b4ee91-2d35-448d-a9f1-043b243fafd5',
      name: 'Transcription - English (Global)',
      tags: ['illuminate-simple-workflow'],
    },
    handleRunJobTemplate: jest.fn(),
    unavailablEngineCategory: false,
  };

  it('renders a Card component', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    expect(getByTestId('card-list')).toBeInTheDocument();
  });
  it('renders a CardContent component', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    expect(getByTestId('card-content')).toBeInTheDocument();
  });
  it('renders a engine icon', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    expect(getByTestId('engine-icon')).toBeInTheDocument();
  });
  it('renders a title with content Transcription', () => {
    const { getByText } = render(<ListEngine {...props} />);
    expect(getByText('Transcription')).toBeInTheDocument();
  });
  it('renders a description with content Converts speech audio to text.', () => {
    const { getByText } = render(<ListEngine {...props} />);
    expect(getByText('Converts speech audio to text.')).toBeInTheDocument();
  });
  it('renders a dagTemplate Button', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    expect(getByTestId('dag-templates-button')).toBeInTheDocument();
  });
  it('renders 2 MenuItem component', () => {
    const { getAllByTestId, getByTestId } = render(<ListEngine {...props} />);
    fireEvent.click(getByTestId('dag-templates-button'));
    expect(getAllByTestId('dag-template-menu-item')).toHaveLength(
      props.dagTemplates.length
    );
  });
  it('click dag templstes button', () => {
    const { getByTestId, getByText } = render(<ListEngine {...props} />);
    fireEvent.click(getByTestId('dag-templates-button'));
    fireEvent.click(getByText('English Transcription for Video/Audio'));
    expect(props.handleClickDagTemplate).toHaveBeenCalled();
  });
  it('renders a run job template Button', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    expect(getByTestId('run-job-template')).toBeInTheDocument();
  });
  it('click run job template', () => {
    const { getByTestId } = render(<ListEngine {...props} />);
    fireEvent.click(getByTestId('run-job-template'));
    expect(props.handleRunJobTemplate).toHaveBeenCalled();
  });
});
