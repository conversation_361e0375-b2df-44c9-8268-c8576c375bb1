import { WordCloud } from './index';
import { render } from '@testing-library/react';

describe('Wordcloud', () => {
  const defaultProps = {
    baseHeight: 70,
    sizeCloud: {
      w: 8,
      h: 2,
    },
    colouredWords: {
      '#438D9C': [['Mobile & Wireless', 8, 'Internet & Telecom']],
      '#CC0000': [['science and technology', 8, 'science and technology']],
      '#E8A664': [['Oil & Gas', 7, 'Energy & Utilities']],
      '#9C6043': [
        [
          'arts, culture and entertainment',
          7,
          'arts, culture and entertainment',
        ],
      ],
      '#6CC7E9': [['education', 7, 'education']],
    },
    wordsPerColour: {
      '#438D9C': ['Mobile & Wireless'],
      '#CC0000': ['science and technology'],
      '#E8A664': ['Oil & Gas'],
      '#9C6043': ['arts, culture and entertainment'],
      '#6CC7E9': ['education'],
    },
    selectedTopics: ['education'],
    containerWidth: 710,
    windowSize: 1480,
  };

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('Should have a div element that the topics are written in', () => {
    const { getByTestId } = render(<WordCloud {...(defaultProps as any)} />);
    expect(getByTestId('wordcloud')).toBeInTheDocument();
  });
});
