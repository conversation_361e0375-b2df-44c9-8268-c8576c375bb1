const fs = require('fs');

main();

function main() {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('usage: node ttml2txt.ts [input folder] [output folder]');
    process.exit(1);
  }

  //const inputFolder = '/Users/<USER>/Downloads/testFolder/ttml/';
  //const outputFolder = '/Users/<USER>/Downloads/testFolder/txt/';

  let inputFolder = args[0];
  let outputFolder = args[1];
  if (!inputFolder.endsWith('/')) {
    inputFolder = inputFolder + '/';
  }
  if (!outputFolder.endsWith('/')) {
    outputFolder = outputFolder + '/';
  }
  processFolder(inputFolder, outputFolder);
}

function processFolder(inputFolder, outputFolder) {
  const ttmlFiles = listTtmlFiles(inputFolder);
  ttmlFiles.forEach((file) => {
    const fileSplit = file.split('.');
    const fnWithoutExt = fileSplit[0];
    const inputFile = inputFolder + file;
    const outputFile = outputFolder + fnWithoutExt + '.txt';
    processFile(inputFile, outputFile);
  });
}

function processFile(inputFile, outputFile) {
  const ttml = fs.readFileSync(inputFile, { encoding: 'utf8' });
  const text = ttmlToText(ttml);
  fs.writeFileSync(outputFile, text);
  console.log(`${inputFile} -> ${outputFile}`);
}

function listTtmlFiles(folder) {
  const files = fs.readdirSync(folder);
  const jsonFiles = files.filter((file) => {
    const ext = file.split('.').pop();
    if (ext === 'json') {
      return true;
    }
    return false;
  });
  return jsonFiles;
}

function ttmlToText(ttmlJson) {
  const wordArray = [];
  const obj = JSON.parse(ttmlJson);
  obj.series.forEach((serie) => {
    serie.words.forEach((word) => {
      wordArray.push(word.word);
    });
  });
  // deal with period.
  const plainText = wordArray.join(' ').replace(/ \./g, '.');
  return plainText;
}
