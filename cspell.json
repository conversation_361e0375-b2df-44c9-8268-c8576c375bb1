// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "mkdirp",
    "tsmerge",
    "githubusercontent",
    "streetsidesoftware",
    "vsmarketplacebadge",
    "visualstudio",
    "powerbi",
    "pangea",

    "aiware",
    "veritone",

    "tdoid",
    "tdos",
    "sdos",
    "udrs",
    "jsondata",

    "wordcloud",
    "authtoken",
    "fulltext",
    "pbix",
    "formurlencoded",
    "topbar",
    "enginerunning",
    "timelapse",
    "inkbar",
    "screenfull",
    "picklist",
    "ingestor",
    "tinyint",
    "datapoints",
    "secrt", // TODO: Probably not worth fixing this but why did we save one character?
    "formgroup",

    "PRCS",

    "flac",
    "msvideo",
    "msword",
    "openxmlformats",
    "officedocument",
    "openxmlformats",
    "opendocument",
    "presentationml",
    "smil",
    "spreadsheetml",
    "ttml",
    "quicktime",
    "wordprocessingml",
    "datetimeoffset",
    "wordprocessingml"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": []
}
