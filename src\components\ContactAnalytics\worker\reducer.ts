import { DateTime } from 'luxon';
import shortId from 'shortid';
import {
  SET_CHARTS,
  SET_SETTINGS,
  SET_CHART_DATA,
  SET_HAS_DATA,
  SET_HAS_RENDERED,
  SET_ANALYTICS_DATE_RANGE,
  REMOVE_CHART_DATA,
  ADD_QUERY_PENDING,
  SUBTRACT_QUERY_PENDING,
  SET_HAS_FAILED,
  GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS,
  SET_VARS,
  SET_CHART_SETTINGS,
  SET_CURRENT_REPORT,
  ADD_REPORT,
  REMOVE_REPORT,
  SET_REPORT_NAME,
  SET_LOADING_USER_CONFIG,
} from './actions';
import { createReducer } from '@reduxjs/toolkit';
import { get, cloneDeep } from 'lodash';

export const lowerDateBoundDefault = DateTime.now()
  .setZone('America/Los_Angeles', { keepLocalTime: true })
  .minus({ months: 1 });
export const upperDateBoundDefault = DateTime.now().setZone(
  'America/Los_Angeles',
  { keepLocalTime: true }
);
export const aggregationSizeDefault = '1d';
export const lineWidthDefault = 2.0;
export const useLegendDefault = true;
export const useTitleDefault = true;

const defaultReportState = {
  name: 'Contact Report',
  charts: [],
  chartData: {} as any,
  dateRange: {
    lowerDateBound: lowerDateBoundDefault.toISO(),
    upperDateBound: upperDateBoundDefault.toISO(),
  },
  queriesPending: 0,
  aggregationSize: aggregationSizeDefault,
  lineWidth: lineWidthDefault,
  useLegend: useLegendDefault,
  useTitle: useTitleDefault,
};

export const defaultState = {
  currentReportIndex: 0,
  reports: [{ ...defaultReportState }],
  loadingUserConfig: true,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(SET_CHART_DATA, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { data, containerId, chartDef, totalDataQueries, variables } =
        action.payload;
      const newReports = cloneDeep(reports);
      const currentReport = (newReports[currentReportIndex] ?? {
        ...defaultReportState,
      })!;
      const newChartData = {
        ...currentReport.chartData?.[containerId]?.data,
        ...data,
      };
      currentReport.chartData = {
        ...currentReport.chartData,
        [containerId]: {
          variables,
          chartDef,
          data: newChartData,
          hasRendered: false,
          hasFailed: false,
          hasAllData:
            (chartDef.hasDemographics
              ? Object.values(newChartData)?.length - 1
              : Object.values(newChartData)?.length) === totalDataQueries,
        },
      };

      return { ...state, reports: newReports };
    })
    .addCase(SET_HAS_DATA, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { containerId, hasAllData } = action.payload;
      const newReports = cloneDeep(reports);
      const currentReport = (newReports[currentReportIndex] ?? {
        ...defaultReportState,
      })!;
      currentReport.chartData = {
        ...currentReport.chartData,
        [containerId]: {
          ...currentReport.chartData[containerId],
          hasAllData,
        },
      };
      return { ...state, reports: newReports };
    })
    .addCase(SET_HAS_RENDERED, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { containerId, hasRendered } = action.payload;
      const newReports = cloneDeep(reports);
      const currentReport = newReports[currentReportIndex] ?? {
        ...defaultReportState,
      };
      if (currentReport) {
        currentReport.chartData = {
          ...currentReport.chartData,
          [containerId]: {
            ...currentReport.chartData[containerId],
            hasRendered,
          },
        };
      }
      return { ...state, reports: newReports };
    })
    .addCase(SET_HAS_FAILED, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { containerId } = action.payload;
      const newReports = cloneDeep(reports);
      const currentReport = (newReports[currentReportIndex] ?? {
        ...defaultReportState,
      })!;
      currentReport.chartData = {
        ...currentReport.chartData,
        [containerId]: {
          ...currentReport.chartData[containerId],
          hasFailed: true,
        },
      };
      return { ...state, reports: newReports };
    })
    .addCase(SET_ANALYTICS_DATE_RANGE, (state, action) => {
      const { reports, currentReportIndex } = state;
      const newReports = cloneDeep(reports);
      (newReports[currentReportIndex] ?? { ...defaultReportState }).dateRange =
        action.payload;
      return { ...state, reports: newReports };
    })
    .addCase(REMOVE_CHART_DATA, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { containerId } = action.payload;
      const newReports = cloneDeep(reports);
      const newChartData = {
        ...(newReports[currentReportIndex] ?? { ...defaultReportState })
          ?.chartData,
      };
      delete newChartData[containerId];
      (newReports[currentReportIndex] ?? { ...defaultReportState }).chartData =
        newChartData;
      return { ...state, reports: newReports };
    })
    .addCase(ADD_QUERY_PENDING, (state) => {
      const { reports, currentReportIndex } = state;
      const newReports = cloneDeep(reports);
      (
        newReports[currentReportIndex] ?? { ...defaultReportState }
      ).queriesPending =
        (newReports[currentReportIndex] ?? { ...defaultReportState })
          .queriesPending + 1;
      return { ...state, reports: newReports };
    })
    .addCase(SUBTRACT_QUERY_PENDING, (state) => {
      const { reports, currentReportIndex } = state;
      const newReports = cloneDeep(reports);
      (
        newReports[currentReportIndex] ?? { ...defaultReportState }
      ).queriesPending =
        (newReports[currentReportIndex] ?? { ...defaultReportState })
          .queriesPending - 1;
      return { ...state, reports: newReports };
    })
    .addCase(SET_VARS, (state, action) => {
      return {
        ...state,
        ...action.payload.config,
        schemaIdContactAnalyticsSDO: action.payload.schemaIdContactAnalyticsSDO,
        orgName: action.payload.orgName,
        veritoneAppId: action.payload.applicationId,
      };
    })
    .addCase(GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS, (state, action) => {
      const contactAnalyticsConfig = get(
        action,
        'payload.payload.getUserSettings',
        []
      ).find(
        (s: { key: string; value: string }) =>
          s.key === 'contactAnalyticsConfig'
      ) ?? { value: '{}' };

      console.log(
        'worker contactAnalyticsConfig',
        JSON.parse(contactAnalyticsConfig.value)
      );
      return { ...state, ...JSON.parse(contactAnalyticsConfig.value) };
    })
    .addCase(SET_CHART_SETTINGS, (state, action) => {
      const { reports, currentReportIndex } = state;
      const newReports = cloneDeep(reports);
      const currentReport = (newReports[currentReportIndex] ?? {
        ...defaultReportState,
      })!;
      currentReport.aggregationSize = action.payload.aggSize;
      currentReport.useLegend = action.payload.useLegendSetting;
      currentReport.useTitle = action.payload.useTitleSetting;
      currentReport.lineWidth = action.payload.lineWidthSetting;
      return { ...state, reports: newReports };
    })
    .addCase(SET_CHARTS, (state, action) => {
      const { reports, currentReportIndex } = state;
      const newReports = cloneDeep(reports);
      (newReports[currentReportIndex] ?? { ...defaultReportState }).charts =
        action.payload.charts;
      return { ...state, reports: newReports };
    })
    .addCase(SET_SETTINGS, (state, action) => {
      const { reports, currentReportIndex } = action.payload;
      return {
        ...state,
        currentReportIndex: currentReportIndex ?? 0,
        reports: reports ?? [],
      };
    })
    .addCase(ADD_REPORT, (state, _action) => {
      let maxN = 0;
      const nameCount = state?.reports.reduce((n, r) => {
        const match = r.name.match(
          /Contact Report \(([0-9]+)\)|Contact Report/
        );
        if (match) {
          maxN =
            match[1] && parseInt(match[1], 10) > maxN
              ? parseInt(match[1], 10)
              : maxN;
          return match ? n + 1 : n;
        }
        return n;
      }, 0);
      const newReports = [
        ...(state?.reports ?? []),
        {
          ...defaultReportState,
          name: `${defaultReportState.name}${
            nameCount > 0
              ? ` (${nameCount <= maxN ? maxN + 1 : nameCount})`
              : ''
          }`,
          id: shortId.generate(),
        },
      ];
      return {
        ...state,
        currentReportIndex: newReports.length - 1,
        reports: newReports,
      };
    })
    .addCase(REMOVE_REPORT, (state, action) => {
      const { reports, currentReportIndex } = state;
      const { reportIndex } = action.payload;
      const newReports = cloneDeep(reports);
      newReports.splice(reportIndex, 1);

      return {
        ...state,
        reports: newReports,
        currentReportIndex:
          reportIndex === currentReportIndex
            ? reportIndex - 1 < 0
              ? 0
              : reportIndex - 1
            : currentReportIndex,
      };
    })
    .addCase(SET_CURRENT_REPORT, (state, action) => {
      const { reports } = state;
      const newReports = reports.map((r) => ({
        ...r,
        chartData: r.chartData
          ? Object.keys(r.chartData).reduce((acc, cdk) => {
              return {
                ...acc,
                [cdk]: { ...r.chartData[cdk], hasRendered: false },
              };
            }, {})
          : {},
      }));
      return {
        ...state,
        reports: newReports,
        currentReportIndex: action.payload.currentReportIndex,
      };
    })
    .addCase(SET_REPORT_NAME, (state, action) => {
      const { reports } = state;
      const { reportIndex, name } = action.payload;
      const newReports = cloneDeep(reports);
      (newReports[reportIndex] ?? { ...defaultReportState }).name = name;
      return { ...state, reports: newReports };
    })
    .addCase(SET_LOADING_USER_CONFIG, (state, action) => {
      return { ...state, loadingUserConfig: action.payload.loadingUserConfig };
    });
});

export default reducer;
export const namespace = 'contactAnalytics-worker';

export const local = (state: any) => state;
export const selectLoadingUserConfig = (state: any) =>
  local(state)?.loadingUserConfig ?? false;
export const selectCurrentReportIndex = (state: any) =>
  local(state)?.currentReportIndex ?? 0;
export const selectReports = (state: any) =>
  local(state)?.reports ?? [{ ...defaultReportState }];
export const selectAggregationSize = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.aggregationSize ??
  aggregationSizeDefault;
export const selectUseLegend = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.useLegend ??
  useLegendDefault;
export const selectUseTitle = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.useTitle ??
  useTitleDefault;
export const selectLineWidth = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.lineWidth ??
  lineWidthDefault;
export const selectChartData = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.chartData ?? {};
export const selectQueriesPending = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.queriesPending ?? 0;
export const selectCharts = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.charts ?? [];
export const selectDateRange = (state: any) =>
  local(state)?.reports?.[selectCurrentReportIndex(state)]?.dateRange ?? {
    lowerDateBound: lowerDateBoundDefault.toISO(),
    upperDateBound: upperDateBoundDefault.toISO(),
  };
