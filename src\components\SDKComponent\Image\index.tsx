import * as styles from './styles.scss';
const Image = ({
  src,
  height = '100px',
  width = '100px',
  type = 'cover',
  border,
  label,
  onClick,
}: Props) => {
  return (
    <div
      style={{
        backgroundImage: `url(${src})`,
        height: height,
        width: width,
        cursor: onClick ? 'pointer' : 'initial',
        border: border ? `1px solid #e4e4e4` : `none`,
      }}
      className={
        type === 'cover' ? styles.containerCover : styles.containerContain
      }
      onClick={onClick}
      data-testid="image"
    >
      {label && (
        <div className={styles.labelBackgroundContainer}>
          <div className={styles.labelContainer}>
            <span>{label}</span>
          </div>
        </div>
      )}
    </div>
  );
};

interface Props {
  src: string;
  height?: string;
  width?: string;
  type?: string;
  onClick?: () => void;
  border?: boolean;
  label?: string;
}
export default Image;
