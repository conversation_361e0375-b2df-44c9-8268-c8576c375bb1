import Typography from '@mui/material/Typography';
import { withStyles, ClassNameMap } from '@mui/styles';
import React from 'react';
import styles from './styles';

interface FilePickerFlatHeaderProps {
  title?: string;
  fileCount?: number;
  maxFiles?: number;
  classes?: ClassNameMap;
}

const FilePickerFlatHeader: React.FC<FilePickerFlatHeaderProps> = ({
  title = 'Uploaded',
  fileCount = 0,
  maxFiles = 1,
  classes,
}) => {
  return (
    <div className={classes?.filePickerFlatHeader || ''}>
      <Typography variant="h6">
        {title}
        &nbsp;
        <span className={classes?.count || ''}>
          {fileCount}/{maxFiles}
        </span>
      </Typography>
    </div>
  );
};

export default withStyles(styles)(FilePickerFlatHeader);
