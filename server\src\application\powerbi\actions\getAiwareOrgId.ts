import { callGQL } from '../../../util/callGraphql';
import { ApiError } from '../errors';
import { Context } from '../../types';

interface MeResponse {
  me: {
    organizationId: string;
  };
}

const meQuery = `query {
  me {
    organizationId
	}
}`;

const getAiwareOrgId = async (context: Context) => {
  const { log, data, req } = context;

  try {
    const headers = { Authorization: req.headers.authorization };
    const response = await callGQL<MeResponse>(context, headers, meQuery);

    data.authorizedOrgId = response.me.organizationId;

    return context;
  } catch (e) {
    log.error('GetAiwareOrgId failed', e);
    throw new ApiError(e);
  }
};

export default getAiwareOrgId;
