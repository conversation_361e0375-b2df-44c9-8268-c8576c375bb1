import React, { Fragment } from 'react';
import {
  AUDIO_MIME_TYPES,
  VIDEO_MIME_TYPES,
  IMAGES_MIME_TYPES,
  DOCS_MIME_TYPES,
} from '../../helpers/common';
import { ConnectedProps, connect } from 'react-redux';
import { inRange } from 'lodash';
import {
  getFilteredFileType,
  UPDATE_FILE_TYPES_FILTERS,
  FILE_TYPES_KEY,
} from 'state/modules/filters';
import { pushOrPopArray } from '../../state/utils/pushOrPop';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUp from '@mui/icons-material/KeyboardArrowUp';
import Collapse from '@mui/material/Collapse';
import * as styles from './styles.scss';
import { UpdateFileTypes } from '.';

const FILE_TYPES = {
  [FILE_TYPES_KEY.VIDEO]: [...VIDEO_MIME_TYPES],
  [FILE_TYPES_KEY.AUDIO]: [...AUDIO_MIME_TYPES],
  [FILE_TYPES_KEY.IMAGE]: [...IMAGES_MIME_TYPES],
  [FILE_TYPES_KEY.DOC]: [...DOCS_MIME_TYPES],
};
const CHECKBOX_TYPE = {
  all: 'checkbox-all',
  child: 'checkbox-child',
};
type FileType = keyof typeof FILE_TYPES;
const isFileType = (type: FileType | string): type is FileType =>
  Object.values(FILE_TYPES_KEY).includes(type as FileType);
interface StateProps {
  collapse: string[];
}
export class FileTypeFilter extends React.Component<PropsFromRedux> {
  state: StateProps = {
    collapse: [],
  };

  onFileTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    value: FileType | string
  ) => {
    const { fileTypesFilter, updateFileFilter } = this.props;
    const checkboxType = event.currentTarget.getAttribute('data-type');
    const checked = this.getChecked(event.target.checked, value);
    if (isFileType(value) && checkboxType === CHECKBOX_TYPE.all) {
      updateFileFilter?.({
        [value]: checked ? [...FILE_TYPES[value]] : [],
      });
    }
    if (checkboxType === CHECKBOX_TYPE.child) {
      const type = this.getFileTypesKey(value);
      if (type) {
        const valueArray = [...(fileTypesFilter?.[type] ?? [])];
        updateFileFilter?.({
          [type]: pushOrPopArray(valueArray, value),
        });
      }
    }
  };

  getChecked = (checked: boolean, value: string) => {
    if (
      isFileType(value) &&
      [FILE_TYPES[value].length, 0].every(
        (el) => el !== this.props.fileTypesFilter?.[value].length
      )
    ) {
      return false; // all inner checkboxes turn false when outer checkbox indeterminate false
    }
    return checked;
  };

  getFileTypesKey = (value: string) => {
    if (!value) {
      return '';
    }
    if (VIDEO_MIME_TYPES.includes(value)) {
      return FILE_TYPES_KEY.VIDEO;
    }
    if (AUDIO_MIME_TYPES.includes(value)) {
      return FILE_TYPES_KEY.AUDIO;
    }
    if (IMAGES_MIME_TYPES.includes(value)) {
      return FILE_TYPES_KEY.IMAGE;
    }
    if (DOCS_MIME_TYPES.includes(value)) {
      return FILE_TYPES_KEY.DOC;
    }
  };

  handleExpand = (type: string) => () => {
    this.setState((prevState: StateProps) => ({
      ...prevState,
      collapse: pushOrPopArray(prevState.collapse, type),
    }));
  };

  renderCheckboxes = (type: FileType) => {
    const { fileTypesFilter } = this.props;
    return (
      <Fragment>
        <div className={styles['file-types']}>
          <FormControlLabel
            control={
              <Checkbox
                data-test={type}
                id={type}
                value={type}
                color="primary"
                onChange={(event) => this.onFileTypeChange(event, type)}
                indeterminate={inRange(
                  fileTypesFilter?.[type].length ?? 0,
                  1,
                  FILE_TYPES[type].length
                )}
                checked={
                  fileTypesFilter?.[type].length === FILE_TYPES[type].length
                }
                inputProps={
                  {
                    'data-testid': `check-box-${type}`,
                    'data-type': `${CHECKBOX_TYPE.all}`,
                  } as CheckboxInputProps
                }
              />
            }
            label={type.toUpperCase()}
            data-testid="form-control-label"
          />
          <Button
            className={styles['expand-button']}
            onClick={this.handleExpand(type)}
            data-testid="button"
          >
            {this.state.collapse.includes(type) ? (
              <KeyboardArrowUp />
            ) : (
              <KeyboardArrowDown />
            )}
          </Button>
        </div>
        <Collapse in={this.state.collapse.includes(type)}>
          {FILE_TYPES[type].map((value, index) => {
            let labelValue = value;
            if (value.length > 20) {
              labelValue = labelValue.slice(0, 20) + '...';
            }
            return (
              <Tooltip
                key={value + index.toString()}
                title={value}
                placement="bottom"
              >
                <FormControlLabel
                  classes={{
                    root: styles['overwrite-form-label'],
                  }}
                  control={
                    <Checkbox
                      id={value}
                      className={styles['file-sub-type']}
                      value={value}
                      color="primary"
                      onChange={(event) => this.onFileTypeChange(event, value)}
                      checked={fileTypesFilter?.[type].includes(value)}
                      inputProps={
                        {
                          'data-testid': `check-box-child-${type}`,
                          'data-type': `${CHECKBOX_TYPE.child}`,
                        } as CheckboxInputProps
                      }
                    />
                  }
                  label={labelValue}
                  data-testid="form-control-label"
                />
              </Tooltip>
            );
          })}
        </Collapse>
      </Fragment>
    );
  };

  render() {
    return (
      <div
        className={styles['file-type-container']}
        data-testid="file-type-filters"
      >
        <FormControl className={styles['file-type-form-control']}>
          <FormGroup>
            {this.renderCheckboxes('video')}
            {this.renderCheckboxes('audio')}
            {this.renderCheckboxes('image')}
            {this.renderCheckboxes('doc')}
          </FormGroup>
        </FormControl>
      </div>
    );
  }
}
interface CheckboxInputProps extends React.HTMLAttributes<HTMLInputElement> {
  'data-testid'?: string;
}

const mapState = (state: any) => ({
  fileTypesFilter: getFilteredFileType(state),
});

const mapDispatch = {
  updateFileFilter: (fileTypes: UpdateFileTypes) =>
    UPDATE_FILE_TYPES_FILTERS({ fileTypes }),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(FileTypeFilter);
