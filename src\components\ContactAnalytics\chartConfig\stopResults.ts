import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    limit: 10000
                    name: "resultOfStop"
                    field: "resultOfStop"
                    operator: "term"
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'resultOfStopAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
             aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    operator: "count",
                    distinct: false,
                    field: "_id"
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'resultOfStopTotalCount',
      dataKey: 'datetimeOfStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stop Results Distribution',
  filterTextType: 'Stop Results Distribution by Type',
  filterType: 'resultOfStop',
  filterTerms: {
    Citation: [
      'Citation for infraction',
      'Warning (verbal or written)',
      'In-field cite and release',
    ],
    Arrest: [
      'Custodial arrest without warrant',
      'Custodial arrest pursuant to outstanding warrant',
    ],
    Other: [
      'Contacted parent/legal guardian or other person responsible for the minor',
      'Field interview card completed',
      'Noncriminal transport or caretaking transport  (including transport by officer, ambulance, or another agency)',
      'Psychiatric hold  (Welfare & Institutions Code sections 5150 and/or 5585.20)',
      'Contacted U.S. Department of Homeland Security  (EG: Immigration, Customs, and Border Protection)',
    ],
    K12: [
      'Referral to school administrator',
      'Referral to school counselor or other support staff',
    ],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);
    chart.data = data.resultOfStopAggregation.map((dateAgg) => {
      const noActionTaken =
        config.useFilter === 'filter'
          ? 0
          : data.resultOfStopTotalCount.find(
              (b) => b.key_as_string === dateAgg.key_as_string
            )!.doc_count - dateAgg.doc_count;
      return {
        date: dateAgg.key_as_string,
        Citation: 0,
        Arrest: 0,
        Other: 0,
        K12: 0,
        'No Action Taken': noActionTaken,
        ...dateAgg.resultOfStop.buckets.reduce(
          (acc: { [key: string]: number }, b) => {
            if (
              [
                'Citation for infraction',
                'Warning (verbal or written)',
                'In-field cite and release',
              ].includes(b.key)
            ) {
              acc['Citation'] = acc['Citation']
                ? acc['Citation'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Custodial arrest without warrant',
                'Custodial arrest pursuant to outstanding warrant',
              ].includes(b.key)
            ) {
              acc['Arrest'] = acc['Arrest']
                ? acc['Arrest'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Contacted parent/legal guardian or other person responsible for the minor',
                'Field interview card completed',
                'Noncriminal transport or caretaking transport  (including transport by officer, ambulance, or another agency)',
                'Psychiatric hold  (Welfare & Institutions Code sections 5150 and/or 5585.20)',
                'Contacted U.S. Department of Homeland Security  (EG: Immigration, Customs, and Border Protection)',
              ].includes(b.key)
            ) {
              acc['Other'] = acc['Other']
                ? acc['Other'] + b.doc_count
                : b.doc_count;
            }
            if (
              [
                'Referral to school administrator',
                'Referral to school counselor or other support staff',
              ].includes(b.key)
            ) {
              acc['K12'] = acc['K12'] ? acc['K12'] + b.doc_count : b.doc_count;
            }
            return acc;
          },
          {} // this reduce need to return an object to update default values
        ),
      };
    });

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'Citation';
    series.dataFields.dateX = 'date';
    series.name = 'Citation';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series2 = chart.series.push(new am4charts.LineSeries());
    series2.strokeWidth = config.lineWidth;
    series2.dataFields.valueY = 'Arrest';
    series2.dataFields.dateX = 'date';
    series2.name = 'Arrest';
    series2.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series3 = chart.series.push(new am4charts.LineSeries());
    series3.strokeWidth = config.lineWidth;
    series3.dataFields.valueY = 'Other';
    series3.dataFields.dateX = 'date';
    series3.name = 'Other';
    series3.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series4 = chart.series.push(new am4charts.LineSeries());
    series4.strokeWidth = config.lineWidth;
    series4.dataFields.valueY = 'K12';
    series4.dataFields.dateX = 'date';
    series4.name = 'K12';
    series4.tooltipText = '{name}: [bold]{valueY}[/]';

    // Create series
    const series6 = chart.series.push(new am4charts.LineSeries());
    series6.strokeWidth = config.lineWidth;
    series6.dataFields.valueY = 'No Action Taken';
    series6.dataFields.dateX = 'date';
    series6.name = 'No Action Taken';
    series6.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  resultOfStopAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
    resultOfStop: {
      buckets: { doc_count: number; key: string; key_as_string: string }[];
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
    };
  }[];
  resultOfStopTotalCount: { key_as_string: string; doc_count: number }[];
}
