import { combineReducers } from 'redux';
import { reducer as formReducer } from 'redux-form';
import { modules } from '@veritone/glc-redux';
const { auth, user, uiState, config } = modules;
const { namespace: userNamespace, reducer: userReducer } = user;
const { namespace: authNamespace, reducer: authReducer } = auth;

import appReducer, { namespace as appNamespace } from 'modules/app';
import enginesExampleReducer, {
  namespace as enginesExampleNamespace,
} from 'modules/engines-example';
import foldersReducer, { namespace as foldersNamespace } from 'modules/folders';
import searchReducer, { namespace as searchNamespace } from 'modules/search';
import tdosTableReducer, {
  namespace as tdoTableNamespace,
} from 'modules/tdosTable';
import sunburstReducer, {
  namespace as sunburstNamespace,
} from 'modules/sunburst';
import dashboardReducer, {
  namespace as dashboardNamespace,
} from 'modules/dashboard';
import snackbarReducer, {
  namespace as snackbarNamespace,
} from 'modules/snackbar';
import wordcloudReducer, {
  namespace as wordcloudNamespace,
} from 'modules/wordcloud';
import filtersReducer, {
  namespace as filtersReducerNamespace,
} from 'modules/filters';
import historyReducer, {
  namespace as historyReducerNamespace,
} from 'modules/history';
// import {uploadFileReducer, folderSelectionDialogReducer, notificationsReducer} from 'veritone-widgets';
import upLoadFileReducer, {
  namespace as uploadFileNamespace,
} from 'modules/uploadFile';
import requiredEnginesAndSchemasReducer, {
  namespace as requiredEnginesAndSchemasNamespace,
} from 'modules/requiredEnginesAndSchemas';
import contactAnalyticsReducer, {
  namespace as contactAnalyticsNamespace,
} from 'modules/contactAnalytics';
import processingJobsReducer, {
  namespace as processingJobsReducerNamespace,
} from 'modules/processingJobs';
import exportTemplateReducer, {
  namespace as exportTemplateReducerNamespace,
} from 'modules/bulkExport/exportTemplate';

export default (extraReducers: any) =>
  combineReducers({
    form: formReducer,
    [uiState.namespace]: uiState.reducer,
    [authNamespace]: authReducer,
    [appNamespace]: appReducer,
    [enginesExampleNamespace]: enginesExampleReducer,
    [foldersNamespace]: foldersReducer,
    [searchNamespace]: searchReducer,
    [tdoTableNamespace]: tdosTableReducer,
    [sunburstNamespace]: sunburstReducer,
    [dashboardNamespace]: dashboardReducer,
    [userNamespace]: userReducer,
    [snackbarNamespace]: snackbarReducer,
    [wordcloudNamespace]: wordcloudReducer,
    [filtersReducerNamespace]: filtersReducer,
    [historyReducerNamespace]: historyReducer,
    // folderSelectionDialog: folderSelectionDialogReducer,
    // snackbar: notificationsReducer,
    [uploadFileNamespace]: upLoadFileReducer,
    [requiredEnginesAndSchemasNamespace]: requiredEnginesAndSchemasReducer,
    [contactAnalyticsNamespace]: contactAnalyticsReducer,
    [processingJobsReducerNamespace]: processingJobsReducer,
    [exportTemplateReducerNamespace]: exportTemplateReducer,
    [config.namespace]: (state = window.config) => state, // fixme?
    ...extraReducers,
  });
