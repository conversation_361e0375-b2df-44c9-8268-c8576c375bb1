import axios from 'axios';
import { ApiError, NoDatasourcesError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';
import sleep from '../../../util/sleep';

export interface DatasourceResponse {
  value: {
    '@odata.context': string;
    gatewayId: string;
    datasourceId: string;
  }[];
}

const getDatasourceGateway = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    let poll = true;
    let pollAttempts = 20;

    while (poll && pollAttempts > 0) {
      const { data: resp } = await axios.get<DatasourceResponse>(
        `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/datasets/${data.datasetId}/datasources`,
        {
          headers: {
            Authorization: data.pbiBearerToken,
            'X-PowerBI-Profile-Id': data.profileId,
          },
        }
      );

      if (resp.value.length > 0) {
        if (resp.value[0]?.gatewayId && resp.value[0]?.datasourceId) {
          poll = false;
          data.gatewayId = resp.value[0].gatewayId;
          data.datasourceId = resp.value[0].datasourceId;
        } else {
          log.error('No gateway or datasource IDs available! Retrying...');
          await sleep(1000);
          pollAttempts--;
        }
      } else {
        log.error('No datasources available! Retrying...');
        await sleep(1000);
        pollAttempts--;
      }
    }

    if (!data.datasourceId || !data.gatewayId) {
      throw new NoDatasourcesError();
    }

    return context;
  } catch (e) {
    log.error('GetDatasourceGateway failed', e);
    throw new ApiError(e);
  }
};

export default getDatasourceGateway;
