import {
  addCucumberPreprocessorPlugin,
  afterRunHandler,
} from '@badeball/cypress-cucumber-preprocessor';
import { defineConfig } from 'cypress';
import { addEnvToConfig } from './cypress/plugins';
import webpackPreprocessor from '@cypress/webpack-preprocessor';
import getAppWebpackConfig from './webpack.config.js';

export default defineConfig({
  viewportWidth: 1920,
  viewportHeight: 1080,
  defaultCommandTimeout: 60000,
  execTimeout: 60000,
  taskTimeout: 60000,
  pageLoadTimeout: 60000,
  requestTimeout: 20000,
  responseTimeout: 60000,
  numTestsKeptInMemory: 20,
  chromeWebSecurity: false,
  projectId: 'hpkn4c',
  e2e: {
    setupNodeEvents: async (on, config) => {
      process.env.CYPRESS_TEST = 'true';
      await addCucumberPreprocessorPlugin(on, config, {
        omitAfterRunHandler: true,
      });

      const curConfig = addEnvToConfig(on, config);
      const webpackConfig = getAppWebpackConfig({}, { mode: 'development' });
            const cypressWebpackResolveFallback = {
        ...webpackConfig.resolve.fallback,
      } as { [key: string]: string | false | string[] };
      const options = {
        webpackOptions: {
          resolve: {...webpackConfig.resolve,
            fallback: cypressWebpackResolveFallback},
          module: {
            rules: [
              ...webpackConfig.module.rules,
              {
                test: /\.feature$/,
                use: [
                  {
                    loader: '@badeball/cypress-cucumber-preprocessor/webpack',
                    options: config,
                  },
                ],
              },
              {
                test: /\.ts$/,
                exclude: [/node_modules/],
                use: [
                  {
                    loader: 'babel-loader',
                  },
                ],
              },
            ],
          },
        },
        watchOptions: {},
      };
      const data = {};

      on('task', {
        setValue: (params) => {
          const { key, value } = params;
          data[key] = value;
          return value;
        },
        getValue: (params) => {
          const { key } = params;
          return data[key] || null;
        },
      });
      on('file:preprocessor', webpackPreprocessor(options));
      config.env.stepDefinitions =
        'cypress/e2e/step_definitions/[filepath]/*.{js,ts}';
      on('after:run', async (results) => {
        await afterRunHandler(config);
      });
      return curConfig;
    },
    baseUrl: 'https://local.veritone.com:8080',
    numTestsKeptInMemory: 8,
    video: false,
    retries: {
      // runMode: 1,
      openMode: 0,
    },
    specPattern: 'cypress/e2e/**/*.feature',
  },
});
