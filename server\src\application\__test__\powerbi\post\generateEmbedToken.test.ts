import supertest from 'supertest';
import { request } from 'graphql-request';
import axios from 'axios';
import expressApp from '../../setupExpress';
import { tokenConfigRecordMock, templateRecordMock } from '../fixtures';

jest.mock('graphql-request', () => ({ request: jest.fn() }));
jest.mock('axios', () => ({ post: jest.fn() }));
jest.mock('../../../powerbi/queries', () => () => ({
  getTemplateRecord: () => Promise.resolve(templateRecordMock),
  getTokenConfig: () => Promise.resolve(tokenConfigRecordMock)
}));

describe('POST /api/v1/powerbi/generateEmbedToken', () => {
  it('returns embedToken', async () => {

    (request as jest.Mock).mockImplementation(({ document }) => {
      if (document.includes('validateToken')) {
        return Promise.resolve({ validateToken: { token: 'valid token' } });
      }
      if (document.includes('me')) {
        return Promise.resolve({ me: { organizationId: 123 } });
      }
      return Promise.resolve()
    });

    (axios.post as jest.Mock).mockImplementation((url) => {
      if (url.includes('GenerateToken')) {
        return Promise.resolve({
          data: {
            token: 'avalidembedtoken',
            tokenId: '12345-67890',
            expiration: 3600
          }
        });
      }
      if (url.includes('oauth')) {
        return Promise.resolve({
          data: {
            access_token: 'validServicePricipalToken',
            expires_in: 3600
          }
        });
      }
      return Promise.resolve()
    });

    await supertest(expressApp)
      .post('/api/v1/powerbi/generateEmbedToken')
      .set('Authorization', 'Bearer valid-token')
      .expect(200)
      .then(res => {
        expect(res.headers['content-type']).toContain('application/json; charset=utf-8');
        expect(res.body).toMatchObject({
          embedUrl: "https://embedurlfromazure.us",
          embedToken: "avalidembedtoken",
          embedTokenId: "12345-67890",
          embedTokenExp: 3600,
          embedTokenHostname: "https://app.high.powerbigov.us"
        });
      })
  })
})
