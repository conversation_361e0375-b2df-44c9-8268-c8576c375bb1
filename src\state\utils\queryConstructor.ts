type Aggregate = {
  field: string;
  operator: string;
  name?: string;
}[];

export const returnSearchQuery = (
  query: any,
  queryRange: string,
  aggregate: Aggregate
): Record<string, any> & { queryRange: string; aggregate: Aggregate } => {
  const searchQuery = {
    ...query,
    queryRange,
    aggregate,
  };

  if (!searchQuery.query) {
    // we must be at the root folder -> trick aggregation endpoint into empty query and pray
    searchQuery.query = {
      operator: 'and',
      conditions: [],
    };
  }

  return searchQuery;
};
