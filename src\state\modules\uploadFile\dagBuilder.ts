import { DefaultEngineId } from './models';
export const CREATE_JOB_MUTATION = `mutation createJob($input: CreateJob){
  createJob(input: $input) {
    id
  }
}
`;

export const IO_FOLDS = {
  wsaOut: 'wsaOut',
  playbackIn: 'playbackIn',
  assetCreatorIn: 'assetCreatorIn',
  videoChunkIn: 'videoChunkIn',
  videoChunkOut: 'videoChunkOut',
  audioChunkIn: 'audioChunkIn',
  audioChunkOut: 'audioChunkOut',
  rawChunkIn: 'rawChunkIn',
  rawChunkOut: 'rawChunkOut',
};

export function buildDAG(
  fileUrl: string,
  fileType: string,
  tdoId: string,
  clusterId: string,
  defaultEngines: DefaultEngineId,
  workflow: 'simple' | 'advanced'
) {
  const { tasks, routes } = buildTasks(
    fileUrl,
    fileType,
    tdoId,
    defaultEngines
  );
  const input = {
    targetId: tdoId,
    clusterId,
    jobConfig: {
      illuminate: {
        workflow,
      },
    },
    tasks,
    routes,
  };
  const variables = {
    input,
  };
  return { query: CREATE_JOB_MUTATION, variables };
}

function buildTasks(
  fileUrl: string,
  fileType: string,
  tdoId: string,
  defaultEngines: DefaultEngineId
) {
  // use glcIngestor for playback for video and audio
  if (isVideoAudio(fileType)) {
    return buildPlayBackTasks(fileUrl, defaultEngines);
  }
  // use wsa + SI2 Stream Asset Creator for non video or audio
  return buildAssetCreationTasks(fileUrl, tdoId, defaultEngines);
}

function buildPlayBackTasks(fileUrl: string, defaultEngines: DefaultEngineId) {
  return {
    tasks: [buildGLCIngestionTask(fileUrl, defaultEngines.glcIngestor)],
    routes: [],
  };
}

function buildAssetCreationTasks(
  fileUrl: string,
  tdoId: string,
  defaultEngines: DefaultEngineId
) {
  const tasks = [
    buildWebstreamAdapterTask(defaultEngines.webstreamAdapter, tdoId, fileUrl),
    buildAssetCreatorTask(defaultEngines.playbackOther),
  ];
  const routes = [
    {
      parentIoFolderReferenceId: IO_FOLDS.wsaOut,
      childIoFolderReferenceId: IO_FOLDS.assetCreatorIn,
      options: {},
    },
  ];
  return {
    tasks,
    routes,
  };
}

function buildWebstreamAdapterTask(
  engineId: string,
  tdoId: string,
  fileUrl: string
) {
  return {
    // webstream adapter v3f
    engineId,
    payload: {
      tdoId: tdoId,
      url: fileUrl,
    },
    ioFolders: [
      {
        referenceId: IO_FOLDS.wsaOut,
        mode: 'stream',
        type: 'output',
      },
    ],
  };
}

function buildAssetCreatorTask(engineId: string) {
  return {
    engineId,
    payload: {
      setAsPrimary: true,
      assetType: 'media',
    },
    ioFolders: [
      {
        referenceId: IO_FOLDS.assetCreatorIn,
        mode: 'stream',
        type: 'input',
      },
    ],
    executionPreferences: {
      parentCompleteBeforeStarting: true,
    },
  };
}

// function buildPlaybackTask(engineId: string) {
//   return {
//     // Playback engine to store playback segments
//     engineId,
//     payload: {
//       setAsPrimary: true,
//       assetType: 'media',
//     },
//     ioFolders: [
//       {
//         referenceId: IO_FOLDS.playbackIn,
//         mode: 'stream',
//         type: 'input',
//       },
//     ],
//     executionPreferences: {
//       parentCompleteBeforeStarting: true,
//     },
//   };
// }

function buildGLCIngestionTask(fileUrl: string, engineId: string) {
  return {
    engineId,
    payload: {
      url: fileUrl,
    },
  };
}

function isAudio(fileType: string) {
  return fileType.includes('audio');
}

function isVideo(fileType: string) {
  return fileType.includes('video');
}

function isVideoAudio(fileType: string) {
  return isVideo(fileType) || isAudio(fileType);
}
