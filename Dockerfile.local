ARG app_name=illuminate-app

FROM node:22 AS builder
ENV APPLICATION=$app_name

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq libpango1.0-dev && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
ADD . /app
WORKDIR /app

RUN ls -a && \
    chmod +x /app/*.sh && \
    yarn && \
    yarn build

ENV APPLICATION=illuminate-app
RUN echo '### /app/buildinfo.sh...' && /app/buildinfo.sh

FROM node:22 AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
RUN mkdir -p /app/api
WORKDIR /app/api
COPY server/package.json .
COPY server/.yarnrc .
COPY server/yarn.lock .
RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .
RUN yarn
RUN yarn build
COPY server/package.json ./dist
COPY server/yarn.lock ./dist
WORKDIR /app/api/dist
RUN yarn workspaces focus --production

FROM nginx:stable-alpine

RUN apk update
RUN apk add jq curl bash nodejs npm
RUN apk add --upgrade pcre libjpeg-turbo libxml2 ncurses curl

ENV APPLICATION=$app_name
ENV NGINX_PORT=9000

EXPOSE ${NGINX_PORT}/tcp

COPY --from=builder /app/config.json /config.json
COPY --from=builder /app/local.veritone.com-key.pem /etc/nginx/
COPY --from=builder /app/local.veritone.com.pem /etc/nginx/

COPY --from=builder /app/getconfig-dynamicConfig.sh /getconfig-dynamicConfig.sh
COPY --from=builder /app/build-manifest.yml /build-manifest.yml
COPY --from=builder /app/configWhitelist.json /configWhitelist.json
COPY --from=builder /app/dynamicConfig-index-html.sh /dynamicConfig-index-html.sh
COPY --from=builder /app/entrypoint-local.sh /entrypoint.sh
COPY --from=builder /app/build-local /usr/share/nginx/html
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /app/nginx.conf.local /etc/nginx/conf.d/default.conf
COPY --from=backend /app/api/apiConfigWhitelist.json /apiConfigWhitelist.json
COPY --from=backend /app/api/dist /api

ENTRYPOINT '/entrypoint-local.sh'
