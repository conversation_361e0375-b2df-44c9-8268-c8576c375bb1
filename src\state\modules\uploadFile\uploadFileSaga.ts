import {
  fork,
  all,
  call,
  put,
  take,
  takeEvery,
  select,
} from 'typed-redux-saga/macro';
import { isArray, noop, get, isEmpty, sortBy, cloneDeep } from 'lodash';
import {
  uploadFilesChannel,
  standardizeFileType,
} from '../../utils/uploadFilesChannel';
import { handleRequest } from '../../utils/util';
import {
  enginesSelected,
  uploadResult,
  openFolderIdUpload,
  templates,
  templateName,
  failedFiles,
} from '.';
import { showNotification } from '../snackbar';
import * as actions from './actions';
import {
  getMoveFolders,
  getFolders,
  getOpenFolderId,
  getRootFolderId,
  SET_SELECTED_FOLDER_ID,
} from '../folders';
import { getTdosForExport, isSelectedAll } from '../tdosTable';
import { getFullSearchQuery, getSearchResultTdosAll } from '../search';
import getDefaultClusterId from '../../../helpers/getDefaultClusterId';
import {
  watchSaveUploadFile,
  watchSaveReprocessFile,
  watchcheckLimitAndSaveReprocessFile,
} from './createJobSaga';
import { UploadResult, UploadDescriptor, SignedWritableUrl } from './models';
import { arrayHasContents } from '@utils';
let requestMap: Record<string, XMLHttpRequest> | undefined;
function* finishUpload(
  result: UploadResult[] | null,
  { warning, error }: { warning?: string | boolean; error?: string | boolean },
  callback: (
    result: UploadResult[] | null,
    {
      warning,
      error,
      cancelled,
    }: {
      warning?: string | boolean;
      error?: string | boolean;
      cancelled?: boolean;
    }
  ) => void
) {
  yield* put(actions.uploadComplete(result, { warning, error }));

  if (warning || error) {
    // There are failed uploads, don't close out and display error screen
    return;
  }
  yield* put(actions.endPick('complete'));
  // Get accumulated results, not just what's in the current upload/retry request
  // If there's no results, then the user must have aborted them all
  const totalResults = yield* select(uploadResult);
  yield* call(callback, totalResults, {
    warning,
    error,
    cancelled: !totalResults.length,
  });
}
const batchSize = 10;
function* uploadFileSaga(fileOrFiles: File[], callback = noop) {
  const files = isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];

  let results: UploadResult[] = [];
  for (let i = 0; i < files.length; i += batchSize) {
    const batch = files.slice(i, i + batchSize);

    const batchResults = yield* uploadFileSagaForBatch(batch, callback);
    if (Array.isArray(batchResults)) {
      results.push(...batchResults);
    }
  }
  // Remove aborted requests
  results = results.filter((r) => !r.aborted);

  const isError = results.length && results.every((e) => e.error);
  const isWarning = !isError && results.some((e) => e.error);
  yield* finishUpload(
    results,
    {
      warning: isWarning ? 'Some files failed to upload.' : false,
      error: isError ? 'All files failed to upload.' : false,
    },
    callback
  );
}

function* uploadFileSagaForBatch(fileOrFiles: File[], callback = noop) {
  const files = isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];
  // get a signed URL for each object to be uploaded
  const signedWritableUrlResponses = yield* getSignedWritableUrlForBatch(
    files,
    callback
  );
  let uploadDescriptors; // { url, key, bucket, etc }
  try {
    uploadDescriptors = signedWritableUrlResponses.map(
      ({ data: { getSignedWritableUrl }, errors }: SignedWritableUrl) => {
        if (arrayHasContents(errors)) {
          throw new Error(
            `Call to getSignedWritableUrl returned error: ${errors[0].message}`
          );
        }

        return getSignedWritableUrl;
      }
    );
  } catch (e: any) {
    return yield* finishUpload(null, { error: e.message }, callback);
  }
  const responseUploadFilesChannel = yield* uploadFilesChannelForBatch(
    files,
    uploadDescriptors,
    callback
  );
  const resultChan = responseUploadFilesChannel?.resultChan;
  requestMap = responseUploadFilesChannel?.requestMap;
  const results = [];
  if (resultChan) {
    while (results.length !== files.length) {
      const {
        progress = 0,
        aborted,
        error,
        success,
        file,
        descriptor: { key, bucket, expiresInSeconds, getUrl, unsignedUrl },
      } = yield* take(resultChan);

      if (success || error) {
        yield* put(
          actions.uploadProgress(key, {
            name: file.name,
            type: standardizeFileType(file.type),
            size: file.size,
            error,
            aborted,
            percent: 100,
          })
        );

        results.push({
          key,
          bucket,
          expiresInSeconds,
          fileName: file.name,
          size: file.size,
          type: standardizeFileType(file.type),
          aborted,
          error: error || false,
          unsignedUrl: error ? null : unsignedUrl,
          getUrl: error ? null : getUrl,
          file,
        });

        continue;
      }

      yield* put(
        actions.uploadProgress(key, {
          name: file.name,
          type: standardizeFileType(file.type),
          size: file.size,
          percent: progress,
        })
      );
    }
  }
  return results;
}

function* getSignedWritableUrlForBatch(files: File[], callback: () => void) {
  const query = `query urls($name: String!){
    getSignedWritableUrl(key: $name, expiresInSeconds:86400) {
      url
      key
      bucket
      expiresInSeconds
      getUrl
      unsignedUrl
    }
  }`;
  try {
    const response: any = yield* all(
      files.map(({ name }: { name: string }) =>
        call(handleRequest, { query, variables: { name } })
      )
    );
    {
      return response.map(
        (item: { response: SignedWritableUrl }) => item.response
      );
    }
  } catch (_error) {
    return yield* finishUpload(null, { error: 'fail to get url' }, callback);
  }
}

function* uploadFilesChannelForBatch(
  files: File[],
  uploadDescriptors: UploadDescriptor[],
  callback: () => void
) {
  let resultChan;
  let requestMap;
  try {
    const uploadChannelResult = yield* call(
      uploadFilesChannel,
      uploadDescriptors,
      files
    );
    resultChan = uploadChannelResult.channel;
    requestMap = uploadChannelResult.requestMap;
  } catch (_error) {
    return yield* finishUpload(null, { error: 'Upload failed' }, callback);
  }
  return {
    resultChan,
    requestMap,
  };
}

function* watchUploadRequest() {
  yield* takeEvery(actions.UPLOAD_REQUEST, function* (action) {
    const { files, callback } = action.payload;
    yield* call(uploadFileSaga, files, callback);
  });
}

function* watchRetryRequest() {
  yield* takeEvery(actions.RETRY_REQUEST, function* (action) {
    const { callback } = action.payload;
    const errorFiles = yield* select(failedFiles) || [];
    yield* call(uploadFileSaga, errorFiles, callback);
  });
}

function* watchRetryDone() {
  yield* takeEvery(actions.RETRY_DONE, function* (action) {
    const { callback = noop } = action.payload;
    const uploads = yield* select(uploadResult) || [];
    const completedUploads = uploads.filter((upload) => !upload.error);

    yield* put(actions.endPick(''));
    yield* call(callback, completedUploads, {
      cancelled: !completedUploads.length,
    });
  });
}

function* watchAbortions() {
  yield* takeEvery(actions.ABORT_REQUEST, function* (action) {
    if (!requestMap) {
      return;
    }
    const { fileKey } = action.meta;
    // Abort requests somehow
    if (fileKey && requestMap[fileKey]) {
      requestMap[fileKey]?.abort?.();
      delete requestMap[fileKey];
      yield null; // use yield to disable You should use yield* for all typed-redux-saga
    } else {
      Object.keys(requestMap).forEach((fileKey) => {
        requestMap?.[fileKey]?.abort?.();
        delete requestMap?.[fileKey];
      });
    }
  });
}

export interface EngineCategoriesResponse {
  engineCategories: {
    count: number;
    records: {
      id: string;
      name: string;
      iconClass: string;
      description: string;
      engines: {
        records: {
          id: string;
          name: string;
          libraryRequired: boolean;
          runtimeType: string;
        }[];
      };
      libraryEntityIdentifierTypes: {
        records: {
          id: string;
          label: string;
        }[];
      };
      libraryEntityIdentifierTypeIds: string[];
      categoryType: string;
      searchConfiguration: {
        isSearchEnabled: boolean;
        searchMetadataKey: string;
        searchFields: {
          searchField: string;
          indexField: string;
        }[];
        autocompleteFields: {
          autocompleteField: string;
          indexField: string;
        }[];
      };
    }[];
  };
}
function* watchFetchEngineCategories() {
  yield* takeEvery(actions.FETCH_LIBRARIES_SUCCESS, function* () {
    const query = `query engineCategories{
      engineCategories(limit:200){
        count
        records{
          id
          name
          iconClass
          description
          engines(limit: 200){
            records{
              id
              name
              libraryRequired
              runtimeType
            }
          }
          libraryEntityIdentifierTypes{
            records{
              id
              label
            }
          }
          libraryEntityIdentifierTypeIds
          categoryType
          searchConfiguration{
            isSearchEnabled
            searchMetadataKey
            searchFields{
              searchField
              indexField
            }
            autocompleteFields{
              autocompleteField
              indexField
            }
          }
        }
      }
     }
    `;
    const { error, response } = yield* call(
      handleRequest<EngineCategoriesResponse>,
      { query }
    );
    const { records = [] } = response?.data?.engineCategories || {};
    if (error) {
      yield* put(actions.fetchEngineCategoriesFailure());
    } else {
      yield* put(actions.fetchEngineCategoriesSuccess(records));
    }
  });
}

interface GetLibrariesResponse {
  libraries: {
    records: {
      id: string;
      libraryId: string;
      name: string;
      version: number;
      coverImageUrl: string;
      organizationId: string;
      libraryType: {
        id: string;
        label: string;
        entityIdentifierTypes: {
          id: string;
        }[];
      };
      summary: {
        entityCount: number;
        unpublishedEntityCount: number;
      };
      createdDateTime: string;
      engineModels: {
        records: {
          id: string;
          trainStatus: string;
          libraryVersion: number;
          engineId: string;
        }[];
      };
    }[];
  };
}
function* watchFetchLibraries() {
  yield* takeEvery(actions.FETCH_LIBRARIES_REQUEST, function* () {
    const query = `query getLibraries{
      libraries(limit: 200) {
        records {
          id
          libraryId: id
          name
          version
          coverImageUrl
          organizationId
          libraryType {
            id
            label
            entityIdentifierTypes {
              id
            }
          }
          summary {
            entityCount
            unpublishedEntityCount
          }
          createdDateTime
          engineModels{
            records{
              id
              trainStatus
              libraryVersion
              engineId
            }
          }
        }
      }
    }
    `;
    const { error, response } = yield* call(
      handleRequest<GetLibrariesResponse>,
      { query }
    );
    const { records = [] } = response?.data?.libraries || {};
    if (error) {
      yield* put(actions.fetchLibrariesFailure());
    } else {
      yield* put(actions.fetchLibrariesSuccess(records));
    }
  });
}

interface GetEnginesResponse {
  engines: {
    records: {
      id: string;
      name: string;
      description: string;
      libraryRequired: boolean;
      runtimeType: string;
      logoPath: string;
      deploymentModel: string;
      rating: number;
      website: string;
      price: number;
      isPublic: boolean;
      asset: string;
      validateUri: string;
      createsTDO: boolean;
      iconPath: string;
      supportedInputFormats: string[];
      isConductor: boolean;
      edgeVersion: number;
      manifest: Record<string, any>;
      mode: string;
      fields: {
        defaultValue: string;
        info: string;
        label: string;
        max: number;
        min: number;
        name: string;
        options: {
          key: string;
          value: string;
        }[];
        step: number;
        type: string;
      }[];
      category: {
        id: string;
        name: string;
        iconClass: string;
      };
      standaloneJobTemplates: {
        type: string;
        template: string;
      }[];
    }[];
  };
}

function* watchFetchEngines() {
  yield* takeEvery(actions.FETCH_ENGINE_CATEGORIES_SUCCESS, function* () {
    const query = ` query getEngines($offset: Int, $limit: Int) {
      engines(offset: $offset,limit: $limit){
        records {
          id
          name
          description
          libraryRequired
          runtimeType
          logoPath
          deploymentModel
          rating
          website
          price
          isPublic
          asset
          validateUri
          createsTDO
          iconPath
          supportedInputFormats
          isConductor
          edgeVersion
          manifest
          mode
          fields {
            defaultValue
            info
            label
            max
            min
            name
            options {
              key
              value
            }
            step
            type
          }
          category {
            id
            name
            iconClass
          }
          standaloneJobTemplates {
            type
            template
          }
        }
     }
    }
    `;
    const results: any = [];
    const pageSize = 200;
    const initialOffset = 0;
    function* fetchEngines(offset: number): any {
      const variables = {
        limit: pageSize,
        offset: offset,
      };
      const { error, response } = yield* call(
        handleRequest<GetEnginesResponse>,
        {
          query,
          variables,
        }
      );
      if (error) {
        yield* put(actions.fetchEnginesFailure());
      }
      const { records = [] } = response?.data?.engines || {};
      results.push(...records);
      if (records.length === pageSize) {
        return yield* fetchEngines(offset + pageSize);
      } else {
        yield* put(actions.fetchEnginesSuccess(results));
      }
    }
    return yield* fetchEngines(initialOffset);
  });
}

function* watchSaveTemplate() {
  yield* takeEvery(actions.SAVE_TEMPLATE_REQUEST, function* () {
    const dataTemplates = yield* select(templates);
    const name = yield* select(templateName);
    const dataTemplatesFilter = dataTemplates.filter(
      (item) => item.name === name
    );
    if (dataTemplatesFilter.length) {
      yield* put(actions.showConfirmSaveTemplate());
    } else {
      const query = `mutation($name: String!, $taskList: JSONData!) {
        createProcessTemplate(input: {
            name: $name
            taskList: $taskList
        }) {
          process_template_id: id
        }
      }
      `;
      const enginesSelectedResults = yield* select(enginesSelected);
      const variables = {
        name,
        taskList: JSON.stringify(enginesSelectedResults),
      };
      yield* call(callSaveAndUpdateTemplate, { query, variables });
    }
  });
}

function* callSaveAndUpdateTemplate({ query, variables }: any) {
  const { error } = yield* call(handleRequest, { query, variables });
  if (error) {
    yield* put(actions.saveTemplateFailure());
    yield* put(showNotification('Failed to save template.', 'error'));
  } else {
    yield* put(actions.saveTemplateSuccess());
    yield* put(showNotification('Successfully saved template.', 'success'));
  }
}

interface QueryProcessTemplatesResponse {
  processTemplates: {
    records: {
      id: string;
      organizationId: string;
      name: string;
      taskList: Record<string, any>;
    }[];
  };
}
function* doFetchTemplates(): any {
  const query = `query getProcessTemplates($offset: Int, $limit: Int) {
      processTemplates (offset: $offset,limit: $limit) {
              records {
                  id
                  organizationId
                  name
                  taskList
              }
          }
      }
    `;

  const results: any = [];
  const pageSize = 200;
  const initialOffset = 0;
  function* fetchProcessTemplates(offset: number): any {
    const variables = {
      limit: pageSize,
      offset: offset,
    };
    const { error, response } = yield* call(
      handleRequest<QueryProcessTemplatesResponse>,
      {
        query,
        variables,
      }
    );
    if (error) {
      yield* put(actions.fetchTemplatesFailure());
    }
    const { records = [] } = response?.data?.processTemplates || {};
    results.push(...records);
    if (records.length === pageSize) {
      return yield* fetchProcessTemplates(offset + pageSize);
    } else {
      yield* put(actions.fetchTemplatesSuccess(results));
    }
  }
  return yield* fetchProcessTemplates(initialOffset);
}
function* watchSaveTemplateSuccess() {
  yield* takeEvery(actions.SAVE_TEMPLATE_SUCCESS, doFetchTemplates);
  yield* takeEvery(actions.FETCH_ENGINES_SUCCESS, doFetchTemplates);
  yield* takeEvery(actions.REMOVE_TEMPLATE_SUCCESS, doFetchTemplates);
}

interface ContentTemplatesResponse {
  dataRegistries: {
    records: {
      id: string;
      name: string;
      description: string;
      schemas: {
        records: {
          id: string;
          status: string;
          definition: Record<string, any>;
          majorVersion: number;
          minorVersion: number;
          validActions: string[];
        }[];
      };
      organizationId: string;
    }[];
  };
}
function* watchFetchContentTemplates() {
  yield* takeEvery(actions.FETCH_CONTENT_TEMPLATES_REQUEST, function* () {
    const query = `query contentTemplates {
      dataRegistries {
        records {
          id
          name
          description
          schemas {
            records {
              id
              status
              definition
              majorVersion
              minorVersion
              validActions
            }
          }
          organizationId
        }
      }
    }
    `;
    const { error, response } = yield* call(
      handleRequest<ContentTemplatesResponse>,
      { query }
    );
    const { records = [] } = response?.data?.dataRegistries || {};
    if (error) {
      yield* put(actions.fetchContentTemplatesFailure());
    } else {
      yield* put(actions.fetchContentTemplatesSuccess(records));
    }
  });
}

function* watchSelectFolderUpload() {
  yield* takeEvery(actions.SELECT_FOLDER, function* () {
    const folderId = yield* select(openFolderIdUpload);
    const moveFolders = yield* select(getMoveFolders);
    if (folderId && !isEmpty(moveFolders)) {
      // safe due to folders = makeFolderRoot with folderId is the Key
      const selectedFolder = moveFolders[folderId]!;
      yield* put(actions.updateSelectedFolderUpload(selectedFolder));
    }
  });
}
function* watchOpenModalUpload() {
  yield* put(actions.fetchLibraries());
  yield* put(actions.fetchContentTemplates());
}

function* watchSetCurrentFolder() {
  yield* takeEvery(SET_SELECTED_FOLDER_ID, function* (action) {
    const openFolderId = yield* select(getOpenFolderId);
    const selectedFolderId = get(
      action,
      'payload.selectedFolderId',
      openFolderId
    );
    const rootFolderId = yield* select(getRootFolderId);
    const folders = cloneDeep(yield* select(getFolders));
    const folder = selectedFolderId && folders[selectedFolderId];
    if (folder) {
      if (selectedFolderId === rootFolderId) {
        folder.name = 'My Cases';
      }
      yield* put(actions.setCurrentFolder(folder));
    }
  });
}

function* watchUpdateTemplate() {
  yield* takeEvery(actions.UPDATE_TEMPLATE_REQUEST, function* () {
    const dataTemplates = yield* select(templates);
    const name = yield* select(templateName);
    const dataTemplateUpdate = dataTemplates.find((item) => item.name === name);
    const id = get(dataTemplateUpdate, 'id', '');
    const query = `mutation($id: ID!, $taskList: JSONData!) {
      updateProcessTemplate(input: {
          id: $id
          taskList: $taskList
      }) {
        process_template_id: id
      }
    }
    `;
    const enginesSelectedResults = yield* select(enginesSelected);
    const variables = {
      id,
      taskList: JSON.stringify(enginesSelectedResults),
    };
    yield* call(callSaveAndUpdateTemplate, { query, variables });
  });
}

function* watchRemoveTemplate() {
  yield* takeEvery(actions.REMOVE_TEMPLATE_REQUEST, function* (action) {
    const { id } = action.payload;
    const query = `mutation{
      deleteProcessTemplate(
          id: "${id}"
      ) {
        process_template_id: id
      }
    }
    `;
    const { error } = yield* call(handleRequest, { query });
    if (error) {
      yield* put(actions.removeTemplateFailed());
      yield* put(showNotification('Failed to delete template.', 'error'));
    } else {
      yield* put(actions.removeTemplateSuccess());
      yield* put(showNotification('Successfully deleted template.', 'success'));
    }
  });
}

function* watchRunTextAnalytics() {
  yield* takeEvery(actions.RUN_TEXT_ANALYTICS_REQUEST, function* () {
    const dataTdosForExport = yield* select(getTdosForExport);
    const isSelectedAllTdos = yield* select(isSelectedAll);
    const fullSearchQuery = yield* select(getFullSearchQuery);
    const defaultClusterId = yield* select(getDefaultClusterId);
    const allTdos = yield* select(getSearchResultTdosAll);
    const errorTdoIds = allTdos
      .filter((tdo) => tdo.status === 'error')
      .map((tdo) => tdo.id);
    const dataTdosForExportValid = dataTdosForExport.filter(
      (id) => !errorTdoIds.includes(id)
    );

    const tdoData: any = {};
    if (dataTdosForExportValid.length && !isSelectedAllTdos) {
      tdoData['tdoIds'] = dataTdosForExportValid;
    } else {
      tdoData['searchQuery'] = fullSearchQuery;
    }

    const query = `mutation createJobTextAnalytics($input: CreateJob) {
      createJob(input: $input) {
        id
        status
      }
    }`;
    const input = buildVariablesTextAnalytics(defaultClusterId, tdoData);
    const variables = {
      input,
    };
    const { error } = yield* call(handleRequest, {
      query,
      variables,
    });
    if (error) {
      yield* put(actions.runTextAnalyticsFailed());
      yield* put(
        showNotification('Submitting text analytics job failed', 'error')
      );
      return;
    }
    yield* put(
      showNotification('Submit text analytics job successfully', 'success')
    );
  });
}

function buildVariablesTextAnalytics(defaultClusterId: string, tdoData: any) {
  const startDateTime = new Date().toISOString();
  const stopDateTime = new Date().toISOString();
  return {
    target: {
      startDateTime: `${startDateTime}`,
      stopDateTime: `${stopDateTime}`,
    },
    clusterId: defaultClusterId,
    tasks: [
      {
        engineId: actions.ENGINE_TEXT_ANALYTICS,
        payload: {
          clusterId: defaultClusterId,
          force: true,
          tdoData,
        },
      },
    ],
    routes: [],
  };
}

interface FetchDagTemplatesResponse {
  dagTemplates: {
    records: {
      id: string;
      name: string;
      description: string;
      tags: string[];
      cognitiveCategoryId: string;
    }[];
  };
}
function* watchFetchDagTemplates() {
  yield* takeEvery(actions.FETCH_ENGINE_CATEGORIES_SUCCESS, function* () {
    const query = `query fetchDagTemplates {
      dagTemplates(limit:1000, tags:["${actions.SIMPLE_COGNITIVE_WORKFLOW}"]) {
        records {
          id
          name
          description
          tags
          cognitiveCategoryId
        }
      }
    }
    `;
    const { error, response } = yield* call(
      handleRequest<FetchDagTemplatesResponse>,
      { query }
    );
    const { records = [] } = response?.data?.dagTemplates || {};
    if (error) {
      yield* put(actions.fetchDagTemplatesFailed());
    } else {
      yield* put(
        actions.fetchDagTemplatesSuccess(sortBy(records, 'description'))
      );
    }
  });
}

export default function* root() {
  yield* all([
    fork(watchUploadRequest),
    fork(watchRetryRequest),
    fork(watchRetryDone),
    fork(watchAbortions),
    fork(watchFetchEngineCategories),
    fork(watchFetchLibraries),
    fork(watchFetchEngines),
    fork(watchSaveTemplate),
    fork(watchSaveTemplateSuccess),
    fork(watchFetchContentTemplates),
    fork(watchSaveUploadFile),
    fork(watchSelectFolderUpload),
    fork(watchOpenModalUpload),
    fork(watchcheckLimitAndSaveReprocessFile),
    fork(watchSaveReprocessFile),
    fork(watchSetCurrentFolder),
    fork(watchUpdateTemplate),
    fork(watchRemoveTemplate),
    fork(watchRunTextAnalytics),
    fork(watchFetchDagTemplates),
  ]);
}
