{"name": "illuminate-backend", "version": "1.0.0", "description": "Rest api for illuminate", "main": "index.ts", "repository": "https://github.com/veritone/illuminate-app", "license": "MIT", "scripts": {"compile": "rm -rf dist/src/database/migrations && tsc && node dist/src/start-server.js --key ../local.veritone.com-key.pem --cert ../local.veritone.com.pem", "start:dev": "nodemon -e ts  --exec \"npm run compile\"", "build": "tsc --project ./", "start:prod": "node dist/src/start-server.js", "lint": "eslint .", "lint-fix": "eslint --fix --ext .ts ./src/", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@veritone/core-logger": "^1.0.0", "@veritone/functional-permissions-lib": "^1.0.11", "axios": "^1.8.3", "body-parser": "^1.20.3", "chalk": "^4.1.2", "cors": "^2.8.5", "csv-stringify": "^6.5.1", "express": "^4.21.2", "form-data": "^4.0.1", "form-urlencoded": "^6.1.5", "fs": "^0.0.1-security", "fs-extra": "^11.2.0", "graphql": "^16.9.0", "graphql-request": "^6.1.0", "http": "^0.0.1-security", "http-errors": "^2.0.0", "https": "^1.0.0", "joi": "^17.13.3", "knex": "^3.1.0", "luxon": "^3.5.0", "minimist": "^1.2.8", "mssql": "^11.0.1", "node-cache": "^5.1.2", "p-iteration": "^1.1.8", "ramda": "^0.31.0", "uuid": "^11.0.3", "yaml": "^2.6.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/express-busboy": "^8.0.3", "@types/fs-extra": "^11.0.4", "@types/http-errors": "^2.0.4", "@types/jest": "^29.5.14", "@types/luxon": "^3.4.2", "@types/minimist": "^1.2.5", "@types/node": "^22.15.31", "@types/supertest": "^6.0.3", "eslint": "^9.28.0", "jest": "^29.7.0", "mock-knex": "^0.4.13", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-files": "^1.1.4", "typescript": "^5.7.3", "typescript-eslint": "^8.34.0"}, "resolutions": {"colors": "^1.4.0", "jsonwebtoken": "9.0.2"}, "packageManager": "yarn@4.9.2"}