import { Knex } from 'knex';
import isSafeToMigrate from '../util/isSafeToMigrate';

function createBootstrap({
  dbConnectionPromise,
  log,
}: {
  dbConnectionPromise: Promise<Knex>;
  log: Logger;
}) {
  log.info('running migrations...');
  return dbConnectionPromise
    .then(async (db: Knex) => {
      const safeToMigrate = await isSafeToMigrate(db);
      if (safeToMigrate) {
        return db.migrate.latest();
      } else {
        return db;
      }
    })
    .then((db: Knex) => {
      log.info('running migrations complete', db);
    })
    .then(() => dbConnectionPromise)
    .catch((err: Error) => {
      log.error('running migrations failed.');
      log.error(err);
      throw err;
    });
}

export default createBootstrap;
