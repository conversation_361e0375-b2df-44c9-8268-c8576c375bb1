import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles(() => ({
  container: {
    position: 'fixed',
    top: 162,
    right: 0,
    height: 'calc(100vh - 162px)',
    width: '840px',
    zIndex: 1000,
    borderBottom: 'solid 5px #4D81B7',
    transitionTimingFunction: 'ease-in-out !important',
    '& Mui-Formgroup': {
      padding: '0, 0, 0, 13px',
      borderBottom: 'none',
    },
  },
  checkboxContainer: {
    marginLeft: '30px',
  },
  title: {
    userSelect: 'none',
    fontStyle: 'normal',
    fontSize: '18px',
    lineHeight: '40px',
    textTransform: 'uppercase',
    color: 'rgba(0, 0, 0, 0.6)',
  },
  content: {
    overflow: 'auto',
    overflowX: 'hidden',
    height: 'calc(100vh - 220px)',
  },
  firstBox: {
    padding: '16px 54px 32px 32px',
    display: 'flex',
    alignItems: 'center',
  },
  boxDescription: {
    width: '70%',
  },
  exportTemplateDropdown: {
    width: '30%',
    display: 'flex',
    justifyContent: 'end',
    paddingLeft: '20px',
  },
  manageBasicInfoLabel: {
    fontWeight: 'normal',
    cursor: 'default',
  },
  paragraph: {
    marginTop: 12,
  },
  formControl: {
    display: 'flex',
    maxWidth: 'fit-content',
  },
  formControlTitle: {
    fontWeight: 'bold',
    fontSize: '14px',
    cursor: 'default',
  },
  optionsLabel: {
    fontWeight: 600,
    fontSize: '14px',
    cursor: 'default',
  },
  formControlLabel: {
    fontWeight: 'normal',
    fontSize: '14px',
    cursor: 'default',
    color: 'rgba(0, 0, 0, 0.6)',
  },
  editIcon: {
    fontSize: '16px',
    color: 'black',
    opacity: '.5',
    cursor: 'pointer',
  },
  primaryButton: {
    marginTop: '2px',
    backgroundColor: '#fff',
    padding: '8px 15px',
    fontSize: '14px',
  },
  tabsParent: {
    '& div': {
      padding: '0, 0, 0, 13px',
      borderBottom: 'none',
    },
    '& div button': {
      flex: '0 1 auto !important',
      marginRight: '10px',
    },
  },
  tab: {
    minWidth: 60,
    width: 60,
  },
  tabPanel: {
    background: 'rgba(247, 247, 247, 0.8)',
    borderRadius: '4px',
    margin: '15px',
    padding: '27px',
    width: '990',
    height: 'calc(100vh - 480px)',
    minHeight: '250px',
    overflow: 'auto',
  },
  divider: {
    opacity: 0.9,
  },
  dividerSpan: {
    marginBottom: '12px',
  },
  passwordCloseButton: {
    width: 50,
    margin: '16px',
  },
  padding: {
    padding: '8px',
  },
  margin: {
    margin: '16px',
  },
  tabName: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '55px',
    padding: '0px 24px 0px 32px',
    backgroundColor: 'rgba(247, 247, 247, 0.8)',
  },
  actionIcons: {
    margin: '-19px',
  },
  noShadow: {
    boxShadow: 'none',
    borderTop: `0.5px solid rgba(0, 0, 0, 0.12)`,
    borderLeft: `0.5px solid rgba(0, 0, 0, 0.12)`,
  },
  switch_track: {
    backgroundColor: '#9CA8B4',
  },
  switch_base: {
    color: '#9CA8B4',
    '& :hover': {
      color: '#9CA8B4',
    },
  },
  checked: {
    '& :hover': {
      color: '#1871E8',
    },
  },
}));
