// polyfill for pipe function
interface Pipe {
  <A, R>(fn1: (v: A) => R): (v: A) => R;
  <A, B, R>(fn1: (v: A) => B, fn2: (v: B) => R): (v: A) => R;
  <A, B, C, R>(
    fn1: (v: A) => B,
    fn2: (v: B) => C,
    fn3: (v: C) => R
  ): (v: A) => R;
  <A, B, C, D, R>(
    fn1: (v: A) => B,
    fn2: (v: B) => C,
    fn3: (v: C) => D,
    fn4: (v: D) => R
  ): (v: A) => R;
  <A, B, C, D, E, R>(
    fn1: (v: A) => B,
    fn2: (v: B) => C,
    fn3: (v: C) => D,
    fn4: (v: D) => E,
    fn5: (v: E) => R
  ): (v: A) => R;
  // ... and so on
}

// export const pipe =
//   <T>(...fns: [(v: T) => any, ...((v: any) => any)[]]) =>
//   (x: T) =>
//     fns.reduce((v: any, f) => f(v), x);

export const pipe: Pipe =
  <T, R>(...fns: Array<(arg: T) => T | R>) =>
  (x: T) =>
    fns.reduce((v: any, f) => f(v), x);
