import CheckCircle from '@mui/icons-material/CheckCircle';
import Info from '@mui/icons-material/Info';
import Warning from '@mui/icons-material/Warning';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import { green } from '@mui/material/colors';
import { withStyles, ClassNameMap } from '@mui/styles';
import React from 'react';
import FilePickerHeader from '../FilePickerHeader';
import FileProgressList from '../FileProgressList';
import styles from './styles';

interface File {
  key: string;
  value: {
    name: string;
    type: string;
    size: number;
    percent: number;
    error?: any;
  };
}

interface FileProgressDialogProps {
  percentByFiles?: File[];
  onClose?: () => void;
  onRetryDone?: () => void;
  retryRequest: () => void;
  handleAbort?: (fileKey?: string) => void;
  width?: number;
  height?: number;
  progressMessage?: string;
  completeStatus: 'success' | 'failure' | 'warning' | null;
  classes?: ClassNameMap;
}

const FileProgressDialog: React.FC<FileProgressDialogProps> = ({
  percentByFiles = [],
  onClose,
  onRetryDone,
  retryRequest,
  handleAbort,
  width = 600,
  height,
  progressMessage = '',
  completeStatus,
  classes,
}) => {
  const handleAbortAll = () => {
    handleAbort && handleAbort();
  };

  const handleClose = () => {
    handleAbortAll();
    onClose && onClose();
  };

  const renderProgress = () => {
    const closeFunc = handleAbort ? handleClose : undefined;
    return (
      <div>
        <FilePickerHeader title="Uploading" hideTabs onClose={closeFunc} />
        <div className={classes?.progressListContainer || ''}>
          <FileProgressList
            percentByFiles={percentByFiles}
            handleAbort={handleAbort}
          />
        </div>
      </div>
    );
  };

  const renderComplete = () => {
    const icon = {
      success: (
        <CheckCircle
          classes={{ root: classes?.resolveIcon || '' }}
          style={{ fill: green[500] }}
          data-testtarget="successIcon"
          data-testid="success-icon"
        />
      ),
      failure: (
        <Info
          classes={{ root: classes?.resolveIcon || '' }}
          style={{ color: '#F44336' }}
          data-testtarget="failureIcon"
          data-testid="failure-icon"
        />
      ),
      warning: (
        <Warning
          classes={{ root: classes?.resolveIcon || '' }}
          style={{ color: '#ffc107' }}
          data-testtarget="warnIcon"
          data-testid="warn-icon"
        />
      ),
    }[completeStatus!];

    const completeDialog =
      completeStatus !== 'success' ? (
        <div className={classes?.contentFlexer || ''}>
          <FilePickerHeader
            title={`Upload ${completeStatus}`}
            message={progressMessage}
            titleIcon={icon}
            hideTabs
            onClose={onClose}
          />
          <div className={classes?.contentFlexer || ''}>
            <div className={classes?.progressListContainer || ''}>
              <FileProgressList percentByFiles={percentByFiles} showErrors />
            </div>
            <CardActions className={classes?.resolveActions || ''}>
              <Button onClick={onRetryDone}>Done</Button>
              <Button
                variant="contained"
                color="primary"
                onClick={retryRequest}
              >
                Retry All
              </Button>
            </CardActions>
          </div>
        </div>
      ) : null;

    return completeDialog;
  };

  return (
    <Card className={classes?.container || ''} style={{ width, height }}>
      {completeStatus ? renderComplete() : renderProgress()}
    </Card>
  );
};

export default withStyles(styles)(FileProgressDialog);
