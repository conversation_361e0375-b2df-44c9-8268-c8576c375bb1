interface Point {
  x: number;
  y: number;
}

interface Box {
  x: number;
  y: number;
  width: number;
  height: number;
}

export function pixelXYWidthHeightToPercentagePoly(
  { x, y, width, height }: Box,
  contentWidth: number,
  contentHeight: number
) {
  // translate from internal (x, y, width, height) format to veritone's
  // percentage-based vertex format
  return [
    // top-left
    {
      x: x / contentWidth,
      y: y / contentHeight,
    },
    // top-right
    {
      x: (x + width) / contentWidth,
      y: y / contentHeight,
    },
    // bottom-right
    {
      x: (x + width) / contentWidth,
      y: (y + height) / contentHeight,
    },
    // bottom-left
    {
      x: x / contentWidth,
      y: (y + height) / contentHeight,
    },
  ] as const;
}

export function percentagePolyToPixelXYWidthHeight(
  poly: Point[],
  contentWidth: number,
  contentHeight: number
) {
  const pixelBoundingBox = boundingBox(poly).map(({ x, y }) =>
    percentageToPixelCoords({ x, y, contentWidth, contentHeight })
  ) as unknown as ReturnType<typeof boundingBox>;

  return pixelBoundingBoxToXYWidthHeight(pixelBoundingBox);
}

function boundingBox(vertices: Point[]) {
  const xVals = vertices.map(({ x }) => x);
  const yVals = vertices.map(({ y }) => y);

  const minX = Math.min.apply(null, xVals);
  const maxX = Math.max.apply(null, xVals);
  const minY = Math.min.apply(null, yVals);
  const maxY = Math.max.apply(null, yVals);

  return [
    // top-left
    { x: minX, y: minY },
    // top-right
    { x: maxX, y: minY },
    // bottom-right
    { x: maxX, y: maxY },
    // bottom-left
    { x: minX, y: maxY },
  ] as const;
}

function percentageToPixelCoords({
  x,
  y,
  contentWidth,
  contentHeight,
}: Point & {
  contentWidth: number;
  contentHeight: number;
}) {
  return {
    x: x * contentWidth,
    y: y * contentHeight,
  };
}

function pixelBoundingBoxToXYWidthHeight([
  topLeft,
  topRight,
  _bottomRight,
  bottomLeft,
]: readonly [Point, Point, Point, Point]) {
  return {
    x: topLeft.x,
    y: topLeft.y,
    width: topRight.x - topLeft.x,
    height: bottomLeft.y - topLeft.y,
  };
}
