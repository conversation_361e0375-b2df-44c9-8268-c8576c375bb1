import React, { ComponentType } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide, { SlideProps } from '@mui/material/Slide';

const Transition = React.forwardRef(function Transition(
  props: SlideProps & {
    children?: React.ReactElement;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});
class AlertDialogSlide extends React.Component<Props> {
  render() {
    return (
      <div>
        <Dialog
          open={this.props.open}
          TransitionComponent={Transition as ComponentType<SlideProps>}
          keepMounted
          aria-labelledby="alert-dialog-slide-title"
          aria-describedby="alert-dialog-slide-description"
        >
          <DialogTitle id="alert-dialog-slide-title">
            {'Selected items have been sent to Redaction'}
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-slide-description">
              {
                'Your selection of items has been successfully sent to the Redaction App.'
              }
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            {/* <Button onClick={this.handleClose} color="primary">
                Disagree
                </Button> */}
            <Button onClick={this.props.handleClose} color="primary">
              OK
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}

interface Props {
  open: boolean;
  handleClose?: () => void;
}

export default AlertDialogSlide;
