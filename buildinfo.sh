#!/bin/sh

filename=build-manifest.yml
rm -f $filename

# Fetch the latest tags from the remote repository
git fetch --tags -- https://github.com/veritone/illuminate-app.git

APP_VERSION=$(cat package.json | jq -r '.version')
VERSION_COMMIT=$(git rev-list -n 1 "${APP_VERSION}")
BUILD_NUMBER=$(git rev-list "${VERSION_COMMIT}"..HEAD --count)
VERSION="${APP_VERSION}.${BUILD_NUMBER}"

BUILD_DATE=$(date)
GIT_REPO=$APPLICATION
if [ -z "${GIT_REPO}" ]; then
  GIT_REPO=$(basename `git rev-parse --show-toplevel`)
fi
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
GIT_COMMIT=$(git rev-parse HEAD)
GIT_AUTHOR=$(git show -s --format='%aN' ${GIT_COMMIT})

{
  echo "---"
  echo "version: ${VERSION}"
  echo "build_date: ${BUILD_DATE}"
  echo "git_repo: ${GIT_REPO}"
  echo "git_branch: ${GIT_BRANCH}"
  echo "git_commit: ${GIT_COMMIT}"
  echo "git_author: ${GIT_AUTHOR}"
  echo "git_owner: glc"
  echo "jenkins_build: ${RUN_DISPLAY_URL}"
} > $filename
