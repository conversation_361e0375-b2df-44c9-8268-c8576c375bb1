import React, { Fragment } from 'react';
import IconButton from '@mui/material/IconButton';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import Paper from '@mui/material/Paper';
import Save from '@mui/icons-material/Save';
import CircularProgress from '@mui/material/CircularProgress';
import DeleteIcon from '@mui/icons-material/Close';
import makeStyles from '@mui/styles/makeStyles';
import cx from 'classnames';
import ListEngineSelected from './listEngineSelected';
import styles from './styles';
import {
  EnginesSelected,
  EngineByCategories,
  Template,
  LibrariesByCategories,
} from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);
function EngineSelected({
  librariesByCategories,
  enginesSelected,
  // engineByCategories,
  templateSelected,
  handleChangeTemplates,
  templates,
  handleShowModalSaveTemplate,
  checkValidateLibrary,
  handleExpandClick,
  handleRemoveEngine,
  handleChangeLibrariesEngineSelected,
  handleChangeFieldsEngine,
  handleChangeJobPriority,
  handleRemoveTemplate,
  isReprocess,
}: Props) {
  const classes = useStyles();
  return (
    <Fragment>
      <div className={classes.contentSelectedEngine}>
        <FormControl className={classes.formEngines}>
          <InputLabel
            variant="standard"
            shrink
            className={classes.titleFormSelectEngine}
          >
            Your Selected Engines
          </InputLabel>
          <Select
            value={templateSelected}
            displayEmpty
            onChange={(event) =>
              handleChangeTemplates(
                event as React.ChangeEvent<HTMLInputElement>
              )
            }
            className={classes.titleSelectEngine}
            data-test="select-template-engine"
            data-testid="select-template-engine"
            variant="standard"
          >
            <MenuItem value={''}>{'Select Existing Template'}</MenuItem>
            {templates.map((item) => {
              return (
                <MenuItem
                  key={item.id}
                  value={item.id}
                  className={classes.titleEngineSelected}
                >
                  {item.name}
                  {item.loadingRemoveTemplate ? (
                    <CircularProgress
                      className={classes.iconLoadingRemoveTemplate}
                      size={20}
                    />
                  ) : (
                    <IconButton
                      className={(classes as any).iconRemoveTemplate}
                      disabled={templateSelected === item.id}
                      onClick={(event) => handleRemoveTemplate(event, item.id)}
                      size="large"
                    >
                      <DeleteIcon className={(classes as any).iconRemoves} />
                    </IconButton>
                  )}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
        <IconButton
          className={classes.iconSaveTemplate}
          onClick={handleShowModalSaveTemplate}
          disabled={!enginesSelected.length}
          data-test="show-modal-save-template-engine"
          data-testid="show-modal-save-template-engine"
          size="large"
        >
          <Save />
        </IconButton>
      </div>

      <Paper
        variant="outlined"
        square
        className={cx(classes.listSelectedEngines, {
          [classes.listSelectedEnginesReprocess]: isReprocess,
        })}
        data-testid="list-engine-paper"
      >
        {enginesSelected &&
          enginesSelected.map((item) => {
            return (
              <ListEngineSelected
                key={item.categoryId}
                item={item}
                // engineByCategories={engineByCategories}
                handleExpandClick={handleExpandClick}
                checkValidateLibrary={checkValidateLibrary}
                librariesByCategories={librariesByCategories}
                handleRemoveEngine={handleRemoveEngine}
                handleChangeLibrariesEngineSelected={
                  handleChangeLibrariesEngineSelected
                }
                handleChangeFieldsEngine={handleChangeFieldsEngine}
                handleChangeJobPriority={handleChangeJobPriority}
              />
            );
          })}
      </Paper>
    </Fragment>
  );
}
interface Props {
  librariesByCategories: LibrariesByCategories;
  enginesSelected: EnginesSelected[];
  engineByCategories: EngineByCategories;
  templateSelected: string;
  handleChangeTemplates: (event: React.ChangeEvent<HTMLInputElement>) => void;
  templates: Template[];
  handleShowModalSaveTemplate: () => void;
  checkValidateLibrary: boolean;
  handleExpandClick: (event: React.MouseEvent<HTMLElement>) => void;
  handleRemoveEngine: (event: React.MouseEvent<HTMLElement>) => void;
  handleChangeLibrariesEngineSelected: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeFieldsEngine: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleChangeJobPriority: (
    event: React.ChangeEvent<HTMLInputElement>,
    engineId: string
  ) => void;
  handleRemoveTemplate: (
    event: React.MouseEvent<HTMLElement>,
    id: string
  ) => void;
  isReprocess: boolean;
}
export default EngineSelected;
