const generateTypesFromKnex = require('./generateTypes');

generateTypesFromKnex({
  client: 'mssql',
  connection: {
    host: 'powerbi-contact.database.usgovcloudapi.net',
    port: 1433,
    user: 'powerbiadmin',
    password: 'yYJgX2aMMb95G2W',
    database: 'powerbi-contact',
    connectionTimeout: 30000,
    options: {
      encrypt: true,
    },
  },
  migrations: {
    directory: '../src/database/migrations',
  },
  pool: {
    min: 2,
    max: 10,
  },
  useNullAsDefault: true,
});
