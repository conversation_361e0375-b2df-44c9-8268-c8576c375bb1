import { callGQL } from '../../../util/callGraphql';
import { ForbiddenError, UnauthorizedError } from '../errors/';
import { Context } from '../../types';

interface ValidateTokenResponse {
  validateToken: {
    token: string;
  };
}

const validateTokenQuery = (token: string) => `
  mutation validateToken {
    validateToken(token:"${token}") {
      token
    }
  }
`;

const validateToken = async (context: Context) => {
  const { req, log } = context;

  const bearerHeader = req.get('Authorization');
  if (bearerHeader) {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];
    if (bearerToken) {
      try {
        const query = validateTokenQuery(bearerToken);
        const headers = { Authorization: req.headers.authorization };
        const response = await callGQL<ValidateTokenResponse>(
          context,
          headers,
          query
        );
        if (response?.validateToken?.token) {
          return context;
        }
      } catch (err) {
        log.error('API Token Validation failed (d)');
        throw new ForbiddenError(err);
      }
    } else {
      log.error('API Token Validation failed (e)');
      throw new UnauthorizedError('API Token Validation failed (e)');
    }
  } else {
    log.error('API Token Validation failed (f)');
    throw new UnauthorizedError('API Token Validation failed (f)');
  }
};

export default validateToken;
