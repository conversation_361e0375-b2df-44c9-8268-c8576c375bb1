import Knex from 'knex';
import createHealthCheckApp from '../health-check';
import createPowerBiApp from '../powerbi';
import createExpressApp from '../express';
import createCache from '../../cache';
import { Config } from "../../config";

jest.mock('knex');

const deps = {
  db: Promise.resolve(Knex({ client: 'mssql' })),
  log: {
    error: (p1: string | Error, p2?: string | Error) => console.error(p1, p2 ?? ''),
    info: (p1: string | Error, p2?: string | Error) => console.log(p1, p2 ?? ''),
    debug: (p1: string | Error, p2?: string | Error) => console.log(p1, p2 ?? '')
  },
  cache: createCache()
};

const config: Config = {
  ...deps,
  powerbiApp: createPowerBiApp(deps),
  healthCheckApp: createHealthCheckApp()
};

export default createExpressApp({ config });
