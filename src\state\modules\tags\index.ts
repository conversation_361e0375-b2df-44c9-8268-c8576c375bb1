import * as Sentry from '@sentry/react';
import { size, isEmpty } from 'lodash';
import { fetchGraphQLApiThrowError } from '../../../helpers/apiHelper';
import { prepareSearchQueryForEngine } from '../search/query';
import { TDO } from '../universal/TDO';
import { createAction } from '@reduxjs/toolkit';

export const APPLY_TAGS_TO_TDO_REQUEST = createAction<{
  tdoId: string;
  tags: { value: string; label: string }[];
  tdos?: TDO[];
  searchQuery?: any;
}>('apply tags to tdo request');

export const APPLY_TAGS_TO_TDO_SUCCESS = createAction<{
  message: string;
}>('apply tags to tdo success');
export const APPLY_TAGS_TO_TDO_FAILURE = createAction<{
  error: any;
  message: string;
}>('apply tags to tdo failure');

export const TDO_TAGS_UPDATED = createAction<TagsUpdate>('tdo tags updated');
interface Tag {
  value: string;
  label?: string;
}
interface TdoTag {
  tdoId: string;
  tags: Tag[];
}
export interface TagsUpdate {
  [key: string]: {
    id: string;
    details: { tags: { label: string; value: string }[] };
  };
}

interface BulkTagsUpdate {
  createJob: {
    id: string;
    status: string;
  };
}

export async function applyTags(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  payload: {
    tdoId: string;
    tags: Tag[];
    tdos?: TDO[];
    searchQuery?: any;
  },
  clusterId: string,
  engineId: string
): Promise<{
  type: 'api' | 'job';
  apiResults?: TagsUpdate;
  jobResults?: BulkTagsUpdate;
} | null> {
  if (payload.tdoId) {
    // for single tdos, it is possible that tags are removed or added. So, we
    // just update tdo using api with the tags in the payload
    const tdoTags = [{ tdoId: payload.tdoId, tags: payload.tags }];
    const results = await updateTdoTag(endpoint, token, veritoneAppId, tdoTags);
    return { type: 'api', apiResults: results };
  }
  const tdoList = payload.tdos?.filter((tdo) => tdo.status !== 'error');
  if (tdoList && tdoList.length > 0 && tdoList.length <= 100) {
    // for more 0 - 100 tdos, update the tdo tag using the api.
    // there is no tags removal. So,just merge the new tags with old tags
    const results = await addTdoNewTag(
      endpoint,
      token,
      veritoneAppId,
      tdoList,
      payload.tags
    );
    return { type: 'api', apiResults: results };
  }

  if (payload.searchQuery || (tdoList && tdoList.length > 100)) {
    // for selecting all tdo(query for tdos) or selectinh more than 100 tdos,
    // the bulk tag engine is used and the bulk tag engine job is created
    const tdoIds = tdoList?.map((tdo) => tdo.id);
    const results = await applyBulkTag(
      endpoint,
      token,
      veritoneAppId,
      payload.tags,
      payload.searchQuery,
      tdoIds,
      clusterId,
      engineId
    );
    return { type: 'job', jobResults: results };
  }
  return null;
}

export async function addTdoNewTag(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  tdoList: TDO[],
  newTags: Tag[]
) {
  const tdoTags = addTags(tdoList, newTags);
  const result = await updateTdoTag(endpoint, token, veritoneAppId, tdoTags);
  return result;
}

export async function updateTdoTag(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  tdoTags: TdoTag[]
) {
  const { query, variables } = buildTagTdoQuery(tdoTags);
  const response = await fetchGraphQLApiThrowError<
    Record<
      string,
      { id: string; details: { tags: { label: string; value: string }[] } }
    >
  >({
    endpoint,
    query,
    variables,
    token,
    veritoneAppId,
  });
  return response.data;
}

export function addTags(tdoList: TDO[], newTags: Tag[]) {
  const tagTdos: TdoTag[] = [];
  const errorTdos: TdoTag[] = [];
  tdoList.forEach((tdo) => {
    const tdoId = tdo.id;
    let oldTags = tdo?.details?.tags || [];
    // Check if output of get operation is not an array
    if (!Array.isArray(oldTags)) {
      errorTdos.push({ tdoId, tags: oldTags });
      oldTags = [];
    }

    const newMergedTags = mergeTags(oldTags, newTags);
    tagTdos.push({ tdoId, tags: newMergedTags });
  });
  if (errorTdos.length) {
    Sentry.captureMessage(
      `tag should be an array - ${errorTdos.map((e) => e.tdoId)}`
    );
    console.error('tag should be an array.', errorTdos);
  }
  return tagTdos;
}

export function buildTagTdoQuery(tdoTags: TdoTag[]) {
  const inputStr: any = [];
  const updateQuery: any = [];
  const variables: any = {};
  tdoTags.forEach(({ tdoId, tags }: { tdoId: string; tags: Tag[] }) => {
    inputStr.push(`$input${tdoId}: UpdateTDO`);
    updateQuery.push(
      `updateTDO_${tdoId}:updateTDO(input: $input${tdoId}) {id, details}`
    );
    variables[`input${tdoId}`] = {
      id: tdoId,
      details: {
        tags: tags,
      },
    };
  });
  const query = `mutation updateTDOTag(${inputStr.join(',')}){
  ${updateQuery.join('\n  ')}
}`;
  return { query, variables };
}

export function mergeTags(tags1: Tag[], tags2: Tag[]) {
  if (size(tags1) === 0 && size(tags2) === 0) {
    return [];
  }
  if (size(tags1) === 0) {
    return [...tags2];
  }
  if (size(tags2) === 0) {
    return [...tags1];
  }
  const newTags = [...tags1];
  for (const tag2 of tags2) {
    let isNew = true;
    for (let i = 0; i < newTags.length; i++) {
      const newTag: any = newTags[i];
      if (newTag.value === tag2.value) {
        const tag = {
          ...newTag,
          ...tag2,
        };
        newTags[i] = tag;
        isNew = false;
        break;
      }
    }
    if (isNew) {
      newTags.push(tag2);
    }
  }
  return newTags;
}

async function applyBulkTag(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  tags: Tag[],
  searchQuery: any,
  tdoIds: string[] | undefined,
  clusterId: string,
  engineId: string
) {
  if (!engineId) {
    throw 'bulk tag engine id is missing for bulk tag';
  }
  const startDateTime = new Date().toISOString();
  const stopDateTime = new Date().toISOString();
  const payload = buildBulkTagPayload(tags, searchQuery, tdoIds);
  const variables = { payload: payload };
  const query = `mutation tagTDO($payload: JSONData!) {
    createJob(input: {
      target:{
        startDateTime: "${startDateTime}",
        stopDateTime:"${stopDateTime}",
        addToIndex: false
      }
      clusterId: "${clusterId}"
      tasks: [
        {
          engineId: "${engineId}"
          payload:  $payload
        }
      ]
      routes:[]
    }) {
      id
      status
    }
  }
  `;

  const response = await fetchGraphQLApiThrowError<{
    createJob: {
      id: string;
      status: string;
    };
  }>({
    endpoint,
    query,
    variables,
    token,
    veritoneAppId,
  });
  return response.data;
}

export function buildBulkTagPayload(
  tags: Tag[],
  searchQuery: any,
  tdoIds: string[] | undefined
) {
  const updatedSearchQuery = prepareSearchQueryForEngine(searchQuery);
  const payload: {
    tagData: {
      tags: Tag[];
      tdoIds?: string[] | undefined;
      searchQuery?: any;
    };
  } = {
    tagData: {
      tags,
    },
  };

  if (!isEmpty(tdoIds)) {
    payload.tagData.tdoIds = tdoIds;
  } else {
    if (updatedSearchQuery !== null) {
      payload.tagData.searchQuery = updatedSearchQuery;
    }
  }
  return payload;
}
