import { modules } from '@veritone/glc-redux';
import { get } from 'lodash';
const {
  config: { getConfig },
} = modules;

const getAdminUrl = (state: any) => {
  const config = getConfig<Window['config']>(state);
  const orgId = get(state, 'user.user.organization.organizationId');
  const coreAdminUrl = config.coreAdminUrl.replace('{organizationId}', orgId);
  return coreAdminUrl;
};

export default getAdminUrl;
