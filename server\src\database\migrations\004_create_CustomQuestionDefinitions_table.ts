import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('CustomQuestionDefinitions').then(async (tableExists: boolean) => {
      if (!tableExists) {
        await knex.schema.raw(`CREATE TABLE CustomQuestionDefinitions (
          id int IDENTITY(1,1) PRIMARY KEY,
          contactId varchar(64),
          orgId smallint,
          component varchar(128),
          title varchar(2048),
          resultPath varchar(2048),
          options varchar(MAX),
          isMultiSelect bit,
          required bit,
          disabled bit,
          disabledDate DATETIME,
        )`);
        return true;
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('CustomQuestionDefinitions')
  ])
