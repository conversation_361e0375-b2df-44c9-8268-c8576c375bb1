# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@azure/abort-controller@npm:^1.0.0, @azure/abort-controller@npm:^1.0.4":
  version: 1.1.0
  resolution: "@azure/abort-controller@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.2.0"
  checksum: 10c0/bb79f0faaa9e9c1ae3c4ec2523ea23ee0879cc491abb4b3ac2dd56c2cc2dfe4b7e8522ffa866d39c7145c0dd61387711368afe0d4eb6534daba7b67ed0a2a730
  languageName: node
  linkType: hard

"@azure/abort-controller@npm:^2.0.0":
  version: 2.1.2
  resolution: "@azure/abort-controller@npm:2.1.2"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/3771b6820e33ebb56e79c7c68e2288296b8c2529556fbd29cf4cf2fbff7776e7ce1120072972d8df9f1bf50e2c3224d71a7565362b589595563f710b8c3d7b79
  languageName: node
  linkType: hard

"@azure/core-auth@npm:^1.3.0, @azure/core-auth@npm:^1.4.0":
  version: 1.5.0
  resolution: "@azure/core-auth@npm:1.5.0"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.1.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/b141a542cad2d36ebe8e151967bc3e425939c28ab41819268b5e0beef557fbf6425030ded6e89992f07cea87f609788985a1e6e97bb1c987f1010d53fcb123d5
  languageName: node
  linkType: hard

"@azure/core-auth@npm:^1.5.0, @azure/core-auth@npm:^1.7.2":
  version: 1.8.0
  resolution: "@azure/core-auth@npm:1.8.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-util": "npm:^1.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/db9d78da0ae9c43258bc735c7eeda598370559c0d0146293cdfc096191712ee6e0fcc1b1da105bd095df0d034603eead949a8598b2209880a14bad822a5f81d2
  languageName: node
  linkType: hard

"@azure/core-client@npm:^1.3.0, @azure/core-client@npm:^1.5.0":
  version: 1.7.3
  resolution: "@azure/core-client@npm:1.7.3"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-auth": "npm:^1.4.0"
    "@azure/core-rest-pipeline": "npm:^1.9.1"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.0.0"
    "@azure/logger": "npm:^1.0.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/410f03e6ee7e76c19c7c18790b87ec6e5a10c9de4023215b02496e0e5e338359b728e4cc9a71a38dc524ce7d06803ab27b042e011ec2dd027638b38bf09fa8db
  languageName: node
  linkType: hard

"@azure/core-client@npm:^1.9.2":
  version: 1.9.2
  resolution: "@azure/core-client@npm:1.9.2"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-auth": "npm:^1.4.0"
    "@azure/core-rest-pipeline": "npm:^1.9.1"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.6.1"
    "@azure/logger": "npm:^1.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4dab1f3b070f7c2c5a8390f81c7afdf31c030ad0599e75e16b9684959fb666cb57d34b63977639a60a7535f63f30a8a708210e8e48ff68a30732b7518044ebce
  languageName: node
  linkType: hard

"@azure/core-http-compat@npm:^1.3.0":
  version: 1.3.0
  resolution: "@azure/core-http-compat@npm:1.3.0"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.4"
    "@azure/core-client": "npm:^1.3.0"
    "@azure/core-rest-pipeline": "npm:^1.3.0"
  checksum: 10c0/a55f4ea0f70132c05ee50d3c3bb2785804b667974109146461895ca7dde093fc37a307f5d0beb44292ceff10ae7c6ea246a9e2b61614dcd51e4d718c30f134df
  languageName: node
  linkType: hard

"@azure/core-lro@npm:^2.2.0":
  version: 2.5.4
  resolution: "@azure/core-lro@npm:2.5.4"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.2.0"
    "@azure/logger": "npm:^1.0.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/65d65aa5b90bbdd98bbee4093157618c7e3d8ac3d155f29f5d6b3ca9e2e3a8a49eb15b24d551dd8e51afd634410186b594fc2fcb24e982acd04ca16c279eca7c
  languageName: node
  linkType: hard

"@azure/core-paging@npm:^1.1.1":
  version: 1.5.0
  resolution: "@azure/core-paging@npm:1.5.0"
  dependencies:
    tslib: "npm:^2.2.0"
  checksum: 10c0/634a1c6540d16cf047035f19d8866b561e8036059aec2ce0304d77999404e95458f47c33a3f523d20cefa329bad33ccc6bf7450efa8f7d77ceb3419d519b1c48
  languageName: node
  linkType: hard

"@azure/core-rest-pipeline@npm:^1.1.0, @azure/core-rest-pipeline@npm:^1.3.0, @azure/core-rest-pipeline@npm:^1.8.1, @azure/core-rest-pipeline@npm:^1.9.1":
  version: 1.12.2
  resolution: "@azure/core-rest-pipeline@npm:1.12.2"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-auth": "npm:^1.4.0"
    "@azure/core-tracing": "npm:^1.0.1"
    "@azure/core-util": "npm:^1.3.0"
    "@azure/logger": "npm:^1.0.0"
    form-data: "npm:^4.0.0"
    http-proxy-agent: "npm:^5.0.0"
    https-proxy-agent: "npm:^5.0.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/eb4b015b385486c9b096d00d9636baf4eaf94343b21fc9e8467d78262eae2518d101194c6e2e44daa8d3a10e4b5131899037a7c019b289c135bb3f967396972c
  languageName: node
  linkType: hard

"@azure/core-tracing@npm:^1.0.0, @azure/core-tracing@npm:^1.0.1":
  version: 1.0.1
  resolution: "@azure/core-tracing@npm:1.0.1"
  dependencies:
    tslib: "npm:^2.2.0"
  checksum: 10c0/bc8ff602392b76e9a1b38be9b56f6bcb33b1c44fc0d19196cbf6f7b5834b264d6a5989db867b77884ffa9b3ff29633120b524cec64f5bbc89c72a1f89fdb4ea1
  languageName: node
  linkType: hard

"@azure/core-util@npm:^1.0.0, @azure/core-util@npm:^1.1.0, @azure/core-util@npm:^1.2.0, @azure/core-util@npm:^1.3.0":
  version: 1.6.1
  resolution: "@azure/core-util@npm:1.6.1"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/e0f6148dc02b3d722ee9030a774f9711dbd393a02e4a09b57bcb27a916ee47b08403b51b264008e66b016894b15041bfc0d9d8fdb3d4e2f0bfdc6c47c2bee107
  languageName: node
  linkType: hard

"@azure/core-util@npm:^1.6.1":
  version: 1.10.0
  resolution: "@azure/core-util@npm:1.10.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/94c89ca7b4d44f85cd39e1ce77b4f4b2c2aa2bdc8230454816ba98fb5333bacd648d5ffcd73518b1d1f40405a40c3411987cfed4d260c62a07b740c7f1d77792
  languageName: node
  linkType: hard

"@azure/identity@npm:^4.2.1":
  version: 4.4.1
  resolution: "@azure/identity@npm:4.4.1"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-auth": "npm:^1.5.0"
    "@azure/core-client": "npm:^1.9.2"
    "@azure/core-rest-pipeline": "npm:^1.1.0"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.3.0"
    "@azure/logger": "npm:^1.0.0"
    "@azure/msal-browser": "npm:^3.14.0"
    "@azure/msal-node": "npm:^2.9.2"
    events: "npm:^3.0.0"
    jws: "npm:^4.0.0"
    open: "npm:^8.0.0"
    stoppable: "npm:^1.1.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/7c660404387ee4f13dea226053868b2e58d1797898a583cf0a8877809f5a50fce1d0fbc93e1b740f57f8bc258a7586487ab0de2ecb7186e743326a5b99c99bdb
  languageName: node
  linkType: hard

"@azure/keyvault-keys@npm:^4.4.0":
  version: 4.7.2
  resolution: "@azure/keyvault-keys@npm:4.7.2"
  dependencies:
    "@azure/abort-controller": "npm:^1.0.0"
    "@azure/core-auth": "npm:^1.3.0"
    "@azure/core-client": "npm:^1.5.0"
    "@azure/core-http-compat": "npm:^1.3.0"
    "@azure/core-lro": "npm:^2.2.0"
    "@azure/core-paging": "npm:^1.1.1"
    "@azure/core-rest-pipeline": "npm:^1.8.1"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.0.0"
    "@azure/logger": "npm:^1.0.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/d1e209ceb3108920950213ed4b442e2f586c4fcf24a3a8b121f6f27ec8847b751cf9e515879f4640a479b4cca0d65158d6b45f7884d6e60a084e340b86e3fc62
  languageName: node
  linkType: hard

"@azure/logger@npm:^1.0.0":
  version: 1.0.4
  resolution: "@azure/logger@npm:1.0.4"
  dependencies:
    tslib: "npm:^2.2.0"
  checksum: 10c0/15af549d8dbf027e7520fc65432577d52c73b5a30bce2c218f97ab7104b037ae6c31d9a5bfa6bc9c7873c05693261ab8d7f5b95c65db6b1a7c8624c7b655afc6
  languageName: node
  linkType: hard

"@azure/msal-browser@npm:^3.14.0":
  version: 3.23.0
  resolution: "@azure/msal-browser@npm:3.23.0"
  dependencies:
    "@azure/msal-common": "npm:14.14.2"
  checksum: 10c0/ced1f98b09d638915c50f436802bd5c5ac127d7c4aa73f901605d1e5fba7f813588295dfb97d5f3895c866883d8da0bd17c4e593fa00e1f1041876d2b1d9513f
  languageName: node
  linkType: hard

"@azure/msal-common@npm:14.14.2":
  version: 14.14.2
  resolution: "@azure/msal-common@npm:14.14.2"
  checksum: 10c0/b2b7cd72a7748ce3f8df4dd3bc7ab9b0faac3f292923459779a1263ccc0ceb251a48e7244919ac4f4cbb6cc73eaac7e196dbea0d865c2bf9d40a02a570239b30
  languageName: node
  linkType: hard

"@azure/msal-node@npm:^2.9.2":
  version: 2.13.1
  resolution: "@azure/msal-node@npm:2.13.1"
  dependencies:
    "@azure/msal-common": "npm:14.14.2"
    jsonwebtoken: "npm:^9.0.0"
    uuid: "npm:^8.3.0"
  checksum: 10c0/9781a2a20f103090ab0ce60a4ca690a6ae2f1f1e7f55d5d1e188032a4e8c1242bc254d34a116e3df343a537b3807b53ead49267bdfb6bbb423e4c7f326dbe558
  languageName: node
  linkType: hard

"@js-joda/core@npm:^5.6.1":
  version: 5.6.3
  resolution: "@js-joda/core@npm:5.6.3"
  checksum: 10c0/0d49154592f1f32db0a57cf33ccc254a9518cae69eae3f625fad4ec26ef8b386981c8bf3c41831708048d77be3bdf55e09fda44275f458ad8d19c16c8a986a06
  languageName: node
  linkType: hard

"@tediousjs/connection-string@npm:^0.5.0":
  version: 0.5.0
  resolution: "@tediousjs/connection-string@npm:0.5.0"
  checksum: 10c0/e09a7495cfdb83849427ae98a3c55b603b87265945373d3fa1bfece843db7c5be145f75d7a6adc01ecad71977ba0a6b05909ce6d44749c9f92b8238db4811848
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: 10c0/073bfa548026b1ebaf1659eb8961e526be22fa77139b10d60e712f46d2f0f05f4e6c8bec62a087d41088ee9e29faa7f54838568e475ab2f776171003c3920858
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=18":
  version: 22.13.1
  resolution: "@types/node@npm:22.13.1"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10c0/d4e56d41d8bd53de93da2651c0a0234e330bd7b1b6d071b1a94bd3b5ee2d9f387519e739c52a15c1faa4fb9d97e825b848421af4b2e50e6518011e7adb4a34b7
  languageName: node
  linkType: hard

"@types/readable-stream@npm:^4.0.0":
  version: 4.0.15
  resolution: "@types/readable-stream@npm:4.0.15"
  dependencies:
    "@types/node": "npm:*"
    safe-buffer: "npm:~5.1.1"
  checksum: 10c0/7424f747dd62e5c0f686a447e891064b1efdce44a7d55304ac4690b4d240ad194481083f0b07419d5e0295c79d81f0db2a677482c50954c1bbc948cbd40d8725
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"bl@npm:^6.0.11":
  version: 6.0.15
  resolution: "bl@npm:6.0.15"
  dependencies:
    "@types/readable-stream": "npm:^4.0.0"
    buffer: "npm:^6.0.3"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^4.2.0"
  checksum: 10c0/213ff3e4741350e468a1d9684d92a16112507d43bc41d8e06109554067c7b9548413f1fecae7ba5dcfad08b284e9b620e33cb0b18b6a9fc6c8d868f2d3e428b7
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 10c0/fb2294e64d23c573d0dd1f1e7a466c3e978fe94a4e0f8183937912ca374619773bef8e2aceb854129d2efecbbc515bbd0cc78d2734a3e3031edb0888531bbc8e
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"colorette@npm:2.0.19":
  version: 2.0.19
  resolution: "colorette@npm:2.0.19"
  checksum: 10c0/2bcc9134095750fece6e88167011499b964b78bf0ea953469130ddb1dba3c8fe6c03debb0ae181e710e2be10900d117460f980483a7df4ba4a1bac3b182ecb64
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 10c0/53f33d8927758a911094adadda4b2cbac111a5b377d8706700587650fd8f45b0bbe336de4b5c3fe47fd61f420a3d9bd452b6e0e6e5600a7e74d7bf0174f6efe3
  languageName: node
  linkType: hard

"commander@npm:^11.0.0":
  version: 11.1.0
  resolution: "commander@npm:11.1.0"
  checksum: 10c0/13cc6ac875e48780250f723fb81c1c1178d35c5decb1abb1b628b3177af08a8554e76b2c0f29de72d69eef7c864d12613272a71fabef8047922bc622ab75a179
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.3":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/ebfbf19d4b8be938f4dd4a83b8788385da353d63307ede301a9252f9f7f88672e76f2191618fd8edfc2f24679236064176fab0b78131b161ee73daa37125408c
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: 10c0/afd02e6ca91ffa813e1108b5e7756566173d6bc0d1eb951cb44d6b21702ec17c1cf116cfe75d4a2b02e05acb0b808a7a9387d0d1ca5cf9c04ad03a8445c3e46d
  languageName: node
  linkType: hard

"esm@npm:^3.2.25":
  version: 3.2.25
  resolution: "esm@npm:3.2.25"
  checksum: 10c0/8e60e8075506a7ce28681c30c8f54623fe18a251c364cd481d86719fc77f58aa055b293d80632d9686d5408aaf865ffa434897dc9fd9153c8b3f469fad23f094
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"events@npm:^3.0.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/cb6f3ac49180be03ff07ba3ff125f9eba2ff0b277fb33c7fc47569fc5e616882c5b1c69b9904c4c4187e97dd0419dd03b134174756f296dec62041e6527e2c6e
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"getopts@npm:2.3.0":
  version: 2.3.0
  resolution: "getopts@npm:2.3.0"
  checksum: 10c0/edbcbd7020e9d87dc41e4ad9add5eb3873ae61339a62431bd92a461be2c0eaa9ec33b6fd0d67fa1b44feedffcf1cf28d6f9dbdb7d604cb1617eaba146a33cbca
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.0
  resolution: "hasown@npm:2.0.0"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/5d415b114f410661208c95e7ab4879f1cc2765b8daceff4dc8718317d1cb7b9ffa7c5d1eafd9a4389c9aab7445d6ea88e05f3096cb1e529618b55304956b87fc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": "npm:2"
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/32a05e413430b2c1e542e5c74b38a9f14865301dd69dff2e53ddb684989440e3d2ce0c4b64d25eb63cf6283e6265ff979a61cf93e3ca3d23047ddfdc8df34a32
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"interpret@npm:^2.2.0":
  version: 2.2.0
  resolution: "interpret@npm:2.2.0"
  checksum: 10c0/c0ef90daec6c4120bb7a226fa09a9511f6b5618aa9c94cf4641472f486948e643bb3b36efbd0136bbffdee876435af9fdf7bbb4622f5a16778eed5397f8a1946
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.13.1
  resolution: "is-core-module@npm:2.13.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: 10c0/2cba9903aaa52718f11c4896dabc189bab980870aae86a62dc0d5cedb546896770ee946fb14c84b7adf0735f5eaea4277243f1b95f5cefa90054f92fbcac2518
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"js-md4@npm:^0.3.2":
  version: 0.3.2
  resolution: "js-md4@npm:0.3.2"
  checksum: 10c0/8313e00c45f710a53bdadc199c095b48ebaf54ea7b8cdb67a3f1863c270a5e9d0f89f204436b73866002af8c7ac4cacc872fdf271fc70e26614e424c7685b577
  languageName: node
  linkType: hard

"jsonwebtoken@npm:^9.0.0":
  version: 9.0.2
  resolution: "jsonwebtoken@npm:9.0.2"
  dependencies:
    jws: "npm:^3.2.2"
    lodash.includes: "npm:^4.3.0"
    lodash.isboolean: "npm:^3.0.3"
    lodash.isinteger: "npm:^4.0.4"
    lodash.isnumber: "npm:^3.0.3"
    lodash.isplainobject: "npm:^4.0.6"
    lodash.isstring: "npm:^4.0.1"
    lodash.once: "npm:^4.0.0"
    ms: "npm:^2.1.1"
    semver: "npm:^7.5.4"
  checksum: 10c0/d287a29814895e866db2e5a0209ce730cbc158441a0e5a70d5e940eb0d28ab7498c6bf45029cc8b479639bca94056e9a7f254e2cdb92a2f5750c7f358657a131
  languageName: node
  linkType: hard

"jwa@npm:^1.4.1":
  version: 1.4.1
  resolution: "jwa@npm:1.4.1"
  dependencies:
    buffer-equal-constant-time: "npm:1.0.1"
    ecdsa-sig-formatter: "npm:1.0.11"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/5c533540bf38702e73cf14765805a94027c66a0aa8b16bc3e89d8d905e61a4ce2791e87e21be97d1293a5ee9d4f3e5e47737e671768265ca4f25706db551d5e9
  languageName: node
  linkType: hard

"jwa@npm:^2.0.0":
  version: 2.0.0
  resolution: "jwa@npm:2.0.0"
  dependencies:
    buffer-equal-constant-time: "npm:1.0.1"
    ecdsa-sig-formatter: "npm:1.0.11"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/6baab823b93c038ba1d2a9e531984dcadbc04e9eb98d171f4901b7a40d2be15961a359335de1671d78cb6d987f07cbe5d350d8143255977a889160c4d90fcc3c
  languageName: node
  linkType: hard

"jws@npm:^3.2.2":
  version: 3.2.2
  resolution: "jws@npm:3.2.2"
  dependencies:
    jwa: "npm:^1.4.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/e770704533d92df358adad7d1261fdecad4d7b66fa153ba80d047e03ca0f1f73007ce5ed3fbc04d2eba09ba6e7e6e645f351e08e5ab51614df1b0aa4f384dfff
  languageName: node
  linkType: hard

"jws@npm:^4.0.0":
  version: 4.0.0
  resolution: "jws@npm:4.0.0"
  dependencies:
    jwa: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/f1ca77ea5451e8dc5ee219cb7053b8a4f1254a79cb22417a2e1043c1eb8a569ae118c68f24d72a589e8a3dd1824697f47d6bd4fb4bebb93a3bdf53545e721661
  languageName: node
  linkType: hard

"knex@npm:^3.1.0":
  version: 3.1.0
  resolution: "knex@npm:3.1.0"
  dependencies:
    colorette: "npm:2.0.19"
    commander: "npm:^10.0.0"
    debug: "npm:4.3.4"
    escalade: "npm:^3.1.1"
    esm: "npm:^3.2.25"
    get-package-type: "npm:^0.1.0"
    getopts: "npm:2.3.0"
    interpret: "npm:^2.2.0"
    lodash: "npm:^4.17.21"
    pg-connection-string: "npm:2.6.2"
    rechoir: "npm:^0.8.0"
    resolve-from: "npm:^5.0.0"
    tarn: "npm:^3.0.2"
    tildify: "npm:2.0.0"
  peerDependenciesMeta:
    better-sqlite3:
      optional: true
    mysql:
      optional: true
    mysql2:
      optional: true
    pg:
      optional: true
    pg-native:
      optional: true
    sqlite3:
      optional: true
    tedious:
      optional: true
  bin:
    knex: bin/cli.js
  checksum: 10c0/d8a1f99fad143c6057e94759b2ae700ae661a0b0b2385f643011962ef501dcc7b32cfdb5bda66ef81283ca56f13630f47691c579ce66ad0e8128e209533c3785
  languageName: node
  linkType: hard

"lodash.includes@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.includes@npm:4.3.0"
  checksum: 10c0/7ca498b9b75bf602d04e48c0adb842dfc7d90f77bcb2a91a2b2be34a723ad24bc1c8b3683ec6b2552a90f216c723cdea530ddb11a3320e08fa38265703978f4b
  languageName: node
  linkType: hard

"lodash.isboolean@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isboolean@npm:3.0.3"
  checksum: 10c0/0aac604c1ef7e72f9a6b798e5b676606042401dd58e49f051df3cc1e3adb497b3d7695635a5cbec4ae5f66456b951fdabe7d6b387055f13267cde521f10ec7f7
  languageName: node
  linkType: hard

"lodash.isinteger@npm:^4.0.4":
  version: 4.0.4
  resolution: "lodash.isinteger@npm:4.0.4"
  checksum: 10c0/4c3e023a2373bf65bf366d3b8605b97ec830bca702a926939bcaa53f8e02789b6a176e7f166b082f9365bfec4121bfeb52e86e9040cb8d450e64c858583f61b7
  languageName: node
  linkType: hard

"lodash.isnumber@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isnumber@npm:3.0.3"
  checksum: 10c0/2d01530513a1ee4f72dd79528444db4e6360588adcb0e2ff663db2b3f642d4bb3d687051ae1115751ca9082db4fdef675160071226ca6bbf5f0c123dbf0aa12d
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 10c0/afd70b5c450d1e09f32a737bed06ff85b873ecd3d3d3400458725283e3f2e0bb6bf48e67dbe7a309eb371a822b16a26cca4a63c8c52db3fc7dc9d5f9dd324cbb
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: 10c0/09eaf980a283f9eef58ef95b30ec7fee61df4d6bf4aba3b5f096869cc58f24c9da17900febc8ffd67819b4e29de29793190e88dc96983db92d84c95fa85d1c92
  languageName: node
  linkType: hard

"lodash.once@npm:^4.0.0":
  version: 4.1.1
  resolution: "lodash.once@npm:4.1.1"
  checksum: 10c0/46a9a0a66c45dd812fcc016e46605d85ad599fe87d71a02f6736220554b52ffbe82e79a483ad40f52a8a95755b0d1077fba259da8bfb6694a7abbf4a48f1fc04
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"mssql@npm:^11.0.1":
  version: 11.0.1
  resolution: "mssql@npm:11.0.1"
  dependencies:
    "@tediousjs/connection-string": "npm:^0.5.0"
    commander: "npm:^11.0.0"
    debug: "npm:^4.3.3"
    rfdc: "npm:^1.3.0"
    tarn: "npm:^3.0.2"
    tedious: "npm:^18.2.1"
  bin:
    mssql: bin/mssql
  checksum: 10c0/6320bf7e03c072dd3a6ee45f396257c5987be7d99c588c086384cd98e40f03e2b3033e9e1d319a8ce4198a4611594e9677f9dedd8a2f4c3482460465704a8c81
  languageName: node
  linkType: hard

"native-duplexpair@npm:^1.0.0":
  version: 1.0.0
  resolution: "native-duplexpair@npm:1.0.0"
  checksum: 10c0/b4285c69526575b4fa10fb054ad80177a556eede485d0b83bd0366d2276ca24dd50580c3bbb5f262bae5ef8b0e7a1e02d9a6ccb02036e5fdf993dd48500adac7
  languageName: node
  linkType: hard

"open@npm:^8.0.0":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"pg-connection-string@npm:2.6.2":
  version: 2.6.2
  resolution: "pg-connection-string@npm:2.6.2"
  checksum: 10c0/e8fdea74fcc8bdc3d7c5c6eadd9425fdba7e67fb7fe836f9c0cecad94c8984e435256657d1d8ce0483d1fedef667e7a57e32449a63cb805cb0289fc34b62da35
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"readable-stream@npm:^4.2.0":
  version: 4.4.2
  resolution: "readable-stream@npm:4.4.2"
  dependencies:
    abort-controller: "npm:^3.0.0"
    buffer: "npm:^6.0.3"
    events: "npm:^3.3.0"
    process: "npm:^0.11.10"
    string_decoder: "npm:^1.3.0"
  checksum: 10c0/cf7cc8daa2b57872d120945a20a1458c13dcb6c6f352505421115827b18ac4df0e483ac1fe195cb1f5cd226e1073fc55b92b569269d8299e8530840bcdbba40c
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: "npm:^1.20.0"
  checksum: 10c0/1a30074124a22abbd5d44d802dac26407fa72a0a95f162aa5504ba8246bc5452f8b1a027b154d9bdbabcd8764920ff9333d934c46a8f17479c8912e92332f3ff
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/07e179f4375e1fd072cfb72ad66d78547f86e6196c4014b31cb0b8bb1db5f7ca871f922d08da0fbc05b94e9fd42206f819648fa3b5b873ebbc8e1dc68fec433a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#optional!builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/0446f024439cd2e50c6c8fa8ba77eaa8370b4180f401a96abf3d1ebc770ac51c1955e12764cde449fde3fff480a61f84388e3505ecdbab778f4bef5f8212c729
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0":
  version: 1.3.0
  resolution: "rfdc@npm:1.3.0"
  checksum: 10c0/a17fd7b81f42c7ae4cb932abd7b2f677b04cc462a03619fb46945ae1ccae17c3bc87c020ffdde1751cbfa8549860a2883486fdcabc9b9de3f3108af32b69a667
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"scripts@workspace:.":
  version: 0.0.0-use.local
  resolution: "scripts@workspace:."
  dependencies:
    knex: "npm:^3.1.0"
    mssql: "npm:^11.0.1"
  languageName: unknown
  linkType: soft

"semver@npm:^7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 10c0/5160b06975a38b11c1ab55950cb5b8a23db78df88275d3d8a42ccf1f29e55112ac995b3a26a522c36e3b5f76b0445f1eef70d696b8c7862a2b4303d7b0e7609e
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"stoppable@npm:^1.1.0":
  version: 1.1.0
  resolution: "stoppable@npm:1.1.0"
  checksum: 10c0/ba91b65e6442bf6f01ce837a727ece597a977ed92a05cb9aea6bf446c5e0dcbccc28f31b793afa8aedd8f34baaf3335398d35f903938d5493f7fbe386a1e090e
  languageName: node
  linkType: hard

"string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"tarn@npm:^3.0.2":
  version: 3.0.2
  resolution: "tarn@npm:3.0.2"
  checksum: 10c0/ea2344e3d21936111176375bd6f34eba69a38ef1bc59434d523fd313166f8a28a47b0a847846c119f72dcf2c1e1231596d74ac3fcfc3cc73966b3d293a327269
  languageName: node
  linkType: hard

"tedious@npm:^18.2.1":
  version: 18.6.2
  resolution: "tedious@npm:18.6.2"
  dependencies:
    "@azure/core-auth": "npm:^1.7.2"
    "@azure/identity": "npm:^4.2.1"
    "@azure/keyvault-keys": "npm:^4.4.0"
    "@js-joda/core": "npm:^5.6.1"
    "@types/node": "npm:>=18"
    bl: "npm:^6.0.11"
    iconv-lite: "npm:^0.6.3"
    js-md4: "npm:^0.3.2"
    native-duplexpair: "npm:^1.0.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/db0bdc2ed8ad1b11e6382d9530c924a890d0c05bd1cb8055ba8eed69317db050006666b8e5079a92c3041ed8fa2ed57d2b50c501cf5793d1433e7a9df6878835
  languageName: node
  linkType: hard

"tildify@npm:2.0.0":
  version: 2.0.0
  resolution: "tildify@npm:2.0.0"
  checksum: 10c0/57961810a6915f47bdba7da7fa66a5f12597a0495fa016785de197b02e7ba9994ffebb30569294061bbf6d9395c6b1319d830076221e5a3f49f1318bc749565c
  languageName: node
  linkType: hard

"tslib@npm:^2.2.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10c0/68e659a98898d6a836a9a59e6adf14a5d799707f5ea629433e025ac90d239f75e408e2e5ff086afc3cace26f8b26ee52155293564593fbb4a2f666af57fc59bf
  languageName: node
  linkType: hard

"uuid@npm:^8.3.0":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard
