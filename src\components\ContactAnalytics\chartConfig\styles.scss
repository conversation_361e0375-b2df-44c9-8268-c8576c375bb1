.data-table {
  margin: auto;
  font-size: 16px;
  border-collapse: collapse;
  width: 100%;
  td,
  th {
    border: 1px solid #ddd;
    padding: 8px;
  }

  tr:nth-child(even) {
    background-color: #f2f2f2;
  }

  tr:hover {
    background-color: #ddd;
  }

  th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #04aa6d;
    color: white;
  }

  .data-table-header-left {
    text-align: left;
  }

  .data-table-header-center,
  .data-table-cell-center {
    text-align: center;
  }
}
.demographics-table-main-title {
  font-weight: 700;
  font-size: 11px;
  line-height: 16px;
  color: var(--inactive-text-color);
  margin-bottom: 5px;
}
.demographics-table-filter-type-container {
  display: flex;
  align-items: center;
  margin-left: -14px;

  .demographics-table-filter-type-square {
    height: 8px;
    width: 8px;
    background: #80598e;
    margin-right: 6px;
  }

  .demographics-table-filter-type-title {
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    color: var(--font-primary);
    width: 100%;
  }
}
.demographics-table-date-range {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: var(--font-primary);
}
.demographics-table-title {
  font-weight: 700;
  font-size: 12px;
  color: var(--font-primary);
  margin-top: 8px;
}
.demographics-table {
  width: 100%;
  margin-bottom: 6px;
  background-color: white;

  tr:nth-child(odd) {
    background-color: #f4f4f4;
  }

  tr:hover {
    background-color: #ddd;
  }
  .demographics-table-cell-value {
    width: 45px;
    font-weight: 700;
    font-size: 12px;
    color: var(--font-primary);
    margin: 0 14px;
    text-align: right;
    padding-right: 12px;
  }
  .demographics-table-cell-label {
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
  }
}
