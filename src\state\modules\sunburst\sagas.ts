import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';

import {
  fetchAggregations,
  emitFetchAggregationsAction,
  SUNBURST_AGGREGATION_QUERY_CLAUSE,
  FETCH_SUNBURST_AGGREGATIONS,
} from '.';

import { ON_FOLDER_QUERY_CHANGE, getFullSearchQuery } from '../search';
import { getDisableAnalytics } from '../tdosTable';

function* watchFetchAggregations() {
  yield* all([takeLatest(FETCH_SUNBURST_AGGREGATIONS, doFetchAggregations)]);
}

function* watchActionsToCauseAggregationsFetch() {
  // here everytime one of these emitted - we will prefetch aggregations in the background
  yield* all([takeLatest(ON_FOLDER_QUERY_CHANGE, doEmitFetchAggregations)]);
}

function* doEmitFetchAggregations() {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    yield* put(emitFetchAggregationsAction());
  }
}

function* doFetchAggregations() {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    const fullSearchQuery = yield* select(getFullSearchQuery);
    const searchQuery: typeof fullSearchQuery & { aggregate: any[] } = {
      ...fullSearchQuery,
      aggregate: [],
    };
    searchQuery.aggregate.push(SUNBURST_AGGREGATION_QUERY_CLAUSE);
    if (!searchQuery.query) {
      // we must be at the root folder -> trick aggregation endpoint into empty query and pray
      searchQuery.query = {
        operator: 'and',
        conditions: [],
      };
    }
    yield* put(fetchAggregations(searchQuery));
  }
}

export default function* sunburst() {
  yield* all([
    fork(watchFetchAggregations),
    fork(watchActionsToCauseAggregationsFetch),
  ]);
}
