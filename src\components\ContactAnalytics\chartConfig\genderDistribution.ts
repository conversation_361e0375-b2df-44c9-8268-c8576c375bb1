import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{ field: "gender", operator: "term" }]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'gender',
      dataKey: 'gender',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                field: "genderNonconforming"
                operator: "term"
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'genderNonconforming',
      dataKey: 'genderNonconforming',
    },
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataObj = {
      Male: 0,
      Female: 0,
      'Trans Man/Boy': 0,
      'Trans Woman/Girl': 0,
      'Gender Non-conforming':
        data?.genderNonconforming?.find((gb) => gb.key_as_string === 'true')
          ?.doc_count ?? 0,
      ...data?.gender?.reduce((acc: { [key: string]: number }, b) => {
        if (b.key) {
          acc[b.key] = b.doc_count;
        }
        return acc;
      }, {}), // this reduce need to return an object to update default values
    };

    // Add data
    chart.data = Object.entries(dataObj).map((kvp) => {
      const [gen, v] = kvp;
      return { gender: gen, count: v };
    });

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'gender';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

export default config;

interface Data {
  genderNonconforming: {
    key: number;
    key_as_string: string;
    doc_count: number;
  }[];
  gender: { key: string; doc_count: number }[];
}
