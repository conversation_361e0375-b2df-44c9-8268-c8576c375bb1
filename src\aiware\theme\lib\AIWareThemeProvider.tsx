import { ReactElement } from 'react';
import {
  ThemeProvider,
  Theme,
  StyledEngineProvider,
} from '@mui/material/styles';
import darkTheme from './dark';
import lightTheme from './light';

import { BaselineStyles } from './BaselineStyles';

declare module '@mui/styles/defaultTheme' {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface DefaultTheme extends Theme {}
}

interface Props {
  darkMode?: boolean;
  scoped?: boolean;
  children: ReactElement;
}

// It is converted to support MUI 4 from https://github.com/veritone/aiware-apps/blob/master/libs/shared/theme/src/lib/AIWareThemeProvider.tsx
// For MUI 5, please use https://github.com/veritone/aiware-apps/blob/master/libs/shared/theme
export function AIWareThemeProvider({
  children,
  darkMode = false,
  scoped = true,
}: Props) {
  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={darkMode ? darkTheme : lightTheme}>
        <BaselineStyles scoped={scoped}>{children}</BaselineStyles>
      </ThemeProvider>
    </StyledEngineProvider>
  );
}

export default AIWareThemeProvider;
