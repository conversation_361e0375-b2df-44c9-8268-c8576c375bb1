import chalk from 'chalk';
import Config from '../apiConfig.json';
import coreLogger from '@veritone/core-logger';
import env from './env';

const formatConsoleArgs = (a: any) =>
  typeof a === 'object' ? JSON.stringify(a) : a;

const consoleLogger = {
  error: function (...args: any[]) {
    console.error(chalk.red(...args.map(formatConsoleArgs)));
  },
  info: function (...args: any[]) {
    console.log(chalk.green(...args.map(formatConsoleArgs)));
  },
  debug: function (...args: any[]) {
    console.log(chalk.blue(...args.map(formatConsoleArgs)));
  },
};

function createLogger() {
  const logger =
    env.nodeEnv === 'development' ? consoleLogger : coreLogger(Config);

  return logger;
}

export default createLogger;
