import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "reasonsForStop"
                field: "reasonsForStop"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'reasonsForStopAggregation',
      dataKey: 'reasonsForStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stops By Type',
  filterTextType: 'Type of Stop ',
  filterType: 'reasonsForStop',
  filterTerms: {
    Traffic: ['Traffic Violation'],
    Criminal: [
      'Reasonable suspicion that the person was engaged in criminal activity',
      'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
      'Knowledge of outstanding arrest warrant/wanted person',
      'Investigation to determine whether the person was truant',
    ],
    Other: [
      'Consensual Encounter resulting in a search',
      'Possible conduct warranting discipline under Education Code sections 48900, et al',
      'Determine whether the student violated school policy',
    ],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap: { [key: string]: number } = {
      Traffic: 0,
      Criminal: 0,
      Other: 0,
      ...data.reasonsForStopAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          if ('Traffic Violation' === b.key) {
            acc['Traffic'] = acc['Traffic']
              ? acc['Traffic'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Reasonable suspicion that the person was engaged in criminal activity',
              'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
              'Knowledge of outstanding arrest warrant/wanted person',
              'Investigation to determine whether the person was truant',
            ].includes(b.key)
          ) {
            acc['Criminal'] = acc['Criminal']
              ? acc['Criminal'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Consensual Encounter resulting in a search',
              'Possible conduct warranting discipline under Education Code sections 48900, et al',
              'Determine whether the student violated school policy',
            ].includes(b.key)
          ) {
            acc['Other'] = acc['Other']
              ? acc['Other'] + b.doc_count
              : b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    const chartData: { primaryReasonType: string; numberOfPeople: number }[] =
      [];

    ['Traffic', 'Criminal', 'Other'].forEach((pr) => {
      chartData.push({
        primaryReasonType: pr,
        numberOfPeople: dataMap[pr] ?? 0,
      });
    });

    chart.data = chartData;

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'numberOfPeople';
    pieSeries.dataFields.category = 'primaryReasonType';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  reasonsForStopAggregation: { key: string; doc_count: number }[];
}
