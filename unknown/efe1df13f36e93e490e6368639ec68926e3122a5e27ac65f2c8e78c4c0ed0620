import { noop } from 'lodash';
import { render } from '@testing-library/react';
import FileProgressList from '.';

describe('FileProgressList', function () {
  const percentByFiles = [
    {
      key: 'audio_file.flac',
      value: {
        type: 'audio',
        percent: 10,
        size: 82356235,
      },
    },
    {
      key: 'video_file.mp4',
      value: {
        type: 'video',
        percent: 20,
        size: 23856925352,
      },
    },
    {
      key: 'image_file.png',
      value: {
        type: 'image',
        percent: 80,
        size: 38529,
      },
    },
    {
      key: 'text_file.txt',
      value: {
        type: 'text',
        percent: 90,
        size: 569182,
      },
    },
    {
      key: 'error_file.bin',
      value: {
        type: 'text',
        percent: 69,
        size: 56283756,
        error: 'error msg',
      },
    },
  ];

  it('shows the progress list', function () {
    const { getAllByTestId } = render(
      <FileProgressList percentByFiles={percentByFiles} />
    );

    expect(getAllByTestId('file-picker-list')).toHaveLength(
      percentByFiles.length
    );
  });

  it('shows the progress list with abort buttons', function () {
    const { getAllByTestId } = render(
      <FileProgressList percentByFiles={percentByFiles} handleAbort={noop} />
    );
    expect(getAllByTestId('file-picker-abort-icon')).toHaveLength(5);
  });

  it('shows errored files only', function () {
    const { getAllByTestId } = render(
      <FileProgressList percentByFiles={percentByFiles} showErrors />
    );
    percentByFiles.forEach(() => {
      expect(getAllByTestId('error_file.bin')).toHaveLength(1);
    });
  });
});
