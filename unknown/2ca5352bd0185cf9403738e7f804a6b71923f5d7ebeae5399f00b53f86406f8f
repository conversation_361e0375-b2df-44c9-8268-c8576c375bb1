import React from 'react';
import cx from 'classnames';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import * as styles from './styles/sectiontree.scss';
import DefaultCheckboxes from './DefaultCheckboxes';

interface SectionTreeProps {
  sections: {
    children: {
      label: string;
      icon?: React.ReactNode;
      valueArray?: (string | number)[];
      type?: string;
      children: {
        formComponentId: string;
        valueArray?: (string | number)[];
      }[];
    }[];
  };
  formComponents: any;
  checkboxCount: Record<string, number>;
  onCheckboxChange?:
    | ((event: React.ChangeEvent<HTMLInputElement>) => void)
    | undefined;
  defaultCheckboxSelectedItems: string[];
}

class SectionTree extends React.Component<SectionTreeProps> {
  render() {
    const currentVisibleSection = this.props.sections;

    return (
      <div className={styles.tabsContainer}>
        {currentVisibleSection.children.map(
          ({ label, icon, type }, i: number) => {
            // ! is justified due to above map
            const sectionChildren = currentVisibleSection.children[i]!;
            // const formComponentIdAtLeaf = .children[0].formComponentId;
            return (
              <SectionTreeTab
                label={label}
                icon={icon}
                key={label}
                checkboxCount={this.props.checkboxCount}
                type={type}
                formComponentIdAtLeaf={
                  sectionChildren.children[0]
                    ? sectionChildren.children[0].formComponentId
                    : 'default-checkboxes'
                }
                checkboxValues={sectionChildren.valueArray}
                formComponents={this.props.formComponents}
                id={i}
                onCheckboxChange={this.props.onCheckboxChange}
                defaultCheckboxSelectedItems={
                  this.props.defaultCheckboxSelectedItems
                }
              />
            );
          }
        )}
      </div>
    );
  }
}

export default SectionTree;

export const SectionTreeTab = ({
  label,
  icon,
  dark,
  type,
  checkboxCount,
  checkboxValues,
  formComponentIdAtLeaf,
  formComponents,
  onCheckboxChange,
  defaultCheckboxSelectedItems,
  id,
}: SectionTreeTabProps) => {
  return (
    <Accordion
      classes={{
        root: styles.noShadow,
        expanded: styles.expandedStyle,
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        classes={{
          root: cx(
            styles.sectionTab,
            { [styles.dark!]: dark }, // safe due to dark exists in styles
            styles.noShadow
          ),
          expanded: styles.expandedStyle,
        }}
      >
        <span data-testid="icon">{icon}</span>
        <span data-test={label} className={styles.label} data-testid={label}>
          {label}
        </span>

        {type &&
        ['display-count', 'checkbox'].includes(type) &&
        ![0, undefined].includes(checkboxCount[formComponentIdAtLeaf]) ? (
          <span className={styles.count} data-testid={`count-${id}`}>
            &nbsp; ({checkboxCount[formComponentIdAtLeaf]})
          </span>
        ) : (
          ''
        )}
      </AccordionSummary>
      <AccordionDetails className={styles['expansion-panel-details']}>
        {formComponentIdAtLeaf.includes('default-checkboxes') &&
        checkboxValues ? (
          <DefaultCheckboxes
            checkboxValues={checkboxValues}
            onCheckboxChange={onCheckboxChange}
            formComponentIdAtLeaf={formComponentIdAtLeaf}
            defaultCheckboxSelectedItems={defaultCheckboxSelectedItems}
          />
        ) : (
          formComponents[formComponentIdAtLeaf]
        )}
      </AccordionDetails>
    </Accordion>
  );
};

interface SectionTreeTabProps {
  label: string;
  icon?: React.ReactNode;
  dark?: boolean;
  formComponentIdAtLeaf: string;
  formComponents: any;
  checkboxCount: Record<string, number>;
  type?: string;
  onCheckboxChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  checkboxValues?: (string | number)[];
  defaultCheckboxSelectedItems: string[];
  id?: number;
}
