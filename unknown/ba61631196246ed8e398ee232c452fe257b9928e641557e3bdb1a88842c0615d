import React from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import * as styles from './styles.scss';

type Props = typeof RouteLoadingScreen.defaultProps & {
  delay?: number;
};

export default class RouteLoadingScreen extends React.Component<Props> {
  static defaultProps = {
    delay: 0,
  };

  _timer: ReturnType<typeof setTimeout> | undefined; // eslint-disable-line react/sort-comp

  state = {
    // immediately visible unless delay is set
    visible: this.props.delay <= 0,
  };

  componentDidMount() {
    if (this.props.delay <= 0) {
      return;
    }

    this._timer = setTimeout(() => {
      this.setState({ visible: true });
    }, this.props.delay);
  }

  componentWillUnmount() {
    clearTimeout(this._timer);
  }

  render() {
    return this.state.visible ? (
      <div className={styles.container}>
        <CircularProgress size={125} thickness={1} />
      </div>
    ) : (
      <div />
    );
  }
}
