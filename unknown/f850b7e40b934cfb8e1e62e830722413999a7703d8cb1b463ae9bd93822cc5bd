import LooksOne from '@mui/icons-material/LooksOne';
import SectionTree, { SectionTreeTab } from './SectionTree';
import { render } from '@testing-library/react';
describe('SectionTree', function () {
  const testSectionTree = {
    children: [
      {
        label: 'section1',
        icon: <LooksOne />,
        type: 'display-count',
        children: [{ formComponentId: 'select-station-form' }],
      },
      {
        label: 'section2',
        type: 'display-count',
        valueArray: ['video', 'audio', 'image'],
        children: [{ formComponentId: 'default-checkboxes-1' }],
      },
      {
        label: 'section3',
        valueArray: [1, 2, 3],
        children: [{ formComponentId: 'default-checkboxes-2' }],
      },
    ],
  };

  const defaultProps = {
    sections: testSectionTree,
    formComponents: {
      'select-station-form': <div>select station form</div>,
    },
    checkboxCount: {
      'select-station-form': 3,
      'default-checkboxes-1': 9,
    },
    onCheckboxChange: jest.fn(),
    defaultCheckboxSelectedItems: [],
  };

  it('should render SectionTreeTabs for the root children by default', function () {
    const { getByTestId } = render(<SectionTree {...defaultProps} />);
    expect(getByTestId('section1')).toBeInTheDocument();
    expect(getByTestId('section2')).toBeInTheDocument();
    expect(getByTestId('section3')).toBeInTheDocument();
  });

  it('should convert valueArray into checkbox list when formComponentId contains default-checkboxes', function () {
    const { getByTestId } = render(<SectionTree {...defaultProps} />);
    expect(getByTestId('video')).toBeInTheDocument();
    expect(getByTestId('audio')).toBeInTheDocument();
    expect(getByTestId('image')).toBeInTheDocument();
    expect(getByTestId(1)).toBeInTheDocument();
    expect(getByTestId(2)).toBeInTheDocument();
    expect(getByTestId(3)).toBeInTheDocument();
  });

  it('should display numbers when checkboxCount is provided with values and type is display-count', function () {
    const { getByTestId } = render(<SectionTree {...defaultProps} />);
    expect(getByTestId('count-0')).toHaveTextContent('(3)');
    expect(getByTestId('count-1')).toHaveTextContent('(9)');
  });
});

describe('SectionTreeTab', function () {
  const defaultProps = {
    label: 'test-label',
    icon: <LooksOne />,
    type: 'display-count',
    checkboxCount: {
      'select-station-form': 3,
      'default-checkboxes-1': 9,
    },
    checkboxValues: [1, 2, 3],
    formComponentIdAtLeaf: 'select-station-form',
    formComponents: {
      'select-station-form': <div>Some form</div>,
    },
    defaultCheckboxSelectedItems: [],
  };

  it('show props.label', function () {
    const { getByTestId } = render(<SectionTreeTab {...defaultProps} />);
    expect(getByTestId('test-label')).toHaveTextContent(defaultProps.label);
  });

  it('show props.icon', function () {
    const { getByTestId } = render(
      <SectionTreeTab
        {...defaultProps}
        icon={<div data-testid="test-icon" />}
      />
    );
    expect(getByTestId('test-icon')).toBeInTheDocument();
  });

  it('if type is not specified as display-count, do not show checkboxCount', function () {
    const { queryByTestId } = render(
      <SectionTreeTab {...defaultProps} type="" />
    );
    expect(queryByTestId('test-icon')).not.toBeInTheDocument();
  });

  it('display either DefaultCheckboxes or custom component', function () {
    const { getAllByTestId } = render(
      <SectionTreeTab
        {...defaultProps}
        formComponentIdAtLeaf="default-checkboxes-1"
      />
    );
    expect(getAllByTestId('default-checkboxes-1')).toHaveLength(3);
  });
});
