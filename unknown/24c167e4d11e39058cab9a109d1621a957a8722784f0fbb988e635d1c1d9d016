import {
  ThemeProvider,
  StyledEngineProvider,
  createTheme,
  Theme,
} from '@mui/material/styles';
import withStyles from '@mui/styles/withStyles';
import { ClassNameMap } from '@mui/styles';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { ConnectedProps, connect } from 'react-redux';
import MoveFileItem from './MoveFileItem';
import CircularProgress from '@mui/material/CircularProgress';
import * as styles from './styles.scss';
import {
  getMoveFolders,
  getRootFolderId,
  foldersFailedToFetch,
  getFetchingFolderRoot,
  getSelectedMoveFolderId,
} from 'state/modules/folders';
import { getSearchResultTdos } from 'state/modules/search';
import { getTdosForExport } from 'state/modules/tdosTable';

const buttonTheme = createTheme({
  palette: {
    primary: {
      main: '#1871E8',
    },
    secondary: {
      main: '#2A323C',
    },
  },
});

const style = (_theme: Theme) => ({
  paper: {
    overflow: 'auto',
    width: '500px',
  },
  dialogContent: {
    height: '500px',
    overflow: 'auto',
  },
  dialogTitle: {
    backgroundColor: '#FAFAFA',
    color: '#5C6269',
    height: '64px',
  },
  dialogActions: {
    height: '84px',
    marginRight: '16px',
  },
  closeButton: {
    position: 'absolute' as const,
    right: '8px',
    top: '8px',
  },
});

const MoveFolder = ({
  classes,
  onClose,
  moveFolders,
  rootFolderId,
  tdoIdSelected,
  onConfirm,
  inPreview,
  fetchingFolderRoot,
  selectedMoveFolderId,
  openFolderIdUpload,
  type,
  open,
}: Props) => (
  <Dialog open={open} onClose={onClose} classes={{ paper: classes.paper }}>
    <DialogTitle id="move-dialog-slide-title" className={classes.dialogTitle}>
      {tdoIdSelected.length > 1 && !inPreview ? 'MOVE FILES' : 'MOVE FILE'}
      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
        size="large"
      >
        <CloseIcon />
      </IconButton>
    </DialogTitle>
    <DialogContent dividers className={classes.dialogContent}>
      <div data-test="move-folder-dialog-content">
        {fetchingFolderRoot ? (
          <CircularProgress
            size={40}
            className={styles['loading-folder']}
            variant="indeterminate"
          />
        ) : (
          <MoveFileItem
            folder={moveFolders[rootFolderId]!} // safe due to folders = makeFolderRoot with rootFolderId is the Key
            allFolders={moveFolders}
            selectedFolderId={
              type === 'uploadFile' ? openFolderIdUpload : selectedMoveFolderId
            }
            type={type}
          />
        )}
      </div>
    </DialogContent>
    <DialogActions className={classes.dialogActions}>
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={buttonTheme}>
          <Button
            data-test="move-dialog-save-button"
            onClick={onConfirm}
            className={classes.buttonColor}
            color="primary"
            variant="contained"
          >
            Move
          </Button>
        </ThemeProvider>
      </StyledEngineProvider>
    </DialogActions>
  </Dialog>
);

type Props = PropsFromRedux & {
  classes: ClassNameMap;
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  inPreview?: boolean;
  openFolderIdUpload?: string;
  type?: string;
  // rootFolderId: string;
  // selectedMoveFolderId: string;
};

// export default withStyles(style, { withTheme: true })(
//   connect((state) => ({
//     moveFolders: getMoveFolders(state),
//     rootFolderId: getRootFolderId(state),
//     tdoIdSelected: getTdosForExport(state),
//     tdos: getSearchResultTdos(state),
//     fetchingFolderRoot: getFetchingFolderRoot(state),
//     error: foldersFailedToFetch(state),
//     selectedMoveFolderId: getSelectedMoveFolderId(state),
//   }))(MoveFolder)
// );

const mapState = (state: any) => ({
  moveFolders: getMoveFolders(state),
  rootFolderId: getRootFolderId(state),
  tdoIdSelected: getTdosForExport(state),
  tdos: getSearchResultTdos(state),
  fetchingFolderRoot: getFetchingFolderRoot(state),
  error: foldersFailedToFetch(state),
  selectedMoveFolderId: getSelectedMoveFolderId(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default withStyles(style, { withTheme: true })(connector(MoveFolder));
