import Close from '@mui/icons-material/Close';
import Grid from '@mui/material/Grid2';
import IconButton from '@mui/material/IconButton';
import { withStyles, ClassNameMap } from '@mui/styles';
import cx from 'classnames';
import { isArray, uniq } from 'lodash';
import mime from 'mime-types';
import React from 'react';
import styles from './styles';

interface ExtensionPanelProps {
  acceptedFileTypes?: string[];
  closeExtensionList: () => void;
  classes?: ClassNameMap;
}

const ExtensionPanel: React.FC<ExtensionPanelProps> = ({
  acceptedFileTypes = [],
  closeExtensionList,
  classes,
}) => {
  const readableTypeNames: { [key: string]: string } = {
    'video/*': 'video',
    'audio/*': 'audio',
    'image/*': 'image',
  };

  const typeMapper: { [key: string]: string[] } = {
    audio: [],
    video: [],
    image: [],
    text: [],
  };

  acceptedFileTypes.forEach((t) => {
    let wasCategorized = false;
    const mappedType = readableTypeNames[t] || mime.extension(t) || t;
    Object.keys(typeMapper).forEach((key) => {
      if (t.includes(key)) {
        if (isArray(typeMapper[key])) {
          typeMapper[key]?.push(mappedType.split('/').slice(-1)[0] ?? '');
        }
        wasCategorized = true;
      }
    });
    if (!wasCategorized) {
      // Default insert into text category
      if (typeMapper.text) {
        typeMapper.text.push(mappedType.split('/').slice(-1)[0] ?? '');
      }
    }
  });

  return (
    <div
      data-testid="extension-panel"
      className={classes?.extensionListContainer || ''}
    >
      <div className={classes?.extensionListHeader || ''}>
        <span className={classes?.extensionListTitle || ''}>
          File Extensions
        </span>
        <IconButton
          classes={{
            root: classes?.extensionListCloseButton || '',
          }}
          data-veritone-element="uploader-extension-close-btn"
          onClick={closeExtensionList}
          disableRipple
          size="large"
        >
          <Close />
        </IconButton>
      </div>
      <div className={classes?.extensionList || ''}>
        {Object.keys(typeMapper)
          .filter((key) => typeMapper[key]?.length)
          .map((key) => (
            <Grid
              key={`${key}-extension-list`}
              data-veritone-element="extension-list-category"
              className={cx(
                classes?.extensionTypeContainer || '',
                classes?.[key] || ''
              )}
              container
              spacing={2}
            >
              <Grid size={{ xs: 8, sm: 6, md: 4 }}>
                <span className={classes?.mediaTypeKey || ''}>{key}</span>
              </Grid>
              {uniq(typeMapper[key]).map((ext) => (
                <Grid
                  key={`${key}-extension-${ext}`}
                  size={{ xs: 8, sm: 6, md: 4 }}
                >
                  <span
                    className={classes?.mediaExtension || ''}
                    data-veritone-element="extension-list-item"
                  >
                    {ext}
                  </span>
                </Grid>
              ))}
            </Grid>
          ))}
      </div>
    </div>
  );
};

export default withStyles(styles)(ExtensionPanel);
