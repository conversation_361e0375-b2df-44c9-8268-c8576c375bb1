import Button from '@mui/material/Button';
import { withStyles, ClassNameMap } from '@mui/styles';
import cx from 'classnames';
import { endsWith, noop, startsWith } from 'lodash';
import React, { useRef, useState } from 'react';
import { DropTargetMonitor, useDrop } from 'react-dnd';
import { NativeTypes } from 'react-dnd-html5-backend';
import ExtensionPanel from './ExtensionPanel';
import styles from './styles';

const { FILE } = NativeTypes;

interface FileUploaderProps {
  acceptedFileTypes: string[];
  onFilesSelected: (files: File[]) => void;
  useFlatStyle?: boolean;
  onFilesRejected?: (numRejectedFiles: number) => void;
  multiple?: boolean;
  classes?: ClassNameMap;
}

interface DropItem {
  type: string;
  files: File[];
}

const FileUploader: React.FC<FileUploaderProps> = ({
  acceptedFileTypes = [],
  onFilesSelected,
  useFlatStyle,
  onFilesRejected = noop,
  multiple,
  classes,
}) => {
  const [showExtensionList, setShowExtensionList] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const [{ isOver }, drop] = useDrop<DropItem, void, { isOver: boolean }>({
    accept: FILE,
    drop: (item: DropItem, _monitor: DropTargetMonitor) => {
      const droppedFiles = item.files;
      const allowableDroppedFiles = droppedFiles.filter(({ type }) => {
        return (
          acceptedFileTypes.includes(type) ||
          acceptedFileTypes.some((acceptedType) => {
            if (endsWith(acceptedType, '/*')) {
              const typePrefix = acceptedType.match(/(.*)\/\*/)?.[1] || '';
              return startsWith(type, typePrefix);
            }
            return false;
          })
        );
      });

      if (acceptedFileTypes.length) {
        if (allowableDroppedFiles.length) {
          onFilesSelected(allowableDroppedFiles);
        }
        const numRejectedFiles =
          droppedFiles.length - allowableDroppedFiles.length;
        if (numRejectedFiles > 0) {
          onFilesRejected(numRejectedFiles);
        }
      } else {
        onFilesSelected(droppedFiles);
      }
    },
    collect: (monitor: DropTargetMonitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const handleFileSelection = () => {
    if (inputRef.current?.files?.length) {
      onFilesSelected(Array.from(inputRef.current.files));
      inputRef.current.value = '';
    }
  };

  const openExtensionList = () => setShowExtensionList(true);
  const closeExtensionList = () => setShowExtensionList(false);

  const acceptMessage = 'Drag & Drop';
  const subMessage = 'your file(s) here, or ';

  return (
    <div
      ref={drop}
      data-testid="file-uploader"
      className={cx(classes?.fileUploader || '', {
        [classes?.flat || '']: useFlatStyle,
      })}
    >
      {showExtensionList ? (
        <ExtensionPanel
          acceptedFileTypes={acceptedFileTypes}
          closeExtensionList={closeExtensionList}
        />
      ) : (
        <div className={classes?.uploaderContainer || ''}>
          {!!acceptedFileTypes.length && (
            <span
              data-testid="extension-types"
              className={classes?.extensionListOpenButton || ''}
              data-veritone-element="uploader-extension-open-btn"
              onClick={openExtensionList}
            >
              Extension Types
            </span>
          )}
          <span>
            <i className={cx(classes?.fileUploadIcon || '', 'icon-ingest')} />
          </span>
          <span className={classes?.fileUploaderAcceptText || ''}>
            {acceptMessage}
          </span>

          <label htmlFor="file">
            <Button component="span" disableFocusRipple disableRipple>
              <span className={classes?.fileUploaderSubtext || ''}>
                {subMessage}
              </span>
              <span
                className={cx(
                  classes?.fileUploaderSubtext || '',
                  classes?.subtextBlue || ''
                )}
              >
                browse
              </span>
            </Button>
          </label>
          <input
            accept={acceptedFileTypes.join(',')}
            style={{ display: 'none' }}
            id="file"
            multiple={multiple}
            type="file"
            onChange={handleFileSelection}
            ref={inputRef}
            data-testid="file-upload-input"
          />
        </div>
      )}
      {isOver && <div className={classes?.uploaderOverlay || ''} />}
    </div>
  );
};

export default withStyles(styles)(FileUploader);
