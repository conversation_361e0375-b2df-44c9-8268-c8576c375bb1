import React from 'react';
import Input from '@mui/material/Input';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import FormControl from '@mui/material/FormControl';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import * as styles from './styles.scss';
export const CreateFolder = ({
  open,
  onClose,
  onSubmit,
  pristine,
  folderDetail,
  type,
  onChange,
}: Props) => {
  return (
    <Dialog
      open={open}
      onClose={onCloseWithDisableBackdropClick(onClose)}
      disableEscapeKeyDown
      maxWidth="md"
    >
      <form onSubmit={onSubmit}>
        <Toolbar
          data-test="dialog-rename-folder"
          className={styles['dialog-header']}
        >
          <Typography color="inherit" className={styles.title}>
            {type === 'rename' ? 'Rename Folder' : 'Create Folder'}
          </Typography>
          <IconButton
            color="inherit"
            onClick={onClose}
            aria-label="Close"
            className={styles['icon-close']}
            data-testid="icon-button"
            size="large"
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
        <DialogContent className={styles['dialog-content']}>
          <DialogContentText className={styles.description}>
            {type === 'rename'
              ? 'Provide a new folder name'
              : 'Create folder in ' +
                (folderDetail.root ? 'My Cases' : folderDetail.name)}
          </DialogContentText>

          <FormControl fullWidth className={styles['form-name']}>
            <Input
              data-test="create-folder-enter-folder-name"
              placeholder="Folder name"
              onChange={onChange}
              defaultValue={type === 'rename' ? folderDetail.name : ''}
              name={'name'}
              data-testid="input"
            />
          </FormControl>
        </DialogContent>
        <DialogActions className={styles['dialog-action']}>
          <Button
            onClick={onClose}
            color="primary"
            className={styles['btn-cancel']}
            data-testid="button"
          >
            Cancel
          </Button>
          <Button
            data-test="create-folder-submit-button"
            type="submit"
            color="primary"
            variant="contained"
            className={styles['btn-create']}
            disabled={pristine}
            data-testid="button"
          >
            {type === 'rename' ? 'Rename' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

interface Props {
  open: boolean;
  onClose: () => void;
  onSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  pristine: boolean;
  folderDetail: {
    id: string;
    root: boolean;
    name: string;
  };
  type: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

function onCloseWithDisableBackdropClick(
  onClose: (e: Event, reason: string) => void
) {
  return function (event: Event, reason: string) {
    if (reason !== 'backdropClick') {
      onClose(event, reason);
    }
  };
}
export default CreateFolder;
