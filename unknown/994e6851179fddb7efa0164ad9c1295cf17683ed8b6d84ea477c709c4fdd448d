import Paper from '@mui/material/Paper';
import { withStyles, ClassNameMap } from '@mui/styles';
import { isArray, isString } from 'lodash';
import mime from 'mime-types';
import pluralize from 'pluralize';
import React, { useCallback, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import FileList from './FileList';
import FilePickerFooter from './FilePickerFooter';
import FilePickerHeader from './FilePickerHeader';
import FilePickerFlatHeader from './FilePickerHeader/FilePickerFlatHeader';
import FileUploader from './FileUploader';
import styles from './styles';
import UrlUploader from './UrlUploader';

interface FilePickerProps {
  accept?: any;
  multiple?: boolean;
  width?: number;
  height?: number;
  onPickFiles?: (files: File[]) => void;
  onRequestClose?: () => void;
  allowUrlUpload?: boolean;
  maxFiles?: number;
  title?: string;
  tooManyFilesErrorMessage?: (maxFiles: number) => string;
  oneFileOnlyErrorMessage?: string;
  enableResize?: boolean;
  classes?: ClassNameMap;
}

const FilePicker: React.FC<FilePickerProps> = ({
  accept,
  multiple,
  width = 600,
  height,
  onPickFiles,
  onRequestClose,
  allowUrlUpload = true,
  maxFiles,
  title,
  tooManyFilesErrorMessage = (maxFiles) =>
    `You can select up to and including ${maxFiles} files. Please remove any unnecessary files.`,
  oneFileOnlyErrorMessage = '',
  enableResize,
  classes,
}) => {
  const [selectedTab, setSelectedTab] = useState('upload');
  const [resize, setResize] = useState<{
    showing: boolean;
    targetFile: File | null;
  }>({
    showing: false,
    targetFile: null,
  });
  const [files, setFiles] = useState<File[]>([]);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => [
      ...prevFiles.slice(0, index),
      ...prevFiles.slice(index + 1),
    ]);
    clearErrorMessage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleFilesSelected = useCallback(
    (fileOrFiles: File | File[]) => {
      const newFiles = isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];

      if (multiple) {
        if (
          maxFiles &&
          (files.length >= maxFiles || newFiles.length > maxFiles)
        ) {
          setErrorMessage(tooManyFilesErrorMessage(maxFiles));
        } else {
          clearErrorMessage();
        }

        setFiles((prevFiles) => [...prevFiles, ...newFiles]);
      } else {
        if (files.length >= 1 || newFiles.length > 1) {
          setErrorMessage(oneFileOnlyErrorMessage);
        } else {
          clearErrorMessage();
        }

        if (newFiles[0]) {
          setFiles([newFiles[0]]);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      multiple,
      files,
      maxFiles,
      oneFileOnlyErrorMessage,
      tooManyFilesErrorMessage,
    ]
  );

  const handleFilesRejected = useCallback((num: number) => {
    const filesText = pluralize('file', num);
    const wereText = pluralize('was', num);

    setErrorMessage(
      `${num} ${filesText} ${wereText} rejected due to filetype restrictions.`
    );
  }, []);

  const handleTabChange = useCallback((value: string) => {
    setSelectedTab(value);
    clearErrorMessage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCloseModal = useCallback(() => {
    if (onRequestClose) {
      onRequestClose();
    }
  }, [onRequestClose]);

  const handlePickFiles = useCallback(() => {
    const selectedFiles = files;
    clearErrorMessage();
    setFiles([]);
    if (onPickFiles) {
      onPickFiles(selectedFiles);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [files, onPickFiles]);

  const onFileResize = useCallback((file: File) => {
    setResize({
      showing: true,
      targetFile: file,
    });
  }, []);

  const clearErrorMessage = useCallback(() => {
    setErrorMessage('');
  }, []);

  const disableNextStep = () => {
    if (multiple && maxFiles) {
      return files.length > maxFiles || files.length === 0 || resize.showing;
    } else {
      return files.length === 0 || resize.showing;
    }
  };

  // const onSubmitResize = useCallback((croppedFile: File) => {
  //   setFiles((prevFiles) =>
  //     prevFiles.map((file) =>
  //       `cropped-${file.name}` === croppedFile.name ? croppedFile : file
  //     )
  //   );
  //   setResize({
  //     showing: false,
  //     targetFile: null,
  //   });
  // }, []);

  // const onCancelResize = useCallback(() => {
  //   setResize({
  //     showing: false,
  //     targetFile: null,
  //   });
  // }, []);

  const acceptedFileTypes = ((isString(accept) ? [accept] : accept) || []).map(
    (t: string) => mime.lookup(t) || t
  ); // use full mimetype when possible

  return (
    <DndProvider backend={HTML5Backend}>
      <Paper
        classes={
          onRequestClose && {
            root: classes?.filePickerPaperOverride || '',
          }
        }
        style={{
          height,
          width,
        }}
      >
        <div
          className={
            onRequestClose
              ? classes?.filePicker || ''
              : classes?.filePickerNonModal || ''
          }
          style={{
            height: '100%',
          }}
        >
          {onRequestClose ? (
            <FilePickerHeader
              selectedTab={selectedTab}
              onSelectTab={handleTabChange}
              onClose={handleCloseModal}
              allowUrlUpload={allowUrlUpload}
              title={title}
              fileCount={files.length}
              maxFiles={maxFiles}
              multiple={multiple}
            />
          ) : (
            <FilePickerFlatHeader
              title={title}
              fileCount={files.length}
              maxFiles={maxFiles}
            />
          )}

          {selectedTab === 'upload' && (
            <div
              className={classes?.filePickerBody || ''}
              data-testid="file-picker-body"
            >
              <FileUploader
                useFlatStyle={!onRequestClose}
                onFilesSelected={handleFilesSelected}
                onFilesRejected={handleFilesRejected}
                acceptedFileTypes={acceptedFileTypes}
                multiple={multiple}
              />
              {files.length > 0 && (
                <FileList
                  enableResize={enableResize}
                  onFileResize={onFileResize}
                  files={files}
                  onRemoveFile={handleRemoveFile}
                />
              )}
            </div>
          )}

          {selectedTab === 'by-url' && (
            <div className={classes?.filePickerBody || ''}>
              <UrlUploader
                onUpload={handleFilesSelected}
                acceptedFileTypes={acceptedFileTypes}
              />
            </div>
          )}
          <div className={classes?.errorMessage || ''}>{errorMessage}</div>
          {onRequestClose && (
            <FilePickerFooter
              onCancel={handleCloseModal}
              onSubmit={handlePickFiles}
              disabled={disableNextStep()}
            />
          )}
        </div>
      </Paper>
    </DndProvider>
  );
};

export default withStyles(styles)(FilePicker);
