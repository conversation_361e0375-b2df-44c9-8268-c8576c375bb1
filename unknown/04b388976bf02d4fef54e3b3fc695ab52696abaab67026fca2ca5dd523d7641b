import Button from '@mui/material/Button';
import { withStyles, ClassNameMap } from '@mui/styles';
import cx from 'classnames';
import React from 'react';
import styles from './styles';

interface FilePickerFooterProps {
  disabled?: boolean;
  onCancel?: () => void;
  onSubmit?: () => void;
  title?: string;
  hasIntercom?: boolean;
  classes?: ClassNameMap;
}

const FilePickerFooter: React.FC<FilePickerFooterProps> = ({
  hasIntercom,
  onCancel,
  disabled,
  onSubmit,
  title = 'Upload',
  classes,
}) => {
  return (
    <div
      className={cx(classes?.filePickerFooter || '', {
        [classes?.hasIntercom || '']: hasIntercom,
      })}
      data-testid="file-picker-footer"
    >
      <Button
        data-veritone-element={`picker-footer-cancel-button`}
        onClick={onCancel}
        data-testid="file-picker-footer-cancel-button"
      >
        Cancel
      </Button>
      <Button
        data-veritone-element={`picker-footer-${title}-button`}
        variant="contained"
        disabled={disabled}
        color="primary"
        onClick={onSubmit}
        data-testid="file-picker-footer-upload-button"
      >
        {title}
      </Button>
    </div>
  );
};

export default withStyles(styles)(FilePickerFooter);
