import { SearchBar } from '.';
import { render, screen } from '@testing-library/react';
jest.mock('@veritone/glc-advanced-search-bar');

const mockProps = {
  // mapState
  apiRoot: 'https://test.com',
  graphQLEndpoint: '',
  apiAuthToken: null,
  sentiment: undefined,

  // mapDispatch
  onSearchBarQueryChange: jest.fn(),
  fetchMediaAggregations: jest.fn(),
  fetchSunburstAggregations: jest.fn(),
  updateSearchParameters: jest.fn(),
  navigateToTab: jest.fn(),
  openTdoPreview: jest.fn(),
};

describe('SearchBar', () => {
  it('renders a div element with data-testid = entity-search', () => {
    render(<SearchBar {...mockProps} />);
    expect(screen.getByTestId('entity-search')).toBeInTheDocument();
  });
});
