import DateRangeFilter from '../DateRangeFilter';
import { render } from '@testing-library/react';
describe('DateRangeFilters', function () {
  const defaultProps = {
    startDate: '2019-1-13',
    endDate: '2019-4-26',
    errorMess: '',
  };
  it('renders two TextFields', function () {
    const { getAllByTestId } = render(<DateRangeFilter {...defaultProps} />);
    expect(getAllByTestId('text-field')).toHaveLength(2);
  });
  it('Should display error message when there is one', function () {
    const dateError = 'There is an error message';
    const { getByTestId } = render(
      <DateRangeFilter {...defaultProps} dateError={dateError} />
    );
    expect(getByTestId('error-message').innerHTML).toEqual('* ' + dateError);
  });
});
