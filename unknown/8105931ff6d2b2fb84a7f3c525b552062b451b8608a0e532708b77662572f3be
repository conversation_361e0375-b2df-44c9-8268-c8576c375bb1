import { DATE_TIME_RANGE_FILTER } from './index';

describe('time range filter', () => {
  it('should return hours in time range filter', () => {
    expect(DATE_TIME_RANGE_FILTER[3].value).toBe(72);
    expect(DATE_TIME_RANGE_FILTER[4].value).toBe(168);
    expect(DATE_TIME_RANGE_FILTER[5].value).toBe(336);
    expect(DATE_TIME_RANGE_FILTER[6].value).toBe(504);
    expect(DATE_TIME_RANGE_FILTER[7].value).toBe(720);
    expect(DATE_TIME_RANGE_FILTER[8].value).toBe(1440);
    expect(DATE_TIME_RANGE_FILTER[9].value).toBe(2160);
  });
});
