import TextField from '@mui/material/TextField';
import FormHelperText from '@mui/material/FormHelperText';

const RenderTextField = ({
  input: { name, onChange },
  meta: { touched, error },
  placeholder,
}: Props) => (
  <div>
    <TextField
      name={name}
      onChange={onChange}
      placeholder={placeholder}
      style={{ width: '100%' }}
    />
    {touched && error && <FormHelperText error>{error}</FormHelperText>}
  </div>
);

interface Props {
  input: {
    name: string;
    value: string;
    onChange: () => void;
  };
  meta: {
    touched: boolean;
    error: string;
  };
  placeholder: string;
}

export default RenderTextField;
