import { Fragment, useState } from 'react';
import {
  Grid,
  TableHeaderRow,
  Table,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui';
import Paper from '@mui/material/Paper';
import * as styles from '../styles.scss';

function ModalItem({ data }: Props) {
  const [columns] = useState([
    { name: '', title: '' },
    { name: 'name', title: 'Name' },
    { name: 'part', title: 'Actions' },
  ]);
  const [tableColumnExtensions] = useState([
    { columnName: '', width: 65 },
    { columnName: 'name', wordWrapEnabled: true },
    { columnName: 'part', width: 120 },
  ]);

  function TableRow({ row, ...restProps }: Table.DataRowProps) {
    return (
      <Table.Row
        row={undefined}
        {...restProps}
        data-id={row.id}
        data-size={row.size}
        className={styles['table-row']}
      />
    );
  }
  return (
    <Fragment>
      <Paper>
        <Grid rows={data} columns={columns}>
          <VirtualTable
            rowComponent={TableRow}
            columnExtensions={tableColumnExtensions}
          />
          <TableHeaderRow />
        </Grid>
      </Paper>
    </Fragment>
  );
}

interface Props {
  data: any;
}

export default ModalItem;
