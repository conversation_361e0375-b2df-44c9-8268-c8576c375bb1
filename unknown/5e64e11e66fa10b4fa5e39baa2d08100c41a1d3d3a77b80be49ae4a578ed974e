import React from 'react';
import cx from 'classnames';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import { makeStyles } from '@mui/styles';
import styles from './styles';
import { Tooltip } from '@mui/material';

const useStyles = makeStyles(styles);

const BreadcrumbItem = ({
  name,
  id,
  index,
  icon,
  onClick,
  isHidden = false,
  isDisabled = false,
  countLevel,
}: Props) => {
  const classes = useStyles();
  return isHidden ? (
    <MenuItem
      onClick={onClick}
      data-id={id}
      data-index={index}
      disabled={isDisabled}
    >
      {name}
    </MenuItem>
  ) : (
    <Tooltip title={name}>
      <Button
        onClick={onClick}
        data-id={id}
        data-index={index}
        className={cx(
          classes.crumbItem,
          isDisabled ? classes.crumbItemDisable : {}
        )}
        disabled={isDisabled}
      >
        {icon}
        <span
          className={classes.textItem}
          style={{
            maxWidth: `calc((100vw - 300px) / ${countLevel ? countLevel : 1})`,
          }}
          data-testid="name"
        >
          {name}
        </span>
      </Button>
    </Tooltip>
  );
};

interface Props {
  id?: string;
  index: number;
  name?: string;
  isHidden?: boolean;
  icon?: React.ReactNode;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  isDisabled?: boolean;
  countLevel?: number;
}

export default BreadcrumbItem;
