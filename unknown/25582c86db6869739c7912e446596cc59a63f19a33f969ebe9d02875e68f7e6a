import React, { PureComponent } from 'react';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import SearchIcon from '@mui/icons-material/Search';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ZoomOutMapIcon from '@mui/icons-material/ZoomOutMap';
import CloseIcon from '@mui/icons-material/Close';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import Typography from '@mui/material/Typography';
import Input from '@mui/material/Input';
import { get, isFinite } from 'lodash';
import screenfull from 'screenfull';

import * as styles from './styles.scss';

const SCALE_CONSTANT = 1.25;

class ViewerToolBar extends PureComponent<Props> {
  static defaultProps = {
    userScale: 1,
    searchText: '',
    currentSearchMatch: null,
    totalSearchMatches: null,
    currentPageNumber: 1,
    isSearchOpen: false,
  };

  state = {
    pageNumberInput: '',
  };

  componentWillUnmount() {
    // Empty callback for screenfull component
    const emptyCallback = function () {
      /* do nothing */
    };

    // TODO: Is this necessary? We never register a screenfull change handler
    // Also, if we did wouldn't the callback need to be the same as the one registered?
    if (screenfull.isEnabled) {
      screenfull.off('change', emptyCallback);
    }
  }

  pageDown = () => {
    if (this.props.onScrollToPage && this.props.currentPageNumber) {
      this.props.onScrollToPage(this.props.currentPageNumber + 1);
    }
  };

  pageUp = () => {
    if (this.props.onScrollToPage && this.props.currentPageNumber) {
      this.props.onScrollToPage(this.props.currentPageNumber - 1);
    }
  };

  zoomIn = () => {
    if (this.props.userScale) {
      const newScale = this.props.userScale * SCALE_CONSTANT;
      this.setZoom(newScale);
    }
  };
  zoomOut = () => {
    if (this.props.userScale) {
      const newScale = this.props.userScale / SCALE_CONSTANT;
      this.setZoom(newScale);
    }
  };
  setZoom = (userScale: number) => {
    if (isFinite(userScale) && this.props.onScale) {
      this.props.onScale({ userScale, overrideScale: null });
    }
  };

  fitWidth = () => {
    this.setZoom(1);
  };

  toggleFullScreen = () => {
    if (screenfull.isEnabled && get(this.props.viewerRef, 'current')) {
      if (screenfull.isFullscreen) {
        screenfull.exit();
      } else {
        screenfull.request(this.props.viewerRef.current);
      }
    }
  };

  handleSearchTextChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (this.props.onSearchTextChange) {
      this.props.onSearchTextChange(e.target.value);
    }
  };

  handlePageChange = (ev: React.KeyboardEvent) => {
    if (ev.key === 'Enter') {
      const target = ev.target as HTMLInputElement;
      const requestedPage = target.valueAsNumber;
      if (requestedPage >= 1 && requestedPage <= this.props.numPages) {
        if (this.props.onScrollToPage) {
          this.props.onScrollToPage(requestedPage);
        }
        this.setState({ pageNumberInput: '' });
      }
    }
  };

  handlePageInput = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    this.setState({ pageNumberInput: e.target.value });
  };

  handlePageInputBlur = () => {
    this.setState({ pageNumberInput: '' });
  };

  toggleSearchBar = () => {
    if (this.props.onToggleSearchBar) {
      this.props.onToggleSearchBar();
    }
  };

  handlePrevSearchMatch = () => {
    if (this.props.onPrevSearchMatch) {
      this.props.onPrevSearchMatch();
    }
  };

  handleNextSearchMatch = () => {
    if (this.props.onNextSearchMatch) {
      this.props.onNextSearchMatch();
    }
  };

  handleSearchKeyDown = (ev: React.KeyboardEvent) => {
    if (ev.key === 'Enter') {
      if (ev.shiftKey) {
        this.handlePrevSearchMatch();
      } else {
        this.handleNextSearchMatch();
      }
    } else if (ev.key === 'Escape') {
      this.toggleSearchBar();
    }
  };

  render() {
    const {
      currentPageNumber,
      numPages,
      searchText,
      currentSearchMatch,
      totalSearchMatches,
      isSearchOpen,
    } = this.props;
    const { pageNumberInput } = this.state;
    const buttonStyle = {
      height: '36px',
      width: '36px',
    };
    return (
      <div>
        <Toolbar
          classes={{
            root: styles.toolbar,
          }}
          style={{ minHeight: '48px', height: '48px' }}
        >
          <Tooltip title="Find in Document">
            <IconButton
              style={buttonStyle}
              onClick={this.toggleSearchBar}
              size="large"
            >
              <SearchIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Page Down">
            <IconButton
              style={buttonStyle}
              onClick={this.pageDown}
              size="large"
            >
              <ArrowDownwardIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Page Up">
            <IconButton style={buttonStyle} size="large">
              <ArrowUpwardIcon onClick={this.pageUp} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Go to Page">
            <Input
              classes={{
                root: styles.pageNumberInputRoot,
                input: styles.pageNumberInput,
              }}
              style={{ fontSize: '16px' }}
              placeholder={String(currentPageNumber)}
              value={pageNumberInput}
              onChange={this.handlePageInput}
              onKeyPress={this.handlePageChange}
              onBlur={this.handlePageInputBlur}
              disableUnderline
            />
          </Tooltip>
          <Typography style={{ fontSize: '16px', paddingRight: '16px' }}>
            / {numPages}
          </Typography>
          <Tooltip title="Fit to Width">
            <IconButton
              style={buttonStyle}
              onClick={this.fitWidth}
              size="large"
            >
              <FullscreenIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton style={buttonStyle} onClick={this.zoomOut} size="large">
              <RemoveCircleOutlineIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom In">
            <IconButton style={buttonStyle} onClick={this.zoomIn} size="large">
              <AddCircleOutlineIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Fullscreen">
            <IconButton style={buttonStyle} size="large">
              <ZoomOutMapIcon onClick={this.toggleFullScreen} />
            </IconButton>
          </Tooltip>
        </Toolbar>
        {isSearchOpen && (
          <Toolbar
            classes={{
              root: styles.toolbar,
            }}
            style={{ minHeight: '48px', height: '48px' }}
          >
            <Input
              value={searchText}
              className={styles.searchInput}
              onChange={this.handleSearchTextChange}
              onKeyDown={this.handleSearchKeyDown}
              autoFocus
            />
            {totalSearchMatches ? (
              <Typography>
                {currentSearchMatch} of {totalSearchMatches}
              </Typography>
            ) : null}
            <Tooltip title="Previous">
              <div>
                <IconButton
                  style={buttonStyle}
                  onClick={this.handlePrevSearchMatch}
                  disabled={!totalSearchMatches}
                  size="large"
                >
                  <ExpandLessIcon />
                </IconButton>
              </div>
            </Tooltip>
            <Tooltip title="Next">
              <div>
                <IconButton
                  style={buttonStyle}
                  onClick={this.handleNextSearchMatch}
                  disabled={!totalSearchMatches}
                  size="large"
                >
                  <ExpandMoreIcon />
                </IconButton>
              </div>
            </Tooltip>
            <Tooltip title="Close">
              <IconButton
                style={buttonStyle}
                onClick={this.toggleSearchBar}
                size="large"
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Toolbar>
        )}
      </div>
    );
  }
}

interface Props {
  currentPageNumber?: number;
  numPages: number;
  userScale?: number;
  onScale: (data: any) => void;
  onSearchTextChange: (value: string) => void;
  searchText?: string;
  viewerRef: any;
  isSearchOpen?: boolean;
  currentSearchMatch?: number;
  totalSearchMatches?: number;
  onPrevSearchMatch: () => void;
  onNextSearchMatch: () => void;
  onScrollToPage: (page: number) => void;
  onToggleSearchBar: () => void;
}

export default ViewerToolBar;
