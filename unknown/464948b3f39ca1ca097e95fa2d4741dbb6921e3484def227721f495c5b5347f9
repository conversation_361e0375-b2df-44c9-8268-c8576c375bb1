@import 'src/variables';

$sidebarwidth: 220;
$color: #555f7c;

:export {
  // stylelint-disable
  sidebarwidth: $sidebarwidth;
  // stylelint-enable
}

.list {
  padding-top: 1px;
}

.move-item-icon-folder svg {
  color: #555f7c;
}

.move-item,
.move-item-selected {
  &:hover .icon-menu {
    display: block;
  }

  .arrow-right {
    color: $color;
  }

  &:hover .arrow-right {
    display: none;
  }
}

.move-item {
  padding: 2px;

  &:focus {
    background-color: #fff;
  }
}

.move-item-selected {
  background-color: #e1fddc;
}

.move-item-text {
  width: 100% !important;
  line-height: 18px !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-transform: none !important;
  font-size: 12px !important;
  font-family: Roboto, sans-serif !important;
  padding-left: 2px !important;
}

.move-item-icon-folder {
  margin-left: 8px !important;
}

.loading-folder {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 90px;
  left: 0;
  right: 0;
  margin: 0 auto;
}
