import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import {
  GET_CHART_DATA,
  SET_ANALYTICS_DATE_RANGE,
  SET_CHART_SETTINGS,
  GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS,
  GET_CONTACT_ANALYTICS_CHART_CONFIG_FAILURE,
  REQUERY,
  requeryData,
  searchContactData,
  searchContactDemographicData,
  addQueryPending,
  setSettings,
  setLoadingUserConfig,
} from './actions';
import { selectChartData } from './reducer';

function* watchGetChartData() {
  yield* takeLatest(GET_CHART_DATA, function* (action) {
    const { containerId, chartDef } = action.payload;
    const chartData = yield* select(selectChartData);

    if (!chartData?.[containerId]?.hasAllData) {
      for (const _ of chartDef.dataQueries) {
        yield* put(addQueryPending());
      }
      for (const dataQuery of chartDef.dataQueries) {
        yield* put(
          searchContactData(dataQuery, containerId, chartDef, false) as any
        );
      }
      if (chartDef.hasDemographics) {
        yield* put(
          searchContactDemographicData(
            chartDef.demographicsConfig,
            containerId,
            chartDef,
            false
          )
        );
        yield* put(addQueryPending());
      }
    }
  });
}

function* watchForRequeryableEvents() {
  yield* takeLatest(
    [SET_ANALYTICS_DATE_RANGE, SET_CHART_SETTINGS, REQUERY],
    function* (action) {
      if (
        SET_CHART_SETTINGS.match(action) &&
        !action.payload.settingChanges.includes('aggSize')
      ) {
        return;
      }
      const chartData: any = yield* select(selectChartData);
      for (const kvp of Object.entries(chartData)) {
        const [containerId, chart]: [containerId: string, chart: any] = kvp;
        if (chart?.chartDef?.dataQueries) {
          for (const _ of chart.chartDef.dataQueries) {
            yield* put(addQueryPending());
          }
          for (const dataQuery of chart.chartDef.dataQueries) {
            yield* put(
              searchContactData(dataQuery, containerId, chart.chartDef, true)
            );
          }
          if (chart.chartDef.hasDemographics) {
            yield* put(
              searchContactDemographicData(
                chart.chartDef.demographicsConfig,
                containerId,
                chart.chartDef,
                false
              )
            );
            yield* put(addQueryPending());
          }
        }
      }
    }
  );
}

function* watchContactAnalyticsChartConfigSuccess() {
  yield* takeLatest(
    GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS,
    function* (action) {
      try {
        const contactAnalyticsSettings =
          action.payload.payload.getUserSettings?.find(
            (s: { key: string; value: string }) =>
              s.key === 'contactAnalyticsConfig'
          );
        yield* put(
          setSettings(JSON.parse(contactAnalyticsSettings?.value ?? '{}'))
        );
        yield* put(requeryData());
      } catch (e) {
        console.error('watchContactAnalyticsChartConfigSuccess', e);
      }
    }
  );
}

function* watchContactAnalyticsChartConfig() {
  yield* takeLatest(
    [
      GET_CONTACT_ANALYTICS_CHART_CONFIG_SUCCESS,
      GET_CONTACT_ANALYTICS_CHART_CONFIG_FAILURE,
    ],
    function* () {
      yield* put(setLoadingUserConfig(false));
    }
  );
}

export default function* contactAnalytics() {
  yield* all([
    fork(watchGetChartData),
    fork(watchForRequeryableEvents),
    fork(watchContactAnalyticsChartConfigSuccess),
    fork(watchContactAnalyticsChartConfig),
  ]);
}
