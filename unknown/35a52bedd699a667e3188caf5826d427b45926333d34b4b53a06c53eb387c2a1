import * as styles from './styles.scss';
import M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './MUITextField';
import { INPUT_NAME_DURATION, DURATION_TYPE } from 'state/modules/filters';

function DurationFilter({
  searchValue,
  handleInputChange,
  clearDuration,
  gt,
  lte,
}: Props) {
  return (
    <div className={styles.durationMain}>
      <div className={styles.duration}>
        <MUITextField
          name={INPUT_NAME_DURATION.HOURSGT}
          value={searchValue[INPUT_NAME_DURATION.HOURSGT]}
          handleInputChange={handleInputChange}
        />{' '}
        <span>:</span>
        <MUITextField
          name={INPUT_NAME_DURATION.MINUTESGT}
          value={searchValue[INPUT_NAME_DURATION.MINUTESGT]}
          handleInputChange={handleInputChange}
        />{' '}
        <span>:</span>
        <MUITextField
          name={INPUT_NAME_DURATION.SECONDSGT}
          value={searchValue[INPUT_NAME_DURATION.SECONDSGT]}
          handleInputChange={handleInputChange}
          clearDuration={clearDuration}
          showClearTimeIcon
          width={50}
          type={DURATION_TYPE.GT}
          totalTime={gt}
        />
      </div>
      <span> - </span>
      <div className={styles.duration}>
        <MUITextField
          name={INPUT_NAME_DURATION.HOURSLTE}
          value={searchValue[INPUT_NAME_DURATION.HOURSLTE]}
          handleInputChange={handleInputChange}
        />{' '}
        <span>:</span>
        <MUITextField
          name={INPUT_NAME_DURATION.MINUTESLTE}
          value={searchValue[INPUT_NAME_DURATION.MINUTESLTE]}
          handleInputChange={handleInputChange}
        />{' '}
        <span>:</span>
        <MUITextField
          name={INPUT_NAME_DURATION.SECONDSLTE}
          value={searchValue[INPUT_NAME_DURATION.SECONDSLTE]}
          handleInputChange={handleInputChange}
          clearDuration={clearDuration}
          showClearTimeIcon
          width={50}
          type={DURATION_TYPE.LTE}
          totalTime={lte}
        />
      </div>
    </div>
  );
}

interface Props {
  handleInputChange: (value: number, name: string) => void;
  searchValue: {
    hoursGt: number;
    minutesGt: number;
    secondsGt: number;
    hoursLte: number;
    minutesLte: number;
    secondsLte: number;
  };
  clearDuration: (type: string) => void;
  gt: number;
  lte: number;
}

export default DurationFilter;
