import IconButton from '@mui/material/IconButton';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { withStyles, ClassNameMap } from '@mui/styles';
import React from 'react';
import styles from './styles';

interface FilePickerHeaderProps {
  selectedTab?: string;
  onSelectTab?: (value: string) => void;
  onClose?: () => void;
  allowUrlUpload?: boolean;
  multiple?: boolean;
  title?: string;
  fileCount?: number;
  maxFiles?: number;
  hideTabs?: boolean;
  titleIcon?: React.ReactNode;
  message?: string;
  classes?: ClassNameMap;
}

const FilePickerHeader: React.FC<FilePickerHeaderProps> = ({
  selectedTab,
  onSelectTab,
  onClose,
  allowUrlUpload = false,
  multiple = false,
  title = 'File Picker',
  fileCount,
  maxFiles,
  hideTabs = false,
  titleIcon,
  message,
  classes,
}) => {
  const handleTabChange = (
    _event: React.ChangeEvent<EventTarget>,
    value: string
  ) => {
    if (onSelectTab) {
      onSelectTab(value);
    }
  };

  return (
    <div className={classes?.filePickerHeader || ''}>
      <span
        className={classes?.filePickerTitle || ''}
        data-veritone-element="picker-header-title"
        data-testid="file-picker-title"
      >
        {titleIcon && (
          <div className={classes?.titleIconContainer || ''}>{titleIcon}</div>
        )}
        {title}
        {multiple && maxFiles && (
          <span className={classes?.count || ''}>
            {fileCount} / {maxFiles}
          </span>
        )}
      </span>
      {onClose && (
        <IconButton
          data-veritone-element="picker-header-close-btn"
          classes={{ root: classes?.filePickerCloseButton || '' }}
          onClick={onClose}
          size="large"
          data-testid="file-picker-close-button"
        >
          <i className="icon-close-exit" />
        </IconButton>
      )}
      {message && (
        <div
          className={classes?.filePickerMessage || ''}
          data-veritone-element="picker-header-msg"
          data-testid="file-picker-msg"
        >
          {message}
        </div>
      )}
      {!hideTabs && (
        <Tabs
          value={selectedTab}
          indicatorColor="primary"
          onChange={handleTabChange}
          className={classes?.filePickerTabs || ''}
          data-testid="file-picker-tab"
        >
          <Tab
            label="Upload"
            value="upload"
            data-testid="file-picker-upload-tab"
          />
          {allowUrlUpload && (
            <Tab
              label="By URL"
              value="by-url"
              data-testid="file-picker-url-tab"
            />
          )}
        </Tabs>
      )}
    </div>
  );
};

export default withStyles(styles)(FilePickerHeader);
