import { withStyles } from '@mui/styles';
import FileListItem from './FileListItem';
import styles from './styles';

interface Props {
  files: File[];
  onRemoveFile: (index: number) => void;
  classes?: any;
  enableResize?: boolean;
  onFileResize: (file: File) => void;
}

function FileList(props: Props) {
  const { classes, enableResize = false, onFileResize } = props;
  return (
    <div className={classes.fileList} data-testid="file-list">
      {props.files.map((file, index) => {
        return (
          <FileListItem
            key={`${file.name}-${file.size}`}
            index={index}
            file={file}
            onRemoveFile={props.onRemoveFile}
            enableResize={enableResize}
            onFileResize={onFileResize}
          />
        );
      })}
    </div>
  );
}

export default withStyles(styles)(FileList);
