import React from 'react';
import TextField from '@mui/material/TextField';
import * as styles from './styles.scss';

function DateRangeFilter(props: Props) {
  const { handleChange, startDate, endDate, dateError } = props;
  return (
    <div data-testid="date-range-filter">
      <form
        data-test="filter-section-date-range"
        className={styles['form-container']}
      >
        <TextField
          className={styles['start-date']}
          id="startDate"
          type="date"
          onChange={handleChange}
          data-testid="text-field"
          value={startDate}
          variant="standard"
        />
        <div className={styles['separator']}>to</div>
        <TextField
          className={styles['end-date']}
          id="endDate"
          type="date"
          onChange={handleChange}
          data-testid="text-field"
          value={endDate}
          variant="standard"
        />
      </form>
      <div className={styles['error-mess']} data-testid="error-message">
        {dateError && `* ${dateError}`}
      </div>
    </div>
  );
}

export default DateRangeFilter;

interface Props {
  handleChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  startDate: string;
  endDate: string;
  dateError?: string;
}
