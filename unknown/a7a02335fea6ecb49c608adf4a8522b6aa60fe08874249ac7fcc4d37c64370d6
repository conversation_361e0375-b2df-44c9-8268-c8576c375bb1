/* cspell:words domtoimage */

import { DateTime } from 'luxon';
import poweredByVeritone from './poweredByVeritoneImg';
import domtoimage from 'dom-to-image';
import { Chart } from '.';

export default async (
  title: string,
  chartInstances: Partial<Record<string, any>>,
  orgName: string,
  charts: Chart[],
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  setLoading(true);

  const chartInstancesK = charts.map((c) => c.containerId);
  const chartInstancesV = chartInstancesK.map((cik) => chartInstances[cik]);
  const democraphicsChartImages: (HTMLElement | null)[] = [];

  chartInstancesK
    .map((ck) => charts.find((c) => c.containerId === ck))
    .map((c) => {
      democraphicsChartImages.push(
        document.getElementById(`${c?.containerId}-demographics`)
      );
    });

  const democraphicsChartImagesResult = await Promise.all(
    democraphicsChartImages.map((node) =>
      node ? domtoimage.toPng(node) : Promise.resolve(false)
    )
  );

  return Promise.all([
    chartInstancesV[0].exporting.pdfmake,
    ...chartInstancesV.map((c) => c.exporting.getImage('svg')),
  ])
    .then((res) => {
      const pdfMake = res[0];

      const doc: Doc = {
        pageSize: 'A4',
        pageOrientation: 'portrait',
        pageMargins: [30, 60, 30, 30],
        content: [
          {
            text: title,
            fontSize: 10,
            margin: [0, 0, 0, 15],
          },
        ],
        header: {
          margin: [0, 20, 0, 0],
          columns: [
            {
              text: orgName,
              fontSize: 12,
              bold: true,
              alignment: 'center',
            },
          ],
        },
        footer: [
          {
            image: poweredByVeritone,
            fit: [60, 60],
            absolutePosition: { x: 265, y: -20 },
          },
          {
            text: `Exported ${DateTime.now().toFormat('FFF')}`,
            fontSize: 6,
            width: 50,
            height: 50,
            absolutePosition: { x: 35, y: -5 },
          },
        ],
      };

      res.forEach((r, i) => {
        if (i !== 0) {
          doc.content.push({
            margin: [0, 25, 0, 0],
            columns: democraphicsChartImagesResult[i - 1]
              ? [
                  {
                    image: r,
                    width: 425,
                  },
                  {
                    image: democraphicsChartImagesResult[i - 1],
                    width: 100,
                  },
                ]
              : [
                  {
                    image: r,
                    width: 525,
                  },
                ],
          });
        }
      });
      return pdfMake.createPdf(doc).download(`${title}.pdf`);
    })
    .catch((error) => {
      console.error('Error exporting PDF:', error);
    })
    .finally(() => {
      setTimeout(() => setLoading(false), 500);
    });
};

interface Doc {
  pageSize: string;
  pageOrientation: string;
  pageMargins: number[];
  content: {
    text?: string;
    fontSize?: 10;
    margin: number[];
    columns?: { image: string; width: number }[];
  }[];
  header: {
    margin: number[];
    columns: {
      text: string;
      fontSize: number;
      bold: boolean;
      alignment: string;
    }[];
  };
  footer: {
    image?: string;
    fit?: number[];
    absolutePosition: { x: number; y: number };
    text?: string;
    fontSize?: number;
    width?: number;
    height?: number;
  }[];
}
