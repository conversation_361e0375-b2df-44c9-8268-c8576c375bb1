import { noop } from 'lodash';
import { render } from '@testing-library/react';
import FileProgressDialog from '.';

describe('FileProgressDialog', function () {
  it('shows the progressMessage', function () {
    const { getByTestId } = render(
      <FileProgressDialog
        progressMessage="test-progress"
        onRetryDone={noop}
        completeStatus={'failure'}
        retryRequest={noop}
        onClose={noop}
      />
    );

    expect(getByTestId('file-picker-msg')).toHaveTextContent('test-progress');
  });

  it('shows the failure icon with props.completeStatus == "failure"', function () {
    const { getByTestId } = render(
      <FileProgressDialog
        completeStatus="failure"
        onRetryDone={noop}
        retryRequest={noop}
        onClose={noop}
      />
    );

    expect(getByTestId('failure-icon')).toBeInTheDocument();
  });

  it('shows the failure icon with props.completeStatus == "warning"', function () {
    const { getByTestId } = render(
      <FileProgressDialog
        completeStatus="warning"
        onRetryDone={noop}
        retryRequest={noop}
        onClose={noop}
      />
    );

    expect(getByTestId('warn-icon')).toBeInTheDocument();
  });
});
