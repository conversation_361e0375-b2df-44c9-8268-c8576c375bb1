import React, { useRef, useEffect, useState } from 'react';
import { defaultState } from './reducer';

const worker = new Worker(new URL('./worker.ts', import.meta.url));

const buildProps = (
  workerMapStateToProps: any,
  workerMapDispatchToProps: any,
  state: typeof defaultState
) => ({
  ...Object.keys(workerMapDispatchToProps).reduce((acc: any, f: any) => {
    acc[f] = (...args: any) => {
      return worker.postMessage({ actionName: f, args: JSON.stringify(args) });
    };
    return acc;
  }, {}),
  ...workerMapStateToProps(state),
  worker,
});

worker.onmessage = function (e) {
  window.dispatchEvent(
    new CustomEvent('contact-analytics-state', {
      detail: { state: e.data.state },
    })
  );
};

const workerConnect =
  (workerMapStateToProps: any, workerMapDispatchToProps: any) =>
  (Component: React.ComponentType<any>) => {
    /* eslint-disable react/display-name */
    return (componentProps: any) => {
      const [props, setProps] = useState(
        buildProps(
          workerMapStateToProps,
          workerMapDispatchToProps,
          defaultState
        )
      );
      const state = useRef('');

      const onContactAnalyticsStateEvent = (e: Event) => {
        const customEvent = e as CustomEvent<{ state: typeof defaultState }>;
        const nextState = JSON.stringify(customEvent.detail.state);
        if (state.current !== nextState) {
          state.current = nextState;
          const newProps = buildProps(
            workerMapStateToProps,
            workerMapDispatchToProps,
            customEvent.detail.state
          );
          setProps(newProps);
        }
      };

      useEffect(() => {
        window.addEventListener(
          'contact-analytics-state',
          onContactAnalyticsStateEvent
        );
        return () => {
          window.removeEventListener(
            'contact-analytics-state',
            onContactAnalyticsStateEvent
          );
        };
      }, []);
      return <Component {...props} {...componentProps} />;
    };
  };

export { workerConnect };
