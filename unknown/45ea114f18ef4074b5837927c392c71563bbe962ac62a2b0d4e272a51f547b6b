import { render } from '@testing-library/react';
import FilePickerFooter from '.';

describe('FilePickerFooter', () => {
  it('should have an "Upload" button ', () => {
    const { getByTestId } = render(<FilePickerFooter />);
    expect(getByTestId('file-picker-footer-upload-button')).toBeInTheDocument();
  });
  it('should have an "Cancel" button', () => {
    const { getByTestId } = render(<FilePickerFooter />);
    expect(getByTestId('file-picker-footer-cancel-button')).toBeInTheDocument();
  });
});
