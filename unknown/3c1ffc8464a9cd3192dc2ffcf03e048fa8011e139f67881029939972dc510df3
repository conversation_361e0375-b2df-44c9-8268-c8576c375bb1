import React from 'react';
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import * as styles from './styles/sectiontree.scss';
const DefaultCheckboxes = ({
  checkboxValues,
  onCheckboxChange,
  formComponentIdAtLeaf,
  defaultCheckboxSelectedItems,
}: Props) => (
  <div>
    <FormControl data-test="filter-section-topics">
      <FormGroup>
        {checkboxValues.map((value, index) => {
          const checkboxValue = value.toString();
          return (
            <FormControlLabel
              key={value + '' + String(index)}
              control={
                <Checkbox
                  id={formComponentIdAtLeaf}
                  color="primary"
                  value={checkboxValue}
                  onChange={onCheckboxChange}
                  checked={defaultCheckboxSelectedItems.includes(String(value))}
                  data-testid={formComponentIdAtLeaf}
                />
              }
              label={value}
              className={styles['form-label']}
              data-testid={checkboxValue}
            />
          );
        })}
      </FormGroup>
    </FormControl>
  </div>
);

export default DefaultCheckboxes;

interface Props {
  onCheckboxChange:
    | ((event: React.ChangeEvent<HTMLInputElement>) => void)
    | undefined;
  formComponentIdAtLeaf: string;
  checkboxValues: (string | number)[];
  defaultCheckboxSelectedItems: string[];
}
