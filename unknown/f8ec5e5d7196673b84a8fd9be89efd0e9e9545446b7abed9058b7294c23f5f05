import { clamp } from 'lodash';

// export function getMousePosition(e) {
//   // https://stackoverflow.com/questions/8389156
//   let el = e.target,
//     x = 0,
//     y = 0;
//
//   // fixme -- scroll position seems to not be taken into account
//   while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {
//     x += el.offsetLeft - el.scrollLeft;
//     y += el.offsetTop - el.scrollTop;
//     el = el.offsetParent;
//   }
//
//   x = e.clientX - x;
//   y = e.clientY - y;
//
//   return { x: x, y: y };
// }

export function getMousePosition(
  e: React.MouseEvent<HTMLElement>,
  clamp_element: HTMLElement | null = null
) {
  let el = e.target as HTMLElement,
    el_left = 0,
    el_top = 0,
    x = e.clientX,
    y = e.clientY;

  if (clamp_element) {
    const bounds = clamp_element.getBoundingClientRect();
    x = clamp(x, bounds.left, bounds.right);
    y = clamp(y, bounds.top, bounds.bottom);
    el = clamp_element;
  }

  while (el.offsetParent) {
    el_left += el.offsetLeft;
    el_top += el.offsetTop;
    el = el.offsetParent as HTMLElement;
  }

  el = clamp_element || (e.target as HTMLElement);
  while (el.parentNode) {
    el_left -= el.scrollLeft;
    el_top -= el.scrollTop;
    el = el.parentNode as HTMLElement;
  }

  x -= el_left;
  y -= el_top;

  return { x, y };
}
