import React, { Fragment } from 'react';
import IconButton from '@mui/material/IconButton';
import Close from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import * as styles from './styles/container.scss';
import Header from './header/Header';
import SectionTree from './SectionTree';

export class FiltersContainer extends React.Component<Props> {
  static defaultProps = {
    selectedFilters: [],
  };

  handleApplyFilter = (event: React.MouseEvent<HTMLButtonElement>) => {
    const selectedItems = (event.target as HTMLInputElement).getAttribute(
      'data-filters'
    );
    if (selectedItems) {
      this.props.onClick(JSON.parse(selectedItems));
    }
  };

  render() {
    return (
      <div className={styles.container} data-testid="filters-sdk-components">
        <Header
          rightIconButtonElement={
            <IconButton
              onClick={this.props.closeFilter}
              aria-label="close filters"
              data-testid="close-filter"
              size="large"
            >
              <Close />
            </IconButton>
          }
        />
        {
          <Fragment>
            <SectionTree
              sections={this.props.filtersSections}
              formComponents={this.props.formComponents}
              checkboxCount={this.props.checkboxCount}
              onCheckboxChange={this.props.onCheckboxChange}
              defaultCheckboxSelectedItems={
                this.props.defaultCheckboxSelectedItems
              }
            />
            <div className={styles.buttonContainer}>
              <Button
                data-test="filter-apply-filter"
                variant="contained"
                color="primary"
                disabled={this.props.isDisabled}
                data-filters={JSON.stringify(this.props.selectedFilters)}
                onClick={this.handleApplyFilter}
                data-testid="apply-filter"
              >
                Apply Filter
              </Button>
              <Button
                data-test="filter-clear-filter"
                variant="contained"
                color="primary"
                disabled={
                  !Object.values(this.props.checkboxCount).some(
                    (el) => typeof el === 'number' && el > 0
                  )
                }
                data-filters={JSON.stringify(this.props.selectedFilters)}
                onClick={this.props.onClearButton}
                data-testid="clear-filter"
              >
                Clear Filter
              </Button>
            </div>
          </Fragment>
        }
      </div>
    );
  }
}
export default FiltersContainer;

interface Props {
  formComponents: any;
  filtersSections: any;
  selectedFilters?: any;
  onClick: (jsonObj: any) => void;
  closeFilter: () => void;
  onClearButton: () => void;
  checkboxCount: any;
  onCheckboxChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isDisabled: boolean;
  defaultCheckboxSelectedItems: string[];
}
