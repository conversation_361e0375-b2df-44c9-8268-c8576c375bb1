import { noop } from 'lodash';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { formatBytes } from '../../../../helpers/format';

import FileListItem from './FileListItem';
import FileList from '.';

const createMockFile = (name: string, size: number, mimeType: string) => {
  const blobName = name || 'mock.txt';
  const blobSize = size || 1024;
  const blobMimeType = mimeType || 'plain/txt';

  const range = (count: number) => {
    let output = '';
    for (let i = 0; i < count; i++) {
      output += 'a';
    }
    return output;
  };

  const blob = new File([range(blobSize)], blobName, { type: blobMimeType });
  return blob;
};

const mockFiles = [
  createMockFile(
    '_89716241_thinkstockphotos-523060154.jpg',
    1024,
    'image/jpeg'
  ),
  createMockFile('funny_picture.jpg', 2048, 'image/jpeg'),
  createMockFile('photo.jpeg', 12398, 'image/jpeg'),
];

describe('FileList', () => {
  it('should render a list of files if an array of files are passed to it', async () => {
    const { getByTestId } = render(
      <FileList files={mockFiles} onRemoveFile={noop} onFileResize={noop} />
    );
    await waitFor(() => {
      expect(getByTestId('file-list')).toBeInTheDocument();
    });
  });
});

describe('FileListItem', () => {
  it('should display the file name', async () => {
    const { getByTestId } = render(
      <FileListItem
        file={mockFiles[0]!}
        index={0}
        onRemoveFile={noop}
        onFileResize={noop}
      />
    );
    await waitFor(() => {
      expect(getByTestId('item-name-text')).toHaveTextContent(
        mockFiles[0]!.name
      );
    });
  });

  it('should display the size of the file', async () => {
    const { getByTestId } = render(
      <FileListItem
        file={mockFiles[0]!}
        index={0}
        onRemoveFile={noop}
        onFileResize={noop}
      />
    );
    await waitFor(() => {
      expect(getByTestId('item-file-size-text')).toHaveTextContent(
        formatBytes(mockFiles[0]!.size)
      );
    });
  });

  it('should display a remove button', async () => {
    const { getByTestId } = render(
      <FileListItem
        file={mockFiles[0]!}
        index={0}
        onRemoveFile={noop}
        onFileResize={noop}
      />
    );
    await waitFor(() => {
      expect(getByTestId('file-picker-delete-btn')).toBeInTheDocument();
    });
  });

  it('onRemoveFIle should be called when the remove button is clicked', async () => {
    const onRemoveFile = jest.fn();
    const { getByTestId } = render(
      <FileListItem
        file={mockFiles[0]!}
        onRemoveFile={onRemoveFile}
        index={0}
        onFileResize={noop}
      />
    );
    fireEvent.click(getByTestId('file-picker-delete-btn'));
    await waitFor(() => {
      expect(onRemoveFile).toHaveBeenCalled();
    });
  });
});
