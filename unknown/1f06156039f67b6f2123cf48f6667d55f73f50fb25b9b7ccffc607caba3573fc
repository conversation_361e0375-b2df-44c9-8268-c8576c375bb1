import React from 'react';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import * as styles from './styles.scss';

export const ENTITY_NONE = '$_NONE_@';

export function EntityTypesFilter(props: Props) {
  const { initialEntityNames, entityValue, onChange, entityError, style } =
    props;
  return (
    <div data-testid="entity-types-filter" style={style}>
      <div className={styles['error-mess']}>
        {entityError && `* ${entityError}`}
      </div>
      <div>
        <FormControl
          data-test="filter-section-entity-types"
          component="fieldset"
        >
          <RadioGroup
            name="entityTypes"
            value={entityValue}
            onChange={onChange}
            data-testid="radio-group"
          >
            <FormControlLabel
              control={
                <Radio
                  color="primary"
                  checked={
                    !entityValue || entityValue === ENTITY_NONE ? true : false
                  }
                />
              }
              value={ENTITY_NONE}
              label="NONE"
              data-test="NONE"
              className={styles['form-label-entity']}
              data-testid="form-control-label"
            />
            {initialEntityNames.map((name, index) => (
              <FormControlLabel
                key={name + index.toString()}
                control={
                  <Radio
                    color="primary"
                    checked={entityValue === name ? true : false}
                  />
                }
                value={name}
                label={name.toUpperCase()}
                className={styles['form-label-entity']}
                data-testid="form-control-label"
              />
            ))}
          </RadioGroup>
        </FormControl>
      </div>
    </div>
  );
}

interface Props {
  initialEntityNames: string[];
  entityValue: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  entityError?: string;
  style?: React.CSSProperties;
}
