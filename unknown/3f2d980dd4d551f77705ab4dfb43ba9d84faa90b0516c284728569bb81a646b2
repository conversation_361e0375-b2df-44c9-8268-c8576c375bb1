.containerCover {
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.containerContain {
  overflow: hidden;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.labelBackgroundContainer {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
}

.labelContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-align: center;
  height: 30%;
  width: 100%;

  span {
    font-weight: 200 !important;
    color: white;
  }
}
