import {
  searchAndSeizure,
  searchAndSeizureDistribution,
  searchAndConsentDistribution,
  contrabandFound,
  contrabandFoundDistribution,
  propertySeizureDistribution,
  propertySeizure,
  stopActionTaken,
  stopActionTakenDistribution,
  stopResults,
  stopResultsDistribution,
  stopResponseToCall,
  stopResponseToCallDistribution,
  stopByPrimaryReason,
  primaryReasonDistribution,
  stopByType,
  stopByTypeDistribution,
  averageStopDuration,
  stopByTimeOfDayMorningEvening,
  morningVersusEveningDistribution,
  stopByTimeOfDayAndNight,
  dayVersusNightDistribution,
  stopCountBySchool,
  numberOfPeopleStopped,
  gender,
  genderDistribution,
  age,
  ageDistribution,
  students,
  studentsDistribution,
  raceOrEthnicity,
  raceOrEthnicityDistribution,
  languageFluency,
  sexualOrientation,
  sexualOrientationDistribution,
  disabled,
  disabledDistributionGeneral,
  disabilities,
  disabilitiesDistribution,
} from './chartConfig';

export const ChartType = {
  Line: 'line',
  Pie: 'pie',
  Bar: 'bar',
  Table: 'table',
};

export interface Config {
  filter?: string;
  useFilter?: string;
  filterTextAll?: string;
  filterTerms: string[];
  useLegend: boolean;
  useTitle: boolean;
  lineWidth: number;
  lowerDateBound: string;
  upperDateBound: string;
  aggregationSize: string;
  TZ: string;
}

export interface ChartDefinition {
  name?: string;
  type?: string;
  containerStyles?: { [key: string]: string };
  configure?: (
    containerId: string,
    data: any,
    name: string,
    title: string,
    config: Config
  ) => void;
  dataQueries?: any;
  filterTerms?: any;
}

const chartDefinitions: ChartDefinition[] = [
  {
    name: 'SEARCH & SEIZURE',
    type: ChartType.Line,
    containerStyles: { height: '400px' },
    configure: searchAndSeizure.configure,
    dataQueries: searchAndSeizure.dataQueries,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Searches
        Search & Seizure
        Searches With Consent
        Searches Without Consent ---> (Searches -  SearchesWithConsent)
        No Search ---> (Total People -  Searches)
        Total People (bolder line)
    */
  },
  {
    name: 'SEARCH & SEIZURE DISTRIBUTION',
    type: ChartType.Bar,
    containerStyles: { height: '400px' },
    configure: searchAndSeizureDistribution.configure,
    dataQueries: searchAndSeizureDistribution.dataQueries,
    /*
      Total count of people
      Slices
        Search
        Search & Seizure
        Other (no search)
    */
  },
  {
    name: 'SEARCH & CONSENT DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '500px' },
    configure: searchAndConsentDistribution.configure,
    dataQueries: searchAndConsentDistribution.dataQueries,
    /*
      Total Count of people
      Slices
        Searches With Consent
        Searches Without Consent (searches - searches with consent)
        No Search (total - searches)
    */
  },
  {
    name: 'CONTRABAND FOUND',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...contrabandFound,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Firearm(s)
        Drugs/Narcotics
        Ammunition
        Drug Paraphernalia
        Weapon(s) other than a firearm
        Alcohol
        Suspected Stolen property
        Cell phone(s) or electronic device(s)
        Money
        Vehicle
        Other Contraband or evidence
    */
  },
  {
    name: 'CONTRABAND FOUND DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...contrabandFoundDistribution,
    /*
      Total Count of all contraband
      Slices
        Firearm(s)
        Drugs/Narcotics
        Ammunition
        Drug Paraphernalia
        Weapon(s) other than a firearm
        Alcohol
        Suspected Stolen property
        Cell phone(s) or electronic device(s)
        Money
        Vehicle
        Other Contraband or evidence
    */
  },
  {
    name: 'PROPERTY SEIZURE',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...propertySeizure,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Ammunition
        Cell phone(s) or electronic device(s)
        Vehicle
        Drug Paraphernalia
        Drugs/Narcotics
        Firearm(s)
        Other Contraband or evidence
        Alcohol
        Money
    */
  },
  {
    name: 'PROPERTY SEIZURE DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...propertySeizureDistribution,
    /*
      Total Count of property seized
      Slices
        Ammunition
        Cell phone(s) or electronic device(s)
        Vehicle
        Drug Paraphernalia
        Drugs/Narcotics
        Firearm(s)
        Other Contraband or evidence
        Alcohol
        Money
    */
  },
  {
    name: 'ACTION TAKEN',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopActionTaken,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months

      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people stopped
        VEHICLE
        DETENTION
        WEAPON
        CANINE
        SEARCH
        OTHER
        K12
        No Action Taken
    */
  },
  {
    name: 'ACTIONS TAKEN DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...stopActionTakenDistribution,
    /*
      Total people stopped
      Slices
        VEHICLE
        DETENTION
        WEAPON
        CANINE
        SEARCH
        OTHER
        K12
        No Action Taken
    */
  },
  {
    name: 'RESULT OF STOP',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopResults,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people stopped
        CITATION
        ARREST
        OTHER
        K-12
        No Action Taken
    */
  },
  {
    name: 'RESULT OF STOP DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...stopResultsDistribution,
    /*
      Total Count people stopped
      Slices
        CITATION
        ARREST
        OTHER
        K-12
        No Action Taken
    */
  },
  {
    name: 'RESPONSE TO CALL',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopResponseToCall,
    /*
      Provide the total stop count some where on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total Stops (bolder line)
        Response To Call = True Count
        Response To Call = False Count
    */
  },
  {
    name: 'RESPONSE TO CALL DISTRIBUTION',
    type: ChartType.Bar,
    containerStyles: { height: '615px' },
    ...stopResponseToCallDistribution,
    /* w
      Total Count Stops
      Slices
        Response To Call = True Count
        Response To Call = False Count
    */
  },
  {
    name: 'PRIMARY REASON FOUND',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopByPrimaryReason,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months

      Y-axis is the count
      Display a line for each Applicable Reason - color code to match the label
        Total People (bolder line)
        Traffic Violation
        Suspicion Of Criminal Activity
        Parole or Other Supervision
        Warrant/Wanted
        Truant Investigation
        Consensual Encounter
        Possible contaduct warrenting discipline (k12)
        Determine whether the student violated school policy (k12)
    */
  },
  {
    name: 'PRIMARY REASON FOUND DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...primaryReasonDistribution,
    /*
      Slices
        Traffic Violation
        Suspicion Of Criminal Activity
        Parole or Other Supervision
        Warrant/Wanted
        Truant Investigation
        Consensual Encounter
        Possible contaduct warrenting discipline (k12)
        Determine whether the student violated school policy (k12)
    */
  },
  {
    name: 'TYPE OF STOP',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopByType,
    /*
     Provide the total stop count somewhere on the chart
     X axis is the time span
       There is a hash or marker
         for each day if the time span is less than a month
         for each month if the time span is greater than 6 months
     Y-axis is the count
     Display a line for each  - color code to match the label
      Total people in stops
      Traffic (Primary Reason of Traffic Violation)
      Criminal (Primary Reason of Reasonable suspicion), known to be parole, knowledge of outstanding arrest, truancy)
      Other   (Primary Reason of possible conduct, determine whether violated school policy, consenting encounter)
   */
  },
  {
    name: 'TYPE OF STOP DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...stopByTypeDistribution,
    /*
    Slices
      Traffic (Primary Reason of Traffic Violation)
      Criminal (Primary Reason of Reasonable suspicion), known to be parole, knowledge of outstanding arrest, truancy)
      Other   (Primary Reason of possible conduct, determine whether violated school policy, consenting encounter)
    */
  },
  {
    name: 'AVERAGE STOP DURATION',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...averageStopDuration,
    /*
      Provide the average length of stops somewhere on the chart
      X-axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than a month (~max 120 units)
      Y-axis is the avg stop duration in minutes
      Legend/Rendering
        Display a line for the average stop duration
    */
  },
  {
    name: 'TIME OF DAY - MORNING & EVENING',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopByTimeOfDayMorningEvening,
    /*
      Provide count total per time of day somewhere on chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each Applicable time of day - color code to match the label
        Total Stops (bolder line)
        Morning
        Evening
    */
  },
  {
    name: 'TIME OF DAY - MORNING VERSUS EVENING DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...morningVersusEveningDistribution,
    /*
      Total Stops (bolder line)
        Slices
          Count for Morning Stops (12:00:00 AM - 11:59:59 AM)
          Count for Evening Stops (12:00:00 PM - 11:59:59 PM)
    */
  },
  {
    name: 'TIME OF DAY - DAY & NIGHT',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...stopByTimeOfDayAndNight,
    /*
      Provide count total per time of day somewhere on chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each Applicable time of day - color code to match the label
        Total Stops (bolder line)
          Day
          Night
    */
  },
  {
    name: 'TIME OF DAY - DAY & NIGHT DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '600px' },
    ...dayVersusNightDistribution,
    /*
      Total Count
      Slices
        Count for Day (6:00:00AM - 06:59:59 PM)
        Count for Night (7:00:00 PM - 5:59:59 AM)
    */
  },
  {
    // name: 'STOPS BY ZIP CODE',
    // type: ChartType.Line
    /*
      Provide the total count of stops somewhere on the chart
      X-axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than a month (~max 120 units)
      Y-axis is the number of stops
        Total Count (Bold)
        Display a line for each Applicable Zip Code - color code to match the label
    */
  },
  {
    name: 'SCHOOL COUNT',
    type: ChartType.Table,
    containerStyles: { height: '615px' },
    ...stopCountBySchool,
    /* 1 row for the entire time range per school
      Column:
        School
        Student Stops
        Non-Student Stops
        Total Stops at that school
    */
  },
  // {
  //   name: 'STOPS BY ZIP CODE',
  //   type: ChartType.Line
  /*
    Provide the total count of stops somewhere on the chart
    X-axis is the time span
      There is a hash or marker
        for each day if the time span is less than a month
        for each month if the time span is greater than a month (~max 120 units)
    Y-axis is the number of stops
      Total Count (Bold)
      Display a line for each Applicable Zip Code - color code to match the label <--- For each zip code??
  */
  // },
  {
    name: 'NUMBER OF PEOPLE STOPPED',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...numberOfPeopleStopped,
  },
  { name: 'ETHNICITY DISTRIBUTION' },
  {
    name: 'GENDER',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...gender,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total People in stops
          Total
          Male
          Trans Man/Boy
          Female
          Trans Woman/Girl
          Gender Non Conforming <---
        Average Per Stop
          Male
          Trans Man/Boy
          Female
          Trans Woman/Girl
          Gender Non Conforming <---
    */
  },
  {
    name: 'GENDER DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...genderDistribution,
    /*
      Total People in stops
      Slices
        Male
        Trans Man/Boy
        Female
        Trans Woman/Girl
        Gender Non Conforming <---
      */
  },
  {
    name: 'AGE',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...age,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total Stops (bolder line)
        Total
          Minor
          18-25
          26-35
          36-45
          46-55
          56-65
          66-75
          76-85
          86-95
          96-100
      */
  },
  {
    name: 'AGE DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...ageDistribution,
    /*
      Total Count of people
      Slices
        Minor
        18-25
        26-35
        36-45
        46-55
        56-65
        66-75
        76-85
        86-95
        96-100
      */
  },
  {
    name: 'STUDENTS',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...students,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people in stops
        Total
          Student
          Non Student
    */
  },
  {
    name: 'STUDENT DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...studentsDistribution,
    /*
      Total Count of people
      Slices
        Student
        Other
    */
  },
  {
    name: 'RACE OR ETHNICITY',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...raceOrEthnicity,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people in stops
        Total
          Asian
          Black/African American
          Hispanic/Latino(a)
          Middle Eastern or South Asian
          Native American
          Pacific Islander
          White
    */
  },
  {
    name: 'RACE OR ETHNICITY DISTRIBUTION',
    type: ChartType.Bar,
    containerStyles: { height: '615px' },
    ...raceOrEthnicityDistribution,
    /*
      Total Counts of people
      Slices
        Asian
        Black/African American
        Hispanic/Latino(a)
        Middle Eastern or South Asian
        Native American
        Pacific Islander
        White
    */
  },
  {
    name: 'LANGUAGE FLUENCY',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...languageFluency,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people
        Total
          Limited English Fluency
          Other
    */
  },
  {
    name: 'SEXUAL ORIENTATION',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...sexualOrientation,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total Stops (bolder line)
        Total
          LGBT
          Other
    */
  },
  {
    name: 'SEXUAL ORIENTATION DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...sexualOrientationDistribution,
    /*
      Total people
        Slices
          LGBT
          Other
    */
  },
  {
    name: 'DISABLED',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...disabled,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Total people
        Total
          Disabled
          Not Disabled
    */
  },
  {
    name: 'DISABLED DISTRIBUTION',
    type: ChartType.Pie,
    containerStyles: { height: '615px' },
    ...disabledDistributionGeneral,
    /*
      Total people
      Slices
        Disabled
        Not Disabled
    */
  },
  {
    name: 'DISABILITIES',
    type: ChartType.Line,
    containerStyles: { height: '615px' },
    ...disabilities,
    /*
      Provide the total stop count somewhere on the chart
      X axis is the time span
        There is a hash or marker
          for each day if the time span is less than a month
          for each month if the time span is greater than 6 months
      Y-axis is the count
      Display a line for each  - color code to match the label
        Deafness or difficulty hearing
        Speech impairment or limited use of language
        Blind or limited vision
        Mental health condition
        Intellectual or developmental disability, including dementia
        Other disability
    */
  },
  {
    name: 'DISABILITIES DISTRIBUTION',
    type: ChartType.Bar,
    containerStyles: { height: '615px' },
    ...disabilitiesDistribution,
    /*
      Total Count of people
      Slices
        Deafness or difficulty hearing
        Speech impairment or limited use of language
        Blind or limited vision
        Mental health condition
        Intellectual or developmental disability, including dementia
        Other disability
    */
  },
]
  .filter((cd) => cd.name && cd.configure)
  .sort((a, b) => {
    if (a.name && b.name) {
      return a.name.localeCompare(b.name);
    }
    return 0;
  });

export default chartDefinitions;
