import React, { Fragment } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import FiltersSDKComponent from './FiltersSDKComponent';
import {
  Reorder,
  DateRange,
  Description,
  Chat,
  Timelapse,
  AttachFile,
  Filter1,
} from '@mui/icons-material';
import debounce from 'lodash/debounce';
import DateRangeFilter from './DateRangeFilter';
import { EntityTypesFilter, ENTITY_NONE } from './EntityTypesFilter';
import FileTypeFilter from './FileTypeFilter';
import AutocompleteFilter from './AutocompleteFilter';
import EnginesRunFilter from './EnginesRunFilter';
import DurationFilter from './DurationFilter';
import { pushOrPopArray } from '../../state/utils/pushOrPop';
import {
  getSelectedEntity,
  UPDATE_SELECTED_ENTITY,
} from 'state/modules/sunburst';
import {
  getFilteredDate,
  getFilteredFileType,
  getFilteredEntityType,
  updateDateUnix,
  getEntityNames,
  getTopics,
  getSelectedTopics,
  UPDATE_DATE_FILTER,
  UPDATE_ENTITY_TYPES_FILTERS,
  UPDATE_TOPICS_FILTERS,
  APPLY_FILTERS,
  getFileNames,
  getIds,
  updateIds,
  updateFileNames,
  getEnginesRun,
  updateEnginesRun,
  updateDuration,
  getDuration,
  clearDuration,
  UPDATE_FILE_TYPES_FILTERS,
  CLEAR_FILTERS,
} from 'state/modules/filters';

import {
  setSelection,
  getDisableAnalytics,
  openTdoPreview,
} from '../../state/modules/tdosTable';

import { engineCategories } from '../../state/modules/uploadFile';
import engineImage from '../../../resources/images/engine.svg';
import { ROUTE_TABS } from 'state/modules/routing';
import { TABS } from 'state/modules/tabs';
import { QUERY_TYPE } from 'state/modules/search';

export class Filters extends React.Component<Props> {
  constructor(props: Props) {
    super(props);
    this.onChangeSearchByDuration = debounce(
      this.onChangeSearchByDuration,
      500
    );
  }

  state = {
    entityValue: ENTITY_NONE,
    startDate: 0,
    endDate: 0,
    dateError: '',
    selectedTopics: [] as string[],
    fileNameValue: '',
    idValue: '',
    time: [0, 0],
  };

  componentDidMount = () => {
    this.getPreviousFiltersState();
  };

  getPreviousFiltersState = () => {
    const { date, selectedEntity, selectedTopics } = this.props;
    const startDate = updateDateUnix(date.startDate);
    const endDate = updateDateUnix(date.endDate);
    const entityValue = selectedEntity?.[0] ?? ENTITY_NONE;
    this.setState((prevState) => ({
      ...prevState,
      startDate,
      endDate,
      entityValue,
      selectedTopics,
    }));
  };

  changeTime = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { updateDateFilter } = this.props;
    const id = event.target.getAttribute('id');
    if (id === 'startDate') {
      const startDate = updateDateUnix(event.target.value);
      this.setState({ startDate });
      if (this.state.endDate < startDate) {
        this.displayErrorAndUpdateDate(id);
      } else {
        this.setState({ dateError: '' });
      }
      updateDateFilter?.(id, event.target.value);
    } else if (id === 'endDate') {
      const endDate = updateDateUnix(event.target.value);
      this.setState({ endDate });
      if (endDate < this.state.startDate) {
        this.displayErrorAndUpdateDate(id);
      } else {
        this.setState({ dateError: '' });
      }
      updateDateFilter?.(id, event.target.value);
    }
  };

  displayErrorAndUpdateDate = (id: string) => {
    this.setState({ dateError: 'End date must be greater than start date!' });
    return this.props.updateDateFilter?.(id, '');
  };

  handleClearFilter = () => {
    this.setState({
      entityValue: ENTITY_NONE,
      startDate: 0,
      endDate: 0,
      dateError: '',
      selectedTopics: [] as string[],
      fileNameValue: '',
      idValue: '',
      time: [0, 0],
    });
    this.props.clearFilters();
  };

  handleApplyFilter = () => {
    const { onFiltersClose, fileNames, ids } = this.props;
    const { fileNameValue, idValue } = this.state;
    const newFileNames = [...fileNames];
    if (fileNameValue && !newFileNames.includes(fileNameValue)) {
      newFileNames.push(fileNameValue);
    }
    const newIds = [...ids];
    if (idValue && !newIds.includes(idValue)) {
      newIds.push(idValue);
    }
    onFiltersClose();
    this.applyFilters(newFileNames, newIds);
  };

  handleEntityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { updateEntityFilter } = this.props;
    const entityValue = event.target.value;
    const entityType = entityValue === ENTITY_NONE ? '' : entityValue;
    updateEntityFilter?.(entityType);
    this.setState({ entityValue });
  };

  handledDefaultCheckboxesChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const topic = event.target.value;
    this.setState(
      (prevState: {
        entityValue: string;
        startDate: number;
        endDate: number;
        dateError: string;
        selectedTopics: string[];
        fileNameValue: string;
        idValue: string;
        time: number[];
      }) => ({
        selectedTopics: pushOrPopArray(prevState.selectedTopics, topic),
      })
    );
    this.props.updateTopicsFilter?.(
      pushOrPopArray([...this.state.selectedTopics], topic)
    );
  };

  handleChangeSearchByFileName = (newValue: string[]) => {
    const { updateFileNames } = this.props;
    updateFileNames?.(newValue);
  };

  handleChangeFileName = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ fileNameValue: event.target.value });
  };

  handleChangeSearchByID = (newValue: string[]) => {
    const { updateIds } = this.props;
    updateIds?.(newValue);
  };

  handleChangeID = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ idValue: event.target.value });
  };

  handleChangeSearchByEnginesRun = (newValue: EngineRun[]) => {
    const { updateEnginesRun } = this.props;
    updateEnginesRun?.(newValue);
  };

  handleSearchByEnginesRunEnter = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.stopPropagation();
  };

  handleChangeSearchByDuration = (value: number, name: string) => {
    this.onChangeSearchByDuration(value, name);
  };

  onChangeSearchByDuration = (value: number, name: string) => {
    const { updateDuration } = this.props;
    updateDuration?.(value, name);
  };

  handleClearDuration = (type: string) => {
    const { clearDuration } = this.props;
    clearDuration?.(type);
  };

  applyFilters = (fileNames: string[], ids: string[]) => {
    const {
      date,
      applyFilters,
      updateSelection,
      duration,
      enginesRun,
      openTdoPreview,
      navigateToTab,
      updateSelectedEntity,
    } = this.props;
    const selectedEntity =
      this.props.initialEntityNames.filter(
        (name) => name === this.state.entityValue
      ) ?? [];
    updateSelectedEntity?.(selectedEntity, QUERY_TYPE.APPLY_FILTER);
    applyFilters(date, fileNames, ids, enginesRun, duration);
    updateSelection([], false);
    this.setState({ fileNameValue: '', idValue: '' });
    openTdoPreview({ isShowPreview: false, initFullScreen: false });
    navigateToTab(TABS.Files);
  };

  render() {
    const {
      date,
      fileTypes,
      entityType,
      initialEntityNames,
      onFiltersClose,
      topics,
      selectedTopics,
      disableAnalytics,
      fileNames,
      ids,
      enginesRun,
      engineCategories,
      duration,
    } = this.props;
    const { startDate, endDate } = date;
    const { video, audio, image, doc } = fileTypes;
    const fileSubTypes = [...video, ...audio, ...image, ...doc];
    let displayingTopics = [...topics];
    displayingTopics = displayingTopics.concat(selectedTopics);
    displayingTopics = [...new Set(displayingTopics)];
    let dateCount = 0;
    if (startDate || endDate) {
      dateCount = 1;
    }
    let sections = {
      children: [
        {
          label: 'FILE NAMES',
          icon: <AttachFile />,
          type: 'display-count',
          children: [{ formComponentId: 'fileNames' }],
        },
        {
          label: 'FILE TYPES',
          icon: <Description />,
          type: 'display-count',
          children: [{ formComponentId: 'fileTypes' }],
        },
        {
          label: 'IDS',
          icon: <Filter1 />,
          type: 'display-count',
          children: [{ formComponentId: 'ids' }],
        },
        {
          label: 'DURATION',
          icon: <Timelapse />,
          type: 'display-count',
          children: [{ formComponentId: 'duration' }],
        },
        {
          label: 'ENGINES RUN',
          icon: (
            <img
              style={{ height: 'auto', cursor: 'pointer' }}
              src={engineImage}
              alt=""
            />
          ),
          type: 'display-count',
          children: [{ formComponentId: 'enginesRun' }],
        },
        {
          label: 'DATE RANGE',
          icon: <DateRange />,
          type: 'display-count',
          children: [{ formComponentId: 'dateRange' }],
        },
        {
          label: 'ENTITY TYPES',
          icon: <Reorder />,
          type: 'display-count',
          children: [{ formComponentId: 'entityTypes' }],
        },
        {
          label: 'TOPICS',
          icon: <Chat />,
          type: 'checkbox',
          valueArray: displayingTopics,
          children: [{ formComponentId: 'default-checkboxes-topics' }],
        },
      ],
    };
    if (disableAnalytics) {
      sections = {
        children: [...sections.children.slice(0, 2)],
      };
    }
    const newEngineCategories: EngineCategory[] = engineCategories.map(
      ({ name, id }) => ({
        name,
        id,
      })
    );
    let totalDuration = 0;
    const lte =
      duration.hoursLte * 3600 + duration.minutesLte * 60 + duration.secondsLte;
    const gt =
      duration.hoursGt * 3600 + duration.minutesGt * 60 + duration.secondsGt;
    if (lte > 0) {
      totalDuration += 1;
    }
    if (gt > 0) {
      totalDuration += 1;
    }
    return (
      <Fragment>
        <FiltersSDKComponent
          formComponents={{
            dateRange: (
              <DateRangeFilter
                handleChange={this.changeTime}
                startDate={startDate}
                endDate={endDate}
                dateError={this.state.dateError}
              />
            ),
            entityTypes: (
              <EntityTypesFilter
                style={{ width: '100%' }}
                initialEntityNames={[...initialEntityNames].sort()}
                onChange={this.handleEntityChange}
                entityValue={this.state.entityValue}
              />
            ),
            fileTypes: <FileTypeFilter />,
            fileNames: (
              <AutocompleteFilter
                handleAutocompleteChange={this.handleChangeSearchByFileName}
                searchValue={fileNames}
                handleInputChange={this.handleChangeFileName}
                label={'Filter by names'}
              />
            ),
            ids: (
              <AutocompleteFilter
                handleAutocompleteChange={this.handleChangeSearchByID}
                searchValue={ids}
                handleInputChange={this.handleChangeID}
                label={'Filter by ids'}
              />
            ),
            enginesRun: (
              <EnginesRunFilter
                handleAutocompleteChange={this.handleChangeSearchByEnginesRun}
                onKeyDown={this.handleSearchByEnginesRunEnter}
                searchValue={enginesRun}
                // handleInputChange={this.handleChangeEngineRun}
                label={'Filter by Engines Run'}
                engineCategories={newEngineCategories}
              />
            ),
            duration: (
              <DurationFilter
                handleInputChange={this.handleChangeSearchByDuration}
                searchValue={duration}
                clearDuration={this.handleClearDuration}
                gt={gt}
                lte={lte}
              />
            ),
          }}
          filtersSections={sections}
          closeFilter={onFiltersClose}
          checkboxCount={{
            fileTypes: fileSubTypes.length,
            entityTypes: entityType ? 1 : 0,
            dateRange: dateCount,
            'default-checkboxes-topics': this.state.selectedTopics.length,
            fileNames: fileNames.length,
            ids: ids.length,
            enginesRun: enginesRun.length,
            duration: totalDuration,
          }}
          onCheckboxChange={this.handledDefaultCheckboxesChange}
          defaultCheckboxSelectedItems={this.state.selectedTopics}
          onClick={this.handleApplyFilter}
          onClearButton={this.handleClearFilter}
          isDisabled={this.state.dateError ? true : false}
        />
      </Fragment>
    );
  }
}

interface EngineCategory {
  id: string;
  name: string;
  iconClass?: string;
}

export interface EngineRun {
  id: string;
  name: string;
}

export interface Duration {
  hoursGt: number;
  minutesGt: number;
  secondsGt: number;
  hoursLte: number;
  minutesLte: number;
  secondsLte: number;
}
export interface FileTypes {
  video: string[];
  audio: string[];
  image: string[];
  doc: string[];
}

export interface UpdateFileTypes {
  [key: string]: string[];
}
export interface DateFilter {
  endDate: string;
  startDate: string;
}
type Props = PropsFromRedux & {
  onFiltersClose: () => void;
};

const mapState = (state: any) => ({
  date: getFilteredDate(state),
  entityType: getFilteredEntityType(state),
  selectedEntity: getSelectedEntity(state),
  fileTypes: getFilteredFileType(state),
  initialEntityNames: getEntityNames(state),
  topics: getTopics(state),
  selectedTopics: getSelectedTopics(state),
  disableAnalytics: getDisableAnalytics(state),
  fileNames: getFileNames(state),
  ids: getIds(state),
  enginesRun: getEnginesRun(state),
  engineCategories: engineCategories(state),
  duration: getDuration(state),
});

const mapDispatch = {
  updateDateFilter: (id: string, filteredDate: string) =>
    UPDATE_DATE_FILTER({ id, filteredDate }),
  updateEntityFilter: (entityType: string) =>
    UPDATE_ENTITY_TYPES_FILTERS({ entityType }),
  updateSelectedEntity: (selectedEntity: string[], queryType: string) =>
    UPDATE_SELECTED_ENTITY({ selectedEntity, queryType }),
  updateTopicsFilter: (selectedTopics: string[]) =>
    UPDATE_TOPICS_FILTERS({ selectedTopics }),
  applyFilters: (
    date: DateFilter,
    fileNames: string[],
    ids: string[],
    enginesRun: EngineRun[],
    duration: Duration
  ) =>
    APPLY_FILTERS({
      date,
      fileNames,
      ids,
      enginesRun,
      duration,
    }),
  updateSelection: (
    selectedRows: Array<string | number>,
    isSelectedAll: boolean
  ) => setSelection(selectedRows, isSelectedAll),
  updateIds: updateIds,
  updateFileNames: updateFileNames,
  updateEnginesRun: updateEnginesRun,
  updateDuration: updateDuration,
  clearDuration: clearDuration,
  navigateToTab: (tabName: string) => ROUTE_TABS({ tab: tabName }),
  openTdoPreview: openTdoPreview,
  updateFileFilter: (fileTypes: UpdateFileTypes) =>
    UPDATE_FILE_TYPES_FILTERS({ fileTypes }),
  clearFilters: () => CLEAR_FILTERS(),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Filters);
