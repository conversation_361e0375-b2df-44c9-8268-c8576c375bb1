import { MouseEvent } from 'react';
import Dialog from '@mui/material/Dialog';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import DialogContent from '@mui/material/DialogContent';
import Tooltip from '@mui/material/Tooltip';
import SaveAlt from '@mui/icons-material/SaveAlt';
import * as styles from '../styles.scss';
import ModalItem from './ModalItem';

function SignedUrisDialog({ signedUris, onClose, modalName, isOpened }: Props) {
  function handleShowSnackBar(event: MouseEvent<HTMLElement>) {
    const signedUri = event.currentTarget.getAttribute('data-url') || undefined;
    window.open(signedUri);
  }
  const rowData = signedUris.map((item) => {
    const part = (
      <IconButton
        onClick={handleShowSnackBar}
        data-url={item.signedUri}
        size="large"
      >
        <Tooltip title={'Ready to download'}>
          <SaveAlt />
        </Tooltip>
      </IconButton>
    );

    const name = (
      <div>
        <Typography>{item.name}</Typography>
      </div>
    );

    return {
      name,
      part,
      id: item.id,
    };
  });

  return (
    <Dialog open={isOpened} onClose={onClose} maxWidth="lg">
      <Toolbar>
        <Typography variant="h6" color="inherit">
          {modalName}
        </Typography>
        <IconButton
          color="inherit"
          onClick={onClose}
          className={styles['icon-close']}
          size="large"
        >
          <CloseIcon />
        </IconButton>
      </Toolbar>
      <DialogContent classes={{ root: styles['dialog-content'] }}>
        <div className={styles['content-batch']}>
          <ModalItem data={rowData} />
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface signedUri {
  name: string;
  signedUri: string;
  id: string;
}

interface Props {
  onClose: () => void;
  signedUris: signedUri[];
  modalName: string;
  isOpened: boolean;
}

export default SignedUrisDialog;
