import React, { Component } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { List, ListItem, ListItemText, Tooltip } from '@mui/material';
import ListItemIcon from '@mui/material/ListItemIcon';
import FolderIcon from '@mui/icons-material/Folder';
import WorkIcon from '@mui/icons-material/Work';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ArrowRight from '@mui/icons-material/ArrowRight';

import ListItemButton from '@mui/material/ListItemButton';

import * as styles from './styles.scss';
import classNames from 'classnames/bind';
import {
  SHOW_FORM_CREATE_FOLDER,
  SHOW_MOVE_FOLDER,
  FETCH_SUB_FOLDERS_REQUEST,
  FETCH_MOVE_FOLDER_REQUEST,
  getRootFolderId,
  FETCH_OPEN_FOLDER,
} from '../../state/modules/folders';
import { Folder as FolderProps, Folders } from '../../model';
const cx = classNames.bind(styles);
export class Folder extends Component<Props> {
  state = {
    anchorEl: null,
    openFolderId: null,
  };

  handleListItemClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const { fetchSubFolder } = this.props;
    const folderId = event.currentTarget.getAttribute('data-id') ?? '';
    fetchSubFolder(folderId);
  };
  handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const folderId = event.currentTarget.getAttribute('data-id') ?? '';
    const { setOpenFolderId } = this.props;
    setOpenFolderId(folderId);
    this.setState({ anchorEl: event.currentTarget, openFolderId: folderId });
  };

  handleMenuClose = (event: React.MouseEvent<HTMLElement>) => {
    if (event) {
      event.stopPropagation();
    }
    this.setState({ anchorEl: null, openFolderId: null });
  };
  handleRenameFolder = (event: React.MouseEvent<HTMLElement>) => {
    const { handleShowCreateFolder } = this.props;
    handleShowCreateFolder({ type: 'rename' });
    this.handleMenuClose(event);
  };
  handleMoveFolder = (event: React.MouseEvent<HTMLElement>) => {
    const { handleShowMoveFolder, fetchMoveFolder } = this.props;
    handleShowMoveFolder({ caseOptions: '' });
    fetchMoveFolder();
    this.handleMenuClose(event);
  };
  checkFolderSelected = (id: string) => {
    const { selectedFolderIds } = this.props;
    if (selectedFolderIds) {
      const folderId = selectedFolderIds.map((item) => item.id);
      return folderId.includes(id);
    }
    return false;
  };
  render() {
    const { folder, allFolders, type, rootFolderId } = this.props;
    const { anchorEl, openFolderId } = this.state;
    const open = Boolean(anchorEl);
    if (!folder) {
      return null;
    }
    if (type === 'root') {
      return (
        <List className={styles.folder} data-testid="list">
          <ListItem disablePadding data-testid="list-item">
            <ListItemButton
              key={folder.id}
              className={styles['list-item-work-selected']}
              data-id={rootFolderId}
              onClick={this.handleListItemClick}
            >
              <ListItemIcon
                className={styles['list-item-icon-work']}
                data-testid="list-item-icon"
              >
                <FolderIcon data-testid="folder-icon" />
              </ListItemIcon>

              <Tooltip
                title={`MY CASES(${folder.subfolders.length})`}
                placement="bottom-end"
              >
                <ListItemText
                  data-test="root-folder"
                  disableTypography
                  className={styles['list-item-text-work']}
                  primary={`MY CASES (${folder.subfolders.length})`}
                  data-testid="list-item-text"
                />
              </Tooltip>
              {allFolders[rootFolderId]?.fetchingSubFolders && (
                <div>
                  <CircularProgress size={20} variant="indeterminate" />
                </div>
              )}
            </ListItemButton>
          </ListItem>
        </List>
      );
    }
    return (
      <React.Fragment>
        {folder.subfolders.map((item) => (
          <List key={item} className={styles.folder} data-testid="list">
            <ListItem disablePadding>
              <ListItemButton
                className={cx({
                  'list-item-selected': this.checkFolderSelected(item),
                  'list-item': !this.checkFolderSelected(item),
                })}
                data-id={allFolders[item]?.id}
                onClick={this.handleListItemClick}
                data-testid="list-item"
              >
                <ListItemIcon
                  className={styles['list-item-icon-folder']}
                  data-testid="list-item-icon"
                >
                  <WorkIcon data-testid="work-icon" />
                </ListItemIcon>

                <Tooltip title={allFolders[item]!.name} placement="bottom-end">
                  <ListItemText
                    disableTypography
                    className={styles['list-item-text']}
                    primary={allFolders[item]?.name}
                    data-testid="list-item-text"
                  />
                </Tooltip>
                {allFolders[item]?.fetchingSubFolders && (
                  <div>
                    <CircularProgress size={20} variant="indeterminate" />
                  </div>
                )}
                {(allFolders[item]?.count ?? 0) > 0 && (
                  <ArrowRight
                    className={cx(styles['arrow-right'], {
                      'none-icon': openFolderId === allFolders[item]?.id,
                    })}
                    width={20}
                  />
                )}
                <IconButton
                  onClick={this.handleMenuClick}
                  // className={styles['icon-menu']}
                  data-id={allFolders[item]?.id}
                  data-testid="icon-button"
                  aria-label="menu-button"
                  className={cx(styles['icon-menu'], {
                    'active-icon': openFolderId === allFolders[item]?.id,
                  })}
                  size="small"
                >
                  <MoreVertIcon data-testid="more-vert-icon" />
                </IconButton>
              </ListItemButton>
            </ListItem>
          </List>
        ))}
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={this.handleMenuClose}
          className={styles['menu']}
          data-testid="menu"
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
        >
          <MenuItem onClick={this.handleRenameFolder} data-testid="menu-item">
            Rename
          </MenuItem>
          <MenuItem onClick={this.handleMoveFolder} data-testid="menu-item">
            Move
          </MenuItem>
        </Menu>
      </React.Fragment>
    );
  }
}

const mapState = (state: any) => ({
  rootFolderId: getRootFolderId(state),
});

const mapDispatch = {
  fetchSubFolder: (subFolderId: string, expanded?: boolean) =>
    FETCH_SUB_FOLDERS_REQUEST({ folderId: subFolderId, expanded: expanded }),
  handleShowCreateFolder: (payload: { type: string }) =>
    SHOW_FORM_CREATE_FOLDER(payload),
  handleShowMoveFolder: (payload: { caseOptions: string }) =>
    SHOW_MOVE_FOLDER(payload),
  fetchMoveFolder: () => FETCH_MOVE_FOLDER_REQUEST(),
  setOpenFolderId: (folderId: string) => FETCH_OPEN_FOLDER(folderId),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Folder);

interface SelectedFolderId {
  id: string;
  level: number;
}

type Props = PropsFromRedux & {
  folder: FolderProps;
  allFolders: Folders;
  // rootFolderId: string;
  type?: string;
  selectedFolderIds: SelectedFolderId[];
};
