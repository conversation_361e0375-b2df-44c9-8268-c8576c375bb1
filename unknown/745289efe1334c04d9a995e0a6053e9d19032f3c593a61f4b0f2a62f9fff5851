import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Add from '@mui/icons-material/Add';
import Grid from '@mui/material/Grid2';
import * as styles from './styles.scss';
import SideBar from '../SideBar';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
const Folder = ({
  open,
  onClose,
  onConfirm,
  handleCreateFolder,
  widthFolder,
  disabled,
}: Props) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        style: {
          maxWidth: widthFolder * 260,
          width: '100%',
          position: 'absolute',
          top: '61px',
          left: '0',
          margin: '0',
          borderRadius: '0',
        },
      }}
    >
      <Toolbar className={styles['dialog-header']}>
        <Button
          data-test="folder-modal-create-new-folder-button"
          className={styles['btn-new-folder']}
          onClick={handleCreateFolder}
        >
          <Add />
          <Typography className={styles['title-new-folder']}>New</Typography>
        </Button>
        <IconButton
          color="inherit"
          onClick={onClose}
          aria-label="Close Folder"
          className={styles['icon-close']}
          size="large"
        >
          <CloseIcon />
        </IconButton>
      </Toolbar>
      <DialogContent
        data-test="folder-modal-dialog-content"
        className={styles['dialog-content']}
      >
        <Grid container>
          <SideBar />
        </Grid>
      </DialogContent>
      <DialogActions className={styles['dialog-action']}>
        <Button
          data-test="folder-modal-cancel-folder-button"
          onClick={onClose}
          color="primary"
          className={styles['btn-cancel']}
        >
          Cancel
        </Button>
        <Button
          data-test="folder-modal-select-folder-button"
          onClick={onConfirm}
          color="primary"
          className={cx({
            'btn-select': !disabled,
            'btn-select-disabled': disabled,
          })}
          disabled={disabled}
          variant="contained"
        >
          Select
        </Button>
      </DialogActions>
    </Dialog>
  );
};

interface Props {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  handleCreateFolder: () => void;
  widthFolder: number;
  disabled: boolean;
}

export default Folder;
