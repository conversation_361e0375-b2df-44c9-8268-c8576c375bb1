import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "typeOfPropertySeized"
                field: "typeOfPropertySeized"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'typeOfPropertySeizedAggregation',
      dataKey: 'typeOfPropertySeized',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Property Seized Distribution',
  filterTextType: 'Property Seized Distribution by Type',
  filterType: 'typeOfPropertySeized',
  filterTerms: [
    'Firearm(s)',
    'Drugs/narcotics',
    'Drug Paraphernalia',
    'Ammunition',
    'Alcohol',
    'Cell phone(s) or electronic device(s)',
    'Vehicle',
    'Money',
    'Other Contraband or evidence',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap = {
      'Firearm(s)': 0,
      'Drugs/narcotics': 0,
      'Drug Paraphernalia': 0,
      Ammunition: 0,
      Alcohol: 0,
      'Cell phone(s) or electronic device(s)': 0,
      Vehicle: 0,
      Money: 0,
      'Other Contraband or evidence': 0,
      ...data.typeOfPropertySeizedAggregation.reduce(
        (acc: { [key: string]: number }, e) => {
          acc[e.key] = e.doc_count;
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = [
      { seized: 'Firearm(s)', numberOfPeople: dataMap['Firearm(s)'] },
      { seized: 'Drugs/narcotics', numberOfPeople: dataMap['Drugs/narcotics'] },
      {
        seized: 'Drug Paraphernalia',
        numberOfPeople: dataMap['Drug Paraphernalia'],
      },
      { seized: 'Ammunition', numberOfPeople: dataMap['Ammunition'] },
      { seized: 'Alcohol', numberOfPeople: dataMap['Alcohol'] },
      {
        seized: 'Cell phone(s) or electronic device(s)',
        numberOfPeople: dataMap['Cell phone(s) or electronic device(s)'],
      },
      { seized: 'Vehicle', numberOfPeople: dataMap['Vehicle'] },
      { seized: 'Money', numberOfPeople: dataMap['Money'] },
      {
        seized: 'Other Contraband or evidence',
        numberOfPeople: dataMap['Other Contraband or evidence'],
      },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'numberOfPeople';
    pieSeries.dataFields.category = 'seized';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  typeOfPropertySeizedAggregation: { key: string; doc_count: number }[];
}
