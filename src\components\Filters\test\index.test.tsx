import { Filters } from '../index';
import configureS<PERSON> from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import * as Redux from 'redux';
import { Engine } from 'state/modules/uploadFile/models';
const middlewares: Redux.Middleware[] = [];
const mockStore = configureStore(middlewares);
const initialState = {
  filters: {
    date: {
      startDate: '',
      endDate: '',
    },
    fileTypes: {
      video: [],
      audio: [],
      image: [],
      doc: [],
    },
    entityNames: [],
    entityType: '',
    topics: [],
    selectedTopics: [],
    fileNames: [],
    ids: [],
    enginesRun: [],
    duration: {},
  },
  sunburst: {
    analyticsData: {},
  },
  user: {
    user: {
      organization: {
        kvp: {
          features: {
            illuminate: {
              exportDestination: 'enable',
            },
          },
        },
      },
      groups: [
        {
          groupId: '77e53e23-d600-4bbf-ae09-dde5a0c88c5f',
          groupName: 'Root Admin',
          organizationGuid: '7f936a87-8e59-4206-9999-938b0e3c62ab',
          kvp: {
            groupType: 'organization',
            organizationId: '1',
            organizationName: 'Root Admin',
          },
        },
      ],
    },
  },
};
const store = mockStore(initialState);
describe('Major test filters', function () {
  const defaultProps = {
    date: {
      startDate: '1967-3-27',
      endDate: '1977-5-23',
    },
    initialEntityNames: ['HUMAN', 'SPHINX', 'MANTICORE'],
    entityType: 'HUMAN',
    selectedEntity: ['HUMAN'],
    fileTypes: {
      video: ['video/mp4'],
      audio: [],
      image: [],
      doc: [],
    },
    topics: [
      'human',
      'Wireless & Mobile',
      'Electricity & Energy',
      'Automobile & Vehicle',
    ],
    selectedTopics: ['human', 'Wireless & Mobile', 'Electricity & Energy'],
    engineCategories: [
      {
        name: 'transcription',
        id: '123',
        description: 'transcription 123',
        iconClass: 'iconClass 123',
        unavailablEngineCategory: false,
        engines: {
          records: [] as Engine[],
        },
        libraryEntityIdentifierTypeIds: null,
      },
      {
        name: 'translate',
        id: '456',
        description: 'translate 456',
        iconClass: ' iconClass 456',
        unavailablEngineCategory: false,
        engines: {
          records: [] as Engine[],
        },
        libraryEntityIdentifierTypeIds: null,
      },
    ],
    duration: {
      hoursLte: 0,
      minutesLte: 2,
      secondsLte: 0,
      hoursGt: 0,
      minutesGt: 1,
      secondsGt: 0,
    },
    fileNames: ['testFileName1'],
    ids: ['tdoId123'],
    enginesRun: [
      { name: 'transcription', id: '123' },
      { name: 'translate', id: '456' },
    ],
    onFiltersClose: jest.fn(),
    applyFilters: jest.fn(),
    updateSelection: jest.fn(),
    openTdoPreview: jest.fn(),
    navigateToTab: jest.fn(),
    updateFileFilter: jest.fn(),
    clearFilters: jest.fn(),
    disableAnalytics: false,
    updateDateFilter: jest.fn(),
    updateEntityFilter: jest.fn(),
    updateSelectedEntity: jest.fn(),
    updateTopicsFilter: jest.fn(),
    updateIds: jest.fn(),
    updateFileNames: jest.fn(),
    updateEnginesRun: jest.fn(),
    updateDuration: jest.fn(),
    clearDuration: jest.fn(),
  };

  it('renders a FiltersSDKComponent', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getByTestId('filters-sdk-components')).toBeInTheDocument();
  });
  it('renders a Header', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getByTestId('header')).toBeInTheDocument();
  });
  it('renders 8 icons selection', () => {
    const { getAllByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getAllByTestId('icon')).toHaveLength(8);
  });
  it('renders a DateRangeFilter', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getByTestId('date-range-filter')).toBeInTheDocument();
  });
  it('renders a EntityTypesFilter', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getByTestId('entity-types-filter')).toBeInTheDocument();
  });
  it('renders a FileTypeFilter', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    expect(getByTestId('file-type-filters')).toBeInTheDocument();
  });
  it('Should have click event to apply filter', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    fireEvent.click(getByTestId('apply-filter'));
    expect(defaultProps.applyFilters).toHaveBeenCalled();
  });
  it('Should have click event to close filter', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...defaultProps} />
      </Provider>
    );
    fireEvent.click(getByTestId('close-filter'));
    expect(defaultProps.onFiltersClose).toHaveBeenCalled();
  });
  it('Should display number of checkbox counts', function () {
    const newProps = {
      ...defaultProps,
      fileTypes: {
        video: ['video/mp4', 'video/3gpp'],
        audio: [],
        image: [],
        doc: [],
      },
      entityType: 'product',
    };
    const { getByTestId } = render(
      <Provider store={store}>
        <Filters {...newProps} />
      </Provider>
    );
    expect(getByTestId('count-1')).toHaveTextContent('(2)');
  });
});
