import { modules } from '@veritone/glc-redux';
import getApiAuthToken from './getApiAuthToken';
import fetchGraphQLApi from './fetchGraphQLApi';
import { ExtendedError } from '@utils';
import { isString } from 'lodash';
import {
  ActionCreatorWithPreparedPayload,
  createAction,
} from '@reduxjs/toolkit';

const {
  config: { getConfig },
} = modules;

export function getApiConnectionParameters(state: any): {
  endpoint: string;
  token: string | null;
  veritoneAppId: string;
} {
  const config: any = getConfig<Window['config']>(state);
  const endpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
  const token = getApiAuthToken(state);
  const veritoneAppId = config.veritoneAppId;
  return { endpoint, token, veritoneAppId };
}

export async function fetchGraphQLApiThrowError<T>({
  endpoint,
  query,
  variables,
  operationName,
  token,
  veritoneAppId,
}: Props) {
  const result = await fetchGraphQLApi<T>({
    endpoint,
    query,
    variables,
    operationName,
    token,
    veritoneAppId,
  });

  if (result.errors) {
    const error = new Error(
      result.errors[0]?.message || 'Unknown Error'
    ) as ExtendedError;
    error.errors = result.errors;
    throw error;
  }
  return result;
}

export async function callAsyncFunc<T>({
  actionTypes: [requestType, successType, failureType],
  asyncfn,
  dispatch,
}: PropsCallAsyncFunc<T>) {
  if (isString(requestType)) {
    dispatch({
      type: requestType,
    });
  } else {
    dispatch(requestType());
  }
  let response;
  try {
    response = await asyncfn();
  } catch (e) {
    if (isString(failureType)) {
      dispatch({
        type: failureType,
        error: true,
        payload: e,
        meta: {
          response,
        },
      });
    } else {
      dispatch(failureType(e, { response }, true));
    }
    console.error(`callAsyncFuncs: ${requestType} failed`, e);
    return;
  }

  if (response?.errors?.length) {
    if (isString(failureType)) {
      dispatch({
        type: failureType,
        error: true,
        payload: response.errors,
        meta: {
          response,
        },
      });
    } else {
      dispatch(failureType(response.errors, { response }, true));
    }
    console.error(
      `callAsyncFuncs: ${requestType} - response error`,
      response.errors[0]
    );
    return;
  }

  if (isString(successType)) {
    dispatch({
      type: successType,
      payload: response.data,
      meta: {
        response,
      },
    });
  } else {
    dispatch(successType(response.data, { response }));
  }
  return response;
}

interface Props {
  endpoint: string;
  query: string;
  variables: any;
  operationName?: string;
  token: string;
  veritoneAppId: string;
}

type AsyncFuncFailureType<T = any> =
  | ActionCreatorWithPreparedPayload<any[], T, string, boolean | undefined, any>
  // | NoopStrings
  | string;
type AsyncFuncSuccessType<T> =
  | ActionCreatorWithPreparedPayload<any[], T, string, boolean | undefined, any>
  // | NoopStrings
  | string;
type AsyncFuncRequestType =
  | ActionCreatorWithPreparedPayload<any[], undefined, string, never, any>
  // | NoopStrings
  | string;

// The created action indicates start of async function call. No payload needed.
export function createAsyncFuncRequestAction(type: string) {
  return createAction(type, (meta: { variables: any; response?: any }) => ({
    payload: undefined,
    meta,
  }));
}

export type AsyncFuncActions<T> = [
  AsyncFuncRequestType,
  AsyncFuncSuccessType<T>,
  AsyncFuncFailureType,
];

export function createAsyncFuncSuccessAction<P>(type: string) {
  return createAction(
    type,
    (payload: P, meta?: { response: any }, error?: false) => ({
      payload,
      meta,
      error,
    })
  );
}

export function createAsyncFuncFailureAction<E = any>(type: string) {
  return createAction(
    type,
    (payload?: E, meta?: { response: any }, error?: boolean) => ({
      payload,
      meta,
      error,
    })
  );
}

interface PropsCallAsyncFunc<T> {
  actionTypes: Readonly<AsyncFuncActions<T>>;
  asyncfn: () => Promise<{ data?: T; errors?: object[] }>;
  dispatch: (action: {
    type: string;
    error?: boolean;
    payload?: any;
    meta?: { response: any };
  }) => void;
  getState: () => any;
}
