import axios, { AxiosResponse } from 'axios';
import fs from 'fs';
import { v4 as uuidV4 } from 'uuid';
import FormData from 'form-data';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface ImportsRequest extends FormData {
  append: (key: string, file: Buffer, pbixFileName: string) => void;
}

export interface ImportsResponse {
  id: string;
}

const uploadPbixToWorkspace = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const form = new FormData();
    const file = fs.readFileSync(data.pbixFilePath);

    form.append('', file, data.templatePbixFileName);

    data.datasetDisplayName = `${data.templatePbixFileName.split('.')[0]}-${uuidV4()}`;

    await axios.post<
      ImportsResponse,
      AxiosResponse<ImportsResponse>,
      ImportsRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/imports?datasetDisplayName=${data.datasetDisplayName}&nameConflict=Abort`,
      form,
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
          ...form.getHeaders(),
        },
      }
    );

    return context;
  } catch (e) {
    console.log('e', e);
    log.error('Upload PBIX API failed', e.response.data);
    throw new ApiError(e);
  }
};

export default uploadPbixToWorkspace;
