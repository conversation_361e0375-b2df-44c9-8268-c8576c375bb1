// saga listen for wordcloud actions
import { all, takeLatest, put, fork, select } from 'typed-redux-saga/macro';
import {
  TOPIC_AGGREGATION_QUERY_CLAUSE,
  fetchWordcloudAggregations,
  emitFetchWordcloudAggregations,
  FETCH_WORDCLOUD_AGGREGATIONS,
} from '.';
import {
  getFullSearchQuery,
  ON_FOLDER_QUERY_CHANGE,
  SEARCH_MEDIA,
} from '../search';

import { getDisableAnalytics } from '../tdosTable';

function* watchWordcloud() {
  yield* all([
    takeLatest(ON_FOLDER_QUERY_CHANGE, doEmitFetchWordcloudAggregations),
    takeLatest(SEARCH_MEDIA, doEmitFetchWordcloudAggregations),
  ]);
}

function* doEmitFetchWordcloudAggregations() {
  const disableAnalytics = yield* select(getDisableAnalytics);
  if (!disableAnalytics) {
    yield* put(emitFetchWordcloudAggregations());
  }
}

function* watchFetchWordcloudAggregations() {
  yield* all([
    takeLatest(FETCH_WORDCLOUD_AGGREGATIONS, function* () {
      const fullSearchQuery = yield* select(getFullSearchQuery);
      const searchQuery: Record<string, any> = {
        ...fullSearchQuery,
        aggregate: [],
      };
      searchQuery.aggregate.push(TOPIC_AGGREGATION_QUERY_CLAUSE);
      if (!searchQuery.query) {
        searchQuery.query = {
          operator: 'and',
          conditions: [],
        };
      }
      yield* put(fetchWordcloudAggregations(searchQuery));
    }),
  ]);
}

export default function* wordcloud() {
  yield* all([fork(watchWordcloud), fork(watchFetchWordcloudAggregations)]);
}
