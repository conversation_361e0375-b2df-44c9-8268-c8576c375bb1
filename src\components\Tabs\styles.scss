@import 'src/variables';

// .container {
//   padding: 0;
//   margin: 0;
//   list-style: none;
//   display: -webkit-box;
//   display: -moz-box;
//   display: -ms-flexbox;
//   display: -webkit-flex;
//   display: flex;
//   flex-flow: row wrap;
//   justify-content: space-around;
// }

.container {
  margin-right: 10px;
  margin-left: 10px;
  display: flex;

  // flex-flow: row wrap;
}

.sun-container {
  margin-right: 10px;
  margin-left: 10px;
  display: block;
  min-height: 400px;
}

.sun-item {
  padding: 5px;
  width: 100%;
  min-height: 400px;
  margin-top: 10px;

  // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.chart-item {
  padding: 5px;
  width: 50%;
  height: 400px;
  margin-top: 10px;
  line-height: 150px;
  box-shadow:
    0 1px 3px $black-3,
    0 1px 2px $black-2;
}

.card-item {
  width: 100%;
  display: flex;
  align-items: center;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 auto;
  min-width: 950px;
}

.collg {
  margin: 10px;
  flex-basis: 75%;
  flex-grow: 0;
  max-width: 100%;
  position: relative;
}

.colsm {
  margin: 10px;
  flex-basis: 25%;
  flex-grow: 1;
  max-width: 100%;
  position: relative;
}

.icon-left {
  margin-left: auto;
  margin-top: 10px;
  padding-right: 20px;
  display: flex;
}

.tabs > div > div {
  height: 60px;
}

.title-tab {
  font-size: 12px !important;
  font-weight: 500 !important;
  line-height: 22px !important;

  svg {
    margin-bottom: 0 !important;
    margin-right: 8px;
  }
}

.icon-menu {
  width: 40px;
  height: 40px;
  padding: 8px;
}

.title-selected {
  margin-top: 12px;
  margin-right: 38px;
  color: $font;
  font-family: Roboto, sans-serif;
  font-size: 13px;
  line-height: 15px;
}

.tabs button {
  max-width: 300px;
}

.icon-class-processing {
  /* stylelint-disable */
  fill: currentColor;
  /* stylelint-enable */
  width: 1em;
  height: 1em;
  display: inline-block;
  font-size: 1.5rem !important;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  flex-shrink: 0;
  user-select: none;
  margin-bottom: 0 !important;
  margin-right: 8px;
}
