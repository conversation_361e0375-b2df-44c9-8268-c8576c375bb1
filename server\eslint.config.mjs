import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import globals from 'globals';
import pluginPromise from 'eslint-plugin-promise'

export default [
  {
    ignores: [
      "**/node_modules",
      "**/dist",
      "**/tmp",
      "scripts/*",
      "jest.config.js"
    ],
  },
  js.configs.recommended,
  ...tseslint.configs.recommended,
  pluginPromise.configs['flat/recommended'],
 {
    files: ["**/*.ts", "test/**/*.ts"],

    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
      parser: tseslint.parser,
      parserOptions: {
        projectService: true,
      },
    },

    rules: {
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-non-null-assertion": "off",

      "@typescript-eslint/no-unused-vars": ["error", {
        args: "all",
        argsIgnorePattern: "^_",
        caughtErrors: "all",
        caughtErrorsIgnorePattern: "^_",
        destructuredArrayIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        ignoreRestSiblings: true,
      }],
      // TODO: Enable and fix
      'promise/always-return': 'off',
    },
}];
