import { dedupById } from '.';

describe('dedupById', () => {
  it('remove duplicated tdo', () => {
    const tdo1 = { id: '123' };
    const tdo2 = { id: '456' };
    const tdo3 = { id: '123' };
    const tdos = [tdo1, tdo2, tdo3];
    const got = dedupById(tdos);
    const want = [tdo1, tdo2];
    expect(got).toEqual(want);
  });

  it('handle no duplicates correctly', () => {
    const tdo1 = { id: '123' };
    const tdo2 = { id: '456' };
    const tdo3 = { id: '789' };
    const tdos = [tdo1, tdo2, tdo3];
    const got = dedupById(tdos);
    const want = [tdo1, tdo2, tdo3];
    expect(got).toEqual(want);
  });

  it('handle single value correctly', () => {
    const tdo1 = { id: '123' };
    const tdos = [tdo1];
    const got = dedupById(tdos);
    const want = [tdo1];
    expect(got).toEqual(want);
  });
});
