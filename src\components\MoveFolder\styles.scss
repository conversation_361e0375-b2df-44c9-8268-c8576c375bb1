$sidebarwidth: 220;

:export {
  // stylelint-disable
  sidebarwidth: $sidebarwidth;
  // stylelint-enable
}

.container {
  width: #{$sidebarwidth}px;
  height: 89%;
  position: fixed;
  left: 0;
  background-color: #eee;
}

.list-container {
  margin-left: -20px;
  margin-top: 10px;
}

.list {
  padding-top: 1px !important;
}

.list-item {
  padding-bottom: 4px !important;
  padding-top: 4px !important;
  padding-right: 12px !important;
}

.list-item-selected {
  background-color: #e0e0e0 !important;
  padding-bottom: 4px !important;
  padding-top: 4px !important;
  padding-right: 12px !important;
}

.list-item-text {
  width: 100% !important;
  line-height: 20px !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-transform: none !important;
  font-size: 14px !important;
  font-family: Roboto, sans-serif !important;
  padding-left: 5px !important;
}

.list-item-icon-folder {
  fill: #f0c75e !important;
  margin-right: 1px !important;
}

.list-item-icon-work {
  fill: #499ff1 !important;
  margin-right: 1px !important;
}

.folder {
  padding: 0 0 0 14px !important;
}

.content-folder {
  width: 800px;
  height: auto;
  border: 1px solid #e0e0e0;
  margin: 7px 20px 20px;
}

.icon-subdirectory {
  transform: rotate(90deg);
}

.list-content-sub {
  padding-left: 38px;
}

.loading-folder {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 90px;
  left: 0;
  right: 0;
  margin: 0 auto;
}
