import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                limit: 10000
                name: "contrabandOrEvidence"
                field: "contrabandOrEvidence"
                operator: "term"
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'contrabandAggregation',
      dataKey: 'contrabandOrEvidence',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Contraband Distribution',
  filterTextType: 'Contraband Distribution by Type',
  filterType: 'contrabandOrEvidence',
  filterTerms: [
    'Firearm(s)',
    'Drugs/narcotics',
    'Drug Paraphernalia',
    'Ammunition',
    'Weapon(s) other than a firearm',
    'Alcohol',
    'Cell phone(s) or electronic device(s)',
    'Suspected Stolen property',
    'Money',
    'Other Contraband or evidence',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap = {
      'Firearm(s)': 0,
      'Drugs/narcotics': 0,
      'Drug Paraphernalia': 0,
      Ammunition: 0,
      'Weapon(s) other than a firearm': 0,
      Alcohol: 0,
      'Cell phone(s) or electronic device(s)': 0,
      'Suspected Stolen property': 0,
      Money: 0,
      'Other Contraband or evidence': 0,
      ...data.contrabandAggregation.reduce(
        (acc: { [key: string]: number }, e) => {
          acc[e.key] = e.doc_count;
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = [
      { contraband: 'Firearm(s)', numberOfPeople: dataMap['Firearm(s)'] },
      {
        contraband: 'Drugs/narcotics',
        numberOfPeople: dataMap['Drugs/narcotics'],
      },
      {
        contraband: 'Drug Paraphernalia',
        numberOfPeople: dataMap['Drug Paraphernalia'],
      },
      { contraband: 'Ammunition', numberOfPeople: dataMap['Ammunition'] },
      {
        contraband: 'Weapon(s) other than a firearm',
        numberOfPeople: dataMap['Weapon(s) other than a firearm'],
      },
      { contraband: 'Alcohol', numberOfPeople: dataMap['Alcohol'] },
      {
        contraband: 'Cell phone(s) or electronic device(s)',
        numberOfPeople: dataMap['Cell phone(s) or electronic device(s)'],
      },
      {
        contraband: 'Suspected Stolen property',
        numberOfPeople: dataMap['Suspected Stolen property'],
      },
      { contraband: 'Money', numberOfPeople: dataMap['Money'] },
      {
        contraband: 'Other Contraband or evidence',
        numberOfPeople: dataMap['Other Contraband or evidence'],
      },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'numberOfPeople';
    pieSeries.dataFields.category = 'contraband';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  contrabandAggregation: { key: string; doc_count: number }[];
}
