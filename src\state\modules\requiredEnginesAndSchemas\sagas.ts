import { all, put, call, select } from 'typed-redux-saga/macro';
import { checkRequiredEnginesAndSchemasFailure } from '.';
import { handleRequest } from '../../utils/util';
import getEnginesRequired from '../../../helpers/getEnginesRequired';
import getSchemasRequired from '../../../helpers/getSchemasRequired';
const STATUS_AVAILABLE = 'Available';
const STATUS_NOT_AVAILABLE = 'N/A';
export function* checkRequiredEnginesAndSchemas() {
  const enginesRequired = yield* select(getEnginesRequired);
  const schemasRequired = yield* select(getSchemasRequired);
  if (!enginesRequired || !schemasRequired) {
    return;
  }
  const querySchemas = `query getSchemas {
      schemas(name: "${schemasRequired.name}") {
        records {
          id
          status
        }
      }
    }`;
  const engineIds = enginesRequired.map((engine) => engine.id);
  const queryEngines = `query getEngines($engineIds: [ID!]) {
      engines(ids: $engineIds) {
        records{
          id
          name
        }
      }
    }`;
  const variables = {
    engineIds,
  };

  // TS throws an error: "Target requires 2 element(s) but source may have fewer"
  // Type cast here is safe, because we pass exactly 2 effects to all(),
  // the result will always be a tuple containing 2 elements.
  const enginesSchemaRequired = (yield* all([
    call(handleRequest<EngineResponse['response']['data']>, {
      query: queryEngines,
      variables,
    }),
    call(handleRequest<SchemaResponse['response']['data']>, {
      query: querySchemas,
    }),
  ])) as RequiredResponses;

  const payload = makeDataRequiredEnginesAndSchemas(
    enginesSchemaRequired,
    enginesRequired,
    schemasRequired
  );
  if (
    enginesSchemaRequired.filter((item) => item.error).length ||
    payload.filter((item) => item.status === STATUS_NOT_AVAILABLE).length
  ) {
    console.error(
      `missing ${JSON.stringify(
        payload.filter((item) => item.status === STATUS_NOT_AVAILABLE)
      )}`
    );
    return yield* put(checkRequiredEnginesAndSchemasFailure(payload));
  }
  return;
}

function makeDataRequiredEnginesAndSchemas(
  enginesSchemaRequired: RequiredResponses,
  enginesRequired: { id: string; name: string }[],
  schemasRequired: { name: string }
) {
  const dataCheckRequiredEngines =
    enginesSchemaRequired[0]?.response?.data?.engines?.records || [];

  const dataCheckRequiredSchemas =
    enginesSchemaRequired[1]?.response?.data?.schemas?.records || [];

  const results = [];
  enginesRequired.forEach((engine) => {
    results.push({
      ...engine,
      status: dataCheckRequiredEngines.some((item) => item.id === engine.id)
        ? STATUS_AVAILABLE
        : STATUS_NOT_AVAILABLE,
      type: 'Engine',
    });
  });
  results.push({
    id: dataCheckRequiredSchemas[0]?.id || '',
    name: schemasRequired.name,
    status: dataCheckRequiredSchemas.length
      ? STATUS_AVAILABLE
      : STATUS_NOT_AVAILABLE,
    type: 'Schema',
  });
  return results.sort((a, b) => b.status.localeCompare(a.status));
}

interface EngineResponse {
  error: null;
  response: {
    data: {
      engines: {
        records: {
          id: string;
          name: string;
        }[];
      };
    };
    errors?: any[];
  };
}

interface SchemaResponse {
  error: null;
  response: {
    data: {
      schemas: {
        records: {
          id: string;
          status: string;
        }[];
      };
    };
    errors?: any[];
  };
}

interface ErrorResponse {
  error: any;
  response?: undefined;
}

type RequiredResponses = [
  EngineResponse | ErrorResponse,
  SchemaResponse | ErrorResponse,
];
