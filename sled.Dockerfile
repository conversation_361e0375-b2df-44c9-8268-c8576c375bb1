FROM node:22 as builder
ENV APPLICATION=illuminate-app
ARG ENVIRONMENT
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
ADD . /app
WORKDIR /app
RUN ls -a && \
    chmod +x /app/*.sh && \
    ./getconfig.sh && \
    cat /app/config.json && \
    yarn && \
    yarn build

RUN echo '### /app/buildinfo.sh...' && /app/buildinfo.sh

FROM nginx:1.29-alpine
ARG ENVIRONMENT
ENV NGINX_PORT=9000
COPY --from=builder "/app/build-${ENVIRONMENT}" /usr/share/nginx/html
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /app/nginx.conf /etc/nginx/conf.d/default.conf
