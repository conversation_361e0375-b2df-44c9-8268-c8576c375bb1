import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { Config } from '../chartDefinitions';

export default {
  dataQueries: [
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
            select: [""]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'searches',
      dataKey: 'totalResults',
    },
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Asked for consent to search person"
                    "Asked for consent to search property"
                  ]
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
            select: [""]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'searchWithConsent',
      dataKey: 'totalResults',
    },
    {
      query: `query search($lowerDateBound: String, $upperDateBound: String, $schemaId: String) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            select: [""]
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'totalRecords',
      dataKey: 'totalResults',
    },
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    // Add data
    chart.data = [
      { action: 'Searches with Consent', amount: data.searchWithConsent },
      {
        action: 'Searches without Consent',
        amount: data.searches - data.searchWithConsent,
      },
      { action: 'No Search', amount: data.totalRecords - data.searches },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'amount';
    pieSeries.dataFields.category = 'action';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

interface Data {
  searchWithConsent: number;
  searches: number;
  totalRecords: number;
}
