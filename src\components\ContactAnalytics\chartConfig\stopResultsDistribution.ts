import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "resultOfStop"
                field: "resultOfStop"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'resultOfStopAggregation',
      dataKey: 'resultOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            limit: 1
            offset: 0
            type: $schemaId
            select: [""]
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'totalRecords',
      dataKey: 'totalResults',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
                {
                  field: "resultOfStop",
                  operator: "query_string",
                  value: "*"
                }
              ]
            }
            aggregate: [{
                operator: "count",
              distinct: false,
                field: "_id"
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'resultOfStopTotalCount',
      dataKey: 'totalResults',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Stop Results Distribution',
  filterTextType: 'Stop Results Distribution by Type',
  filterType: 'resultOfStop',
  filterTerms: {
    Citation: [
      'Citation for infraction',
      'Warning (verbal or written)',
      'In-field cite and release',
    ],
    Arrest: [
      'Custodial arrest without warrant',
      'Custodial arrest pursuant to outstanding warrant',
    ],
    Other: [
      'Contacted parent/legal guardian or other person responsible for the minor',
      'Field interview card completed',
      'Noncriminal transport or caretaking transport  (including transport by officer, ambulance, or another agency)',
      'Psychiatric hold  (Welfare & Institutions Code sections 5150 and/or 5585.20)',
      'Contacted U.S. Department of Homeland Security  (EG: Immigration, Customs, and Border Protection)',
    ],
    K12: [
      'Referral to school administrator',
      'Referral to school counselor or other support staff',
    ],
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap = {
      Citation: 0,
      Arrest: 0,
      Other: 0,
      K12: 0,
      'No Action Taken':
        config.useFilter === 'all'
          ? data.totalRecords - data.resultOfStopTotalCount
          : 0,
      ...data.resultOfStopAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          if (
            [
              'Citation for infraction',
              'Warning (verbal or written)',
              'In-field cite and release',
            ].includes(b.key)
          ) {
            acc['Citation'] = acc['Citation']
              ? acc['Citation'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Custodial arrest without warrant',
              'Custodial arrest pursuant to outstanding warrant',
            ].includes(b.key)
          ) {
            acc['Arrest'] = acc['Arrest']
              ? acc['Arrest'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Contacted parent/legal guardian or other person responsible for the minor',
              'Field interview card completed',
              'Noncriminal transport or caretaking transport  (including transport by officer, ambulance, or another agency)',
              'Psychiatric hold  (Welfare & Institutions Code sections 5150 and/or 5585.20)',
              'Contacted U.S. Department of Homeland Security  (EG: Immigration, Customs, and Border Protection)',
            ].includes(b.key)
          ) {
            acc['Other'] = acc['Other']
              ? acc['Other'] + b.doc_count
              : b.doc_count;
          }
          if (
            [
              'Referral to school administrator',
              'Referral to school counselor or other support staff',
            ].includes(b.key)
          ) {
            acc['K12'] = acc['K12'] ? acc['K12'] + b.doc_count : b.doc_count;
          }
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // Add data
    chart.data = [
      { result: 'Citation', count: dataMap['Citation'] },
      { result: 'Arrest', count: dataMap['Arrest'] },
      { result: 'Other', count: dataMap['Other'] },
      { result: 'K12', count: dataMap['K12'] },
      { result: 'No Action Taken', count: dataMap['No Action Taken'] },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'count';
    pieSeries.dataFields.category = 'result';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  totalRecords: number;
  resultOfStopTotalCount: number;
  resultOfStopAggregation: { key: string; doc_count: number }[];
}
