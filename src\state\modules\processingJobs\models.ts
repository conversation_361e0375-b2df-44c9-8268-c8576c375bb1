export interface Task {
  id: string;
  status: string;
  engine: {
    id: string;
    name: string;
  };
  completedDateTime: string;
  failureMessage: string;
  taskOutput: {
    failureMessage: string;
  };
}
export interface Job {
  id: string;
  status: string;
  target: {
    id: string;
    name: string;
    details: {
      veritoneFile: {
        fileName: string;
        filename: string;
      };
    };
  };
  createdDateTime: string;
  modifiedDateTime: string;
  tasks: {
    records: Task[];
  };
}
export interface ProcessingJob {
  targetId: string;
  name: string;
  jobs: Job[];
  modifiedDateTime: string;
}
