import { constant, isFunction, isString } from 'lodash';
import { guid } from './guid';
import { modules } from '@veritone/glc-redux';
import fetchGraphQLApi from './fetchGraphQLApi';
import getApiAuthToken from './getApiAuthToken';
import { ExtendedError } from '@utils';
import {
  ActionCreatorWithPreparedPayload,
  createAction,
} from '@reduxjs/toolkit';

const {
  config: { getConfig },
} = modules;

async function callGraphQLApi<T>({
  actionTypes: [requestType, successType, failureType],
  query,
  variables,
  operationName,
  bailout = constant(false),
  dispatch,
  getState,
  // optional requestId which can be given to the selectors generated by
  // handleApiCall to track state of an individual request (by default only the
  // latest request is tracked)
  requestId,
}: Props<T>) {
  try {
    if (!isFunction(dispatch) || !isFunction(getState)) {
      throw new Error(
        'callGraphQLApi requires dispatch and getState functions'
      );
    }

    const state = getState();
    const config = getConfig<Window['config']>(state);
    const endpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
    const token = getApiAuthToken(state);
    const veritoneAppId = config.veritoneAppId;
    const shouldBail = bailout(state);
    if (shouldBail) {
      return;
    }

    // attach an ID so the handleApiCall reducer can track this request across
    // its multiple actions
    const _internalRequestId = requestId || guid();

    if (isString(requestType)) {
      dispatch({
        type: requestType,
        meta: {
          variables,
          operationName,
          query,
          _internalRequestId,
          _shouldTrackRequestsIndividually: !!requestId,
        },
      });
    } else {
      dispatch(
        requestType({
          variables,
          operationName,
          query,
          _internalRequestId,
          _shouldTrackRequestsIndividually: !!requestId,
        })
      );
    }
    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    let response;
    try {
      response = await fetchGraphQLApi<T>({
        endpoint,
        query,
        variables,
        operationName,
        token: token,
        veritoneAppId,
      });
    } catch (e) {
      const meta = {
        response,
        variables,
        operationName,
        query,
        _internalRequestId,
        _shouldTrackRequestsIndividually: !!requestId,
      };
      if (isString(failureType)) {
        dispatch({
          type: failureType,
          error: true,
          payload: e,
          meta,
        });
      } else {
        dispatch(failureType(e, meta, true));
      }
      const error = new Error('API call failed') as ExtendedError;
      // wrap this single error for consistency with graphQL errors, which are always
      // wrapped.
      error.errors = [e];
      throw error;
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify({ query, variables }) }
      );
    }

    if (response.errors && response.errors.length) {
      const meta = {
        response,
        variables,
        operationName,
        query,
        _internalRequestId,
        _shouldTrackRequestsIndividually: !!requestId,
      };
      if (isString(failureType)) {
        dispatch({
          type: failureType,
          error: true,
          payload: response.errors,
          meta,
        });
      } else {
        dispatch(failureType(response.errors, meta, true));
      }
      const error = new Error('API response included errors') as ExtendedError;
      error.errors = response.errors;
      throw error;
    }

    const meta = {
      response,
      variables,
      operationName,
      query,
      _internalRequestId,
      _shouldTrackRequestsIndividually: !!requestId,
    };
    if (isString(successType)) {
      dispatch({
        type: successType,
        payload: response.data,
        meta,
      });
    } else {
      dispatch(successType(response.data, meta));
    }
    return response.data;
  } catch (ex) {
    console.error('callGraphQLApi error', ex, {
      requestType: isString(requestType) ? requestType : requestType.type,
      query,
      variables,
      operationName,
    });
    throw ex;
  }
}

type GQLFailureType<T = any> =
  | ActionCreatorWithPreparedPayload<any[], T, string, boolean | undefined, any>
  // | NoopStrings
  | string;
type GQLSuccessType<T> =
  | ActionCreatorWithPreparedPayload<any[], T, string, boolean | undefined, any>
  // | NoopStrings
  | string;
type GQLRequestType =
  | ActionCreatorWithPreparedPayload<any[], undefined, string, never, any>
  // | NoopStrings
  | string;

export type GQLActions<T> = readonly [
  GQLRequestType,
  GQLSuccessType<T>,
  GQLFailureType,
];

// type GQLActionType<T = any> =
//   | string
//   | ActionCreatorWithPayload<T, string>
//   | ActionCreatorWithoutPayload<string>;

export function createGQLRequestAction<R = any>(type: string) {
  return createAction(
    type,
    (
      payload: R,
      meta: {
        variables: Record<string, any>;
        operationName: string;
        query: string;
        _internalRequestId?: string;
        _shouldTrackRequestsIndividually: boolean;
      }
    ) => ({ payload, meta })
  );
}

export function createGQLSuccessAction<P>(type: string) {
  return createAction(
    type,
    (
      payload: P,
      meta: {
        response: any;
        variables: Record<string, any>;
        operationName: string;
        query: string;
        _internalRequestId?: string;
        _shouldTrackRequestsIndividually: boolean;
      },
      error?: false
    ) => ({ payload, meta, error })
  );
}

export function createGQLFailureAction<E = any>(type: string) {
  return createAction(
    type,
    (
      payload: E,
      meta: {
        response: any;
        variables: Record<string, any>;
        operationName: string;
        query: string;
        _internalRequestId?: string;
        _shouldTrackRequestsIndividually: boolean;
      },
      error?: boolean
    ) => ({ payload, meta, error })
  );
}

interface Props<T> {
  actionTypes: GQLActions<T>;
  query: string;
  variables?: object;
  operationName?: string;
  bailout?: (state: any) => boolean;
  dispatch: (...args: any[]) => any;
  getState: (...args: any[]) => any;
  requestId?: string;
}

export default callGraphQLApi;
