# The end to end test setup

## test folder and file set up

1. create e2e/mediaDetail folder in the test account
   there are 3 test files need to be uploaded into e2e/mediaDetail folder from setup folder

- e2e_video.mp4
- e2e_audio.mp3
- bloomberg.mp4

2. create e2e/reprocess folder
   upload spanish-mp3.mp3 into e2e/reprocess from setup folder

3. create e2e/upload folder

## search setup

1. create tag 'tagSearch' on bloomberg.mp4
2. create bookmark 'bookmarkSearch' on e2e_video.mp4

## engine setup

In addition to the engines required by illuminate (https://veritone.atlassian.net/wiki/spaces/GE/pages/**********/New+Org+Setup+for+Illuminate), please whitelist following engines:

c0e55cde-340b-44d7-bb42-2e0d65e98255 (Transcription English)
02f4d710-cbcd-4534-98d6-a31522f8f4a6 (Speaker Separation English);
d66f553d-3cef-4c5a-9b66-3e551cc48b4b (Machine box Tag box)
95c910f6-4a26-4b66-96cb-488befa86466 (Translate Spanish to English)
62f2b2a7-bfd6-44c8-ab78-c02b47f95974 (Text Extraction)

## run test

### test local illuminate-app

- run the illuminate-app locally
  - yarn startssl
- run the e2e test
  - create a cypress.env.json file with the test account user name and password.
  ```
  {
    "username": "<EMAIL>",
    "password": "veri_tester_2468"
  }
  ```
  - yarn cy:open

### test the remote illuminate-app

First, please setup folders and seeded files in corresponding environment.
And then, when running the cypress, please pass in argument azure-stage, azure-prod, dev, stage, prod respectively.

- e.g. test the illuminate-app in azure stage
  - create a cypress.env.json with user name and password if you don't have it.
  - yarn cypress run --env ENVIRONMENT=azure-stage
    e.g. `yarn e2e` will test azure stage illuminate.

## jenkins integration
