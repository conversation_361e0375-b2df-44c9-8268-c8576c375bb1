import FilePickerHeader from '.';
import { render, fireEvent } from '@testing-library/react';

describe('FilePickerHeader', () => {
  const onSelectTab = jest.fn();
  const onCloseModal = jest.fn();

  it('should have a title of props.title', () => {
    const { getByTestId } = render(
      <FilePickerHeader
        selectedTab="upload"
        onSelectTab={onSelectTab}
        onClose={onCloseModal}
        allowUrlUpload
        title="My Cool File Picker"
      />
    );
    expect(getByTestId('file-picker-title')).toBeInTheDocument();

    expect(getByTestId('file-picker-title')).toHaveTextContent(
      'My Cool File Picker'
    );
  });

  it('should have a Tabs bar with two tabs', () => {
    const { getByTestId } = render(
      <FilePickerHeader
        selectedTab="upload"
        onSelectTab={onSelectTab}
        onClose={onCloseModal}
        allowUrlUpload
        title="My Cool File Picker"
      />
    );
    expect(getByTestId('file-picker-upload-tab')).toBeInTheDocument();
    expect(getByTestId('file-picker-url-tab')).toBeInTheDocument();
  });

  it('onSelectTab should be called when a tab is clicked', () => {
    const { getByTestId } = render(
      <FilePickerHeader
        selectedTab="upload"
        onSelectTab={onSelectTab}
        onClose={onCloseModal}
        allowUrlUpload
        title="My Cool File Picker"
      />
    );
    fireEvent.click(getByTestId('file-picker-url-tab'));
    expect(onSelectTab).toHaveBeenCalled();
  });

  it('should have a close "x" button', () => {
    const { getByTestId } = render(
      <FilePickerHeader
        selectedTab="upload"
        onSelectTab={onSelectTab}
        onClose={onCloseModal}
        allowUrlUpload
        title="My Cool File Picker"
      />
    );

    expect(getByTestId('file-picker-close-button')).toBeInTheDocument();
  });

  it('should call onCloseModal when the close button is clicked', () => {
    const { getByTestId } = render(
      <FilePickerHeader
        selectedTab="upload"
        onSelectTab={onSelectTab}
        onClose={onCloseModal}
        allowUrlUpload
        title="My Cool File Picker"
      />
    );
    fireEvent.click(getByTestId('file-picker-close-button'));
    expect(onCloseModal).toHaveBeenCalled();
  });
});
