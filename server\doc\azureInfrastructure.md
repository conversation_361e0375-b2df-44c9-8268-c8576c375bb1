# Azure PowerBI Infrastructure

## Create Power BI Embedded resource

- Provides PowerBI Embedded compute resources

## Create Service principal

- Add an App Registration in Azure AD named 'PBIEmbedded'
- Supply OAuth 2.0 token endpoint (v2) to developer _(E.G. https://login.microsoftonline.us/2b6ba87b-e3cb-4547-bd91-e4160125db25/oauth2/v2.0/token)_
- Supply Azure AD TenantId to developer
- Supply Azure AD User ID and Email with PowerBI Admin Privileges
- Supply PBIEmbedded Object ID to developer
- Under PBIEmbedded Certiicates and Secret, create a client secrete and key, **supply them to developer**
- Under PBIEmbedded API Permissions, **add and grant** the following **delegated** permissions:
  - Azure Storage (1)
    - user_impersonation
  - Power BI Service (22)
    - App.Read.All
    - Capacity.Read.All
    - Capacity.ReadWrite.All
    - Content.Create
    - Dashboard.Read.All
    - Dashboard.ReadWrite.All
    - Read and write all dashboards
    - Data.Alter_Any
    - Dataflow.Read.All
    - Dataflow.ReadWrite.All
    - Dataset.Read.All
    - Dataset.ReadWrite.All
    - Gateway.Read.All
    - Gateway.ReadWrite.All
    - Group.Read
    - Group.Read.All
    - Metadata.View_Any
    - Report.Read.All
    - Report.ReadWrite.All
    - StorageAccount.Read.All
    - StorageAccount.ReadWrite.All
    - Workspace.Read.All
    - Workspace.ReadWrite.All

## Create Blob Storage Container

- Container name: powerbi-contact
- Add folder named PbixTemplates
- Under User Accesss Control - add the role 'Storage Blob Data Contributor' to the 'PBIEmbedded' service principal
- Supply blob service endpoint to developer _(E.G. 'https://vtstorcoredev.blob.core.usgovcloudapi.net/')_

## Create Azure SQL Server + DB

- Db name: powerbi-contact
- Enable 'Allow azure services and resources to access this server' in Server Networking tab
- Supply admin username/password to developer
- Supply hostname to developer
- Supply port to developer (if not 1433)
