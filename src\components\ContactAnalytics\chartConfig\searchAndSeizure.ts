import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { DateTime } from 'luxon';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

export default {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
              }
            ]
          }
        ) {
          jsondata
        }
      }
      `,
      isAggregation: true,
      storageKey: 'searchAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
                {
                  operator: "term"
                  field: "actionsTakenDuringStop"
                  value: "Property was seized"
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
              }
            ]
          }
        ) {
          jsondata
        }
      }
    `,
      isAggregation: true,
      storageKey: 'searchAndSeizureAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Search of person was conducted"
                    "Search of property was conducted"
                  ]
                }
                {
                  operator: "terms"
                  field: "actionsTakenDuringStop"
                  values: [
                    "Asked for consent to search person"
                    "Asked for consent to search property"
                  ]
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'searchWithConsentAggregation',
      dataKey: 'datetimeOfStop',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'totalPeople',
      dataKey: 'datetimeOfStop',
    },
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    const dataMap: { [key: string]: { [key: string]: number } } = {};
    Object.entries(data).map((kvp) => {
      const [storageKey, dataAry] = kvp;
      dataAry.forEach(
        (e: { doc_count: number; key_as_string: string; key: number }) => {
          if (!dataMap[e.key_as_string]) {
            dataMap[e.key_as_string] = {
              searchAggregation: 0,
              searchAndSeizureAggregation: 0,
              searchWithConsentAggregation: 0,
              totalPeople: 0,
              searchWithoutConsent: 0,
              noSearch: 0,
            };
          }
          dataMap[e.key_as_string]![storageKey] = e.doc_count;
        }
      );
    });

    chart.data = Object.keys(dataMap)
      .map((k) => {
        const {
          searchAggregation,
          searchAndSeizureAggregation,
          searchWithConsentAggregation,
          totalPeople,
        } = dataMap[k] as { [key: string]: number };
        return {
          date: k,
          searchAggregation,
          searchAndSeizureAggregation,
          searchWithConsentAggregation,
          totalPeople,
          searchWithoutConsent:
            searchAggregation! - searchWithConsentAggregation!,
          noSearch: totalPeople! - searchAggregation!,
        };
      })
      .sort(
        (a, b) =>
          DateTime.fromISO(a.date).toUnixInteger() -
          DateTime.fromISO(b.date).toUnixInteger()
      );

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';
    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Count';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'searchAggregation';
    series.dataFields.dateX = 'date';
    series.name = 'Searches';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    const series2 = chart.series.push(new am4charts.LineSeries());
    series2.strokeWidth = config.lineWidth;
    series2.dataFields.valueY = 'searchAndSeizureAggregation';
    series2.dataFields.dateX = 'date';
    series2.name = 'Search with Seizure';
    series2.tooltipText = '{name}: [bold]{valueY}[/]';

    const series3 = chart.series.push(new am4charts.LineSeries());
    series3.strokeWidth = config.lineWidth;
    series3.dataFields.valueY = 'searchWithConsentAggregation';
    series3.dataFields.dateX = 'date';
    series3.name = 'Search with Consent';
    series3.tooltipText = '{name}: [bold]{valueY}[/]';

    const series4 = chart.series.push(new am4charts.LineSeries());
    series4.strokeWidth = config.lineWidth;
    series4.dataFields.valueY = 'totalPeople';
    series4.dataFields.dateX = 'date';
    series4.name = 'Total People Stopped';
    series4.tooltipText = '{name}: [bold]{valueY}[/]';

    const series5 = chart.series.push(new am4charts.LineSeries());
    series5.strokeWidth = config.lineWidth;
    series5.dataFields.valueY = 'searchWithoutConsent';
    series5.dataFields.dateX = 'date';
    series5.name = 'Search without Consent';
    series5.tooltipText = '{name}: [bold]{valueY}[/]';

    const series6 = chart.series.push(new am4charts.LineSeries());
    series6.strokeWidth = config.lineWidth;
    series6.dataFields.valueY = 'noSearch';
    series6.dataFields.dateX = 'date';
    series6.name = 'No Search';
    series6.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    chart.paddingTop = 0;
    chart.paddingBottom = 0;
    chart.paddingLeft = 0;
    chart.paddingRight = 0;

    chart.padding(0, 0, 0, 0);

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

interface Data {
  searchAggregation: {
    doc_count: number;
    key_as_string: string;
    key: number;
  }[];
  searchAndSeizureAggregation: {
    doc_count: number;
    key_as_string: string;
    key: number;
  }[];
  searchWithConsentAggregation: {
    doc_count: number;
    key_as_string: string;
    key: number;
  }[];
  totalPeople: { doc_count: number; key_as_string: string; key: number }[];
}
