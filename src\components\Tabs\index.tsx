import React, { Fragment, ReactNode } from 'react';
import {
  ThemeProvider,
  StyledEngineProvider,
  createTheme,
} from '@mui/material/styles';
import withStyles from '@mui/styles/withStyles';
import Paper from '@mui/material/Paper';
import MaterialTabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import * as containerStyles from './styles.scss';
import Assessment from '@mui/icons-material/Assessment';
import Description from '@mui/icons-material/Description';
import BarChart from '@mui/icons-material/BarChart';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import FolderOpenRoundedIcon from '@mui/icons-material/FolderOpenRounded';

import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Label from '@mui/icons-material/Label';
import Tooltip from '@mui/material/Tooltip';
import SaveAlt from '@mui/icons-material/SaveAlt';
import HistoryIcon from '@mui/icons-material/History';
import Icon from '@mui/material/Icon';
import {
  getSelectedRows,
  getTdosForExport,
  isSelectedAll,
  IS_EXPORT_ALL,
  SHOW_BULK_EXPORT,
  getDisableAnalytics,
  getContactAnalyticsStatus,
  getContactAnalyticsPbiStatus,
  getSelectedCount,
  getSelectedTdos,
} from '../../state/modules/tdosTable';
import { TABS } from 'state/modules/tabs';
import { SEND_TO_REDACT } from '../../state/modules/sendToRedact';
import {
  getTotalResults,
  isSearchingMedia,
  onClickFilesTab,
} from 'state/modules/search';
import { getTotalCount } from 'state/modules/history';
import { ConnectedProps, connect } from 'react-redux';
import TagDialog from '../Dialogs/tagDialog';
import MoveDialog from '../Dialogs/MoveDialog/moveDialog';
import DeleteDialog from '../Dialogs/deleteDialog';
import {
  SHOW_FORM_CREATE_FOLDER,
  SHOW_MOVE_FOLDER,
  FETCH_MOVE_FOLDER_REQUEST,
  FETCH_UPDATE_MOVE_FILES_REQUEST,
  DELETE_FILES_REQUEST,
  getRootFolderId,
  getSelectedFolderId,
  getShowMoveFolder,
} from 'state/modules/folders';
import {
  openModalReprocess,
  runTextAnalytics,
} from 'state/modules/uploadFile/actions';
import { selectCurrentRoutePayload, ROUTE_TABS } from 'state/modules/routing';
import Analytics from './Analytics';
import Files from './Files';
import Exports from './Exports';
import ProcessingStatus from './ProcessingStatus';
import ContactAnalytics from './ContactAnalytics';
import ContactAnalyticsPowerbi from './ContactAnalyticsPowerbi';
import { ClassNameMap } from '@mui/styles';
import getMoveDeleteFlag from '~helpers/getMoveDeleteFlag';

const tabTheme = createTheme({
  palette: {
    primary: {
      main: '#2196F3',
      light: '#4dabf5',
      dark: '#1769aa',
      contrastText: '#ffffff',
    },
  },
  components: {
    MuiTab: {
      styleOverrides: {
        root: {
          minWidth: '220px',
          fontSize: 14,
          fontWeight: 400,
          fontFamily: 'Roboto',
          '&$selected': {
            color: '#2196F3',
            fontWeight: 500,
          },
        },
      },
    },
  },
});

const styles = () => ({
  root: {
    flexGrow: 1,
    width: '100%',
    backgroundColor: '#fff',
    boxShodow: 'none',
  },
  indicator: {
    backgroundColor: '#2196F3',
  },
  wrapper: {
    display: 'flex',
    flexDirection: 'row' as const,
  },
  labelIcon: {
    paddingTop: 6,
    minHeight: 60,
    minWidth: 186,
  },
  fileIcon: {
    color: '#555F7C',
  },
});

const TabContainer = (props: { children: ReactNode }) => (
  <Typography component="div" className={containerStyles['icon-left']}>
    {props.children}
  </Typography>
);
export class CenteredTabs extends React.Component<Props> {
  state = {
    anchorEl: null,
    moveDialogOpen: false,
    deleteDialogOpen: false,
    tagDialogOpen: false,
    redactDialogOpen: false,
  };

  onTabSelected = (
    _event: React.ChangeEvent<EventTarget>,
    tabValue: string
  ) => {
    const { navigateToTab } = this.props;
    navigateToTab(tabValue);
  };

  handleMenuClick = (event: React.MouseEvent) => {
    this.setState({ anchorEl: event.currentTarget });
  };

  handleMenuClose = () => {
    this.setState({ anchorEl: null });
  };

  getTags = () => {
    this.setState({
      tagDialogOpen: true,
    });
  };

  onTagDialogClose = () => {
    this.setState({
      tagDialogOpen: false,
    });
  };

  onTagButtonClick = () => {
    this.getTags();
  };

  openDeleteDialog = () => {
    this.setState({
      deleteDialogOpen: true,
    });
  };

  onDeleteDialogClose = () => {
    this.setState({
      deleteDialogOpen: false,
    });
  };

  onDeleteButtonClick = () => {
    this.openDeleteDialog();
  };

  handleDeleteFiles = () => {
    const { handleDeleteFiles } = this.props;
    handleDeleteFiles();
    this.setState({
      deleteDialogOpen: false,
    });
  };

  openMoveDialog = () => {
    const { fetchMoveFolder } = this.props;
    fetchMoveFolder();
    this.setState({
      moveDialogOpen: true,
    });
  };

  onMoveDialogClose = () => {
    this.setState({
      moveDialogOpen: false,
    });
  };

  handleMoveFiles = () => {
    const { handleMoveFiles } = this.props;
    handleMoveFiles();
    this.setState({
      moveDialogOpen: false,
    });
  };

  onMoveButtonClick = () => {
    this.openMoveDialog();
  };

  handleRenameFolder = () => {
    const { handleShowCreateFolder } = this.props;
    handleShowCreateFolder({ type: 'rename', caseOptions: 'tab' });
    this.handleMenuClose();
  };

  handleMoveFolder = () => {
    const { handleShowMoveFolder, fetchMoveFolder } = this.props;
    handleShowMoveFolder({ type: 'moveFolder', caseOptions: 'tab' });
    fetchMoveFolder();
    this.handleMenuClose();
  };

  onExportButtonClick = (event: React.MouseEvent) => {
    this.handleExport(event);
  };

  handleExport = (event: React.MouseEvent) => {
    const exportType = event.currentTarget.getAttribute('data-type');
    const { isClickExportAll, showBulkExport } = this.props;
    if (exportType === 'exportAll') {
      isClickExportAll();
    }
    showBulkExport();
  };

  onRedactButtonClick = () => {
    const { tdosForExport, handleSendToRedact } = this.props;
    handleSendToRedact(tdosForExport);
  };

  handleReprocess = () => {
    const { openModalReprocess } = this.props;
    openModalReprocess();
  };
  handleRunTextAnalytics = () => {
    const { runTextAnalytics } = this.props;
    runTextAnalytics();
  };
  render() {
    const {
      classes,
      // allRowsSelectedIndexes,
      totalResults,
      isSelectedAll,
      rootFolderId,
      selectedFolderId,
      totalExportCount,
      disableAnalytics,
      contactAnalyticsEnabled,
      contactAnalyticsPbiEnabled,
      currentTab,
      moveDeleteFlag,
      tdosForExport,
      selectedCount,
      selectedTdos,
    } = this.props;
    const { anchorEl, tagDialogOpen, deleteDialogOpen, moveDialogOpen } =
      this.state;
    const open = Boolean(anchorEl);
    const isRootSelected = rootFolderId === selectedFolderId;
    const tdoSelected = Object.values(selectedTdos).flatMap((tdo) => tdo);
    const isErrorTDOSelected =
      tdoSelected &&
      tdoSelected.length === 1 &&
      tdoSelected[0].status === 'error';
    const TabComponent = {
      [TABS.Analytics]: Analytics,
      [TABS.Files]: Files,
      [TABS.Exports]: Exports,
      [TABS.ProcessingStatus]: ProcessingStatus,
      [TABS.ContactAnalytics]: ContactAnalytics,
      [TABS.ContactAnalyticsPowerbi]: ContactAnalyticsPowerbi,
    }[currentTab];
    return (
      <Paper className={classes.root}>
        <StyledEngineProvider injectFirst>
          <ThemeProvider theme={tabTheme}>
            <MaterialTabs
              value={currentTab}
              onChange={this.onTabSelected}
              indicatorColor="primary"
              textColor="primary"
              className={containerStyles.tabs}
            >
              {!disableAnalytics && (
                <Tab
                  label="ANALYTICS"
                  data-test="analytics-tab-button"
                  icon={<Assessment />}
                  classes={{
                    wrapped: classes.wrapper,
                    labelIcon: classes.labelIcon,
                  }}
                  className={containerStyles['title-tab']}
                  value={TABS.Analytics}
                  data-testid="tab"
                  iconPosition="start"
                />
              )}
              <Tab
                label={`FILES(${totalResults})`}
                data-test="files-tab-button"
                icon={<Description />}
                classes={{
                  wrapped: classes.wrapper,
                  labelIcon: classes.labelIcon,
                }}
                className={containerStyles['title-tab']}
                value={TABS.Files}
                data-testid="tab"
                iconPosition="start"
                onClick={this.props.onClickFilesTab}
              />
              <Tab
                label={`EXPORTS(${totalExportCount || 0})`}
                data-test="history-tab-button"
                icon={<HistoryIcon />}
                classes={{
                  wrapped: classes.wrapper,
                  labelIcon: classes.labelIcon,
                }}
                className={containerStyles['title-tab']}
                value={TABS.Exports}
                data-testid="tab"
                iconPosition="start"
              />
              <Tab
                label={'PROCESSING STATUS'}
                icon={
                  <Icon
                    className="icon-enginerunning"
                    classes={{
                      root: containerStyles['icon-class-processing'],
                    }}
                  />
                }
                classes={{
                  wrapped: classes.wrapper,
                  labelIcon: classes.labelIcon,
                }}
                className={containerStyles['title-tab']}
                data-test="processing-status"
                value={TABS.ProcessingStatus}
                data-testid="tab"
                iconPosition="start"
              />
              {contactAnalyticsEnabled && (
                <Tab
                  label={'CONTACT ANALYTICS'}
                  icon={<BarChart />}
                  classes={{
                    wrapped: classes.wrapper,
                    labelIcon: classes.labelIcon,
                  }}
                  className={containerStyles['title-tab']}
                  data-test="contact-analytics"
                  value={TABS.ContactAnalytics}
                  iconPosition="start"
                />
              )}
              {contactAnalyticsPbiEnabled && (
                <Tab
                  label={'CONTACT ANALYTICS POWERBI'}
                  icon={<BarChart />}
                  classes={{
                    wrapped: classes.wrapper,
                    labelIcon: classes.labelIcon,
                  }}
                  className={containerStyles['title-tab']}
                  data-test="contact-analytics-powerbi"
                  value={TABS.ContactAnalyticsPowerbi}
                  iconPosition="start"
                />
              )}
              <TabContainer>
                {currentTab === TABS.Analytics ? (
                  <Fragment>
                    <IconButton
                      className={containerStyles['icon-menu']}
                      data-test="main-export-icon-button"
                      onClick={this.onExportButtonClick}
                      disabled={totalResults === 0 ? true : false}
                      data-testid="icon-button"
                      aria-label="export-icon"
                      data-type="exportAll"
                      size="large"
                    >
                      <Tooltip
                        title={`Export files currently in scope (${totalResults})`}
                        placement="bottom"
                      >
                        <SaveAlt data-testid="save-alt" />
                      </Tooltip>
                    </IconButton>
                    <IconButton
                      data-test="main-additional-case-option-icon-button"
                      onClick={this.handleMenuClick}
                      className={containerStyles['icon-menu']}
                      disabled={isRootSelected}
                      data-testid="icon-button"
                      size="large"
                    >
                      <Tooltip
                        title="Additional Case options"
                        placement="bottom"
                      >
                        <MoreVertIcon data-testid="more-vert-icon" />
                      </Tooltip>
                    </IconButton>
                    <Menu
                      data-test="menu-case-option"
                      anchorEl={anchorEl}
                      open={open}
                      onClose={this.handleMenuClose}
                      data-testid="menu"
                    >
                      <MenuItem
                        onClick={this.handleRenameFolder}
                        data-test="menu-rename-case-option"
                      >
                        Rename Case
                      </MenuItem>
                      <MenuItem
                        onClick={this.handleMoveFolder}
                        data-test="menu-move-case-option"
                      >
                        Move Case
                      </MenuItem>
                    </Menu>
                  </Fragment>
                ) : (
                  currentTab === TABS.Files &&
                  tdosForExport.length > 0 && (
                    <Fragment>
                      <Typography
                        component="p"
                        className={containerStyles['title-selected']}
                      >
                        Selected {isSelectedAll ? totalResults : selectedCount}{' '}
                        Files
                      </Typography>

                      <IconButton
                        onClick={this.onTagButtonClick}
                        data-test="files-bulk-tag-icon-button"
                        className={containerStyles['icon-menu']}
                        data-testid="icon-button"
                        size="large"
                        disabled={isErrorTDOSelected}
                      >
                        <Tooltip title="Tag selected items" placement="bottom">
                          <Label />
                        </Tooltip>
                      </IconButton>
                      <IconButton
                        onClick={this.onExportButtonClick}
                        data-test="files-bulk-export-icon-button"
                        className={containerStyles['icon-menu']}
                        data-testid="icon-button"
                        data-type={isSelectedAll ? 'exportAll' : 'exportSingle'}
                        size="large"
                        disabled={isErrorTDOSelected}
                      >
                        <Tooltip
                          title={`Export the selected files (${
                            isSelectedAll ? totalResults : tdosForExport.length
                          })`}
                          placement="bottom"
                        >
                          <SaveAlt data-testid="save-alt" />
                        </Tooltip>
                      </IconButton>
                      <IconButton
                        onClick={this.handleReprocess}
                        className={containerStyles['icon-menu']}
                        data-test="reprocess-file-icon-button"
                        data-testid="icon-button"
                        size="large"
                        disabled={isErrorTDOSelected}
                      >
                        <Tooltip title={`Run process`} placement="bottom">
                          <Icon className="icon-enginerunning" />
                        </Tooltip>
                      </IconButton>
                      {moveDeleteFlag && (
                        <>
                          <IconButton
                            onClick={this.onMoveButtonClick}
                            data-test="files-bulk-move-icon-button"
                            className={containerStyles['icon-menu']}
                            data-testid="icon-button"
                            size="large"
                            disabled={isErrorTDOSelected}
                          >
                            <Tooltip
                              title="Move selected items"
                              placement="bottom"
                            >
                              <FolderOpenRoundedIcon />
                            </Tooltip>
                          </IconButton>
                          <IconButton
                            onClick={this.onDeleteButtonClick}
                            data-test="files-bulk-delete-icon-button"
                            className={containerStyles['icon-menu']}
                            data-testid="icon-button"
                            size="large"
                            disabled={isErrorTDOSelected}
                          >
                            <Tooltip
                              title="Delete selected items"
                              placement="bottom"
                            >
                              <DeleteOutlinedIcon />
                            </Tooltip>
                          </IconButton>
                        </>
                      )}
                      <IconButton
                        onClick={this.onRedactButtonClick}
                        data-test="files-send-to-redact-icon-button"
                        className={containerStyles['icon-menu']}
                        data-testid="icon-button"
                        size="large"
                        disabled={isErrorTDOSelected}
                      >
                        <Tooltip title="Send to Redact" placement="bottom">
                          <i className="icon-redaction-app" />
                        </Tooltip>
                      </IconButton>
                      <IconButton
                        onClick={this.handleRunTextAnalytics}
                        className={containerStyles['icon-menu']}
                        data-testid="icon-button"
                        size="large"
                        disabled={isErrorTDOSelected}
                      >
                        <Tooltip
                          title={`Run text analytics`}
                          placement="bottom"
                        >
                          <Icon className="icon-entity" />
                        </Tooltip>
                      </IconButton>
                    </Fragment>
                  )
                )}
              </TabContainer>
            </MaterialTabs>
          </ThemeProvider>
        </StyledEngineProvider>
        <TabComponent />
        {tagDialogOpen && (
          <TagDialog
            open={tagDialogOpen}
            handleClose={this.onTagDialogClose}
            data-veritone-element="tag-dialog"
          />
        )}
        {moveDialogOpen && (
          <MoveDialog
            open={moveDialogOpen}
            onConfirm={this.handleMoveFiles}
            onClose={this.onMoveDialogClose}
            data-veritone-element="move-dialog"
          />
        )}
        {deleteDialogOpen && (
          <DeleteDialog
            open={deleteDialogOpen}
            onConfirm={this.handleDeleteFiles}
            handleClose={this.onDeleteDialogClose}
            data-veritone-element="delete-dialog"
          />
        )}
      </Paper>
    );
  }
}

type Props = PropsFromRedux & {
  classes: ClassNameMap;
};

const mapState = (state: any) => ({
  allRowsSelectedIndexes: getSelectedRows(state),
  totalResults: getTotalResults(state),
  tdosForExport: getTdosForExport(state),
  isSelectedAll: isSelectedAll(state),
  rootFolderId: getRootFolderId(state),
  selectedFolderId: getSelectedFolderId(state),
  isSearchingMedia: isSearchingMedia(state),
  totalExportCount: getTotalCount(state),
  disableAnalytics: getDisableAnalytics(state),
  contactAnalyticsEnabled: getContactAnalyticsStatus(state),
  contactAnalyticsPbiEnabled: getContactAnalyticsPbiStatus(state),
  currentTab: selectCurrentRoutePayload(state).tab,
  isShowMoveFolder: getShowMoveFolder(state),
  moveDeleteFlag: getMoveDeleteFlag(state),
  selectedCount: getSelectedCount(state),
  selectedTdos: getSelectedTdos(state),
});

const mapDispatch = {
  handleShowCreateFolder: (payload: { type: string; caseOptions: string }) =>
    SHOW_FORM_CREATE_FOLDER(payload),
  handleShowMoveFolder: (payload: { type: string; caseOptions: string }) =>
    SHOW_MOVE_FOLDER(payload),
  fetchMoveFolder: () => FETCH_MOVE_FOLDER_REQUEST(),
  handleMoveFiles: () => FETCH_UPDATE_MOVE_FILES_REQUEST(),
  handleDeleteFiles: () => DELETE_FILES_REQUEST(),
  isClickExportAll: () => IS_EXPORT_ALL({ isExportAll: true }),
  handleSendToRedact: (tdoIds: string[]) => SEND_TO_REDACT({ tdoIds }),
  showBulkExport: () => SHOW_BULK_EXPORT(),
  openModalReprocess: openModalReprocess,
  runTextAnalytics: runTextAnalytics,
  onClickFilesTab: onClickFilesTab,
  navigateToTab: (tabName: string) => ROUTE_TABS({ tab: tabName }),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(withStyles(styles)(CenteredTabs));
