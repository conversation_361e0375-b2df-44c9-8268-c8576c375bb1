import React from 'react';
import ListFileUpload from '../listFile';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { truncate } from 'lodash';
describe('ListFileUpload', () => {
  const props = {
    data: [
      {
        key: '1',
        bucket: 'api',
        expiresInSeconds: 86400,
        fileName: 'bloomberg.mp4',
        size: 1740192,
        type: 'video/mp4',
        error: false,
        unsignedUrl: 'unsignedUrl',
        getUrl: 'getUrl',
      },
    ],
    checked: [0],
    handleToggle: jest.fn(),
    checkedAll: true,
  };

  it('renders a ListItem component', () => {
    const { getByTestId } = render(<ListFileUpload {...props} />);
    expect(getByTestId('list-file')).toBeInTheDocument();
  });
  it('renders a Checkbox All component', () => {
    const { getByTestId } = render(<ListFileUpload {...props} />);
    expect(getByTestId('check-box-all')).toBeInTheDocument();
  });
  it('renders text with content 1 file currently selected', () => {
    const { getByText } = render(<ListFileUpload {...props} />);
    expect(getByText('1 file currently selected')).toBeInTheDocument();
  });
  it('renders text with content 1 files', () => {
    const newProps = {
      ...props,
      checked: [],
    };
    const { getByText } = render(<ListFileUpload {...newProps} />);
    expect(getByText('1 files')).toBeInTheDocument();
  });
  it('click handleToggle', () => {
    const { getByTestId } = render(<ListFileUpload {...props} />);
    fireEvent.click(getByTestId('list-file'));
    expect(props.handleToggle).toHaveBeenCalled();
  });
});
