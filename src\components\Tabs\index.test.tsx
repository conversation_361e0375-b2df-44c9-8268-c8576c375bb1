import { CenteredTabs } from './index';
import configureStore from 'redux-mock-store';
import * as Redux from 'redux';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';

describe(CenteredTabs, () => {
  const props = {
    onSunburstAggregation: jest.fn(),
    onWordCloudAggregation: jest.fn(),
    totalDuration: 'totalDuration',
    isDashboardLoading: false,
    allRowsSelectedIndexes: [0, 1],
    totalResults: 100,
    isSelectedAll: false,
    classes: {},
    handleShowCreateFolder: jest.fn(),
    handleShowMoveFolder: jest.fn(),
    fetchMoveFolder: jest.fn,
    selectedFolderId: 'selectedFolderId',
    handleSendToRedact: jest.fn(),
    tdosForExport: ['600003190', '600003191'],
    rootFolderId: 'rootFolderId',
    isClickExportAll: jest.fn(),
    disableAnalytics: false,
    currentTab: 'analytics',
    moveDeleteFlag: true,
    selectedTdos: {},
  };

  const middlewares: Redux.Middleware[] = [];
  const mockStore = configureStore(middlewares);
  const initialState = {
    dashboard: {
      durPerEngineClass: [],
      totalMediaProcessedTime: '0',
      totalDuration: '0',
      fetchingMediaAggregations: false,
    },
    search: {
      totalResults: 0,
      sortQuery: [],
      searchParameters: [],
      searchResultTdos: [],
    },
    sunburst: {
      analyticsData: {},
      fetchingAggregations: false,
    },
    filters: {
      entityType: '',
    },
    wordcloud: {
      isWordcloudRedrawn: false,
      fetchingWordcloudAggregation: false,
      colouredWords: {},
      wordsPerColour: {},
    },
    tdosTable: {
      isSelectedAll: false,
      illuminateAppFolders: [],
    },
    config: {
      redactUrl: '',
    },
    user: {
      user: {
        organization: {
          kvp: {
            features: {
              illuminate: {
                enableRedactNavigation: 'enable',
              },
            },
          },
        },
      },
    },
  };
  const store = mockStore(initialState);
  it('renders a CardContent', () => {
    const { getAllByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    expect(getAllByTestId('tab')).toHaveLength(4);
  });

  it('renders 2 IconButton', () => {
    const { getAllByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    expect(getAllByTestId('icon-button')).toHaveLength(2);
  });
  it('renders a MoreVertIcon', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    expect(getByTestId('more-vert-icon')).toBeInTheDocument();
  });

  it('renders a Tooltip', async () => {
    const { getByTestId, findByText } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    fireEvent.mouseOver(getByTestId('save-alt'));
    expect(
      await findByText('Export files currently in scope (100)')
    ).toBeInTheDocument();
  });
  it('does not render a Label when currentTab is analytics', () => {
    const { queryByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    expect(queryByTestId('label')).not.toBeInTheDocument();
  });
  it('does not render a TdosTable when currentTab is analytics', () => {
    const { queryByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(props as any)} />
      </Provider>
    );
    expect(queryByTestId('tdos-table')).not.toBeInTheDocument();
  });

  it('when set props currentTab', async () => {
    const newProps = {
      ...props,
      currentTab: 'files',
    };
    const { getByTestId, findByText, getAllByTestId } = render(
      <Provider store={store}>
        <CenteredTabs {...(newProps as any)} />
      </Provider>
    );
    expect(getByTestId('save-alt')).toBeInTheDocument();
    expect(getAllByTestId('icon-button')).toHaveLength(7);
    fireEvent.mouseOver(getByTestId('save-alt'));
    expect(
      await findByText('Export the selected files (2)')
    ).toBeInTheDocument();
  });
});
