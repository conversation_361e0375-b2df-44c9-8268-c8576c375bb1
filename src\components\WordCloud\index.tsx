import React, { Fragment } from 'react';
import wordcloud from 'wordcloud';
import * as styles from './styles.scss';
import { connect, ConnectedProps } from 'react-redux';
import {
  getColouredWords,
  getWordsPerColour,
  ON_SELECTED_TOPIC_CHANGE,
} from '../../state/modules/wordcloud';
import {
  getSelectedTopics,
  UPDATE_TOPICS_FILTERS,
} from '../../state/modules/filters';
import { FETCH_MEDIA_AGGREGATIONS } from '../../state/modules/dashboard';
import { FETCH_SUNBURST_AGGREGATIONS } from '../../state/modules/sunburst';
import { setSelection } from '../../state/modules/tdosTable';
import { WordsPerColourTuple } from '../../state/modules/wordcloud/models';
export class WordCloud extends React.Component<Props> {
  componentDidMount() {
    this.createWordCloud();
  }

  componentDidUpdate(prevProps: {
    colouredWords: { [key: string]: string[] };
    sizeCloud: { w: number; h: number };
    containerWidth: number;
    windowSize: number;
  }) {
    const { colouredWords, sizeCloud, containerWidth, windowSize } = this.props;
    if (
      prevProps.colouredWords !== colouredWords ||
      prevProps.sizeCloud.h !== sizeCloud.h ||
      prevProps.containerWidth !== containerWidth ||
      prevProps.windowSize !== windowSize
    ) {
      this.createWordCloud();
    }
  }

  createWordCloud = () => {
    const {
      colouredWords,
      wordsPerColour,
      onWordClick,
      selectedTopics,
      updateTopicsFilter,
      mediaAggregations,
      containerWidth,
      sunburstAggregations,
      updateSelection,
    } = this.props;
    const list: WordsPerColourTuple[] = [];
    const colors = Object.keys(colouredWords);
    colors.map((item) => {
      const words = wordsPerColour[item];
      if (words) {
        list.push(...words);
      }
    });
    const options = {
      list,
      gridSize: Math.round((11 * 1230) / 1024),
      weightFactor: function (size: number) {
        return Math.round((Math.pow(size, 0.4) * containerWidth * 30) / 1024);
      },
      fontFamily: 'Roboto, serif',
      color: function (word: string) {
        const color =
          colors.filter((item) => {
            if (colouredWords[item]?.includes(word)) {
              return item;
            }
          })?.[0] ?? '';
        return color;
      },
      minRotation: 0,
      maxRotation: 0,
      rotationSteps: 2,
      shuffle: false,
      classes: function (word: string) {
        return `${word}`;
      },
      click: function (item: string[]) {
        const topic = item?.[0];
        if (!topic) {
          return;
        }
        let updateSelectTopics = [];

        if (selectedTopics.includes(topic)) {
          updateSelectTopics = selectedTopics.filter((t) => t !== topic);
        } else {
          updateSelectTopics = [...selectedTopics, topic];
        }
        updateSelection([], false);
        updateTopicsFilter(updateSelectTopics);
        onWordClick(updateSelectTopics);
        mediaAggregations();
        sunburstAggregations();
      },
      hover: function (item: WordCloud.ListEntry) {
        if (item) {
          const el = item[0];
          const elements = document.getElementsByClassName(el);
          if (elements[0] instanceof HTMLElement) {
            elements[0].style.cursor = 'pointer';
          }
        }
      },
    };
    const wordcloudEl = document.querySelector<HTMLElement>('.wordcloud');
    if (wordcloudEl) {
      wordcloud(wordcloudEl, options);
    }
  };

  render() {
    const { cloudHeight } = this.props;
    return (
      <Fragment>
        <div className={styles['wordcloud-title']}>Word Cloud</div>
        <div
          className="wordcloud"
          style={{
            width: '80%',
            height: `${cloudHeight * 0.9}px`,
            margin: '0 auto',
            marginBottom: '10px',
          }}
          data-testid="wordcloud"
        />
      </Fragment>
    );
  }
}
type Props = PropsFromRedux & {
  cloudHeight: number;
  sizeCloud: {
    w: number;
    h: number;
  };
  containerWidth: number;
  windowSize: number;
};

const mapState = (state: any) => ({
  colouredWords: getColouredWords(state),
  wordsPerColour: getWordsPerColour(state),
  selectedTopics: getSelectedTopics(state),
});

const mapDispatch = {
  onWordClick: (wordArray: string[]) => ON_SELECTED_TOPIC_CHANGE(wordArray),

  updateTopicsFilter: (selectedTopics: string[]) =>
    UPDATE_TOPICS_FILTERS({ selectedTopics }),
  sunburstAggregations: () => FETCH_SUNBURST_AGGREGATIONS(),
  mediaAggregations: () => FETCH_MEDIA_AGGREGATIONS(),
  updateSelection: (
    selectedRows: Array<string | number>,
    isSelectedAll: boolean
  ) => setSelection(selectedRows, isSelectedAll),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(WordCloud);
