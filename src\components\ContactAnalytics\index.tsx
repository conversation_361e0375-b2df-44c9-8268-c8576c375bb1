import { useState, useEffect } from 'react';
import { connect } from 'react-redux';
// @ts-expect-error TODO: Fix
import $ from 'jquery';
import moment from 'moment';
import { DateTime } from 'luxon';
import shortId from 'shortid';
import throttle from 'lodash.throttle';
import ListIcon from '@mui/icons-material/List';
import InsertChartIcon from '@mui/icons-material/InsertChartOutlined';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import Delete from '@mui/icons-material/DeleteOutline';
import Clear from '@mui/icons-material/Clear';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import CreateNewFolderOutlinedIcon from '@mui/icons-material/CreateNewFolderOutlined';
import MenuItem from '@mui/material/MenuItem';
import CircularProgress from '@mui/material/CircularProgress';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Tooltip from '@mui/material/Tooltip';
import RadioGroup from '@mui/material/RadioGroup';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';
import AddChartIcon from './icons/AddChart';
import IosShare from './icons/IosShare';
import Export from './icons/Export';
import ReorderCharts from './ReorderCharts';
import ChartSettings from './ChartSettings';
import * as styles from './styles.scss';
import chartDefinitions from './chartDefinitions';
import exportAllAsPDF from './exportAllAsPDF';
import {
  selectChartData,
  selectDateRange,
  selectQueriesPending,
  selectUseLegend,
  selectUseTitle,
  selectLineWidth,
  selectCharts,
  selectReports,
  selectCurrentReportIndex,
  selectLoadingUserConfig,
  selectAggregationSize,
} from './worker/reducer';
import { selectSchemaIdContactAnalyticsSDO } from 'state/modules/contactAnalytics';
import { workerConnect } from './worker/workerConnect';
import 'moment-timezone';
import 'daterangepicker';
import './amchartsInit';

import * as workerActions from './worker/actions';

const TZ = 'America/Los_Angeles';

function toTitleCase(str: string) {
  return str.replace(/\w\S*/g, function (txt) {
    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
  });
}

const ContactAnalytics = ({
  getChartData,
  setAnalyticsDateRange,
  removeChartData,
  chartData,
  lowerDateBound,
  upperDateBound,
  queriesPending,
  useLegend,
  useTitle,
  lineWidth,
  setCharts,
  charts,
  fetchChartUserConfig,
  currentReportIndex,
  reports,
  addReport,
  setCurrentReport,
  removeReport,
  setReportName,
  loadingUserConfig,
  orgName,
  schemaIdContactAnalyticsSDO,
  applicationId,
  setVars,
  config,
  aggregationSize,
}: ContactAnalyticsProps) => {
  const [fetchChartDataInProgress, setFetchChartDataInProgress] = useState<
    string[]
  >([]);
  const [showLoadingOverlay, setShowLoadingOverlay] = useState(false);
  const [showExportingOverlay, setShowExportingOverlay] = useState(false);
  const [chartInstances, setChartInstances] = useState<
    Partial<Record<string, any>>
  >({});
  const [reorderChartsDialogOpen, setReorderChartsDialogOpen] = useState(false);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [editReportNameIndex, setEditReportNameIndex] = useState(-1);

  const hasZeroReports = reports.length === 0;
  const isLoading =
    reports.reduce((acc, r) => acc + r.queriesPending, 0) > 0 ||
    loadingUserConfig;
  const configureChart = (
    containerId: string,
    chartDef: ChartDef,
    data: object
  ) => {
    try {
      const configChart = chartDefinitions.find(
        (c) => c.name === chartDef.name
      )?.configure;
      if (chartDef) {
        const chart = charts.find((c) => c.containerId === containerId);
        const name =
          chartDef.name &&
          `${chartDef.name.toLowerCase().replace(' ', '-')}-${DateTime.fromISO(
            lowerDateBound,
            {
              zone: TZ,
            }
          ).toFormat('mm_dd_YYYY')}-${DateTime.fromISO(upperDateBound, {
            zone: TZ,
          }).toFormat('mm_dd_YYYY')}`;

        return (
          configChart &&
          chartDef.name &&
          configChart(
            containerId,
            data,
            name ?? '',
            toTitleCase(chartDef.name),
            {
              filter: chart?.filter,
              useFilter: chart?.useFilter,
              filterTextAll: chart?.chartDef?.filterTextAll,
              filterTerms: Array.isArray(chart?.chartDef?.filterTerms)
                ? chart?.chartDef?.filterTerms
                : Object.keys(chartDef?.filterTerms ?? {}),
              useLegend,
              useTitle,
              lineWidth,
              lowerDateBound,
              upperDateBound,
              aggregationSize,
              TZ,
            }
          )
        );
      } else {
        console.error(`No chart definition for '${containerId}'`);
      }
    } catch (e) {
      console.error(`Error configuring chart '${containerId}'`, e);
      // Remove the chart if something goes wrong while configuring
      removeChart(containerId)();
    }
  };

  const cleanupCharts = () => {
    Object.keys(chartInstances).forEach((cId) => {
      // setHasRendered({ containerId: cId, hasRendered: false });
      chartInstances[cId]?.dispose();
    });
    setChartInstances({});
  };

  const handleExportAll = () => {
    const title = `Contact Analytics (${DateTime.fromISO(
      lowerDateBound
    ).toFormat('M.d.yyyy')} - ${DateTime.fromISO(upperDateBound).toFormat(
      'M.d.yyyy'
    )})`;

    exportAllAsPDF(
      title,
      chartInstances,
      orgName,
      charts,
      setShowExportingOverlay
    );
  };

  const removeChart = (containerId: string) => () => {
    const newCharts = charts.map((c) => ({ ...c }));
    const newChartInstances = { ...chartInstances };
    chartInstances[containerId]?.dispose();
    delete newChartInstances[containerId];
    newCharts.splice(
      newCharts.findIndex((nc) => nc.containerId === containerId),
      1
    );
    setCharts(newCharts);
    setChartInstances(newChartInstances);
    removeChartData(containerId);
  };

  const changeChartType = (type: string, containerId: string) => {
    const key = shortId.generate();
    const newCharts = charts.map((c) => ({ ...c }));
    const newChartInstances = { ...chartInstances };
    const chartIndex = newCharts.findIndex(
      (nc) => nc.containerId === containerId
    );
    chartInstances[containerId]?.dispose();
    delete newChartInstances[containerId];
    const chartDef = chartDefinitions.find((cd) => cd.name === type);

    const chart = newCharts[chartIndex];
    if (chart) {
      chart.type = type;
      chart.useFilter = 'all';
      chart.chartDef = chartDef;

      chart.filter = Array.isArray(chartDef?.filterTerms)
        ? (chartDef?.filterTerms?.[0] ?? 'error')
        : (Object.keys(chartDef?.filterTerms ?? {})[0] ?? 'error');
      chart.containerId = `chart-section-chart-${key}`;
      chart.key = key;
    }
    removeChartData(containerId);
    setChartInstances(newChartInstances);
    setCharts(newCharts);
  };

  const addChart = throttle(
    () => {
      const remainingCharts = chartDefinitions.filter(
        (cd) => !charts.find((c) => c.type === cd.name)
      );
      const newChartType =
        remainingCharts.length > 0
          ? remainingCharts[0]?.name
          : chartDefinitions[0]?.name;
      const newCharts = charts.map((c) => ({ ...c }));
      const key = shortId.generate();
      const containerId = `chart-section-chart-${key}`;
      const chartDef = chartDefinitions.find((cd) => cd.name === newChartType);
      newCharts.splice(0, 0, {
        containerId,
        type: newChartType,
        useFilter: 'all',
        filter: Array.isArray(chartDef?.filterTerms)
          ? (chartDef?.filterTerms?.[0] ?? 'error')
          : (Object.keys(chartDef?.filterTerms ?? {})[0] ?? 'error'),
        chartDef,
        key,
      });
      setCharts(newCharts);
    },
    200,
    { trailing: false, leading: true }
  );

  const setReport = (reportIndex: number) => () => {
    Object.keys(chartInstances).forEach((cId) => {
      chartInstances[cId]?.dispose();
    });
    setChartInstances({});
    setCurrentReport({ currentReportIndex: reportIndex });
  };

  const removeThisReport = (reportIndex: number) => () => {
    removeReport({ reportIndex });
  };

  const onReportNameChange =
    (reportIndex: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setReportName({ name: e.target.value, reportIndex });
    };

  const onReportNameClick =
    (reportIndex: number) => (_e: React.MouseEvent<HTMLSpanElement>) => {
      setEditReportNameIndex(reportIndex);
    };

  const onReportNameInputBlur = () => {
    setEditReportNameIndex(-1);
  };

  const onReportNameKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setEditReportNameIndex(-1);
    }
  };

  const onAddReport = () => {
    addReport();
    setEditReportNameIndex(reports.length);
  };

  const setUseFilter = (useFilter: string, containerId: string) => {
    const key = shortId.generate();
    const newCharts = charts.map((c) => ({ ...c }));
    const newChartInstances = { ...chartInstances };
    const chartIndex = newCharts.findIndex(
      (nc) => nc.containerId === containerId
    );
    chartInstances[containerId]?.dispose();
    delete newChartInstances[containerId];

    const chart = newCharts[chartIndex];
    if (chart) {
      chart.useFilter = useFilter;
      chart.containerId = `chart-section-chart-${key}`;
      chart.key = key;
    }
    removeChartData(containerId);
    setChartInstances(newChartInstances);
    setCharts(newCharts);
  };

  const changeFilterType = (filterTerm: string, containerId: string) => {
    const key = shortId.generate();
    const newCharts = charts.map((c) => ({ ...c }));
    const newChartInstances = { ...chartInstances };
    const chartIndex = newCharts.findIndex(
      (nc) => nc.containerId === containerId
    );
    chartInstances[containerId]?.dispose();
    delete newChartInstances[containerId];

    const chart = newCharts[chartIndex];
    if (chart) {
      chart.filter = filterTerm;
      chart.containerId = `chart-section-chart-${key}`;
      chart.key = key;
    }
    removeChartData(containerId);
    setChartInstances(newChartInstances);
    setCharts(newCharts);
  };

  useEffect(() => {
    const fetchingCharts = [...fetchChartDataInProgress];
    charts.forEach((chart) => {
      if (!fetchingCharts.includes(chart.containerId)) {
        if (!chartData?.[chart.containerId]?.data) {
          getChartData({
            containerId: chart.containerId,
            chartDef: chart.chartDef,
          });
          fetchingCharts.push(chart.containerId);
        }
      }
    });
    setFetchChartDataInProgress(fetchingCharts);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [charts]);

  useEffect(() => {
    const newChartInstances = { ...chartInstances };
    let modifyInstances = false;

    if (queriesPending === 0) {
      Object.entries(chartData).forEach((kvp) => {
        const [containerId, chart] = kvp;
        if (chart.hasAllData && chart.chartDef) {
          if (!chartInstances[containerId]) {
            modifyInstances = true;
            const chartInstance = configureChart(
              containerId,
              chart.chartDef,
              chart.data
            );
            modifyInstances = true;
            newChartInstances[containerId] = chartInstance;
          }
        }
      });
      if (modifyInstances) {
        setChartInstances(newChartInstances);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chartData, chartInstances, queriesPending]);

  useEffect(() => {
    $('#contact-analytics-date-range').daterangepicker(
      {
        startDate: moment(lowerDateBound).tz(TZ),
        endDate: moment(upperDateBound).tz(TZ),
        ranges: {
          Today: [moment().tz(TZ), moment().tz(TZ)],
          Yesterday: [
            moment().tz(TZ).subtract(1, 'days'),
            moment().tz(TZ).subtract(1, 'days'),
          ],
          'Last 7 Days': [moment().tz(TZ).subtract(6, 'days'), moment().tz(TZ)],
          'Last 30 Days': [
            moment().tz(TZ).subtract(29, 'days'),
            moment().tz(TZ),
          ],
          'This Month': [
            moment().tz(TZ).startOf('month'),
            moment().tz(TZ).endOf('month'),
          ],
          'This Year': [
            moment().tz(TZ).startOf('year'),
            moment().tz(TZ).endOf('year'),
          ],
        },
      },
      (start: Date, end: Date) => {
        console.log('start', start);
        console.log('end', end);

        setAnalyticsDateRange({
          lowerDateBound: DateTime.fromISO(start.toISOString(), { zone: TZ })
            .set({ hour: 0, second: 0, minute: 0, millisecond: 0 })
            .toISO() as string,
          upperDateBound: DateTime.fromISO(end.toISOString(), { zone: TZ })
            .set({ hour: 23, second: 59, minute: 59, millisecond: 999 })
            .toISO() as string,
        });
        setTimeout(() => cleanupCharts(), 100);
      }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lowerDateBound, upperDateBound, reports]);

  useEffect(() => {
    if (queriesPending > 0) {
      setShowLoadingOverlay(true);
    }
    if (queriesPending === 0) {
      setShowLoadingOverlay(false);
    }
  }, [queriesPending]);

  useEffect(() => {
    if (schemaIdContactAnalyticsSDO) {
      setVars(schemaIdContactAnalyticsSDO, orgName, applicationId, config);
      fetchChartUserConfig();
      return () => {
        cleanupCharts();
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schemaIdContactAnalyticsSDO]);

  useEffect(() => {
    cleanupCharts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentReportIndex]);

  return (
    <div className={styles['contact-analytics']}>
      <ReorderCharts
        open={reorderChartsDialogOpen}
        charts={charts}
        onClose={() => setReorderChartsDialogOpen(false)}
        onConfirm={(newCharts: Chart[]) => {
          setReorderChartsDialogOpen(false);
          const charts = newCharts.map((_: Chart, i: number) =>
            newCharts.find((nc: Chart) => nc.order === i)
          );
          if (charts) {
            setCharts(charts);
          }
        }}
      />
      <ChartSettings
        open={settingsDialogOpen}
        onClose={() => setSettingsDialogOpen(false)}
        cleanupCharts={cleanupCharts}
      />
      <div
        className={`${styles['contact-analytics-report-tab-container']} ${
          isLoading ? styles['loading'] : ''
        } ${editReportNameIndex !== -1 ? styles['editing'] : ''}`}
      >
        {reports.map((report, idx) => (
          <div
            className={styles['contact-analytics-report-tab']}
            data-active={idx === currentReportIndex}
            onClick={setReport(idx)}
            key={`ContactReportTab-${report.id}`}
          >
            {editReportNameIndex !== idx && (
              <Tooltip
                title={report.name}
                enterDelay={1000}
                classes={{
                  tooltip: styles['contact-analytics-report-tab-tooltip'],
                }}
              >
                <span onDoubleClick={onReportNameClick(idx)}>
                  {report.name}
                </span>
              </Tooltip>
            )}
            {editReportNameIndex === idx && (
              <ClickAwayListener onClickAway={onReportNameInputBlur}>
                <input
                  autoFocus
                  className={styles['contact-analytics-report-tab-input']}
                  value={report.name}
                  onChange={onReportNameChange(idx)}
                  onBlur={onReportNameInputBlur}
                  onKeyUp={onReportNameKeyUp}
                />
              </ClickAwayListener>
            )}
            {editReportNameIndex !== idx && (
              <Clear
                className={styles['contact-analytics-report-tab-delete']}
                onClick={removeThisReport(idx)}
              />
            )}
          </div>
        ))}
        {reports.length > 0 && (
          <div
            className={styles['contact-analytics-report-tab-add']}
            onClick={onAddReport}
          >
            +
          </div>
        )}
      </div>
      {hasZeroReports && (
        <div className={styles['contact-analytics-add-report-prompt']}>
          <div>
            <p>No Reports Found</p>
            <Button
              className={styles['contact-analytics-add-report-prompt-button']}
              color="primary"
              onClick={onAddReport}
            >
              <CreateNewFolderOutlinedIcon />
              Add your first report
            </Button>
          </div>
        </div>
      )}
      {(showLoadingOverlay || loadingUserConfig) && (
        <div className={styles['contact-analytics-charts-loading-overlay']}>
          <div>
            <p>
              {loadingUserConfig
                ? 'Retrieving User Config'
                : 'Retrieving Chart Data'}{' '}
            </p>
            <CircularProgress size={50} />
            {!loadingUserConfig && (
              <p>{`${queriesPending} Datapoints Remaining`}</p>
            )}
          </div>
        </div>
      )}
      {showExportingOverlay && (
        <div className={styles['contact-analytics-charts-loading-overlay']}>
          <div>
            <p>Generating Document...</p>
            <CircularProgress size={50} />
          </div>
        </div>
      )}
      <div className={styles['filter-bar']}>
        {!hasZeroReports && (
          <div className={styles['filter-bar-left']}>
            <div className={styles['date-range']}>
              <input
                id="contact-analytics-date-range"
                className={styles['date-range-input']}
              />
              <CalendarTodayIcon className={styles['date-range-icon']} />
            </div>
            <Button
              className={styles['settings-dialog-button']}
              color="primary"
              onClick={() => setSettingsDialogOpen(true)}
            >
              <InsertChartIcon />
              Chart Settings
            </Button>
            {charts.length > 0 && (
              <Button
                className={`${styles['export-all-button']} ${
                  isLoading ? styles['export-all-button-disabled'] : ''
                } `}
                disabled={isLoading}
                color="primary"
                onClick={handleExportAll}
              >
                <IosShare />
                Export All
              </Button>
            )}
            {charts.length > 1 && (
              <Button
                className={styles['reorder-button']}
                color="primary"
                onClick={() => setReorderChartsDialogOpen(true)}
                disabled={isLoading}
              >
                <ListIcon />
                Re-Order Charts
              </Button>
            )}
            <Button
              className={styles['add-report-type-button']}
              color="primary"
              onClick={addChart}
              disabled={isLoading}
            >
              <AddChartIcon />
              Add Chart
            </Button>
          </div>
        )}
      </div>
      <div className={styles['chart-area-container']}>
        {charts.map((chart) => (
          <div
            className={styles['chart-section']}
            id={`ContactAnalyticsChart-${chart?.key}`}
            key={`ContactAnalyticsChart-${chart?.key}`}
            style={chart?.chartDef?.containerStyles}
          >
            <div className={styles['chart-section-controls']}>
              <FormControl
                classes={{ root: styles['chart-section-select-chart-type'] }}
              >
                <Select
                  value={chart?.type}
                  onChange={(e) =>
                    changeChartType(e.target.value, chart.containerId)
                  }
                >
                  {chartDefinitions.map((cd, _i) => (
                    <MenuItem
                      key={`CharDefMenuItem-${cd?.name}`}
                      value={cd?.name}
                    >
                      {cd?.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              {chart?.chartDef?.hasFilters && (
                <div className={styles['chart-section-filters-container']}>
                  <RadioGroup
                    name="filtering"
                    value={chart.useFilter}
                    onChange={(e) =>
                      setUseFilter(e.target.value, chart.containerId)
                    }
                  >
                    <FormControlLabel
                      value="all"
                      control={<Radio />}
                      label={chart.chartDef.filterTextAll}
                    />
                    <FormControlLabel
                      value="filter"
                      control={<Radio />}
                      label={chart.chartDef.filterTextType}
                    />
                  </RadioGroup>
                  {chart.chartDef.hasFilters && (
                    <FormControl variant="outlined">
                      <Select
                        disabled={chart.useFilter === 'all'}
                        value={chart.filter}
                        onChange={(e) =>
                          changeFilterType(e.target.value, chart.containerId)
                        }
                      >
                        {chart?.chartDef?.filterTerms &&
                          (!Array.isArray(chart.chartDef.filterTerms)
                            ? Object.keys(chart?.chartDef?.filterTerms)
                            : chart.chartDef.filterTerms
                          ).map((ft: any, _fti: number) => (
                            <MenuItem
                              key={`ContactAnalyticsChart-${chart.key}-FilterTerms-${ft}`}
                              value={ft}
                            >
                              {ft}
                            </MenuItem>
                          ))}
                      </Select>
                    </FormControl>
                  )}
                  {/* <div onClick={copyChart(chart.containerId)}>
                    <Copy
                      className={styles['chart-section-copy-button']}
                    />
                  </div> */}
                </div>
              )}
              <div className={styles['chart-section-buttons-container']}>
                <div
                  className={styles['chart-section-export-button']}
                  id={`chart-section-export-button-${chart.containerId}`}
                  color="primary"
                >
                  <Export />
                </div>
                <div onClick={removeChart(chart.containerId)}>
                  <Delete className={styles['chart-section-remove-button']} />
                </div>
              </div>
            </div>
            <div className={styles['chart-area']}>
              <div
                id={chart.containerId}
                className={`${styles['chart-section-chart']} ${
                  chart?.chartDef?.hasDemographics
                    ? styles['has-demographics']
                    : null
                }`}
              />
              {chart?.chartDef?.hasDemographics && (
                <>
                  <div
                    className={styles['chart-section-demographics-divider']}
                  />
                  <div
                    id={`${chart.containerId}-demographics`}
                    className={styles['chart-section-demographics']}
                  />
                </>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface ChartDef {
  containerStyles?: object;
  dataQueries?: {
    dataKey: string;
    isAggregation: boolean;
    query: string;
    storageKey: string;
  }[];
  demographicsConfig?: {
    dataQuery: string;
  };
  filterTerms?: Record<string, string> | string[];
  filterTextAll?: string;
  filterTextType?: string;
  filterType?: string;
  hasDemographics?: boolean;
  hasFilters?: boolean;
  name?: string;
  type?: string;
}

interface ChartData {
  hasAllData: boolean;
  hasFailed: boolean;
  hasRendered: boolean;
  chartDef?: ChartDef;
  data: object;
}

export interface Chart {
  containerId: string;
  filter?: string;
  key?: string;
  type?: string;
  useFilter?: string;
  chartDef?: ChartDef;
  order?: number;
}

interface Report {
  aggregationSize: string;
  chartData: Record<string, ChartData>;
  charts: Chart[];
  dateRange: {
    lowerDateBound: string;
    upperDateBound: string;
  };
  id: string;
  lineWidth: number;
  name: string;
  queriesPending: number;
  useLegend: boolean;
  useTitle: boolean;
}

interface ContactAnalyticsProps {
  chartData: Record<string, ChartData>;
  setAnalyticsDateRange: ({
    lowerDateBound,
    upperDateBound,
  }: {
    lowerDateBound: string;
    upperDateBound: string;
  }) => void;
  getChartData: ({
    containerId,
    chartDef,
  }: {
    containerId: string;
    chartDef?: ChartDef;
  }) => void;
  removeChartData: (containerId: string) => void;
  setHasRendered: ({
    containerId,
    hasRendered,
  }: {
    containerId: string;
    hasRendered: boolean;
  }) => void;
  lowerDateBound: string;
  upperDateBound: string;
  queriesPending: number;
  useLegend: boolean;
  useTitle: boolean;
  lineWidth: number;
  charts: Chart[];
  setCharts: (charts: (Chart | undefined)[]) => void;
  fetchChartUserConfig: () => void;
  addReport: () => void;
  removeReport: ({ reportIndex }: { reportIndex: number }) => void;
  currentReportIndex: number;
  reports: Report[];
  setReportName: ({
    reportIndex,
    name,
  }: {
    reportIndex: number;
    name: string;
  }) => void;
  loadingUserConfig: boolean;
  setCurrentReport: ({
    currentReportIndex,
  }: {
    currentReportIndex: number;
  }) => void;
  orgName: string;
  applicationId: string;
  schemaIdContactAnalyticsSDO: string;
  setVars: (
    schemaIdContactAnalyticsSDO: string,
    orgName: string,
    applicationId: string,
    config: object
  ) => void;
  config: object;
  requeryData: () => void;
  worker: Worker;
  aggregationSize: string;
}

const mapStateToProps = (state: any) => ({
  schemaIdContactAnalyticsSDO: selectSchemaIdContactAnalyticsSDO(state),
  orgName: state.user.user.organization.organizationName,
  applicationId: state.config.veritoneAppId,
  config: {
    config: state.config,
    auth: state.auth,
  },
});

const mapDispatchToProps = {};

const workerMapStateToProps = (state: any) => ({
  chartData: selectChartData(state),
  lowerDateBound: selectDateRange(state).lowerDateBound,
  upperDateBound: selectDateRange(state).upperDateBound,
  queriesPending: selectQueriesPending(state),
  useLegend: selectUseLegend(state),
  useTitle: selectUseTitle(state),
  lineWidth: selectLineWidth(state),
  charts: selectCharts(state),
  currentReportIndex: selectCurrentReportIndex(state),
  reports: selectReports(state),
  loadingUserConfig: selectLoadingUserConfig(state),
  aggregationSize: selectAggregationSize(state),
});

const workerMapDispatchToProps = {
  ...workerActions,
};

export default workerConnect(
  workerMapStateToProps,
  workerMapDispatchToProps
)(connect(mapStateToProps, mapDispatchToProps)(ContactAnalytics));
