import { TopBar } from './index';
import { render, fireEvent } from '@testing-library/react';

describe(TopBar, () => {
  const props = {
    elevation: 10,
    leftOffset: 10,
    selected: false,
    selectedFolderId: 'selectedFolderId',
    selectedFolderIds: [{ id: 'string', level: 1 }],
    folders: {},
    filters: {
      entityType: 'entityType',
      date: {
        startDate: 'startDate',
        endDate: 'endDate',
      },
      fileTypes: {
        video: [],
        audio: [],
        image: [],
        doc: [],
      },
      selectedTopics: [],
      entityNames: ['PERSON', 'ORGANIZATION'],
      topics: ['science and technology', 'unrest, conflicts and war'],
      fileNames: [],
      ids: [],
      enginesRun: [],
      duration: {
        hoursGt: 0,
        minutesGt: 0,
        secondsGt: 0,
        hoursLte: 0,
        minutesLte: 0,
        secondsLte: 0,
      },
    },
    selectedEntity: [],
    searchByFileNames: [],
    searchByIds: [],
    searchByEnginesRun: [
      {
        id: '123',
        name: 'name',
      },
    ],
    searchByDuration: {
      hoursGt: 10,
      minutesGt: 10,
      secondsGt: 10,
      hoursLte: 10,
      minutesLte: 10,
      secondsLte: 10,
    },
    handleShowFolder: jest.fn(),
    navigateCurrentTab: jest.fn(),
    isFetchSubFolder: false,
    fetchingFolderRoot: false,
    fetchSubFolder: jest.fn(),
    isExportModalOpened: false,
    signedUris: [
      {
        id: '123',
        name: 'name',
        uri: '',
        signedUri: '',
        totalBatch: 1,
        numberBatch: 1,
      },
    ],
    modalName: 'string',
    onCloseModal: jest.fn(),
    openModalUpload: jest.fn(),
  };

  it('renders 3 buttons', () => {
    const { getAllByTestId } = render(<TopBar {...props} />);
    expect(getAllByTestId('topbar-button')).toHaveLength(3);
  });

  it('should handle click event', () => {
    const { getByRole } = render(<TopBar {...props} />);
    fireEvent.click(getByRole('button', { name: /folder-button/i }));
    expect(props.handleShowFolder).toHaveBeenCalled();
  });
});
