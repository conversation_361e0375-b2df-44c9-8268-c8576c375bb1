import React, { Component } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import {
  Button,
  Collapse,
  List,
  ListItem,
  ListItemText,
  Tooltip,
} from '@mui/material';
import ListItemIcon from '@mui/material/ListItemIcon';
import FolderIcon from '@mui/icons-material/Folder';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import WorkIcon from '@mui/icons-material/Work';
import ArrowRight from 'resources/images/baseline_arrow_right_black_18dp.png';
import ArrowDown from 'resources/images/baseline_arrow_drop_down_black_18dp.png';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import * as styles from './styles.scss';
import classNames from 'classnames/bind';
import {
  SET_SELECTED_FOLDER_ID,
  SHOW_FORM_CREATE_FOLDER,
  SHOW_MOVE_FOLDER,
  FETCH_SUB_FOLDERS_REQUEST,
  HANDLE_EXPANDED,
  FETCH_MOVE_FOLDER_REQUEST,
  SET_SELECTED_MOVE_FOLDER_ID,
  FETCH_MOVE_SUB_FOLDERS_REQUEST,
  HANDLE_MOVE_EXPANDED,
} from '../../state/modules/folders';
import { setOpenFolderUpload } from '../../state/modules/uploadFile/actions';
import { Folder as FolderProps, Folders } from '../../model';
const cx = classNames.bind(styles);
export class Folder extends Component<Props> {
  state = {
    anchorEl: null,
  };

  get isSelected() {
    const { selectedFolderId, folder } = this.props;
    return selectedFolderId === folder.id;
  }
  handleExpandClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const {
      fetchSubFolder,
      setStatusExpanded,
      folder,
      inSidebar,
      fetchMoveSubFolder,
      setStatusMoveExpanded,
    } = this.props;
    if (!folder.expanded) {
      if (inSidebar) {
        fetchSubFolder(folder.id, !folder.expanded);
      } else {
        fetchMoveSubFolder(folder.id, !folder.expanded);
      }
    } else {
      if (inSidebar) {
        setStatusExpanded(folder.id, !folder.expanded);
      } else {
        setStatusMoveExpanded(folder.id, !folder.expanded);
      }
    }
  };
  handleListItemClick = (event: React.MouseEvent<HTMLElement>) => {
    const {
      inSidebar,
      setSelectedFolderId,
      setSelectedMoveFolderId,
      type,
      setOpenFolderUpload,
    } = this.props;
    const folderId = event.currentTarget.getAttribute('data-id') ?? '';
    if (type === 'uploadFile') {
      if (setOpenFolderUpload) {
        setOpenFolderUpload(folderId);
      }
    } else if (inSidebar) {
      setSelectedFolderId(folderId);
    } else {
      setSelectedMoveFolderId(folderId);
    }
  };
  handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    this.setState({ anchorEl: event.currentTarget });
  };

  handleMenuClose = () => {
    this.setState({ anchorEl: null });
  };
  handleRenameFolder = () => {
    const { handleShowCreateFolder } = this.props;
    handleShowCreateFolder({ type: 'rename' });
    this.handleMenuClose();
  };
  handleMoveFolder = () => {
    const { handleShowMoveFolder, fetchMoveFolder } = this.props;
    handleShowMoveFolder({ caseOptions: '' });
    fetchMoveFolder();
    this.handleMenuClose();
  };

  render() {
    const { folder, allFolders, inSidebar } = this.props;
    const { anchorEl } = this.state;
    const open = Boolean(anchorEl);
    if (!folder) {
      return null;
    }
    return (
      <List
        className={styles.folder}
        data-test={`list-folder`}
        data-testid="list"
      >
        <ListItem
          key={folder.id}
          component={Button}
          sx={{ fontWeight: 'normal' }}
          className={cx({
            'list-item-selected': this.isSelected,
            'list-item': !this.isSelected,
          })}
          data-id={folder.id}
          onClick={this.handleListItemClick}
          style={{ paddingLeft: 22 * folder.level }}
          data-test={`folder_${folder.id}`}
          data-testid="list-item"
        >
          {folder.count > 0 ? (
            <div onClick={this.handleExpandClick}>
              {folder ? (
                folder.expanded ? (
                  <img
                    src={ArrowDown}
                    alt="Logo"
                    data-testid="arrow-down-img"
                    aria-label="arrow-down"
                  />
                ) : (
                  <img
                    src={ArrowRight}
                    alt="Logo"
                    data-testid="arrow-right-img"
                  />
                )
              ) : (
                ''
              )}
            </div>
          ) : (
            <div style={{ paddingLeft: 18 }} />
          )}

          <ListItemIcon
            className={cx({
              'list-item-icon-work': folder.root,
              'list-item-icon-folder': !folder.root,
            })}
            data-testid="list-item-icon"
          >
            {folder.root ? (
              <WorkIcon data-testid="work-icon" />
            ) : folder.expanded || this.isSelected ? (
              <FolderOpenIcon data-testid="folder-open-icon" />
            ) : (
              <FolderIcon data-testid="folder-icon" />
            )}
          </ListItemIcon>

          <Tooltip
            title={folder.root ? 'My Cases' : folder.name}
            placement="bottom-end"
          >
            <ListItemText
              disableTypography
              className={styles['list-item-text']}
              primary={folder.root ? 'My Cases' : folder.name}
              data-testid="list-item-text"
            />
          </Tooltip>
          {(folder.fetchingSubFolders || folder.fetchingMoveSubFolders) && (
            <CircularProgress size={20} variant="indeterminate" />
          )}
          {!folder.root && inSidebar && (
            <React.Fragment>
              <IconButton
                data-test="folder-item-menu-option-icon-button"
                onClick={this.handleMenuClick}
                className={styles['icon-menu']}
                data-testid="icon-button"
                size="large"
              >
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={this.handleMenuClose}
                className={styles['menu']}
                data-testid="menu"
              >
                <MenuItem
                  data-test="folder-item-rename-option-icon-button"
                  onClick={this.handleRenameFolder}
                  data-testid="menu-item"
                >
                  Rename
                </MenuItem>
                <MenuItem
                  data-test="folder-item-move-option-icon-button"
                  onClick={this.handleMoveFolder}
                  data-testid="menu-item"
                >
                  Move
                </MenuItem>
              </Menu>
            </React.Fragment>
          )}
        </ListItem>
        {
          <Collapse in={folder.expanded} style={{ padding: 0 }}>
            <List component="div" disablePadding data-testid="list">
              {folder.subfolders.map((subFolderId) => (
                <Folder
                  {...this.props}
                  folder={allFolders[subFolderId]!} // safe due to folders = makeSubfolder with subFolderId is the Key
                  key={subFolderId}
                />
              ))}
            </List>
          </Collapse>
        }
      </List>
    );
  }
}

const mapState = (_state: any) => ({});
const mapDispatch = {
  fetchSubFolder: (subFolderId: string, expanded: boolean) =>
    FETCH_SUB_FOLDERS_REQUEST({ folderId: subFolderId, expanded: expanded }),
  setStatusExpanded: (folderId: string, expanded: boolean) =>
    HANDLE_EXPANDED({
      folderId: folderId,
      expanded: expanded,
    }),
  setSelectedFolderId: (folderId: string) =>
    SET_SELECTED_FOLDER_ID({
      selectedFolderId: folderId,
    }),
  handleShowCreateFolder: (payload: { type: string }) =>
    SHOW_FORM_CREATE_FOLDER(payload),
  handleShowMoveFolder: (payload: { caseOptions: string }) =>
    SHOW_MOVE_FOLDER(payload),

  fetchMoveFolder: () => FETCH_MOVE_FOLDER_REQUEST(),
  fetchMoveSubFolder: (subFolderId: string, expanded: boolean) =>
    FETCH_MOVE_SUB_FOLDERS_REQUEST({
      folderId: subFolderId,
      expanded: expanded,
    }),
  setSelectedMoveFolderId: (folderId: string) =>
    SET_SELECTED_MOVE_FOLDER_ID({
      selectedMoveFolderId: folderId,
    }),
  setStatusMoveExpanded: (folderId: string, expanded: boolean) =>
    HANDLE_MOVE_EXPANDED({
      folderId: folderId,
      expanded: expanded,
    }),
  setOpenFolderUpload: (folderId: string) => setOpenFolderUpload(folderId),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(Folder);

type Props = PropsFromRedux & {
  selectedFolderId: string | undefined;
  folder: FolderProps;
  allFolders: Folders;
  isShowConfirmModal?: boolean;
  inSidebar?: boolean;
  type: string | undefined;
  handleListItemClick?: () => void;
};
