import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface UpdateDatasourceRequest {
  updateDetails: [
    {
      datasourceName: string;
      connectionDetails: {
        server: string;
        database: string;
      };
    },
  ];
}

const setDatasetDatasource = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg, mssqlHost, mssqlDatabase } =
    env;

  try {
    await axios.post<never, AxiosResponse<never>, UpdateDatasourceRequest>(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/groups/${data.workspaceId}/datasets/${data.datasetId}/Default.UpdateDatasources`,
      {
        updateDetails: [
          {
            datasourceName: data.datasetName,
            connectionDetails: {
              server: mssqlHost,
              database: mssqlDatabase,
            },
          },
        ],
      },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    return context;
  } catch (e) {
    log.error('Update Datasources API failed', e);
    throw new ApiError(e);
  }
};

export default setDatasetDatasource;
