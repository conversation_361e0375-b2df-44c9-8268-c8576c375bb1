import axios from 'axios';
import { ApiError, NoCapacitiesError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface CapacitiesResponse {
  value: {
    '@odata.context': string;
    id: string;
    displayName: string;
    admins: string[];
    sku: string;
    state: string;
    capacityUserAccessRight: string;
    region: string;
  }[];
}

const getCapacities = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const { data: resp } = await axios.get<CapacitiesResponse>(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/capacities`,
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    if (resp.value.length === 0) {
      log.error('No capacities available! Check Azure Embedded PBI resources.');
      throw new NoCapacitiesError();
    }

    data.capacityId = resp.value[0].id;
    data.capacityName = resp.value[0].displayName;

    return context;
  } catch (e) {
    log.error('Workspace API failed', e);
    throw new ApiError(e);
  }
};

export default getCapacities;
