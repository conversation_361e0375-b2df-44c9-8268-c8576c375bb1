import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import { Config } from '../chartDefinitions';
import { AxisItemLocation } from './util';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $dateBucketSize: String
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
              ]
            }
            aggregate: [
              {
                name: "datetimeOfStop"
                field: "datetimeOfStop"
                operator: "date"
                timezone: "America/Los_Angeles"
                interval: $dateBucketSize
                limit: 10000
                aggregate: [
                  {
                    name: "durationOfStop"
                    field: "durationOfStop"
                    operator: "term"
                    limit: 10000
                  }
                ]
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'durationOfStopAggregation',
      dataKey: 'datetimeOfStop',
    },
  ],
  hasDemographics: false,
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.XYChart);

    chart.data = data.durationOfStopAggregation.map((dateAgg) => {
      return {
        date: dateAgg.key_as_string,
        'Average Stop Duration':
          dateAgg.durationOfStop.buckets.length > 0
            ? (
                dateAgg.durationOfStop.buckets.reduce(
                  (acc, b) => b.key * b.doc_count + acc,
                  0
                ) /
                dateAgg.durationOfStop.buckets.reduce(
                  (acc, b) => b.doc_count + acc,
                  0
                )
              ).toPrecision(2)
            : 0,
      };
    });

    // Create axes
    const dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.title.text = 'Date';
    dateAxis.baseInterval = { timeUnit: 'day', count: 1 };
    dateAxis.dateFormats.setKey('day', 'MM/dd/yyyy');
    chart.dateFormatter.dateFormat = 'MM/dd/yyyy';

    dateAxis.renderer.grid.template.location = AxisItemLocation.Middle;
    dateAxis.renderer.labels.template.location = AxisItemLocation.Middle;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    const valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = 'Average Stop Duration (minutes)';

    // Create series
    const series = chart.series.push(new am4charts.LineSeries());
    series.strokeWidth = config.lineWidth;
    series.dataFields.valueY = 'Average Stop Duration';
    series.dataFields.dateX = 'date';
    series.name = 'Average Stop Duration';
    series.tooltipText = '{name}: [bold]{valueY}[/]';

    // Add cursor
    chart.cursor = new am4charts.XYCursor();

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    return chart;
  },
};

export default config;

interface Data {
  durationOfStopAggregation: {
    doc_count: number;
    key: number;
    key_as_string: string;
    durationOfStop: {
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
      buckets: { key: number; doc_count: number }[];
    };
  }[];
}
