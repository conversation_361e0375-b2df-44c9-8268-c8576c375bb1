import React, { Component } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import {
  Button,
  Collapse,
  List,
  ListItem,
  ListItemText,
  Theme,
  Tooltip,
} from '@mui/material';
import ListItemIcon from '@mui/material/ListItemIcon';
import FolderIcon from '@mui/icons-material/Folder';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import CircularProgress from '@mui/material/CircularProgress';
import withStyles from '@mui/styles/withStyles';
import * as styles from './styles.scss';
import classNames from 'classnames/bind';
import {
  SHOW_FORM_CREATE_FOLDER,
  SHOW_MOVE_FOLDER,
  FETCH_MOVE_FOLDER_REQUEST,
  SET_SELECTED_MOVE_FOLDER_ID,
  FETCH_MOVE_SUB_FOLDERS_REQUEST,
  HANDLE_MOVE_EXPANDED,
} from 'state/modules/folders';
import { setOpenFolderUpload } from 'state/modules/uploadFile/actions';
import { Folder as TFolder } from 'src/model';
const cx = classNames.bind(styles);

const style = (_theme: Theme) => ({
  textColor: {
    color: '#2A323C',
  },
  padding: {
    paddingTop: '4px',
    paddingBottom: '4px',
  },
  spacing: {
    minWidth: '0px',
    paddingLeft: '6px',
  },
  folderPadding: {
    marginRight: '6px',
  },
});

export class Folder extends Component<Props> {
  state = {
    anchorEl: null,
  };

  get isSelected() {
    const { selectedFolderId, folder } = this.props;
    return selectedFolderId === folder.id;
  }
  handleExpandClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    const { folder, fetchMoveSubFolder, setStatusMoveExpanded } = this.props;
    if (!folder.expanded) {
      fetchMoveSubFolder(folder.id, !folder.expanded);
    } else {
      setStatusMoveExpanded(folder.id, !folder.expanded);
    }
  };
  handleListItemClick = (event: React.MouseEvent<HTMLElement>) => {
    const { setSelectedMoveFolderId, type, setOpenFolderUpload } = this.props;
    const folderId = event.currentTarget.getAttribute('data-id') ?? '';
    if (type === 'uploadFile') {
      if (setOpenFolderUpload) {
        setOpenFolderUpload(folderId);
      }
    } else {
      setSelectedMoveFolderId(folderId);
    }
  };
  handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    this.setState({ anchorEl: event.currentTarget });
  };

  handleMenuClose = () => {
    this.setState({ anchorEl: null });
  };
  handleRenameFolder = () => {
    const { handleShowCreateFolder } = this.props;
    handleShowCreateFolder({ type: 'rename' });
    this.handleMenuClose();
  };
  handleMoveFolder = () => {
    const { handleShowMoveFolder, fetchMoveFolder } = this.props;
    handleShowMoveFolder({ caseOptions: '' });
    fetchMoveFolder();
    this.handleMenuClose();
  };

  render() {
    const { folder, allFolders, classes } = this.props;
    if (!folder) {
      return null;
    }
    return (
      <List
        data-test={`move_file`}
        data-testid="move-file-list"
        classes={{ padding: classes.padding }}
      >
        <ListItem
          key={folder.id}
          component={Button}
          sx={{ textTransform: 'none' }}
          disableGutters
          className={cx({
            'move-item-selected': this.isSelected,
            'move-item': !this.isSelected,
          })}
          data-id={folder.id}
          onClick={this.handleListItemClick}
          style={{ paddingLeft: 22 * folder.level }}
          data-test={`move_file_${folder.id}`}
          data-testid="move-item"
        >
          {folder.count > 0 ? (
            <div onClick={this.handleExpandClick}>
              {folder ? (
                folder.expanded ? (
                  <ArrowDropDownIcon
                    style={{ color: '#555F7C' }}
                    fontSize="small"
                    data-testid="file_arrow-down-img"
                    aria-label="arrow-down"
                  />
                ) : (
                  <ArrowRightIcon
                    style={{ color: '#555F7C' }}
                    fontSize="small"
                    data-testid="file_arrow-right-img"
                  />
                )
              ) : (
                ''
              )}
            </div>
          ) : (
            <div style={{ paddingLeft: 20 }} />
          )}

          <ListItemIcon
            className="move-item-icon-folder"
            classes={{ root: classes.spacing }}
            data-testid="move-item-icon"
          >
            {folder.root ? (
              <></>
            ) : folder.expanded || this.isSelected ? (
              <FolderOpenIcon
                classes={{ root: classes.folderPadding }}
                fontSize="small"
                data-testid="move-file-open-icon"
                style={{ color: '#555F7C' }}
              />
            ) : (
              <FolderIcon
                classes={{ root: classes.folderPadding }}
                fontSize="small"
                data-testid="move-file-icon"
                style={{ color: '#555F7C' }}
              />
            )}
          </ListItemIcon>

          <Tooltip
            title={folder.root ? 'My Cases' : folder.name}
            placement="bottom-end"
          >
            <ListItemText
              disableTypography
              className={styles['move-item-text']}
              classes={{ root: classes.textColor }}
              primary={folder.root ? 'My Cases' : folder.name}
              data-testid="move-item-text"
            />
          </Tooltip>
          {(folder.fetchingSubFolders || folder.fetchingMoveSubFolders) && (
            <CircularProgress size={20} variant="indeterminate" />
          )}
        </ListItem>
        {
          <Collapse in={folder.expanded} style={{ padding: 0 }}>
            <List
              component="div"
              disablePadding
              data-testid="move-file-list-collapse"
            >
              {folder.subfolders.map((subFolderId) => (
                <Folder
                  {...this.props}
                  folder={allFolders[subFolderId]!} // subfolder has been preloaded already
                  key={subFolderId}
                />
              ))}
            </List>
          </Collapse>
        }
      </List>
    );
  }
}

const mapState = (_state: any) => ({});
const mapDispatch = {
  handleShowCreateFolder: (payload: { type: string }) =>
    SHOW_FORM_CREATE_FOLDER(payload),
  handleShowMoveFolder: (payload: { caseOptions: string }) =>
    SHOW_MOVE_FOLDER(payload),

  fetchMoveFolder: () => FETCH_MOVE_FOLDER_REQUEST(),
  fetchMoveSubFolder: (subFolderId: string, expanded: boolean) =>
    FETCH_MOVE_SUB_FOLDERS_REQUEST({
      folderId: subFolderId,
      expanded: expanded,
    }),
  setSelectedMoveFolderId: (folderId: string) =>
    SET_SELECTED_MOVE_FOLDER_ID({
      selectedMoveFolderId: folderId,
    }),
  setStatusMoveExpanded: (folderId: string, expanded: boolean) =>
    HANDLE_MOVE_EXPANDED({
      folderId: folderId,
      expanded: expanded,
    }),
  setOpenFolderUpload: (folderId: string) => setOpenFolderUpload(folderId),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default withStyles(style, { withTheme: true })(connector(Folder));

type Props = PropsFromRedux & {
  selectedFolderId: string | undefined;
  classes: Record<string, string>;
  folder: TFolder;
  allFolders: Record<string, TFolder>;
  isShowConfirmModal?: boolean;
  type: string | undefined;
  handleListItemClick?: () => void;
};
