import makeStyles from '@mui/styles/makeStyles';
import LinearProgress, {
  LinearProgressProps,
} from '@mui/material/LinearProgress';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function LinearProgressWithLabel(
  props: LinearProgressProps & { value: number }
) {
  return (
    <Box display="flex" alignItems="center">
      <Box width="100%" mr={1}>
        <LinearProgress color="primary" variant="determinate" {...props} />
      </Box>
      <Box minWidth={35}>
        <Typography variant="body2" color="textSecondary">{`${Math.round(
          props.value
        )}%`}</Typography>
      </Box>
    </Box>
  );
}
const useStyles = makeStyles({
  paper: {
    width: 500,
  },
});
function CustomizedProgressBars({ open, percentageFilesUploaded }: Props) {
  const classes = useStyles();
  return (
    <Dialog open={open} classes={{ paper: classes.paper }}>
      <DialogTitle>Processing</DialogTitle>
      <DialogContent>
        <LinearProgressWithLabel value={percentageFilesUploaded} />
      </DialogContent>
    </Dialog>
  );
}
interface Props {
  open: boolean;
  percentageFilesUploaded: number;
}
export default CustomizedProgressBars;
