import { noop } from 'lodash';
import { render } from '@testing-library/react';

import Breadcrumbs from '.';

describe('Breadcrumbs', () => {
  it('Should have Root Crumb Text when empty', () => {
    const { getByTestId } = render(
      <Breadcrumbs pathList={[]} onCrumbClick={noop} />
    );
    expect(getByTestId('name')).toHaveTextContent('My Files');
  });

  it('Should have 4 crumbs + root', () => {
    const pathList = [
      { id: 'first', name: 'Parent' },
      { id: 'second', name: 'Child' },
      { id: 'third', name: '<PERSON>Child' },
      { id: 'fourth', name: '<PERSON> GrandChild' },
    ];
    const { getAllByTestId } = render(
      <Breadcrumbs pathList={pathList} onCrumbClick={noop} />
    );

    expect(getAllByTestId('name')).toHaveLength(5);
  });

  it('Should have crumbs within hidden list', () => {
    const pathList = [
      { id: 'first', name: '<PERSON><PERSON>' },
      { id: 'second', name: 'Child' },
      { id: 'third', name: '<PERSON><PERSON>hild' },
      { id: 'fourth', name: 'Super GrandChild' },
      { id: 'fifth', name: 'Ultra GrandChild' },
    ];

    const { getAllByTestId } = render(
      <Breadcrumbs pathList={pathList} onCrumbClick={noop} />
    );

    expect(getAllByTestId('name')).toHaveLength(3);
  });
});
