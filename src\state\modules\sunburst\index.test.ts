import { getBucketColorHex, ENTITY_COLORS, dedupBuckets } from './index';

const bucketJson = `
[
      {
          "key": "PERSON",
          "doc_count": 5323,
          "entity": {
              "doc_count_error_upper_bound": 0,
              "sum_other_doc_count": 0,
              "buckets": [
                  {
                      "key": "He",
                      "doc_count": 931,
                      "tdoCount": {
                          "doc_count": 133,
                          "tdoCount": {
                              "value": 1
                          }
                      }
                  },
                  {
                      "key": "Andy",
                      "doc_count": 665,
                      "tdoCount": {
                          "doc_count": 133,
                          "tdoCount": {
                              "value": 1
                          }
                      }
                  },
                  {
                      "key": "He",
                      "doc_count": 400,
                      "tdoCount": {
                          "doc_count": 134,
                          "tdoCount": {
                              "value": 2
                          }
                      }
                  }
              ]
          }
      },
      {
          "key": "ORGANIZATION",
          "doc_count": 2947,
          "entity": {
              "doc_count_error_upper_bound": 0,
              "sum_other_doc_count": 0,
              "buckets": [
                  {
                      "key": "BBC",
                      "doc_count": 276,
                      "tdoCount": {
                          "doc_count": 138,
                          "tdoCount": {
                              "value": 2
                          }
                      }
                  }
              ]
          }
      }
  ]
`;
test('getBucketColorHex', () => {
  expect(getBucketColorHex(0, ENTITY_COLORS)).toBe('6CC7E9');
  expect(getBucketColorHex(ENTITY_COLORS.length - 1, ENTITY_COLORS)).toBe(
    '98FFDA'
  );
  expect(getBucketColorHex(ENTITY_COLORS.length, ENTITY_COLORS)).toBe('6CC7E9');
});

test('dedupBuckets', () => {
  const buckets = JSON.parse(bucketJson);
  const result = dedupBuckets(buckets);
  expect(result.PERSON!.He).toBe(3);
  expect(result.PERSON!.Andy).toBe(1);
  expect(result.ORGANIZATION!.BBC).toBe(2);
});
