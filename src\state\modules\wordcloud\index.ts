import getApiAuthToken from '../../../helpers/getApiAuthToken';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { ENTITY_CHILDREN_COLORS } from '../sunburst';
import { pipe } from '../../utils/pipe';
import {
  TopicsBucket,
  WordsPerColourTuple,
  ColouredWords,
  Wordcloud,
} from './models';
import { createAction, createReducer } from '@reduxjs/toolkit';
import fetch from '../../../helpers/fetchRetry';

export const FETCH_WORDCLOUD_AGGREGATIONS = createAction(
  'fetch wordcloud aggregations start'
);

export const FETCH_WORDCLOUD_AGGREGATIONS_SUCCESS = createAction<Wordcloud>(
  'fetch wordcloud aggregations success'
);
export const FETCH_WORDCLOUD_AGGREGATIONS_FAILURE = createAction(
  'fetch wordcloud aggregations failure',
  (payload?: any) => ({
    payload,
  })
);
export const ON_SELECTED_TOPIC_CHANGE = createAction<string[]>(
  'selected topic change'
);

export const SEARCH_PATH_TOPIC = 'content-classification.concept.class';

export const TOPIC_AGGREGATION_QUERY_CLAUSE = {
  name: 'topic',
  field: SEARCH_PATH_TOPIC,
  operator: 'term',
  limit: 2000,
  aggregate: [
    {
      operator: 'count',
      field: 'recordingId',
    },
  ],
};

const TOPIC_COLOURS = [
  '#438D9C',
  '#CC0000',
  '#E8A664',
  '#9C6043',
  '#6CC7E9',
  ...ENTITY_CHILDREN_COLORS,
];

// display 30 topics in wordcloud
const NUM_OF_TOPICS = 30;

const defaultState = {
  isWordcloudRedrawn: false,
  fetchingWordcloudAggregation: false,
  colouredWords: {} as ColouredWords,
  wordsPerColour: {} as { [key: string]: WordsPerColourTuple[] },
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_WORDCLOUD_AGGREGATIONS, (state) => {
      return {
        ...state,
        fetchingWordcloudAggregation: true,
      };
    })
    .addCase(FETCH_WORDCLOUD_AGGREGATIONS_SUCCESS, (state, action) => {
      const response = action?.payload?.aggregations;
      // const response = get(action, 'payload.aggregations', {});
      // const topicsBuckets = get(response, 'topic.topic.topic.buckets', []);
      const topicsBuckets = response?.topic?.topic?.topic?.buckets || [];
      const wordsPerColour = pipe(
        makeColoursForCloud,
        changeSizeToProperWeight
      )(topicsBuckets);
      const colouredWords: ColouredWords = {};
      if (!isEmpty(wordsPerColour)) {
        for (const i of Object.keys(wordsPerColour)) {
          colouredWords[i] = wordsPerColour[i]!.reduce<Array<string>>(
            (acc, val) => {
              acc.push(val[0]);
              return acc;
            },
            []
          );
        }
      }
      return {
        ...state,
        wordsPerColour,
        colouredWords,
        fetchingWordcloudAggregation: false,
      };
    })
    .addCase(FETCH_WORDCLOUD_AGGREGATIONS_FAILURE, (state) => {
      return {
        ...state,
        fetchingWordcloudAggregation: false,
      };
    });
});

const convertKeyForCloud = (input: TopicsBucket): WordsPerColourTuple => {
  const wholeWord = get(input, 'key', '');
  const size = get(input, 'recordingId.recordingId.value');
  if (wholeWord.includes('/')) {
    const phrases = input.key.split('/');
    const childIndex = phrases.length - 1;
    return [phrases[childIndex]!, size, phrases[childIndex - 1]!]; // string.split will give an array of length at lease 2.
  } else {
    return [wholeWord, size, wholeWord];
  }
};

const makeColoursForCloud = (inputData: TopicsBucket[]) => {
  // display 30 topics in UI
  let topicNum = NUM_OF_TOPICS;
  if (TOPIC_COLOURS.length < NUM_OF_TOPICS) {
    console.error(
      `display ${NUM_OF_TOPICS} topic, but ${TOPIC_COLOURS.length} colors for topic is found`
    );
    topicNum = TOPIC_COLOURS.length;
  }
  const data = inputData
    .map(convertKeyForCloud)
    .sort((a: WordsPerColourTuple, b: WordsPerColourTuple) => b[1] - a[1])
    .slice(0, topicNum); // use the highest 30 values
  const fatherCategories = data.reduce<string[]>((acc, val) => {
    acc.push(val[2]);
    return [...new Set(acc)];
  }, []);
  const result = fatherCategories.reduce<{
    [key: string]: WordsPerColourTuple[];
  }>((acc, val, index) => {
    // there are 30 colors in TOPIC_COLOURS and fatherCategories is
    // calculated from top 30 data.
    acc[TOPIC_COLOURS[index]!] = data.filter((item) => item[2] === val);
    return acc;
  }, {});
  return result;
};

const transformData = (sortedData: number[]) => {
  if (isEmpty(sortedData) || !sortedData[0]) {
    return sortedData;
  }
  const outputValues = [5, 4, 3, 2, 1] as const;
  // const lastIndex = outputValues.length - 1;
  const weight = outputValues[0] / sortedData[0];
  // const output = [];
  // for (let i = 0; i < sortedData.length; i++) {
  //   let value = Math.ceil(sortedData[i] * weight);

  //   // deal with sortedData is 0
  //   if (value === 0) {
  //     value = outputValues[lastIndex];
  //   }
  //   output.push(value);
  // }
  // return output;
  return sortedData.map((item) => {
    let value = Math.ceil(item * weight);
    // deal with sortedData is 0
    if (value === 0) {
      value = 1;
    }
    return value;
  });
};

const changeSizeToProperWeight = (dataInput: {
  [key: string]: WordsPerColourTuple[];
}) => {
  let sizes: number[] = [];
  const allElements: WordsPerColourTuple[] = [];
  // for (const i of Object.keys(dataInput)) {
  //   dataInput[i].map((item: WordsPerColourTuple) => sizes.push(item[1]));
  //   dataInput[i].map((item: WordsPerColourTuple) => allElements.push(item));
  // }
  for (const value of Object.values(dataInput)) {
    value.map((item) => sizes.push(item[1]));
    value.map((item) => allElements.push(item));
  }
  sizes.sort((a: number, b: number) => b - a);
  sizes = transformData(sizes);
  allElements.forEach((el, idx) => {
    // for (const i of Object.keys(dataInput)) {
    //   dataInput[i].forEach((item: WordsPerColourTuple) => {
    //     if (item[0] === el[0]) {
    //       item[1] = sizes[idx];
    //     }
    //   });
    // }
    for (const values of Object.values(dataInput)) {
      values.forEach((item) => {
        if (item[0] === el[0]) {
          item[1] = sizes[idx]!; // Safe due to being mapped off dataInput
        }
      });
    }
  });
  return dataInput;
};

export const fetchWordcloudAggregations =
  (aggregateQuery: object) => async (dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = `${state.config.apiRoot}/api/search/aggregate`;
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;
    const headers: { [key: string]: string } = {
      Authorization: 'Bearer ' + token,
      'Content-type': 'application/json',
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }

    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    let result;
    try {
      result = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(aggregateQuery),
      })
        .then((res) => res.text())
        .then((resText) => JSON.parse(resText))
        .catch((error) => console.log(error));
    } catch (_err) {
      dispatch(FETCH_WORDCLOUD_AGGREGATIONS_FAILURE());
      return;
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify(aggregateQuery) }
      );
    }

    if (!result || result.error) {
      dispatch(FETCH_WORDCLOUD_AGGREGATIONS_FAILURE(get(result, 'data')));
      return;
    }
    dispatch(FETCH_WORDCLOUD_AGGREGATIONS_SUCCESS(result));
  };

export const emitFetchWordcloudAggregations = () =>
  FETCH_WORDCLOUD_AGGREGATIONS();

export default reducer;
export const namespace = 'wordcloud';
export const local = (state: any) => state[namespace] as typeof defaultState;
export const getColouredWords = (state: any) => local(state).colouredWords;
export const getWordsPerColour = (state: any) => local(state).wordsPerColour;
