// External dependencies
import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from '@mui/material';

// Internal dependencies
import { ExportTemplateData } from '../../../model';

export default function ExportTemplateNamingDialog(
  props: ExportTemplateNamingDialogProps
) {
  const {
    open,
    setOpenTemplateNaming,
    selectedTemplate,
    setSelectedTemplate,
    exportTemplateData = [],
    onSaveTemplate,
  } = props;
  const [templateName, setTemplateName] = useState('');
  const { isValidName, errorMessage } = validateName(
    templateName,
    exportTemplateData
  );

  const handleSaveTemplate = () => {
    onSaveTemplate(templateName);
    setOpenTemplateNaming(false);
    setSelectedTemplate({
      label: templateName,
      value: selectedTemplate.value,
    });
    setTemplateName('');
  };
  const handleCancel = () => {
    setOpenTemplateNaming(false);
    setTemplateName('');
  };

  return (
    <Dialog
      open={open}
      onClose={() => setOpenTemplateNaming(false)}
      fullWidth
      maxWidth="xs"
    >
      <DialogTitle>Name Template</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          id="export-template-name"
          label="Template label"
          type="text"
          fullWidth
          variant="outlined"
          onChange={(e) => setTemplateName(e.target.value)}
          defaultValue={selectedTemplate.label}
          error={!isValidName}
          helperText={errorMessage}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button
          variant="contained"
          color="primary"
          disabled={!isValidName}
          onClick={handleSaveTemplate}
        >
          Save template
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function validateName(
  templateName: string,
  exportTemplateData: { data: { name: string } }[]
): { isValidName: boolean; errorMessage: string } {
  let errorMessage = ' ';
  let isValidName = true;
  const nameExist = exportTemplateData?.some(
    (e) => e.data.name === templateName
  );
  if (nameExist) {
    errorMessage = "This label isn't available";
    isValidName = false;
  } else if (!templateName) {
    errorMessage = 'Template Label cannot be empty';
    isValidName = false;
  }
  return { isValidName, errorMessage };
}

interface ExportTemplateNamingDialogProps {
  open: boolean;
  setOpenTemplateNaming: (value: boolean) => void;
  selectedTemplate: { label: string; value: string };
  setSelectedTemplate: ({
    label,
    value,
  }: {
    label: string;
    value: string;
  }) => void;
  exportTemplateData?: ExportTemplateData[];
  onSaveTemplate: (templateName: string) => void;
}
