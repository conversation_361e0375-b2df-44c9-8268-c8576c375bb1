import { TokenConfigurationRows, TokenConfigurationRow } from '../definitions';
import { DatabaseError, OrgsHaveNoTokenRegisteredError } from '../errors';
import { Context } from '../../types';

const checkForTokenConfigs = async (context: Context) => {
  const { req, log, queries } = context;

  const { orgIds } = req.body;

  if (!orgIds || orgIds.length === 0) {
    log.error('orgIds are not provided to action');
    throw new Error('`orgIds are not provided to action');
  }

  try {
    return queries
      .getTokenConfigs(orgIds)
      .then((tokenConfigs: TokenConfigurationRows) => {
        const provisionedOrg = tokenConfigs.map(
          (tokenConfig: TokenConfigurationRow) => tokenConfig.orgId
        );

        const notProvisionedOrgs = orgIds.filter(
          (orgId: string) => !provisionedOrg.includes(orgId)
        );

        if (notProvisionedOrgs.length > 0) {
          log.error('OrgHasNoTokenRegisteredError: Orgs', notProvisionedOrgs);
          throw new OrgsHaveNoTokenRegisteredError(notProvisionedOrgs);
        }

        return context;
      });
  } catch (e) {
    log.error('FetchTokenConfig failed', e);
    throw new DatabaseError(e);
  }
};

export default checkForTokenConfigs;
