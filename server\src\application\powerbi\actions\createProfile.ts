import axios, { AxiosResponse } from 'axios';
import { ApiError, OrgNameAlreadyExists } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface ProfilesRequest {
  displayName: string;
}

export interface ProfilesResponse {
  '@odata.context': string;
  id: string;
  displayName: string;
}

const createProfile = async (context: Context) => {
  const { log, req, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg } = env;

  try {
    const { data: resp } = await axios.post<
      ProfilesResponse,
      AxiosResponse<ProfilesResponse>,
      ProfilesRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/profiles`,
      { displayName: req.body.orgName },
      { headers: { Authorization: data.pbiBearerToken } }
    );

    data.profileId = resp.id;

    return context;
  } catch (e) {
    log.error('Profiles API failed', e);
    if (
      JSON.stringify(e?.response?.data).includes(
        'PowerBIEntityAlreadyExistsException'
      )
    ) {
      throw new OrgNameAlreadyExists();
    }
    throw new ApiError(e);
  }
};

export default createProfile;
