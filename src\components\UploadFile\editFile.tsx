import React, { KeyboardEvent } from 'react';
import makeStyles from '@mui/styles/makeStyles';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MuiDialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Paper from '@mui/material/Paper';
import AddPhotoAlternate from '@mui/icons-material/AddPhotoAlternate';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import styles from './styles';
import TagsCustomize from './tagsCustomize';
import {
  UploadResultEdit,
  UploadResult,
} from '../../state/modules/uploadFile/models';
const useStyles = makeStyles(styles);
const DialogTitle = ({ children, onClose, ...other }: DialogTitleProps) => {
  const classes = useStyles();
  return (
    <MuiDialogTitle {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          size="large"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
};
interface DialogTitleProps {
  children: React.ReactNode;
  onClose: () => void;
  id: string;
}

function EditFileUpload({
  open,
  title,
  handleClose,
  data,
  onChangeDateTime,
  handlePick,
  onChangeFileName,
  uploadResultEdit,
  handleSave,
  onKeyPress,
  handleOnChangeTagsCustomize,
  tagsEditFileUpload,
  handleRemoveTagsCustomize,
  onClickAddTags,
}: Props) {
  const [value, setValue] = React.useState('general');
  const classes = useStyles();
  function handleChange(_event: React.ChangeEvent<object>, newValue: string) {
    setValue(newValue);
  }

  return (
    <Dialog
      onClose={handleClose}
      open={open}
      maxWidth="md"
      data-testid="edit-file-dialog"
    >
      <DialogTitle id="customized-dialog-title" onClose={handleClose}>
        {title}
      </DialogTitle>
      <DialogContent
        dividers
        className={classes.dialogEditFileContent}
        data-testid="dialog-content"
      >
        {data.length > 1 && (
          <p className={classes.titleEditMultipleFile}>
            All values will be overwritten when bulk editing multiple files.
          </p>
        )}
        <Paper className={classes.tabsContent}>
          <Tabs
            value={value}
            onChange={handleChange}
            indicatorColor="primary"
            textColor="primary"
            centered
            className={classes.tabs}
            data-testid="tabs-edit"
          >
            <Tab
              label="General"
              value="general"
              className={classes.tab}
              data-testid="general-edit"
            />
            <Tab
              label="Tags"
              value="tags"
              className={classes.tab}
              data-testid="tags-edit"
            />
          </Tabs>
        </Paper>
        {value === 'general' && (
          <div className={classes.generalContent}>
            <p className={classes.generalInfo}>General Info</p>
            <span className={classes.generalText}>
              Add information to help describe and identify your media{' '}
            </span>
            <Grid container spacing={3}>
              <Grid size={{ xs: 4 }}>
                <p className={classes.generalText}>Avatar Image (Optional)</p>
                <div className={classes.uploadImage}>
                  <div
                    className={classes.uploadImageContent}
                    data-type={'programImage'}
                    onClick={handlePick}
                    style={{
                      backgroundImage: `url(${uploadResultEdit.getUrlProgramImage})`,
                    }}
                  >
                    <AddPhotoAlternate className={classes.iconUpload} />
                    <p>Upload Image</p>
                  </div>
                </div>
              </Grid>
              <Grid size={{ xs: 8 }}>
                <p className={classes.generalText}>Cover Image (Optional)</p>
                <div className={classes.uploadImage}>
                  <div
                    className={classes.uploadImageContent}
                    data-type={'programLiveImage'}
                    onClick={handlePick}
                    style={{
                      backgroundImage: `url(${uploadResultEdit.getUrlProgramLiveImage})`,
                    }}
                  >
                    <AddPhotoAlternate className={classes.iconUpload} />
                    <p>Upload Image</p>
                  </div>
                </div>
              </Grid>
            </Grid>
            <div className={classes.fileName}>
              <TextField
                required
                label="File Name (Required)"
                data-test="file-name-upload"
                defaultValue={uploadResultEdit.fileName}
                onChange={onChangeFileName}
                error={!uploadResultEdit.fileName}
                helperText={`${
                  !uploadResultEdit.fileName ? 'File Name is required' : ''
                }`}
                slotProps={{
                  htmlInput: { 'data-testid': 'input-name' },
                }}
                variant="standard"
              />
            </div>
            <div>
              <p className={classes.generalInfo}>Media Display Time</p>
              <TextField
                label="Date Time (Required)"
                type="datetime-local"
                defaultValue={uploadResultEdit.dateTime}
                className={(classes as any).textField}
                onChange={onChangeDateTime}
                error={!uploadResultEdit.dateTime}
                helperText={`${
                  !uploadResultEdit.dateTime ? 'Date Time is required' : ''
                }`}
                slotProps={{
                  inputLabel: { shrink: true },
                  htmlInput: { 'data-testid': 'input-date' },
                }}
                variant="standard"
              />
            </div>
          </div>
        )}
        {value === 'tags' && (
          <div className={classes.generalContent}>
            <p className={classes.generalInfo}>Tagging Media</p>
            <span className={classes.generalText}>
              Tags provide a useful way to group related media together and make
              it easier for people to find content.
            </span>
            <div className={classes.fileName}>
              <TagsCustomize
                type="editFileUpload"
                onKeyPress={onKeyPress}
                handleOnChangeTagsCustomize={handleOnChangeTagsCustomize}
                tagsCustomizeName={tagsEditFileUpload}
                tagsCustomize={uploadResultEdit.tagsEdit}
                handleRemoveTagsCustomize={handleRemoveTagsCustomize}
                onClickAddTags={onClickAddTags}
              />
            </div>
          </div>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={handleClose}
          color="primary"
          data-testid="cancel-edit-file"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          color="primary"
          disabled={!uploadResultEdit.fileName || !uploadResultEdit.dateTime}
          data-test="save-edit-file"
          data-testid="save-edit-file"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
interface Props {
  open: boolean;
  title: string;
  handleClose: () => void;
  data: UploadResult[];
  onChangeDateTime: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handlePick: (event: React.MouseEvent<HTMLElement>) => void;
  onChangeFileName: (event: React.ChangeEvent<HTMLInputElement>) => void;
  uploadResultEdit: UploadResultEdit;
  handleSave: () => void;
  onKeyPress: (event: KeyboardEvent<HTMLInputElement>) => void;
  handleOnChangeTagsCustomize: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  tagsEditFileUpload: string;
  handleRemoveTagsCustomize: (name: string, type: string) => void;
  onClickAddTags: (event: React.MouseEvent<HTMLElement>) => void;
}
export default EditFileUpload;
