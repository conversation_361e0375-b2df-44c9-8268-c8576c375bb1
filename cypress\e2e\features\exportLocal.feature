Feature: ExportLocal

  Background:
    Given The user is on the media list page

  # @e2e @exportLocal
  # Scenario: User exports a single file and all items
  #   When The user exports a file
  #   And The user exports all items

  # @e2e @exportLocal
  # Scenario: Verify the content of the Advanced Export pop-up
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   And the "Fields" tab should be selected
  #   And the following export options should be visible:
  #     | Option Name     |
  #     | File Properties |
  #     | Transcription   |
  #     | Other Cognition |
  #   And the following buttons should be visible on the export pop-up:
  #     | Button Name   |
  #     | Cancel        |
  #     | Save Template |
  #     | Export        |

  # @e2e @exportLocal
  # Scenario: Verify Fields tab of Advanced Export pop-up
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   And the "File ID" export option should be checked
  #   And the "File ID" export option should be disabled
  #   And the "Plain Text (TXT)" export option should be checked
  #   When the user checks the "Filename" export option
  #   Then the "Filename" export option should be checked
  #   When the user unchecks the "Plain Text (TXT)" export option
  #   Then the "Plain Text (TXT)" export option should not be checked

  # @e2e @exportLocal
  # Scenario: Verify user can edit name of each field
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   And the user clicks the edit icon for the "Plain Text (TXT)" field
  #   And the user renames the field "Plain Text (TXT)" to "My Custom TXT Name"
  #   And the user clicks the "OK" button in the dialog
  #   When the user clicks the edit icon for the "Plain Text (TXT)" field
  #   Then the input for "Plain Text (TXT)" should have the value "My Custom TXT Name"

  # @e2e @exportLocal
  # Scenario: Verify user can export file using Export Templates
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   When the user selects the export template "E2E - Test Template"
  #   And the user clicks the "Export" button on the export pop-up
  #   Then a success message "Your export job has been submitted" is displayed

  # @e2e @exportLocal
  # Scenario: Verify user can create your own export template
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   When the user checks the "Filename" export option
  #   And the user clicks the "Save Template" button on the export pop-up
  #   Then the "Name Template" dialog should appear
  #   When the user names the template "My New Test Template"
  #   And the user clicks the "Save template" button in the new template dialog
  #   Then a success message "Template saved" is displayed

  # @e2e @exportLocal
  # Scenario: Verify saving multiple checked options to a new template
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   Then the "Advanced Export" pop-up is displayed
  #   When the user checks the following export options:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |
  #   And the user clicks the "Save Template" button on the export pop-up
  #   And the user names the template "My Multi-Option Template"
  #   And the user clicks the "Save template" button in the new template dialog
  #   And a success message "Template saved" is displayed
  #   When the user unchecks the following export options:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |
  #   And the user selects the export template "My Multi-Option Template"
  #   Then the following export options should be checked:
  #     | Option Name   |
  #     | Filename      |
  #     | Base Filename |
  #     | Tags          |

  # @e2e @exportLocal
  # Scenario: Verify user can change Archive Name in Advanced Export
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   And the user navigates to the "Options" tab
  #   When the user clears the Archive Name field
  #   Then the Archive Name field should show the error message "invalid name. Minimum 3 characters required"
  #   And the "Export" button on the export pop-up should be disabled
  #   When the user sets the Archive Name to "My-Custom-Archive-Name"
  #   Then the Archive Name field should not show an error message
  #   And the "Export" button on the export pop-up should be enabled
  #   When the user clicks the "Export" button on the export pop-up
  #   Then a success message "Your export job has been submitted" is displayed

  # @e2e @exportLocal
  # Scenario: Verify user can set password for file before export
  #   When the user selects the file "e2e_audio.mp3"
  #   And the user clicks the "Export" toolbar button
  #   And the user navigates to the "Options" tab
  #   And the user clicks the "Set Password" button in option tab
  #   Then the "Set Password" dialog of option should appear
  #   When the user enters the password "MySecretPassword"
  #   And the user clicks the "Save" button in the password dialog
  #   Then the "Set Password" dialog should disappear
  #   When the user clicks the "Export" button on the export pop-up
  #   Then a success message "Your export job has been submitted" is displayed

  @e2e @exportLocal
  Scenario: Verify user can download a completed export
    When the user selects the file "e2e_audio.mp3"
    And the user clicks the "Export" toolbar button
    And the user clicks the "Export" button on the export pop-up and waits for the export to complete
    Then the user can download the export from the notifications panel
    When the user navigates to the main application tab "EXPORTS(31)"
    Then the user can download the first completed export from the exports table