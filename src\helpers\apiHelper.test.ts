import { configureStore } from '@reduxjs/toolkit';
import { thunk } from 'redux-thunk';
import {
  callAsyncFunc,
  createAsyncFuncFailureAction,
  createAsyncFuncRequestAction,
  createAsyncFuncSuccessAction,
} from './apiHelper';

describe('createAsyncFuncRequestAction', () => {
  it('it creates action without payload', () => {
    const initialState = {};
    let gotAction = {} as {
      type: string;
      error?: boolean;
      payload?: any;
      meta?: any;
    };
    function mockedReducer(
      state = initialState,
      action: { type: string; payload?: any; meta?: any; error?: boolean }
    ) {
      gotAction = { ...action };
      return state;
    }
    const store = configureStore({ reducer: mockedReducer });
    const testRequest = createAsyncFuncRequestAction('test request');
    const variables = { input: 123 };
    store.dispatch(testRequest({ variables }));
    expect(gotAction.type).toBe('test request');
    expect(gotAction.payload).toBeUndefined();
    expect(gotAction.meta).toEqual({ variables });
    expect(gotAction.error).toBeUndefined();
  });
});

describe('createAsyncFuncSuccessAction', () => {
  it('it creates action with payload', () => {
    const initialState = {};
    let gotAction = {} as {
      type: string;
      error?: boolean;
      payload?: any;
      meta?: any;
    };
    function mockedReducer(
      state = initialState,
      action: { type: string; payload?: any; meta?: any; error?: boolean }
    ) {
      gotAction = { ...action };
      return state;
    }
    const store = configureStore({ reducer: mockedReducer });
    const testRequest = createAsyncFuncSuccessAction('test request success');
    const payload = { value: 1 };
    const response = { result: 789 };
    store.dispatch(testRequest(payload, { response }));
    expect(gotAction.type).toBe('test request success');
    expect(gotAction.payload).toEqual(payload);
    expect(gotAction.meta).toEqual({ response });
    expect(gotAction.error).toBeUndefined();
  });
});

describe('createAsyncFuncFailureAction', () => {
  it('it creates action with error', () => {
    const initialState = {};
    let gotAction = {} as {
      type: string;
      error?: boolean;
      payload?: any;
      meta?: any;
    };
    function mockedReducer(
      state = initialState,
      action: { type: string; payload?: any; meta?: any; error?: boolean }
    ) {
      gotAction = { ...action };
      return state;
    }
    const store = configureStore({ reducer: mockedReducer });
    const testRequest = createAsyncFuncFailureAction('test request failed');
    const error = new Error('test error');
    store.dispatch(testRequest(error, { response: { error } }, true));
    expect(gotAction.type).toBe('test request failed');
    expect(gotAction.payload).toEqual(error);
    expect(gotAction.meta).toEqual({ response: { error } });
    expect(gotAction.error).toBeTruthy();
  });
});

describe('callAsyncFunc', () => {
  const initialState = {};
  let gotAction = {} as {
    type: string;
    payload?: any;
    meta?: any;
    error?: boolean;
  };
  function mockedReducer(
    state = initialState,
    action: { type: string; payload?: any; meta?: any; error?: boolean }
  ) {
    gotAction = { ...action };
    return state;
  }

  const mockStore = configureStore({
    reducer: mockedReducer,
    middleware: [thunk],
  });
  const testRequest = createAsyncFuncRequestAction('test request');
  const testRequestSuccess = createAsyncFuncSuccessAction<{ result: string }>(
    'test request success'
  );
  const testRequestFailure = createAsyncFuncFailureAction(
    'test request failed'
  );

  it('callAsyncFunc make call successfully', async () => {
    gotAction = {} as {
      type: string;
      payload?: any;
      meta?: any;
      error?: boolean;
    };
    const asyncfn = async () => {
      const result = await new Promise<string>((resolve) => {
        setTimeout(() => {
          resolve('foo');
        }, 300);
      });
      return { data: { result } };
    };
    const got = await callAsyncFunc({
      actionTypes: [testRequest, testRequestSuccess, testRequestFailure],
      asyncfn,
      dispatch: mockStore.dispatch,
      getState: () => {},
    });
    expect(gotAction.type).toBe('test request success');
    expect(gotAction.payload).toEqual({ result: 'foo' });
    expect(gotAction.meta).toEqual({ response: { data: { result: 'foo' } } });
    expect(gotAction.error).toBeUndefined();
    expect(got).toEqual({ data: { result: 'foo' } });
  });

  it('callAsyncFunc make call failed', async () => {
    gotAction = {} as {
      type: string;
      payload?: any;
      meta?: any;
      error?: boolean;
    };
    const error = new Error('test error');
    const asyncfn = async () => {
      const result = await new Promise<string>((reject) => {
        setTimeout(() => {
          reject(error.message);
        }, 100);
      });
      return { errors: [new Error(result)] };
    };
    const got = await callAsyncFunc({
      actionTypes: [testRequest, testRequestSuccess, testRequestFailure],
      asyncfn,
      dispatch: mockStore.dispatch,
      getState: () => {},
    });
    expect(gotAction.type).toBe('test request failed');
    expect(gotAction.payload).toEqual([error]);
    expect(gotAction.meta).toEqual({
      response: { errors: [error] },
    });
    expect(gotAction.error).toBeTruthy();
    expect(got).toBeUndefined();
  });
});
