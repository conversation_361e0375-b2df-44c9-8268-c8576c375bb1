import React from 'react';
import { isEqual, includes } from 'lodash';
import cx from 'classnames';
import * as styles from './overlayPositioningProvider.styles.scss';

export const OverlayPositioningContext = React.createContext({
  top: 0,
  left: 0,
  height: 0,
  width: 0,
});

interface Props {
  contentHeight: number;
  contentWidth: number;
  fixedWidth?: boolean;
  children?: React.ReactNode;
}

export default class OverlayPositioningProvider extends React.Component<Props> {
  static defaultProps = {};

  state = {
    overlayPosition: { top: 0, left: 0, height: 0, width: 0 },
  };

  measuredChildRef: HTMLElement | null = null; // eslint-disable-line react/sort-comp
  resizeObserver: ResizeObserver | null = null;
  pollingInterval: number | null = null;

  measureChildTimerId: number | null = null;

  componentWillUnmount() {
    this._mounted = false;
    this.pollingInterval && clearInterval(this.pollingInterval);
    this.resizeObserver?.disconnect();
    this.measureChildTimerId && clearTimeout(this.measureChildTimerId);
  }

  componentDidMount() {
    this._mounted = true;
    window.addEventListener('error', (e) => {
      if (
        includes(e.message, 'ResizeObserver loop limit exceeded') ||
        includes(
          e.message,
          'ResizeObserver loop completed with undelivered notifications'
        )
      ) {
        return;
      }
    });
  }

  _mounted = false;

  componentDidUpdate() {
    this.measureChild();
  }

  measureChild = (element: Element | null = this.measuredChildRef) => {
    // calculate the actual size of the element we're going to lay on top of

    // calculate the actual size of the element we're going to lay on top of
    if (!this._mounted || !element) {
      return;
    }

    const { height: screenHeight, width: screenWidth } =
      element.getBoundingClientRect();
    const { contentWidth, contentHeight } = this.props;

    const ratioScreen = screenWidth / screenHeight;
    const ratioContent = contentWidth / contentHeight;

    const [width, height] =
      ratioScreen > ratioContent
        ? [(contentWidth * screenHeight) / contentHeight, screenHeight]
        : [screenWidth, (contentHeight * screenWidth) / contentWidth];

    // figure out what styles need to be applied to the overlay component so that
    // it aligns with the content (considering letter/pillarboxing)
    const measuredOverlayPosition = {
      top: (screenHeight - height) / 2,
      left: (screenWidth - width) / 2,
      height,
      width,
    };

    if (!isEqual(this.state.overlayPosition, measuredOverlayPosition)) {
      this.setState({
        overlayPosition: measuredOverlayPosition,
      });
    }
  };

  setMeasuredChildRef = (r: HTMLElement | null) => {
    if (!r) {
      return;
    }

    this.measuredChildRef = r;

    if (!window.ResizeObserver) {
      this.pollingInterval && clearInterval(this.pollingInterval);
      // poll for changes in the measured element's size
      this.pollingInterval = window.setInterval(this.measureChild, 250);
      return;
    }

    // use ResizeObserver if available (Chrome only), to avoid polling
    this.resizeObserver = new ResizeObserver(
      ([entry]: ResizeObserverEntry[]) => {
        this.measureChild(entry?.target);
      }
    );
    this.resizeObserver.observe(r);
  };

  render() {
    // clearfix and float are to make sure the "measured child" div sizes
    // exactly to the size of its child content in fixed/fluid width scenarios
    return (
      <OverlayPositioningContext.Provider value={this.state.overlayPosition}>
        <div
          className={cx({ [styles.clearfix!]: this.props.fixedWidth })} // safe due to clearfix exists in styles
          style={{ height: 'calc(100% - 36px)' }}
        >
          <div
            style={{
              float: this.props.fixedWidth ? 'left' : 'none',
              position: 'relative',
              verticalAlign: 'bottom',
              height: '100%',
            }}
            ref={this.setMeasuredChildRef}
          >
            {this.props.children}
          </div>
        </div>
      </OverlayPositioningContext.Provider>
    );
  }
}
