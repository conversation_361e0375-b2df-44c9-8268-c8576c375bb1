import React from 'react';
import TagsCustomize from '../tagsCustomize';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('TagsCustomize', () => {
  const props = {
    onKeyPress: jest.fn(),
    handleOnChangeTagsCustomize: jest.fn(),
    tagsCustomizeName: '',
    tagsCustomize: [
      {
        value: 'test',
      },
    ],
    handleRemoveTagsCustomize: jest.fn(),
    onClickAddTags: jest.fn(),
    type: 'type',
  };

  it('renders a Box component', () => {
    const { getByTestId } = render(<TagsCustomize {...props} />);
    expect(getByTestId('tags-customize')).toBeInTheDocument();
  });
  it('renders a Input Tags component', () => {
    const { getByTestId } = render(<TagsCustomize {...props} />);
    expect(getByTestId('input-tags')).toBeInTheDocument();
  });
  it('renders a Button Add Tag', () => {
    const { getByTestId } = render(<TagsCustomize {...props} />);
    expect(getByTestId('add-tag')).toBeInTheDocument();
  });
  it('renders a Chip component', () => {
    const { getByTestId } = render(<TagsCustomize {...props} />);
    expect(getByTestId('chip')).toBeInTheDocument();
  });
});
