import { CreateFolder } from './index';
import { render, fireEvent } from '@testing-library/react';

describe('CreateFolder', () => {
  const props = {
    onSubmit: jest.fn(),
    open: true,
    onClose: jest.fn(),
    pristine: false,
    folderDetail: {
      id: 'id',
      name: 'name',
      treeObjectId: 'treeObjectId',
      expanded: false,
      fetchingSubFolders: false,
      subfolders: [],
      count: 10,
      root: false,
      parentId: 'parentId',
      level: 1,
    },
    type: 'type',
    onChange: jest.fn(),
  };
  it('should handle button click event', () => {
    const { getByText } = render(<CreateFolder {...props} />);
    fireEvent.click(getByText('Cancel'));
    expect(props.onClose).toHaveBeenCalled();
  });

  it('renders a IconButton', () => {
    const { getByTestId } = render(<CreateFolder {...props} />);
    expect(getByTestId('icon-button')).toBeInTheDocument();
  });
  it('renders 2 Button', () => {
    const { getAllByTestId } = render(<CreateFolder {...props} />);
    expect(getAllByTestId('button')).toHaveLength(2);
  });
  it('renders a Input', () => {
    const { getByTestId } = render(<CreateFolder {...props} />);
    expect(getByTestId('input')).toBeInTheDocument();
  });
});
