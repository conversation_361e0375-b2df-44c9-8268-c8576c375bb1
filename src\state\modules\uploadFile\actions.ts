import { clamp } from 'lodash';
import {
  UploadProgress,
  Library,
  Engine,
  Template,
  ContentTemplate,
  DagTemplate,
  UploadResult,
  EnginesSelected,
  UploadFileSelectionType,
} from './models';
import { Folder } from '../../../model';
import { createAction } from '@reduxjs/toolkit';
export const namespace = 'uploadFile';
import { DateTime } from 'luxon';
import { EngineCategoriesResponse } from './uploadFileSaga';
export const PICK_START = createAction(
  `${namespace}_PICK_START`,
  (type: string) => ({
    payload: undefined,
    meta: {
      type,
    },
  })
);

export const PICK_END = createAction<{
  type: string;
}>(`${namespace}_PICK_END`);

export const RETRY_REQUEST = createAction<{
  callback?: () => void;
}>(`${namespace}_RETRY_REQUEST`);

export const RETRY_DONE = createAction<{
  callback?: () => void;
}>(`${namespace}_RETRY_DONE`);
export const ABORT_REQUEST = createAction(
  `${namespace}_ABORT_REQUEST`,
  (meta: { fileKey?: string }) => ({
    payload: undefined,
    meta,
  })
);

export const UPLOAD_REQUEST = createAction<{
  files: File[];
  callback?: () => void;
}>(`${namespace}_UPLOAD_REQUEST`);

export const UPLOAD_PROGRESS = createAction(
  `${namespace}_UPLOAD_PROGRESS`,
  (payload: UploadProgress, meta: { fileKey: string }) => ({ payload, meta })
);

export const UPLOAD_COMPLETE = createAction(
  `${namespace}_UPLOAD_COMPLETE`,
  (
    payload: UploadResult[] | null,
    meta: { warning?: string | boolean; error?: string | boolean }
  ) => ({
    payload,
    meta,
  })
);

export const ON_SELECTION_CHANGE = createAction<{
  value: number;
  type: UploadFileSelectionType;
}>(`${namespace}_ON_SELECTION_CHANGE`);

export const REMOVE_FILE_UPLOAD = createAction<{
  value: number[];
}>(`${namespace}_REMOVE_FILE_UPLOAD`);

export const SHOW_EDIT_FILE_UPLOAD = createAction(
  `${namespace}_SHOW_EDIT_FILE_UPLOAD`
);
export const HIDE_EDIT_FILE_UPLOAD = createAction(
  `${namespace}_HIDE_EDIT_FILE_UPLOAD`
);
export const FETCH_ENGINE_CATEGORIES_REQUEST = createAction(
  `${namespace}_FETCH_ENGINE_CATEGORIES_REQUEST`
);
export const FETCH_ENGINE_CATEGORIES_SUCCESS = createAction<{
  engineCategories: EngineCategoriesResponse['engineCategories']['records'];
}>(`${namespace}_FETCH_ENGINE_CATEGORIES_SUCCESS`);
export const FETCH_ENGINE_CATEGORIES_FAILURE = createAction(
  `${namespace}_FETCH_ENGINE_CATEGORIES_FAILURE`
);
export const FETCH_LIBRARIES_REQUEST = createAction(
  `${namespace}_FETCH_LIBRARIES_REQUEST`
);
export const FETCH_LIBRARIES_SUCCESS = createAction<{
  libraries: Library[];
}>(`${namespace}_FETCH_LIBRARIES_SUCCESS`);
export const FETCH_LIBRARIES_FAILURE = createAction(
  `${namespace}_FETCH_LIBRARIES_FAILURE`
);
export const FETCH_ENGINES_REQUEST = createAction(
  `${namespace}_FETCH_ENGINES_REQUEST`
);
export const FETCH_ENGINES_SUCCESS = createAction<{
  engines: Engine[];
}>(`${namespace}_FETCH_ENGINES_SUCCESS`);
export const FETCH_ENGINES_FAILURE = createAction(
  `${namespace}_FETCH_ENGINES_FAILURE`
);
export const ADD_ENGINE = createAction<{
  engineId: string;
}>(`${namespace}_ADD_ENGINE`);
export const REMOVE_ENGINE = createAction<{
  engineId: string;
}>(`${namespace}_REMOVE_ENGINE`);
export const CHANGE_ENGINE = createAction<{
  engineId: string;
}>(`${namespace}_CHANGE_ENGINE`);
export const SHOW_MODAL_SAVE_TEMPLATE = createAction<{
  value: boolean;
}>(`${namespace}_SHOW_MODAL_SAVE_TEMPLATE`);
export const HIDE_MODAL_SAVE_TEMPLATE = createAction<{
  value: boolean;
}>(`${namespace}_HIDE_MODAL_SAVE_TEMPLATE`);
export const SAVE_TEMPLATE_REQUEST = createAction(
  `${namespace}_SAVE_TEMPLATE_REQUEST`
);
export const SAVE_TEMPLATE_SUCCESS = createAction(
  `${namespace}_SAVE_TEMPLATE_SUCCESS`
);
export const SAVE_TEMPLATE_FAILURE = createAction(
  `${namespace}_SAVE_TEMPLATE_FAILURE`
);
export const FETCH_TEMPLATES_SUCCESS = createAction<{
  templates: Template[];
}>(`${namespace}_FETCH_TEMPLATES_SUCCESS`);
export const FETCH_TEMPLATES_FAILURE = createAction(
  `${namespace}_FETCH_TEMPLATES_FAILURE`
);
export const CHANGE_TEMPLATE = createAction<{
  templateId: string;
}>(`${namespace}_CHANGE_TEMPLATE`);
export const FETCH_CONTENT_TEMPLATES_REQUEST = createAction(
  `${namespace}_FETCH_CONTENT_TEMPLATES_REQUEST`
);

export const FETCH_CONTENT_TEMPLATES_SUCCESS = createAction<{
  contentTemplates: ContentTemplate[];
}>(`${namespace}_FETCH_CONTENT_TEMPLATES_SUCCESS`);

export const FETCH_CONTENT_TEMPLATES_FAILURE = createAction(
  `${namespace}_FETCH_CONTENT_TEMPLATES_FAILURE`
);

export const ADD_CONTENT_TEMPLATE = createAction<{
  contentTemplateId: string;
}>(`${namespace}_ADD_CONTENT_TEMPLATE`);
export const REMOVE_CONTENT_TEMPLATE = createAction<{
  contentTemplateId: string;
}>(`${namespace}_REMOVE_CONTENT_TEMPLATE`);
export const ON_CHANGE_FORM_CONTENT_TEMPLATE = createAction<{
  contentTemplateId: string;
  name: string;
  value: DateTime | null | string;
}>(`${namespace}_ON_CHANGE_FORM_CONTENT_TEMPLATE`);
export const SELECT_FOLDER = createAction(`${namespace}_SELECT_FOLDER`);
export const ADD_TAGS_CUSTOMIZE = createAction<{
  value: string;
  type: string;
}>(`${namespace}_ADD_TAGS_CUSTOMIZE`);
export const REMOVE_TAGS_CUSTOMIZE = createAction<{
  value: string;
  type: string;
}>(`${namespace}_REMOVE_TAGS_CUSTOMIZE`);
export const FETCH_CREATE_JOB_SUCCESS = createAction<object>(
  `${namespace}_FETCH_CREATE_JOB_SUCCESS`
);
export const FETCH_CREATE_JOB_FAILURE = `${namespace}_FETCH_CREATE_JOB_FAILURE`;

export const ON_CHANGE_FORM_ENGINE_SELECTED = createAction<{
  engineId: string;
  name: string;
  value: string;
}>(`${namespace}_ON_CHANGE_FORM_ENGINE_SELECTED`);

export const ON_CHANGE_JOB_PRIORITY_ENGINE_SELECTED = createAction<{
  engineId: string;
  priority: number | undefined;
}>(`${namespace}_ON_CHANGE_JOB_PRIORITY_ENGINE_SELECTED`);

export const ON_CHANGE_LIBRARIES_ENGINE_SELECTED = createAction<{
  engineId: string;
  value: string;
}>(`${namespace}_ON_CHANGE_LIBRARIES_ENGINE_SELECTED`);
export const ON_CHANGE_EXPAND = createAction<{
  engineId: string | null;
  expand: boolean;
}>(`${namespace}_ON_CHANGE_EXPAND`);
export const ON_CHANGE_FILE_NAME_EDIT = createAction<{
  value: string;
}>(`${namespace}_ON_CHANGE_FILE_NAME_EDIT`);
export const ON_CHANGE_DATE_TIME_EDIT = createAction<{
  value: string;
}>(`${namespace}_ON_CHANGE_DATE_TIME_EDIT`);
export const SAVE_EDIT_FILE_UPLOAD = createAction(
  `${namespace}_SAVE_EDIT_FILE_UPLOAD`
);
export const FETCH_FOLDER_UPLOAD_REQUEST = createAction(
  `${namespace}_FETCH_FOLDER_UPLOAD_REQUEST`
);
export const SET_OPEN_FOLDER_UPLOAD = createAction<{
  folderId: string;
}>(`${namespace}_SET_OPEN_FOLDER_UPLOAD`);
export const UPDATE_SELECTED_FOLDER_UPLOAD = createAction<{
  selectedFolder: Folder;
}>(`${namespace}_UPDATE_SELECTED_FOLDER_UPLOAD`);
export const OPEN_MODAL_UPLOAD = createAction(`${namespace}_OPEN_MODAL_UPLOAD`);
export const CLOSE_MODAL_UPLOAD = createAction(
  `${namespace}_CLOSE_MODAL_UPLOAD`
);

export const SET_CURRENT_FOLDER = createAction<{
  folder: Folder;
}>(`${namespace}_SET_CURRENT_FOLDER`);
export const OPEN_MODAL_REPROCESS = createAction(
  `${namespace}_OPEN_MODAL_REPROCESS`
);
export const SAVE_REPROCESS_FILE = createAction(
  `${namespace}_SAVE_REPROCESS_FILE`
);
export const CHECK_LIMIT_AND_SAVE_REPROCESS_FILE = createAction(
  `${namespace}_CHECK_LIMIT_AND_SAVE_REPROCESS_FILE`
);
export const SHOW_MODAL_PROCESS_LIMIT_EXCEEDED = createAction<{
  value: boolean;
}>(`${namespace}_SHOW_MODAL_PROCESS_LIMIT_EXCEEDED`);
export const UPDATE_DAG_TEMPLATE_ID_SELECTED = createAction<{
  dagTemplateId: string;
}>(`${namespace}_UPDATE_DAG_TEMPLATE_ID_SELECTED`);
export const UPDATE_ENGINES_SELECTED = createAction<{
  enginesSelected: EnginesSelected[];
}>(`${namespace}_UPDATE_ENGINES_SELECTED`);
export const UPDATE_LOADING_SAVE_UPLOAD = createAction(
  `${namespace}_UPDATE_LOADING_SAVE_UPLOAD`
);

export const SAVE_UPLOAD_FILE = createAction<{
  dagTemplateId: string;
  workflow: 'simple' | 'advanced';
}>(`${namespace}_SAVE_UPLOAD_FILE`);
export const SHOW_CONFIRM_SAVE_TEMPLATE = createAction(
  `${namespace}_SHOW_CONFIRM_SAVE_TEMPLATE`
);
export const HIDE_CONFIRM_SAVE_TEMPLATE = createAction(
  `${namespace}_HIDE_CONFIRM_SAVE_TEMPLATE`
);
export const ON_CHANGE_TEMPLATE_NAME = createAction<{
  name: string;
}>(`${namespace}_ON_CHANGE_TEMPLATE_NAME`);
export const UPDATE_TEMPLATE_REQUEST = createAction(
  `${namespace}_UPDATE_TEMPLATE_REQUEST`
);
export const SET_DEFAULT_STATE_UPLOAD_FILE = createAction(
  `${namespace}_SET_DEFAULT_STATE_UPLOAD_FILE`
);

export const REMOVE_TEMPLATE_REQUEST = createAction<{
  id: string;
}>(`${namespace}_REMOVE_TEMPLATE_REQUEST`);
export const REMOVE_TEMPLATE_SUCCESS = createAction(
  `${namespace}_REMOVE_TEMPLATE_SUCCESS`
);
export const REMOVE_TEMPLATE_FAILED = createAction(
  `${namespace}_REMOVE_TEMPLATE_FAILED`
);

export const RUN_TEXT_ANALYTICS_REQUEST = createAction(
  `${namespace}_RUN_TEXT_ANALYTICS_REQUEST`
);
export const RUN_TEXT_ANALYTICS_SUCCESS = createAction(
  `${namespace}_RUN_TEXT_ANALYTICS_SUCCESS`
);
export const RUN_TEXT_ANALYTICS_FAILED = createAction(
  `${namespace}_RUN_TEXT_ANALYTICS_FAILED`
);

export const UPDATE_PERCENTAGE_FILES_UPLOADED = createAction<{
  percentage: number;
}>(`${namespace}_UPDATE_PERCENTAGE_FILES_UPLOADED`);

export const FETCH_DAG_TEMPLATES_REQUEST = createAction(
  `${namespace}_FETCH_DAG_TEMPLATES_REQUEST`
);
export const FETCH_DAG_TEMPLATES_SUCCESS = createAction<{
  dagTemplates: DagTemplate[];
}>(`${namespace}_FETCH_DAG_TEMPLATES_SUCCESS`);
export const FETCH_DAG_TEMPLATES_FAILED = createAction(
  `${namespace}_FETCH_DAG_TEMPLATES_FAILED`
);

export const ON_CLICK_DAG_TEMPLATE = createAction<{
  dagTemplateSelected: DagTemplate;
  categoryId: string;
}>(`${namespace}_ON_CLICK_DAG_TEMPLATE`);
export const UPDATE_SHOW_PROCESS_BY_CATEGORY = createAction<{
  categoryId: string;
  value: boolean;
}>(`${namespace}_UPDATE_SHOW_PROCESS_BY_CATEGORY`);

export const CATEGORY_IDS_TO_EXCLUDE = [
  '4fef6040-3fb6-4757-9aae-4044e8b46bc9', // Search
  '4be1a1b2-653d-4eaa-ba18-747a265305d8', // Ingestion
  'c1e5f177-ca10-433a-a155-bb5e4872cf9a', // Intercategory
  '925e8039-5246-4ced-9f2b-b456d0b57ea1', // Intracategory
  '988c1bbe-a537-46b5-80ce-415f39cc0e4d', // Media Aggregator
  'ffd8b41b-b28d-48fb-8acd-df0fdfd085b0', // Output Aggregator
  '4b150c85-82d0-4a18-b7fb-63e4a58dfcce', // Media (Pull)
  '0b10da6b-3485-496c-a2cb-aabf59a6352d', // Media (Push)
  'f951fbf9-aa69-47a2-87c8-12dfb51a1f18', // Cognition Utility
];

export const AUDIO_MIME_TYPES = [
  'audio/aac',
  'audio/flac',
  'audio/midi',
  'audio/mp4',
  'audio/mp3',
  'audio/mpeg',
  'audio/wav',
  'audio/x-wav',
  'audio/webm',
  'audio/x-m4a',
  'audio/x-ms-wma',
];

export const VIDEO_MIMES_TYPE = [
  'video/3gpp',
  'video/mp4',
  'video/mpeg',
  'video/ogg',
  'video/quicktime',
  'video/webm',
  'video/x-m4v',
  'video/x-ms-wmv',
  'video/x-msvideo',
];

export const IMAGE_MIMES_TYPE = ['image/png', 'image/jpeg', 'image/gif'];

export const DOCS_MIMES_TYPE = [
  'application/json',
  'application/msword',
  'application/pdf',
  'application/rtf',
  'application/smil+xml',
  'application/ttml+xml',
  'application/vnd.ms-outlook',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.oasis.opendocument.text',
  'application/vnd.oasis.opendocument.spreadsheet',
  'application/x-flv',
  'application/xml',
  'application/x-www-form-urlencoded',
];
export const MESSAGE_MIME_TYPE = ['message/rfc822'];
export const SUPPORTED_FORMATS_ENGINE = {
  // Speechmatics Transcription - English (Global) V3, supportedInputFormats "text/html", standaloneJobTemplates with glc-ingestor
  // 'c0e55cde-340b-44d7-bb42-2e0d65e98255': [
  //   ...VIDEO_MIMES_TYPE,
  //   ...AUDIO_MIME_TYPES,
  // ],
  // Speechmatics Transcription (v8.1) - Portuguese V3, supportedInputFormats "text/html", standaloneJobTemplates []
  'd36606a9-1006-4f8f-a0d1-095c78b1c8c9': [
    ...VIDEO_MIMES_TYPE,
    ...AUDIO_MIME_TYPES,
  ],
  // open-pdf-writer-v3f
  '62f2b2a7-bfd6-44c8-ab78-c02b47f95974': [
    ...DOCS_MIMES_TYPE,
    ...MESSAGE_MIME_TYPE,
  ],
  // Amazon - Transcription - (US East) V3, supportedInputFormats "text/html", standaloneJobTemplates []
  '796722b7-245b-4451-8e5f-c3534e6524f1': [
    ...VIDEO_MIMES_TYPE,
    ...AUDIO_MIME_TYPES,
  ],
  // Facebox Recognize engine
  'e62665c7-f855-4168-8aa3-668a7b0a50ea': [
    ...VIDEO_MIMES_TYPE,
    ...IMAGE_MIMES_TYPE,
  ],
  // Machinebox - Tagbox V3
  'd66f553d-3cef-4c5a-9b66-3e551cc48b4b': [
    ...VIDEO_MIMES_TYPE,
    ...IMAGE_MIMES_TYPE,
  ],
  // Amazon Rekognition - Object Recognition (USE) V3
  'e718f832-0f1b-4729-b661-ed42d56d367f': [
    ...VIDEO_MIMES_TYPE,
    ...IMAGE_MIMES_TYPE,
  ],
  // Amazon Rekognition Object  Custom Labels, supportedInputFormats "image/jpeg", standaloneJobTemplates []
  '48143c0d-854a-4640-be6e-56e59a6fa2c5': [
    ...VIDEO_MIMES_TYPE,
    ...AUDIO_MIME_TYPES,
  ],
  // Speechmatics Transcription (v8.1) - English V3
  'a8c4a9f4-d6c4-4524-ab98-c0de560abf9b': [
    ...VIDEO_MIMES_TYPE,
    ...IMAGE_MIMES_TYPE,
  ],
  // LogoGrab - Text Recognition (OCR)
  '6906f937-04dd-4276-81d9-95a3245da59e': [
    ...VIDEO_MIMES_TYPE,
    ...AUDIO_MIME_TYPES,
  ],
  // Google - OCR
  '1e5b4e36-acbe-49f1-bebf-1d3a0edd6219': [
    ...VIDEO_MIMES_TYPE,
    ...AUDIO_MIME_TYPES,
  ],
  // LogoGrab - Text Recognition (OCR) On Image
  '6906f937-04dd-4276-81d9-95a3245dafff': [...IMAGE_MIMES_TYPE],
};

export const CATEGORY_ID_TRANSLATE = '3b2b2ff8-44aa-4db4-9b71-ff96c3bf5923';

export const CATEGORY_ID_TRANSCRIPTION = '67cd4dd0-2f75-445d-a6f0-2f297d6cd182';

export const CATEGORY_ID_TEXT_EXTRACTION =
  'ba2a423e-99c9-4422-b3a5-0b188d8388ab';

export const ENGINE_OPEN_PDF = '62f2b2a7-bfd6-44c8-ab78-c02b47f95974';
export const ENGINE_FILE_TRANSLATOR = 'a38676f6-7557-4ba1-a544-c4020f800778';

export const ENGINE_TEXT_ANALYTICS = '114f8e78-2bba-4ed8-90d2-d83d74e3b6b6';

export const SIMPLE_COGNITIVE_WORKFLOW = 'illuminate-simple-workflow';

export const ENGINE_GLC_FAST_INGESTOR = 'da093aca-2a6b-4577-8bfe-2b19a2f2faea';

export const pick = (type: string) => PICK_START(type);

export const endPick = (type: string) => PICK_END({ type });

export const retryRequest = (callback?: () => void) =>
  RETRY_REQUEST({ callback });

export const abortRequest = (fileKey?: string) => ABORT_REQUEST({ fileKey });

export const retryDone = (callback?: () => void) => RETRY_DONE({ callback });

export const uploadRequest = ({
  files,
  callback,
}: {
  files: File[];
  callback?: () => void;
}) => UPLOAD_REQUEST({ files, callback });

export const uploadProgress = (fileKey: string, data: UploadProgress) =>
  UPLOAD_PROGRESS(
    {
      ...data,
      percent: clamp(Math.round(data.percent), 100),
    },
    { fileKey }
  );

export const uploadComplete = (
  result: UploadResult[] | null,
  { warning, error }: { warning?: string | boolean; error?: string | boolean }
) => UPLOAD_COMPLETE(result, { warning, error });

export const onSelectionChange = (
  value: number,
  type: UploadFileSelectionType
) => ON_SELECTION_CHANGE({ value, type });

export const removeFileUpload = (value: number[]) =>
  REMOVE_FILE_UPLOAD({ value });

export const showEditFileUpload = () => SHOW_EDIT_FILE_UPLOAD();

export const hideEditFileUpload = () => HIDE_EDIT_FILE_UPLOAD();

export const fetchEngineCategories = () => FETCH_ENGINE_CATEGORIES_REQUEST();

export const fetchEngineCategoriesSuccess = (
  engineCategories: EngineCategoriesResponse['engineCategories']['records']
) => FETCH_ENGINE_CATEGORIES_SUCCESS({ engineCategories });

export const fetchEngineCategoriesFailure = () =>
  FETCH_ENGINE_CATEGORIES_FAILURE();

export const fetchLibraries = () => FETCH_LIBRARIES_REQUEST();

export const fetchLibrariesSuccess = (libraries: Library[]) =>
  FETCH_LIBRARIES_SUCCESS({ libraries });

export const fetchLibrariesFailure = () => FETCH_LIBRARIES_FAILURE();

export const fetchEngines = () => FETCH_ENGINES_REQUEST();

export const fetchEnginesSuccess = (engines: Engine[]) =>
  FETCH_ENGINES_SUCCESS({ engines });

export const fetchEnginesFailure = () => FETCH_ENGINES_FAILURE();

export const addEngine = (engineId: string) => ADD_ENGINE({ engineId });

export const onChangeEngine = (engineId: string) => CHANGE_ENGINE({ engineId });

export const removeEngine = (engineId: string) => REMOVE_ENGINE({ engineId });

export const showModalSaveTemplate = (value: boolean) =>
  SHOW_MODAL_SAVE_TEMPLATE({ value });

export const hideModalSaveTemplate = (value: boolean) =>
  HIDE_MODAL_SAVE_TEMPLATE({ value });

export const saveTemplate = () => SAVE_TEMPLATE_REQUEST();

export const saveTemplateSuccess = () => SAVE_TEMPLATE_SUCCESS();

export const saveTemplateFailure = () => SAVE_TEMPLATE_FAILURE();

export const fetchTemplatesSuccess = (templates: Template[]) =>
  FETCH_TEMPLATES_SUCCESS({ templates });

export const fetchTemplatesFailure = () => FETCH_TEMPLATES_FAILURE();

export const onChangeTemplate = (templateId: string) =>
  CHANGE_TEMPLATE({ templateId });

export const fetchContentTemplates = () => FETCH_CONTENT_TEMPLATES_REQUEST();

export const fetchContentTemplatesSuccess = (
  contentTemplates: ContentTemplate[]
) => FETCH_CONTENT_TEMPLATES_SUCCESS({ contentTemplates });

export const fetchContentTemplatesFailure = () =>
  FETCH_CONTENT_TEMPLATES_FAILURE();

export const addContentTemplate = (contentTemplateId: string) =>
  ADD_CONTENT_TEMPLATE({ contentTemplateId });

export const removeContentTemplate = (contentTemplateId: string) =>
  REMOVE_CONTENT_TEMPLATE({ contentTemplateId });

export const onChangeFormContentTemplate = (
  contentTemplateId: string,
  name: string,
  value: DateTime | null | string
) => ON_CHANGE_FORM_CONTENT_TEMPLATE({ contentTemplateId, name, value });

export const selectFolder = () => SELECT_FOLDER();

export const addTagsCustomize = (value: string, type: string) =>
  ADD_TAGS_CUSTOMIZE({ value, type });

export const removeTagsCustomize = (value: string, type: string) =>
  REMOVE_TAGS_CUSTOMIZE({ value, type });

export const fetchCreateJobSuccess = () => FETCH_CREATE_JOB_SUCCESS({});

export const onChangeFormEngineSelected = (
  engineId: string,
  name: string,
  value: string
) => ON_CHANGE_FORM_ENGINE_SELECTED({ engineId, name, value });

export const onChangeJobPriorityEngineSelected = (
  engineId: string,
  priority: number | undefined
) => ON_CHANGE_JOB_PRIORITY_ENGINE_SELECTED({ engineId, priority });

export const onChangeLibrariesEngineSelected = (
  engineId: string,
  value: string
) => ON_CHANGE_LIBRARIES_ENGINE_SELECTED({ engineId, value });

export const onChangeExpand = (engineId: string | null, expand: boolean) =>
  ON_CHANGE_EXPAND({ engineId, expand });

export const onChangeFileNameEdit = (value: string) =>
  ON_CHANGE_FILE_NAME_EDIT({ value });

export const onChangeDateTimeEdit = (value: string) =>
  ON_CHANGE_DATE_TIME_EDIT({ value });

export const saveEditFileUpload = () => SAVE_EDIT_FILE_UPLOAD();

export const fetchFolderUpload = () => FETCH_FOLDER_UPLOAD_REQUEST();

export const setOpenFolderUpload = (folderId: string) =>
  SET_OPEN_FOLDER_UPLOAD({ folderId });

export const updateSelectedFolderUpload = (selectedFolder: Folder) =>
  UPDATE_SELECTED_FOLDER_UPLOAD({ selectedFolder });

export const openModalUpload = () => OPEN_MODAL_UPLOAD();

export const closeModalUpload = () => CLOSE_MODAL_UPLOAD();

export const setCurrentFolder = (folder: Folder) =>
  SET_CURRENT_FOLDER({ folder });

export const openModalReprocess = () => OPEN_MODAL_REPROCESS();

export const checkLimitAndSaveReprocessFile = () =>
  CHECK_LIMIT_AND_SAVE_REPROCESS_FILE();

export const saveReprocessFile = () => SAVE_REPROCESS_FILE();

export const showModalProcessLimitExceeded = (value: boolean) =>
  SHOW_MODAL_PROCESS_LIMIT_EXCEEDED({ value });

export const updateDagTemplateIdSelected = (dagTemplateId: string) =>
  UPDATE_DAG_TEMPLATE_ID_SELECTED({ dagTemplateId });

export const updateEnginesSelected = (enginesSelected: EnginesSelected[]) =>
  UPDATE_ENGINES_SELECTED({ enginesSelected });

export const updateLoadingSaveUpload = () => UPDATE_LOADING_SAVE_UPLOAD();

export const saveUploadFile = (
  dagTemplateId: string,
  workflow: 'simple' | 'advanced'
) => SAVE_UPLOAD_FILE({ dagTemplateId, workflow });

export const showConfirmSaveTemplate = () => SHOW_CONFIRM_SAVE_TEMPLATE();

export const hideConfirmSaveTemplate = () => HIDE_CONFIRM_SAVE_TEMPLATE();

export const onChangeTemplateName = (name: string) =>
  ON_CHANGE_TEMPLATE_NAME({ name });

export const updateTemplate = () => UPDATE_TEMPLATE_REQUEST();

export const setDefaultStateUploadFile = () => SET_DEFAULT_STATE_UPLOAD_FILE();

export const removeTemplate = (id: string) => REMOVE_TEMPLATE_REQUEST({ id });

export const removeTemplateSuccess = () => REMOVE_TEMPLATE_SUCCESS();

export const removeTemplateFailed = () => REMOVE_TEMPLATE_FAILED();

export const runTextAnalytics = () => RUN_TEXT_ANALYTICS_REQUEST();

export const runTextAnalyticsSuccess = () => RUN_TEXT_ANALYTICS_SUCCESS();

export const runTextAnalyticsFailed = () => RUN_TEXT_ANALYTICS_FAILED();

export const updatePercentageFilesUploaded = (percentage: number) =>
  UPDATE_PERCENTAGE_FILES_UPLOADED({ percentage });

export const fetchDagTemplatesRequest = () => FETCH_DAG_TEMPLATES_REQUEST();

export const fetchDagTemplatesSuccess = (dagTemplates: DagTemplate[]) =>
  FETCH_DAG_TEMPLATES_SUCCESS({ dagTemplates });

export const fetchDagTemplatesFailed = () => FETCH_DAG_TEMPLATES_FAILED();

export const onClickDagTemplate = (
  dagTemplateSelected: DagTemplate,
  categoryId: string
) => ON_CLICK_DAG_TEMPLATE({ dagTemplateSelected, categoryId });

export const showProcessByCategory = (categoryId: string, value: boolean) =>
  UPDATE_SHOW_PROCESS_BY_CATEGORY({ categoryId, value });
