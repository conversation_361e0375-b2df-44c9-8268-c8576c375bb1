import axios, { AxiosResponse } from 'axios';
import { ApiError } from '../errors';
import { Context } from '../../types';
import env from '../../../env';

export interface DatasourceCredentialsRequest {
  credentialDetails: {
    credentials: string;
    credentialType: string;
    encryptedConnection: string;
    encryptionAlgorithm: string;
    privacyLevel: string;
  };
}

const setDatasourceCredentials = async (context: Context) => {
  const { log, data } = context;
  const { powerbiApiRoot, powerbiApiVersionOrg, mssqlUser, mssqlPw } = env;

  try {
    await axios.patch<
      never,
      AxiosResponse<never>,
      DatasourceCredentialsRequest
    >(
      `${powerbiApiRoot}/${powerbiApiVersionOrg}/gateways/${data.gatewayId}/datasources/${data.datasourceId}`,
      {
        credentialDetails: {
          credentials: `{"credentialData":[{"name":"username","value":"${mssqlUser}"},{"name":"password","value":"${mssqlPw}"}]}`,
          credentialType: 'Basic',
          encryptedConnection: 'Encrypted',
          encryptionAlgorithm: 'None',
          privacyLevel: 'Private',
        },
      },
      {
        headers: {
          Authorization: data.pbiBearerToken,
          'X-PowerBI-Profile-Id': data.profileId,
        },
      }
    );

    return context;
  } catch (e) {
    log.error('Update Datasource Credentials API failed', e);
    throw new ApiError(e);
  }
};

export default setDatasourceCredentials;
