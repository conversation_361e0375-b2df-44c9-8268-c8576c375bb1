import fetch from './fetchRetry';

export default async function fetchGraphQLApi<T>({
  endpoint = 'https://api.veritone.com/v3/graphql',
  query,
  variables,
  operationName,
  token,
  veritoneAppId,
}: ApiCall): Promise<{ data: T; errors?: any[] }> {
  const headers: HeadersInit = {
    Authorization: token ? `bearer ${token}` : '',
    'Content-Type': 'application/json',
  };
  if (veritoneAppId) {
    headers['x-veritone-application'] = veritoneAppId;
  }

  const enableTimeWarnings = endpoint.includes('stage');
  const reqStartTime = Date.now();
  const result = await fetch(endpoint, {
    method: 'post',
    body: JSON.stringify({
      query,
      variables,
      operationName,
    }),
    headers,
    credentials: getCredentialsMode() as RequestCredentials,
  }).then((r) => r.json());

  const reqEndTime = Date.now();
  if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
    console.error(
      `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
      { endpoint, query: JSON.stringify({ query, variables }) }
    );
  }

  return result;
}

function getCredentialsMode() {
  return 'include';
}

interface ApiCall {
  endpoint: string;
  query: string;
  variables?: object;
  operationName?: string;
  token: string | null;
  veritoneAppId?: string;
}
