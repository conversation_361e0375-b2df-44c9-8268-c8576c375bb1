import React, { useState } from 'react';
import {
  Box,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from '@mui/material';
import { IndeterminateCheckBox, Edit } from '@mui/icons-material';
import { isEmpty } from 'lodash';

import { useStyles } from './useStyles';
import { BulkExportCustomizedNames, BulkExportField } from 'src/model';

// Used for saving the default values if none was passed and also as the label for renaming modal
const defaultCustomizedNames: BulkExportCustomizedNames = {
  tdoId: 'File ID',
  filename: 'Filename',
  foldername: 'Foldername',
  native: 'Native File Path',
  ttml: 'TTML File Path',
  ttmlEdited: 'Edited TTML File Path',
  plainText: 'Plaintext File Path',
  plainTextEdited: 'Edited Plaintext File Path',
  word: 'Word File Path',
  wordEdited: 'Edited Word File Path',
  speakerSeparation: 'Speaker Separation File Path',
  speakerSeparationEdited: 'Edited Speaker Separation File Path',
  speakerSeparationObjectNotation: 'Speaker Separation VTN Standard File Path',
  speakerSeparationObjectNotationEdited:
    'Edited Speaker Separation VTN Standard File Path',
  objectNotation: 'VTN Standard File Path',
  objectNotationEdited: 'Edited VTN Standard File Path',
  speakerSeparationWord: 'Speaker Separation Word File Path',
  speakerSeparationWordEdited: 'Edited Speaker Separation Word File Path',
  bookmark: 'Bookmark File Path',
  tag: 'Tag File Path',
  exportStatus: 'Export status',
  translation: 'Translation File Path',
  nativeTranslation: 'Native Translation File Path',
  sentiment: 'Sentiment File Path',
  baseFilename: 'Base Filename',
  objectDetection: 'Object Detection File Path',
  engineName: 'Transcription engine name',
  closedCaption: 'Closed Caption File Path',
};

const EditButton = ({
  customizedNameFields,
  customizedNames,
  onCustomizedNameChange,
}: EditButtonProps) => {
  const [open, setOpen] = useState(false);
  const [customizedNameState, setCustomizedNameState] = useState({
    ...customizedNames,
  });

  const handleOK = () => {
    const delta: BulkExportCustomizedNames = {};
    for (const customizedField of customizedNameFields) {
      if (
        customizedNameState[customizedField.field] !==
        customizedNames[customizedField.field]
      ) {
        delta[customizedField.field] =
          customizedNameState[customizedField.field];
      }
    }
    if (!isEmpty(delta)) {
      onCustomizedNameChange(delta);
    }
    setOpen(false);
  };

  const handleOnChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: BulkExportField
  ) => {
    setCustomizedNameState({ ...customizedNameState, [field]: e.target.value });
  };

  const handleCancel = () => {
    setCustomizedNameState({ ...customizedNames });
    setOpen(false);
  };
  const isCustomizedNameValid = Object.values(customizedNameState).every(
    (name) => isValidName(name)
  );
  return (
    <>
      <Edit color="action" fontSize="small" onClick={() => setOpen(true)} />
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="xs"
      >
        <DialogTitle>Rename fields</DialogTitle>
        <DialogContent>
          {customizedNameFields.map((customizedNameField) => {
            const customizedName =
              customizedNameState[customizedNameField.field] ?? '';
            return (
              <TextField
                required
                fullWidth
                key={customizedNameField.field}
                margin="dense"
                id={`fields-naming-${customizedNameField}`}
                data-testid={`fields-naming-${customizedNameField}`}
                className={`fields-naming-${customizedNameField}`}
                label={customizedNameField.label}
                value={customizedName}
                onChange={(e) => handleOnChange(e, customizedNameField.field)}
                variant="outlined"
                error={!isValidName(customizedName)}
                helperText={
                  isValidName(customizedName)
                    ? ' '
                    : 'invalid name. Minimum 3 characters required.'
                }
              />
            );
          })}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancel}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleOK}
            disabled={!isCustomizedNameValid}
          >
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export function GroupedCheckbox(props: GroupedCheckboxProps) {
  const {
    groupOfExportOption,
    exportOptionDetail,
    onExportOptionDetailChange,
    customizedNames,
    onCustomizedNameChange,
  } = props;

  const classes = useStyles();

  const getChecked = (exportType: string) => {
    return exportOptionDetail.exportOption
      .filter((item) => item.optionType === exportType)
      .every((fileProperty) => fileProperty.checked);
  };

  const getIndeterminate = (exportType: string) => {
    const filteredOptions = exportOptionDetail.exportOption.filter(
      (item) => item.optionType === exportType
    );
    return (
      !filteredOptions.every((fileProperty) => fileProperty.checked) &&
      filteredOptions.some((fileProperty) => fileProperty.checked)
    );
  };

  return (
    <>
      {groupOfExportOption.map(({ label, optionType }) => (
        <Box key={optionType}>
          <div>
            <FormGroup>
              <FormControlLabel
                className={classes.formControl}
                control={
                  <Checkbox
                    checked={getChecked(optionType)}
                    indeterminate={getIndeterminate(optionType)}
                    indeterminateIcon={
                      <IndeterminateCheckBox color="primary" />
                    }
                    onChange={(e) =>
                      onExportOptionDetailChange({
                        type: 'setAll',
                        setAll: e.target.checked,
                        optionType,
                      })
                    }
                    value={label}
                    name={label}
                  />
                }
                label={
                  <Typography className={classes.formControlTitle}>
                    {label}
                  </Typography>
                }
              />
              <div className={classes.checkboxContainer}>
                {exportOptionDetail.exportOption
                  .filter((item) => item.optionType === optionType)
                  .map((item) => (
                    <Box
                      key={item.field}
                      sx={{ display: 'flex', alignItems: 'center' }}
                    >
                      <FormControlLabel
                        className={classes.formControl}
                        control={
                          <Checkbox
                            checked={item.checked}
                            disabled={item.disabled}
                            onChange={() =>
                              onExportOptionDetailChange({
                                type: 'field',
                                field: item.field,
                                optionType,
                              })
                            }
                            value={item.name}
                            name={item.name}
                          />
                        }
                        label={
                          <Typography className={classes.formControlLabel}>
                            {item.name}
                          </Typography>
                        }
                        key={item.field}
                      />
                      {item.checked && item.customizedNameFields ? (
                        <EditButton
                          customizedNameFields={item.customizedNameFields}
                          customizedNames={pickCustomizedNames(
                            item.customizedNameFields,
                            customizedNames
                          )}
                          onCustomizedNameChange={onCustomizedNameChange}
                        />
                      ) : null}
                    </Box>
                  ))}
              </div>
            </FormGroup>
          </div>
        </Box>
      ))}
    </>
  );
}

function isValidName(name: string) {
  return name?.length >= 3;
}

function pickCustomizedNames(
  customizedNameFields: CustomizedNameField[],
  customizedNames: BulkExportCustomizedNames
): BulkExportCustomizedNames {
  const picked: BulkExportCustomizedNames = {};
  for (const customizedField of customizedNameFields) {
    picked[customizedField.field] =
      customizedNames[customizedField.field] ??
      defaultCustomizedNames[customizedField.field];
  }
  return picked;
}

interface EditButtonProps {
  customizedNameFields: CustomizedNameField[];
  customizedNames: BulkExportCustomizedNames;
  onCustomizedNameChange: (customizedNames: BulkExportCustomizedNames) => void;
}

interface CustomizedNameField {
  label: string;
  field: BulkExportField;
}

export interface ExportOptionDetail {
  exportOption: Array<{
    name: string;
    checked: boolean;
    disabled: boolean;
    field: BulkExportField;
    jsonField: string;
    optionType:
      | 'fileProperties'
      | 'transcription'
      | 'otherCognition'
      | 'optionsGroup'
      | 'optionsOther';
    customizedNameFields?: CustomizedNameField[];
  }>;
}

interface GroupedCheckboxProps {
  groupOfExportOption: { label: string; optionType: string }[];
  exportOptionDetail: ExportOptionDetail;
  customizedNames: BulkExportCustomizedNames;
  onCustomizedNameChange: (customizedNames: BulkExportCustomizedNames) => void;
  onExportOptionDetailChange: (param: {
    type: 'field' | 'setAll' | 'template';
    field?: BulkExportField;
    setAll?: boolean;
    optionType?: string;
  }) => void;
}
