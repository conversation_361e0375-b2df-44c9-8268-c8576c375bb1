import React from 'react';
import IconButton from '@mui/material/IconButton';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import CloseIcon from '@mui/icons-material/Close';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import SaveAlt from '@mui/icons-material/SaveAlt';
import CircularProgress from '@mui/material/CircularProgress';
import { STATUS } from 'state/modules/history';
import * as styles from './styles.scss';
import ErrorIconSvg from '../../../resources/images/icon_error.svg';
import CheckIconSvg from '../../../resources/images/icon_check_circle.svg';
import { Notification } from '../../model';
import { ListItemButton } from '@mui/material';
const iconStatusNotifications = {
  complete: <img src={CheckIconSvg} />,
  failed: <img src={ErrorIconSvg} />,
};
function NotificationsList({
  notifications,
  hideNotifications,
  removeNotification,
  handleDownload,
  loadingFetchUriExport,
  tdoIdDownload,
}: Props) {
  const totalList = notifications.filter((item) => !item.data.hide).length;
  return (
    <div className={styles.notificationContainer}>
      <div className={styles.notificationHeader}>
        <div className={styles.notificationCountTitle}>
          Last {totalList} Exports
        </div>
        <div className={styles.notificationCount}>{totalList}</div>
        <IconButton
          className={styles.notificationClose}
          onClick={hideNotifications}
          size="large"
        >
          <KeyboardArrowUpIcon />
        </IconButton>
      </div>

      <div className={styles.notificationContent}>
        {notifications
          .filter((item) => !item.data.hide)
          .map((item) => {
            if (item.data.status === 'pending') {
              return {
                ...item,
                data: {
                  ...item.data,
                  status: 'running',
                },
              };
            }
            return item;
          })
          .map((item) => {
            return (
              <ListItem key={item.id} disablePadding>
                <ListItemButton>
                  <ListItemIcon className={styles.notificationIcon}>
                    {!['complete', 'failed'].includes(item.data.status) ? (
                      <div className={styles.spinner} />
                    ) : (
                      iconStatusNotifications[
                        item.data.status as keyof typeof iconStatusNotifications
                      ]
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      item.data.exportName
                        ? item.data.exportName
                        : item.data.jobId
                    }
                    className={styles.notificationTitle}
                  />
                  <ListItemText
                    primary={item.data.status}
                    className={styles.notificationStatus}
                  />
                  <IconButton
                    data-tdo={item.data.tdoId}
                    data-name={item.data.exportName}
                    onClick={handleDownload}
                    data-status={item.data.status}
                    disabled={item.data.status !== STATUS.COMPLETE}
                    size="large"
                  >
                    {loadingFetchUriExport &&
                    tdoIdDownload === item.data.tdoId ? (
                      <CircularProgress size={24} variant="indeterminate" />
                    ) : (
                      <SaveAlt />
                    )}
                  </IconButton>
                  <IconButton
                    data-id={item.id}
                    onClick={removeNotification}
                    data-status={item.data.status}
                    size="large"
                  >
                    <CloseIcon className={styles.removeNotification} />
                  </IconButton>
                </ListItemButton>
              </ListItem>
            );
          })}
      </div>
    </div>
  );
}
export default NotificationsList;

interface Props {
  notifications: Array<Notification>;
  hideNotifications: () => void;
  removeNotification: (event: React.MouseEvent<HTMLButtonElement>) => void;
  handleDownload: (event: React.MouseEvent<HTMLButtonElement>) => void;
  loadingFetchUriExport: boolean;
  tdoIdDownload: string;
}
