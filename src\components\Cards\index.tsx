import React from 'react';
import { ConnectedProps, connect } from 'react-redux';
import {
  getTotalMediaProcessedTime,
  getDurationPerEngineClass,
} from 'state/modules/dashboard';
import * as style from './styles.scss';
import cx from 'classnames';
import DashboardCard from './DashboardCard';

export class Analy<PERSON><PERSON>eader extends React.Component<Props> {
  render() {
    const {
      totalDuration,
      totalNumber,
      isLoading,
      durPerEngineClass,
      totalMediaProcessedTime,
    } = this.props;
    return (
      <div className={cx(style['container'])}>
        <div className={cx(style['row'])}>
          <DashboardCard
            stylescard={cx(
              style['card-traffic-from-ads'],
              style['col-sm'],
              style['card-total-file']
            )}
            data={isLoading ? 'Loading...' : totalNumber.toLocaleString()}
            option={1}
          />
          <DashboardCard
            stylescard={cx(
              style['card-traffic-from-ads'],
              style['col-sm'],
              style['card-total-hours']
            )}
            data={isLoading ? 'Loading...' : totalDuration}
            option={2}
          />
          <DashboardCard
            stylescard={cx(
              style['card-traffic-from-ads'],
              style['col-sm'],
              style['card-total-process']
            )}
            data={isLoading ? 'Loading...' : totalMediaProcessedTime}
            option={3}
          />
          <DashboardCard
            stylescard={cx(
              style['card-traffic-from-ads'],
              style['col-sm'],
              style['card-total-process'],
              style['card-total-process-detail']
            )}
            data={isLoading ? 'Loading...' : totalDuration}
            option={4}
            durPerEngineClass={durPerEngineClass}
          />
        </div>
      </div>
    );
  }
}
type Props = PropsFromRedux & {
  totalDuration: string;
  totalNumber: number;
  isLoading: boolean;
};

const mapState = (state: any) => ({
  totalMediaProcessedTime: getTotalMediaProcessedTime(state),
  durPerEngineClass: getDurationPerEngineClass(state),
});

const connector = connect(mapState);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(AnalyzeHeader);
