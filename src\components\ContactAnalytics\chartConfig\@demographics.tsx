import { createRoot } from 'react-dom/client';
import * as am4core from '@amcharts/amcharts4/core';
import * as styles from './styles.scss';
import { DateTime } from 'luxon';
import { Config } from '../chartDefinitions';
import _ from 'lodash';

export default {
  dataQuery: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                %%conditions%%
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                { 
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              { field: "gender", operator: "term" }
              { field: "ethnicityExclusive", operator: "term" }
              { field: "genderNonconforming", operator: "term" }
            ]
          }
        ) {
          jsondata
        }
      }`,
  configure: (
    chartContainerId: string,
    data: DemographicsType,
    _name: string,
    _title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chartData: {
      race: { [key: string]: number };
      gender: { [key: string]: number };
      genderNonconforming: number;
    } = {
      race: data?.ethnicityExclusive?.buckets?.reduce(
        (acc, b) => ({
          ...acc,
          [b.key]: b.doc_count,
        }),
        {
          'Hispanic/Latino(a)': 0,
          White: 0,
          'Black/African American': 0,
          Asian: 0,
          'Middle Eastern or South Asian': 0,
          Multiracial: 0,
          'Pacific Islander': 0,
          'Native American': 0,
        }
      ),
      gender: data?.gender?.buckets?.reduce(
        (acc, b) => ({
          ...acc,
          [b.key]: b.doc_count,
        }),
        {
          Male: 0,
          Female: 0,
          'Trans Man/Boy': 0,
          'Trans Woman/Girl': 0,
        }
      ),
      genderNonconforming:
        data?.genderNonconforming?.buckets?.find(
          (b) => b.key_as_string === 'true'
        )?.doc_count ?? 0,
    };

    const { filterTextAll, useFilter, filter, filterTerms } = config;
    const colorSet = new am4core.ColorSet();
    const filterIndex = filterTerms?.findIndex((term) => term === filter) ?? 0;
    const filterColor =
      useFilter === 'all'
        ? '#222222'
        : colorSet.getIndex(filterIndex > 0 ? filterIndex : 0).toString();

    if (data) {
      const chartContainer = document.getElementById(
        `${chartContainerId}-demographics`
      )!;
      const chart = createRoot(chartContainer);
      chart.render(
        <div className={styles['demographics-table']}>
          <div className={styles['demographics-table-main-title']}>
            Demographic Information
          </div>
          {filterTextAll && (
            <div className={styles['demographics-table-filter-type-container']}>
              <span
                className={styles['demographics-table-filter-type-square']}
                style={{ backgroundColor: filterColor }}
              />
              <span className={styles['demographics-table-filter-type-title']}>
                {useFilter === 'all' ? filterTextAll : filter}
              </span>
            </div>
          )}
          <div className={styles['demographics-table-date-range']}>
            {`${DateTime.fromISO(config.lowerDateBound).toFormat(
              'M/d/yyyy'
            )} - ${DateTime.fromISO(config.upperDateBound).toFormat(
              'M/d/yyyy'
            )}`}
          </div>
          <div className={styles['demographics-table-title']}>By Race</div>
          <table className={styles['demographics-table']}>
            <tbody>
              {[
                'Hispanic/Latino(a)',
                'White',
                'Black/African American',
                'Asian',
                'Middle Eastern or South Asian',
                'Multiracial',
                'Pacific Islander',
                'Native American',
              ].map((k) => (
                <tr key={`DemographicsRow-Race-${k}`}>
                  <td className={styles['demographics-table-cell-value']}>
                    {chartData?.race[k]}
                  </td>
                  <td className={styles['demographics-table-cell-label']}>
                    {k}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <span className={styles['demographics-table-title']}>By Gender</span>
          <table className={styles['demographics-table']}>
            <tbody>
              {['Male', 'Female'].map((k) => (
                <tr key={`DemographicsRow-Gender-${k}`}>
                  <td className={styles['demographics-table-cell-value']}>
                    {chartData?.gender[k]}
                  </td>
                  <td className={styles['demographics-table-cell-label']}>
                    {k}
                  </td>
                </tr>
              ))}
              {['Trans Man/Boy', 'Trans Woman/Girl'].map((k) => (
                <tr key={`DemographicsRow-Sexuality-${k}`}>
                  <td className={styles['demographics-table-cell-value']}>
                    {chartData?.gender[k]}
                  </td>
                  <td className={styles['demographics-table-cell-label']}>
                    {k}
                  </td>
                </tr>
              ))}
              <tr>
                <td className={styles['demographics-table-cell-value']}>
                  {chartData.genderNonconforming}
                </td>
                <td className={styles['demographics-table-cell-label']}>
                  Gender Nonconforming
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }

    return { dispose: _.constant(null) };
  },
};

export interface DemographicsType {
  ethnicityExclusive: {
    buckets: {
      key: string;
      doc_count: number;
    }[];
    doc_count_error_upper_bound: number;
    sum_other_doc_count: number;
  };
  gender: {
    buckets: {
      key: string;
      doc_count: number;
    }[];
    doc_count_error_upper_bound: number;
    sum_other_doc_count: number;
  };
  genderNonconforming: {
    buckets: {
      key: number;
      doc_count: number;
      key_as_string: string;
    }[];
    doc_count_error_upper_bound: number;
    sum_other_doc_count: number;
  };
}
