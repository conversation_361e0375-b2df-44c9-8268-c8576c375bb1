import { isUndefined } from 'lodash';

export function formatBytes(bytes: number | undefined, decimals = 2) {
  if (isUndefined(bytes)) {
    return 'Size Undetected';
  }
  if (bytes === 0) {
    return '0 Bytes';
  }

  const k = 1024;
  const dm = Math.max(0, decimals);
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
