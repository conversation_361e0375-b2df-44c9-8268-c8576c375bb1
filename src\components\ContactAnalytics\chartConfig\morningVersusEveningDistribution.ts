import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            select: [""]
            limit: 1
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "00:00",
                  lt: "12:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'morningStops',
      dataKey: 'totalResults',
    },
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            select: [""]
            limit: 1
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: "timeOfStop",
                  operator: "range",
                  gte: "12:00",
                  lt: "24:00"
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [{
              operator: "count",
              distinct: false,
              field: "_id"
            }]
          }
        ) {
          jsondata
        }
      }`,
      storageKey: 'eveningStops',
      dataKey: 'totalResults',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Morning Versus Evening Distribution',
  filterTextType: 'Morning Versus Evening Distribution by Type',
  filterType: 'totalResults',
  filterTerms: {
    'Morning Hours (00:00-12:00)': {
      queryFilter: `{
          field: "timeOfStop",
          operator: "range",
          gte: "00:00",
          lt: "12:00"
      }`,
      storageKey: 'morningStops',
    },
    'Evening Hours (12:00-23:59)': {
      queryFilter: `{
          field: "timeOfStop",
          operator: "range",
          gte: "12:00",
          lt: "24:00"
        }`,
      storageKey: 'eveningStops',
    },
  },
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    // Add data
    chart.data = [
      {
        action: 'People stopped in Morning Hours (00:00-12:00)',
        amount: data.morningStops,
      },
      {
        action: 'People stopped in Evening Hours (12:00-23:59)',
        amount: data.eveningStops,
      },
    ];

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'amount';
    pieSeries.dataFields.category = 'action';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );

    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  morningStops: number;
  eveningStops: number;
}
