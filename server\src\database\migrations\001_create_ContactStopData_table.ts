import { Knex } from 'knex';

exports.up = (knex: Knex) =>
  Promise.all([
    knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
      if (!tableExists) {
        await knex.schema.raw(`CREATE TABLE ContactStopData (
          id int IDENTITY(1,1) PRIMARY KEY,
          recordId varchar(64),
          orgId smallint,
          basisForPropertySeizure varchar(512),
          officerYearsOfExperience tinyint,
          basisForSearchNarrative varchar(512),
          gender varchar(256),
          ethnicity varchar(2048),
          city varchar(256),
          disability varchar(512),
          timeOfStop time,
          reasonsForStop varchar(512),
          personNumber tinyint,
          k12_school bit,
          basisForSearch varchar(512),
          custodialArrestOffCode varchar(512),
          citationOffCode varchar(512),
          school varchar(512),
          warningOffCode varchar(512),
          responseToServiceCall bit,
          officerTypeOfAssignment varchar(512),
          inFieldCiteAndReleaseCode varchar(512),
          trafficViolationOffenseCode varchar(512),
          trafficViolation varchar(512),
          ecSubdivision varchar(512),
          durationOfStop smallint,
          reasonableSuspicion varchar(1024),
          suspicionOffenseCode varchar(512),
          limitedEnglish bit,
          disciplineUnderEc varchar(512),
          leaRecordId varchar(512),
          contrabandOrEvidence varchar(512),
          officerOtherAssignmentType varchar(512),
          resultOfStop varchar(1024),
          actionsTakenDuringStop varchar(2048),
          datetimeOfStop datetime,
          stopForAStudent bit,
          typeOfPropertySeized varchar(512),
          location varchar(256),
          genderNonconforming bit,
          reasonForStopNarrative varchar(512),
          age tinyint,
          ethnicityExclusive varchar(2048),
          lgbt bit
          );`
        );
        return true;
      }
    })
  ])

exports.down = (knex: Knex) =>
  Promise.all([
    knex.schema.dropTable('ContactStopData')
  ])
