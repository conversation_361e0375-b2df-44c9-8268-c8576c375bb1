import React from 'react';
import Customize from '../customize';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('Customize', () => {
  const props = {
    handleOpenFolder: jest.fn(),
    selectedFolder: {
      name: 'My Cases',
      treeObjectId: '1b9a2642-7b94-49d2-96be-42c42f0fdcbd',
    },
    onKeyPress: jest.fn(),
    handleOnChangeTagsCustomize: jest.fn(),
    tagsCustomizeName: '',
    tagsCustomize: [],
    handleRemoveTagsCustomize: jest.fn(),
    onClickAddTags: jest.fn(),
  };

  it('renders a title with content Ingestion Customization', () => {
    const { getByText } = render(<Customize {...props} />);
    expect(getByText('Ingestion Customization')).toBeInTheDocument();
  });
  it('renders a title with content Manage and help organize the location of the files being ingested.', () => {
    const { getByText } = render(<Customize {...props} />);
    expect(
      getByText(
        'Manage and help organize the location of the files being ingested.'
      )
    ).toBeInTheDocument();
  });
  it('renders a title with content Select Folder', () => {
    const { getByText } = render(<Customize {...props} />);
    expect(getByText('Select Folder')).toBeInTheDocument();
  });
  it('renders a title with content Choose a folder for these files.', () => {
    const { getByText } = render(<Customize {...props} />);
    expect(getByText('Choose a folder for these files.')).toBeInTheDocument();
  });
  it('renders a TextField', () => {
    const { getByTestId } = render(<Customize {...props} />);
    expect(getByTestId('select-folder')).toBeInTheDocument();
  });
  it('renders a description with content Label and group your ingested files by using keywords or terms to help describe them.', () => {
    const { getByText } = render(<Customize {...props} />);
    expect(
      getByText(
        'Label and group your ingested files by using keywords or terms to help describe them.'
      )
    ).toBeInTheDocument();
  });
  it('click add vailable engine', () => {
    const { getByTestId } = render(<Customize {...props} />);
    fireEvent.click(getByTestId('select-folder'));
    expect(props.handleOpenFolder).toHaveBeenCalled();
  });
  it('renders a TagsCustomize component', () => {
    const { getByTestId } = render(<Customize {...props} />);
    expect(getByTestId('tags-customize')).toBeInTheDocument();
  });
});
