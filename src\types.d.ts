/* cspell:words powerbi */
interface Window {
  analytics: unknown;
  pendo: any;
  readonly Worker: any;
  readonly __veritoneAppSingleton: any; // IVeritoneApp<any, any, any>;
  readonly config: {
    readonly apiRoot: string;
    readonly cmsPageUrl: string;
    readonly defaultEngineId: string;
    readonly switchAppRoute: string;
    readonly loginRoute: string;
    readonly graphQLEndpoint: string;
    readonly OAuthClientID: string;
    readonly engineIdBulkTag: string;
    readonly engineIdCreateJob: string;
    readonly engineIdExportDesktop: string;
    readonly enginesRequired?: {
      id: string;
      name: string;
    }[];
    readonly exportBatchSize: {
      hasNative: number;
      noNative: number;
    };
    readonly eventSchemaId: string;
    readonly tdoRedactStateSchemaId: string;
    readonly redactNotificationSchemaId: string;
    readonly cbsaCaseRegistryId: string;
    readonly cbsaCaseNotificationRegistryId: string;
    readonly segmentWriteKey: string;
    readonly pendoKey: string;
    readonly sentryDSN: string;
    readonly sentry?: {
      DSN: string;
      tracing: boolean;
    };
    readonly nodeEnv: string;

    readonly glcIngestionEngineId: string;
    readonly fastChunkerEngineId: string;

    readonly redactEngineId: string;

    readonly downloadEngineCategoryId: string;
    readonly downloadEngineId: string;

    readonly opticalTrackingEngineCatagoryId: string;
    readonly opticalTrackingEngineCategoryId: string;
    readonly opticalTrackingEngineId: string;

    readonly detectionCategory: string;
    readonly detectionEngineId: string;

    readonly transcriptionCategoryId: string;
    readonly transcriptionEngineId: string;

    readonly fastSpringStorefrontUrl: string;
    readonly requestQuoteUrl: string;
    readonly signUpRoute: string;

    readonly defaultClusterId: string;

    readonly exportTemplateDataRegistryId: string;

    readonly featureFlags?: { [key: string]: boolean };
    readonly veritoneAppId: string;
    readonly aiwareJSPath: string;
    readonly aiwareJSVersion: string;
    readonly redactDataRegistryId: string;
    readonly redactUrl: string;
    readonly schemasRequired?: {
      name: string;
    };
    readonly coreAdminUrl: string;
    readonly moveDeleteFlag?: boolean;
    readonly useOAuthGrant: boolean;
  }; // Config;
  readonly __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: any; // compose
  readonly mediaPlayer: {
    playbackRate: number;
    forward(seconds: number): void;
    play(): void;
    pause(): void;
    replay(seconds: number): void;
    seek(seconds: number): void;
  };
  aiware: any;
  isAiwareInitialized?: boolean;
  chatWithSupport?: () => void;
}

declare module '*.scss' {
  const content: { [className: string]: string };
  export = content;
}

declare module '*.svg' {
  const image: string;
  export default image;
}

declare module 'redux-first-router-link' {
  export default class Link extends React.Component<any> {}
}

declare module 'react-select/lib/Creatable';

// declare module 'video-react' {
//   const operationReducer: any;
//   const playerReducer: any;
//   const BigPlayButton: any; // React.Component
//   const CurrentTimeDisplay: any; // React.Component
//   const DurationDisplay: any; // React.Component
//   const ForwardControl: any; // React.Component
//   const FullscreenToggle: any; // React.Component
//   const PlaybackRateMenuButton: any; // React.Component
//   const PlayToggle: any; // React.Component
//   const Player: any; // React.Component
//   const ProgressControl: any; // React.Component
//   const RemainingTimeDisplay: any; // React.Component
//   const ReplayControl: any; // React.Component
//   const TimeDivider: any; // React.Component
//   const VolumeMenuButton: any; // React.Component
//   const LoadingSpinner: any; // React.Component
// }

declare module '*.mp3';

declare module '*.png';

declare module 'redux-mock-store';

declare module 'react-test-renderer';

declare module 'powerbi';
