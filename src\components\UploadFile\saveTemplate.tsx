import React from 'react';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
function SaveTemplate({
  open,
  handleSave,
  handleClose,
  onChange,
  templateName,
}: Props) {
  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md">
      <DialogTitle id="form-dialog-title">Save Process Template</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Please enter a name for this template
        </DialogContentText>
        <TextField
          autoFocus
          label="Template Name"
          data-test="template-engine-name"
          fullWidth
          onChange={onChange}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          color="primary"
          disabled={templateName ? false : true}
          data-test="save-template-engine"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
interface Props {
  open: boolean;
  handleSave: () => void;
  handleClose: () => void;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  templateName: string;
}
export default SaveTemplate;
