import { bookmarkName } from '../fixtures/variables';

export const mediaListPage = {
  goToMediaListPage: (): void => {
    cy.contains('ILLUMINATE').should('be.visible');
    cy.GoToTestFolder();
    cy.get('[data-testid^=files-table-row]').should('be.visible');
  },

  highlightsTextForBookmark: (mediaName: string): void => {
    cy.contains(mediaName).click();
    cy.get('[data-veritone-component="mdp_header_close_button"]')
      .prev()
      .click();
    // TODO add a attribute data-veritone-component="mdp_bookmark_button"
    cy.get('[data-veritone-component="mdp-engine-action-header"] button')
      .last()
      .click();

    // TODO: Refactor command chain more robust.
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get(
      '[data-veritone-component="transcription-engine-output-content"] span:first'
    )
      .trigger('mousedown', { force: true })
      .then(($el) => {
        const el = $el[0];

        if (el) {
          const doc = el.ownerDocument;
          const range = doc.createRange();
          range.selectNodeContents(el);
          const selection = doc.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
            return $el;
          }
        }
        cy.log(
          'Could not create text selection for bookmark because the target element or selection object was not available.'
        );
        return $el;
      })
      .trigger('mouseup', { force: true });
    cy.document().trigger('selectionchange', { force: true });
  },

  verifyBookmark: (mediaName: string): void => {
    cy.contains(mediaName).click({ force: true });
    cy.get('[data-veritone-component="mdp_header_close_button"]')
      .prev()
      .click();
    // TODO add a attribute data-veritone-component="mdp_bookmark_button"
    cy.get('[data-veritone-component="mdp-engine-action-header"] button')
      .last()
      .click();
    cy.contains(bookmarkName);
    cy.contains('note1');
  },

  exportFile: () => {
    cy.get('[data-testid^=files-table-row]')
      .eq(0)
      .find('input[type="checkbox"]')
      .check();
    cy.get('[data-test="files-bulk-export-icon-button"]').click();
    cy.contains('Filename').click();
    cy.get('[data-test="export-select-button"]').click();
    cy.contains(
      'Your export job has been submitted and is currently processing. You will receive a notification upon its completion.'
    );
  },

  exportAllItems: () => {
    cy.get('[data-testid="select-all"]').find('input[type="checkbox"]').check();
    cy.get('[data-test="files-bulk-export-icon-button"]').click();
    cy.contains('Filename').click();
    cy.get('[data-test="export-select-button"]').click();
    cy.contains(
      'Your export job has been submitted and is currently processing. You will receive a notification upon its completion.'
    );
  },

  verifyMediaDetailAudio: (audioName: string, audioTranscript: string) => {
    cy.get('[data-testid^=files-table-row]').contains(audioName).click();
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get('[data-veritone-component="transcription-engine-output-content"]')
      .scrollIntoView()
      .contains(audioTranscript);
  },

  verifyMediaDetailVideo: (videoName: string, videoTranscript: string) => {
    cy.get('[data-testid^=files-table-row]').contains(videoName).click();
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get('[data-veritone-component="transcription-engine-output-content"]')
      .scrollIntoView()
      .contains(videoTranscript);
  },

  send1TdoToRedact: () => {
    cy.get('[data-testid^=files-table-row]')
      .eq(0)
      .find('input[type="checkbox"]')
      .check();
    cy.get('[data-test="files-send-to-redact-icon-button"]').click();
    cy.contains('Sent 1 files to Redact');
  },

  sendAllTdosToRedact: () => {
    cy.get('[data-testid="select-all"]').find('input[type="checkbox"]').check();
    cy.get('[data-test="files-send-to-redact-icon-button"]').click();
    cy.contains('Sent 3 files to Redact');
  },

  searchByKeyword: (keyword: string, fileName: string) => {
    cy.get('[data-test="main-entity-search"]').click();
    cy.get('#transcript_search_field').type(keyword);
    cy.get('.transcriptSubmit').click();
    cy.contains('Close').click();
    cy.contains('FILES(1)');
    cy.contains(fileName);
    cy.get('[data-test="main-entity-search"]')
      .find('span')
      .next()
      .next()
      .click();
  },

  searchByTag: (
    tag: string,
    fileName: string,
    options?: {
      exclude?: boolean;
    }
  ) => {
    cy.get('[data-test="main-entity-search"]').click();
    cy.get('.icon-tag').click();
    cy.get('[placeholder="Type to search"]').as('inputElement').type(tag);
    cy.get('@inputElement')
      .parent()
      .parent()
      .next()
      .as('results')
      .contains('Results');
    cy.get('@results').contains(tag).click();
    if (options?.exclude) {
      cy.get('input[type="checkbox"]').check({ force: true });
    }
    cy.get('.transcriptSubmit').click();
    cy.contains('Close').click();
    cy.contains('FILES');
    cy.contains(fileName);
    cy.get('[data-test="main-entity-search"]')
      .find('span')
      .next()
      .next()
      .click();
  },

  searchByBookmark: (bookmark: string, fileName: string) => {
    cy.get('[data-test="main-entity-search"]').click();
    cy.get('.icon-third-party-data').click();
    cy.get('[placeholder="All Schemas"]').type('bookma');
    cy.contains('Bookmark v').click();
    cy.get('[placeholder="Search by property"]').type('bookmarkNa');
    cy.contains('bookmarkName').click();
    cy.contains('contains').parent().parent().parent().next().type(bookmark);
    cy.contains(bookmarkName).click();
    cy.get('.transcriptSubmit').click();
    cy.contains('Close').click();
    cy.contains('FILES(1)');
    cy.contains(fileName);
    cy.get('[data-test="main-entity-search"]')
      .find('span')
      .next()
      .next()
      .click();
  },

  searchByTime: (startTime: string, endTime: string) => {
    cy.get('[data-test="appbarSearch"]').click();
    cy.get('[data-test="engineCategoryList"] [title="Search by Time"]').click();
    cy.get('[data-test="engine-icon-calendar"]').first().click();
    cy.get('.dayPartStartTimeInput input').type(startTime);
    cy.get('.dayPartEndTimeInput input').type(endTime);
    cy.get('[data-test="searchModalActionBtn"]').contains('Add').click();
    cy.get('[data-test="searchModalActionBtnClose"]').click();
  },

  clicksOnTheSearchBar: () => {
    cy.get('[data-test="appbarSearch"]').click();
  },

  clicksButtonOnSearchBarPopup: (buttonText: string) => {
    if (buttonText === 'Close') {
      cy.get('[data-test="searchModalActionBtnClose"]').click();
    } else if (buttonText === 'Add') {
      cy.get('[data-test="searchModalActionBtn"]').click();
    }
  },

  searchByExcludingTag: (tagName: string) => {
    cy.get('[data-test="appbarSearch"]').click();
    cy.get('[data-test="engineCategoryList"] [title="Search by Tag"]').click();
    const partialTag = tagName.split('-')[0]!;
    cy.get('[placeholder="Type to search"]').type(partialTag);
    cy.contains(tagName).click();
    cy.contains('label', 'Exclude').find('input[type="checkbox"]').click();
    cy.get('[data-test="searchModalActionBtn"]').contains('Add').click();
    cy.get('[data-test="searchModalActionBtnClose"]').click();
    cy.get('[role="document"]').should('not.exist');
  },

  addSearchCriteria: (dataTable: any) => {
    cy.get('[data-test="appbarSearch"]').click();
    dataTable.hashes().forEach((row: any) => {
      const searchType = row.type;
      const searchValue = row.value;
      if (searchType && searchValue) {
        cy.get(
          `[data-test="engineCategoryList"] [title="Search by ${searchType}"]`
        ).click();
        cy.get('body')
          .find('input')
          .filter(':visible')
          .first()
          .type(searchValue);
        cy.get('[data-test="searchModalActionBtn"]').contains('Add').click();
      }
    });
    cy.get('[data-test="searchModalActionBtnClose"]').click();
    cy.get('[role="document"]').should('not.exist');
  },

  removeSearch: (keyword: string) => {
    cy.contains('[role="button"]', keyword)
      .find('svg[class*="deleteIcon"]')
      .click();
  },

  clickOnFile: (fileName: string) => {
    cy.get(`[data-testid^="files-table-row"]`).contains(fileName).click();
  },

  selectCategory: (categoryName: string) => {
    cy.get(`[aria-label="${categoryName}"]`).closest('button').click();
  },

  verifySearchBarPopupDisplayed: () => {
    cy.get('[role="document"]').should('be.visible');
  },

  verifySearchBarPopupNotVisible: () => {
    cy.get('[role="document"]').should('not.exist');
  },

  verifyPopupContainsSearchableOptions: () => {
    cy.get('[data-test="engineCategoryCard"]').should('exist');
  },

  verifyDefaultFocus: (text: string) => {
    cy.get('[data-veritone-element="search-category-label-transcript"]').should(
      'have.text',
      text
    );
    cy.get(`[title="${text}"]`)
      .should('have.attr', 'style')
      .and('include', 'background-color');
  },

  verifyTextDisplayed: (text: string) => {
    cy.contains(text).should('be.visible');
  },

  verifyTextBoxExists: (placeholder: string) => {
    cy.get('#transcript_search_field')
      .should('exist')
      .should('have.attr', 'placeholder', placeholder);
  },

  verifyButtonsDisplayed: (closeButtonText: string, addButtonText: string) => {
    cy.get('[data-test="searchModalActionBtnClose"]')
      .should('exist')
      .should('have.text', closeButtonText);
    cy.get('[data-test="searchModalActionBtn"]')
      .should('exist')
      .should('have.text', addButtonText);
  },

  verifyFileListDisplayed: () => {
    cy.get('[data-testid="file-paging"]').should('be.visible');
  },

  verifyFileListMessage: (message: string) => {
    cy.get('tbody').contains(message).should('be.visible');
    cy.get('[data-testid="file-paging"]')
      .contains('0–0 of 0')
      .should('be.visible');
  },

  verifyFileInList: (fileName: string, negation?: string) => {
    if (negation) {
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid^="files-table-row"]').length) {
          cy.get('[data-testid^="files-table-row"]')
            .contains(fileName)
            .should('not.exist');
        } else {
          cy.get('[data-testid^="files-table-row"]').should('not.exist');
        }
        return null;
      });
    } else {
      cy.get(`[data-testid^="files-table-row"]`)
        .contains(fileName)
        .should('be.visible');
    }
  },

  verifyMediaDetailsPageDisplayed: () => {
    cy.get('#media-details-page-content').should('be.visible');
  },

  verifyTranscriptHighlightedKeywords: (dataTable: any) => {
    dataTable.rows().forEach((row: string[]) => {
      const keyword = row[0];
      if (keyword) {
        cy.get(`span[class*="_highlightSearch"]`)
          .contains(keyword)
          .should('be.visible')
          .then(($el: JQuery<HTMLElement>) => {
            const element = $el[0]!;
            const computedStyle = window.getComputedStyle(element);
            expect(computedStyle.backgroundColor).to.equal(
              'rgb(255, 225, 104)'
            );
            return null;
          });
      }
    });
  },

  verifyTranscriptFocusOnKeyword: (keyword: string) => {
    cy.get(`span[class*="_highlightSearch"]`)
      .first()
      .should('contain.text', keyword)
      .then(($el: JQuery<HTMLElement>) => {
        const element = $el[0]!;
        const computedStyle = window.getComputedStyle(element);
        expect(computedStyle.backgroundColor).to.equal('rgb(255, 225, 104)');
        return null;
      });
  },

  verifyOCRResultsContainText: (ocrText: string) => {
    cy.get('[data-testid="ocr-container"]:visible').should(
      'contain.text',
      ocrText
    );
  },

  addsTagTo1TDO: () => {
    cy.get('[data-testid^=files-table-row]')
      .eq(0)
      .as('selectedTdo')
      .find('input[type="checkbox"]')
      .check();
    cy.get('[data-test="files-bulk-tag-icon-button"]').click();
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get('[data-test="tag-autocomplete"]')
      .find('input')
      .type(newTag, { force: true })
      .type('{enter}');
    cy.get('[data-test="tag-dialog-save-button"]').click();
    cy.contains('Successfully tagged files').next().click();
    cy.get('@selectedTdo').contains(newTag);
  },

  deletesTagFrom1TDO: () => {
    cy.get('[data-testid^=files-table-row]')
      .eq(0)
      .as('selectedTdo')
      .find('input[type="checkbox"]')
      .check();
    cy.get('[data-test="files-bulk-tag-icon-button"]').click();
    cy.get('[data-test="tag-autocomplete"]').contains(newTag).next().click();
    cy.get('[data-test="tag-dialog-save-button"]').click();
    cy.contains('Successfully tagged files').next().click();
    cy.get('@selectedTdo').contains(newTag).should('not.exist');
  },

  getFilesTab: () => {
    return cy.get('[data-test="files-tab-button"]');
  },

  getTableHeaders: () => {
    return cy.get(
      'thead th[scope="col"]:not(:has([data-testid="select-all"]))'
    );
  },

  verifyFileTableRows: () => {
    cy.get('[data-testid^=files-table-row]').each(($row) => {
      cy.wrap($row).within(() => {
        cy.get('td').should('have.length', 9);
        cy.get('td').should('exist');
      });
    });
  },

  getPagination: () => {
    return cy.get('[data-testid="file-paging"]');
  },

  sortColumn: (columnName: string, sortedBy: 'a-z' | 'z-a') => {
    cy.contains('th', 'Type').find('span[role="button"]').click();
    cy.get('[data-testid^=files-table-row]').should('have.length.gt', 0);
    cy.contains('th', columnName).find('span[role="button"]').click();
    cy.get('[data-testid^=files-table-row]').should('have.length.gt', 0);
    if (sortedBy === 'z-a') {
      cy.contains('th', columnName).find('span[role="button"]').click();
      cy.get('[data-testid^=files-table-row]').should('have.length.gt', 0);
    }
    return cy.wrap(null);
  },
};

const newTag = `tag${Date.now()}`;
