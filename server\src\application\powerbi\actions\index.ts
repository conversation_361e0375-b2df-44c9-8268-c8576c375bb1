import send from './send';
import sendError from './sendError';
import validateToken from './validateToken';
import validateRequest from './validateRequest';
import refreshServicePrincipalToken from './refreshServicePrincipalToken';
import createProfile from './createProfile';
import createWorkspace from './createWorkspace';
import assignCapacityToWorkspace from './assignCapacityToWorkspace';
import refreshAzureBlobStorageToken from './refreshAzureBlobStorageToken';
import getPbixTemplate from './getPbixTemplate';
import getCapacities from './getCapacities';
import getDatasetsInWorkspace from './getDatasetsInWorkspace';
import uploadPbixToWorkspace from './uploadPbixToWorkspace';
import addSuperUserToWorkspace from './addSuperUserToWorkspace';
import setDatasetDatasource from './setDatasetDatasource';
import getDatasourceGateway from './getDatasourceGateway';
import setDatasourceCredentials from './setDatasourceCredentials';
import generateEmbedToken from './generateEmbedToken';
import getReportsInWorkspace from './getReportsInWorkspace';
import cleanupTmp from './cleanupTmp';
import registerTokenConfig from './registerTokenConfig';
import getAiwareOrgId from './getAiwareOrgId';
import fetchTokenConfig from './fetchTokenConfig';
import checkForTokenConfig from './checkForTokenConfig';
import pushContactDataRows from './pushContactDataRows';
import getTemplates from './getTemplates';
import updateTemplate from './updateTemplate';
import registerTemplate from './registerTemplate';
import uploadPbixToBlobStorage from './uploadPbixToBlobStorage';
import checkForRequestedTemplate from './checkForRequestedTemplate';
import checkIsRoot from './checkIsRoot';
import pushQuestionDefinitionsRows from './pushQuestionDefinitionsRows';
import findQuestionDefinitions from './findQuestionDefinitions';
import cleanupPowerBI from './cleanupPowerBI';
import validateJwtToken from './validateJwtToken';
import checkForTokenConfigs from './checkForTokenConfigs';
import bulkAssociateTemplate from './bulkAssociateTemplate';

// Useful code for debugging
const DEBUG_LOGS = false;
const actionWrapper = (actionFn: (ctx: any) => any) => {
  return function (context?: any) {
    if (DEBUG_LOGS) {
      console.log(actionFn.name);
    }
    return actionFn(context);
  };
};

export default {
  send: actionWrapper(send),
  sendError: actionWrapper(sendError),
  validateToken: actionWrapper(validateToken),
  validateRequest: actionWrapper(validateRequest),
  refreshServicePrincipalToken: actionWrapper(refreshServicePrincipalToken),
  createProfile: actionWrapper(createProfile),
  createWorkspace: actionWrapper(createWorkspace),
  assignCapacityToWorkspace: actionWrapper(assignCapacityToWorkspace),
  refreshAzureBlobStorageToken: actionWrapper(refreshAzureBlobStorageToken),
  getPbixTemplate: actionWrapper(getPbixTemplate),
  getCapacities: actionWrapper(getCapacities),
  getDatasetsInWorkspace: actionWrapper(getDatasetsInWorkspace),
  uploadPbixToWorkspace: actionWrapper(uploadPbixToWorkspace),
  addSuperUserToWorkspace: actionWrapper(addSuperUserToWorkspace),
  setDatasetDatasource: actionWrapper(setDatasetDatasource),
  getDatasourceGateway: actionWrapper(getDatasourceGateway),
  setDatasourceCredentials: actionWrapper(setDatasourceCredentials),
  generateEmbedToken: actionWrapper(generateEmbedToken),
  getReportsInWorkspace: actionWrapper(getReportsInWorkspace),
  cleanupTmp: actionWrapper(cleanupTmp),
  registerTokenConfig: actionWrapper(registerTokenConfig),
  getAiwareOrgId: actionWrapper(getAiwareOrgId),
  fetchTokenConfig: actionWrapper(fetchTokenConfig),
  checkForTokenConfig: actionWrapper(checkForTokenConfig),
  pushContactDataRows: actionWrapper(pushContactDataRows),
  getTemplates: actionWrapper(getTemplates),
  updateTemplate: actionWrapper(updateTemplate),
  registerTemplate: actionWrapper(registerTemplate),
  uploadPbixToBlobStorage: actionWrapper(uploadPbixToBlobStorage),
  checkForRequestedTemplate: actionWrapper(checkForRequestedTemplate),
  checkIsRoot: actionWrapper(checkIsRoot),
  pushQuestionDefinitionsRows: actionWrapper(pushQuestionDefinitionsRows),
  findQuestionDefinitions: actionWrapper(findQuestionDefinitions),
  cleanupPowerBI: actionWrapper(cleanupPowerBI),
  validateJwtToken: actionWrapper(validateJwtToken),
  checkForTokenConfigs: actionWrapper(checkForTokenConfigs),
  bulkAssociateTemplate: actionWrapper(bulkAssociateTemplate),
};
