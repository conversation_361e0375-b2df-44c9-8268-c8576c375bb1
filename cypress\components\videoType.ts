export const videoType = [
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/3gpp',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/mp4',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/mpeg',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/ogg',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/quicktime',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/webm',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/x-m4v',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/x-ms-wmv',
  },
  {
    field: 'fileType',
    operator: 'term',
    value: 'video/x-msvideo',
  },
];
