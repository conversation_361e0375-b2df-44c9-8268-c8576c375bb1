import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import Demographics, { DemographicsType } from './@demographics';
import { Config } from '../chartDefinitions';

const config = {
  dataQueries: [
    {
      query: `query search(
        $lowerDateBound: String
        $upperDateBound: String
        $schemaId: String
        $filterType: String
        $filterTerms: [String]
      ) {
        searchMedia(
          search: {
            index: ["mine"]
            type: $schemaId
            query: {
              operator: "and"
              conditions: [
                {
                  field: "datetimeOfStop"
                  operator: "range"
                  gte: $lowerDateBound
                  lte: $upperDateBound
                }
                {
                  field: $filterType
                  operator: "terms"
                  values: $filterTerms
                }
              ]
            }
            aggregate: [
              {
                name: "reasonsForStop"
                field: "reasonsForStop"
                operator: "term"
                limit: 10000
              }
            ]
          }
        ) {
          jsondata
        }
      }`,
      isAggregation: true,
      storageKey: 'reasonsForStopAggregation',
      dataKey: 'reasonsForStop',
    },
  ],
  demographicsConfig: Demographics,
  hasDemographics: true,
  hasFilters: true,
  filterTextAll: 'All Primary Reason Distribution',
  filterTextType: 'Primary Reason Distribution by Type',
  filterType: 'reasonsForStop',
  filterTerms: [
    'Traffic Violation',
    'Reasonable suspicion that the person was engaged in criminal activity',
    'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
    'Knowledge of outstanding arrest warrant/wanted person',
    'Investigation to determine whether the person was truant',
    'Consensual Encounter resulting in a search',
    'Possible conduct warranting discipline under Education Code sections 48900, et al',
    'Determine whether the student violated school policy',
  ],
  configure: (
    chartContainerId: string,
    data: Data,
    name: string,
    title: string,
    config: Config
  ) => {
    if (!document.getElementById(chartContainerId)) {
      return;
    }
    const chart = am4core.create(chartContainerId, am4charts.PieChart);

    const dataMap: { [key: string]: number } = {
      'Traffic Violation': 0,
      'Reasonable suspicion that the person was engaged in criminal activity': 0,
      'Known to be on Parole / Probation / PRCS / Mandatory Supervision': 0,
      'Knowledge of outstanding arrest warrant/wanted person': 0,
      'Investigation to determine whether the person was truant': 0,
      'Consensual Encounter resulting in a search': 0,
      'Possible conduct warranting discipline under Education Code sections 48900, et al': 0,
      'Determine whether the student violated school policy': 0,
      ...data.reasonsForStopAggregation.reduce(
        (acc: { [key: string]: number }, b) => {
          acc[b.key] = b.doc_count;
          return acc;
        },
        {} // this reduce need to return an object to update default values
      ),
    };

    // data.reasonsForStopAggregation is an empty array
    const chartData: { primaryReasonType: string; numberOfPeople: number }[] =
      [];

    [
      'Traffic Violation',
      'Reasonable suspicion that the person was engaged in criminal activity',
      'Known to be on Parole / Probation / PRCS / Mandatory Supervision',
      'Knowledge of outstanding arrest warrant/wanted person',
      'Investigation to determine whether the person was truant',
      'Consensual Encounter resulting in a search',
      'Possible conduct warranting discipline under Education Code sections 48900, et al',
      'Determine whether the student violated school policy',
    ].forEach((pr) => {
      chartData.push({
        primaryReasonType: pr,
        numberOfPeople: dataMap[pr] ?? 0,
      });
    });

    chart.data = chartData;

    // Add and configure Series
    const pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = 'numberOfPeople';
    pieSeries.dataFields.category = 'primaryReasonType';

    // Enable Export
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.container = document.getElementById(
      `chart-section-export-button-${chartContainerId}`
    )!;
    chart.exporting.filePrefix = name;

    if (config.useLegend) {
      chart.legend = new am4charts.Legend();
    }

    if (config.useTitle) {
      const chartTitle = chart.titles.create();
      chartTitle.text = title;
      chartTitle.fontSize = 18;
      chartTitle.marginBottom = 20;
    }

    Demographics.configure(
      chartContainerId,
      data.demographics,
      name,
      title,
      config
    );
    return chart;
  },
};

export default config;

interface Data {
  demographics: DemographicsType;
  reasonsForStopAggregation: { key: string; doc_count: number }[];
}
