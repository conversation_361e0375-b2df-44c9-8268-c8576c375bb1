@import 'src/variables';

.tabsContainer {
  width: 100%;
  flex: 1 0 auto;
}

.expandedStyle {
  margin: 3px 0 !important;
}

.noShadow {
  box-shadow: none !important;

  > div:nth-child(2) {
    padding: 0 !important;
    margin-right: 0;
  }
}

.sectionTab {
  width: 100%;
  height: 50px;
  text-transform: uppercase;
  color: $font;
  background-color: $grey-2 !important;
  font-weight: 500;
  font-size: 12px;
  border: none;
  padding: 0 24px;

  &.dark {
    background-color: $grey-2 !important;
  }

  .label {
    line-height: 26px;
    margin-left: 14px;
    text-align: left;
  }

  .count {
    line-height: 26px;
  }
}

.expansion-panel-details {
  color: black;
  padding: 8px 32px 24px;
}

.form-label {
  > span:nth-child(2) {
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
    letter-spacing: 0.0107em;
  }
}
