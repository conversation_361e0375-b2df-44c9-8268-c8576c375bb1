import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import { fetchSchemasContactAnalyticsDataSDO } from '.';
import { BOOT_FINISHED } from '../app';
import { getContactAnalyticsStatus } from '../tdosTable';

function* fetchContactAnalyticsSDOSchemaId() {
  yield* takeLatest(BOOT_FINISHED, function* () {
    const isContactAnalytics = yield* select(getContactAnalyticsStatus);
    if (isContactAnalytics) {
      yield* put(fetchSchemasContactAnalyticsDataSDO());
    }
  });
}

export default function* contactAnalytics() {
  yield* all([fork(fetchContactAnalyticsSDOSchemaId)]);
}
