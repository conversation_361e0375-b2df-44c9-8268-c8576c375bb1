import React from 'react';
import FileUpload from '../fileUpload';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
describe('FileUpload', () => {
  const props = {
    uploadResult: [
      {
        key: '1',
        bucket: 'api',
        expiresInSeconds: 86400,
        fileName: 'bloomberg.mp4',
        size: 1740192,
        type: 'video/mp4',
        error: false,
        unsignedUrl: 'unsignedUrl',
        getUrl: 'getUrl',
      },
    ],
    isShowListFile: true,
    handlePick: jest.fn(),
    handleEditFile: jest.fn(),
    handleRemoveFile: jest.fn(),
    checkedFile: [0],
    handleToggle: jest.fn(),
  };

  it('renders a List component', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    expect(getByTestId('list')).toBeInTheDocument();
  });
  it('renders a ListItem component', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    expect(getByTestId('list-item')).toBeInTheDocument();
  });
  it('renders a ListItemIcon component', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    expect(getByTestId('list-item-icon')).toBeInTheDocument();
  });
  it('renders a ListItemText component', () => {
    const { getByTestId, getByText } = render(<FileUpload {...props} />);
    expect(getByTestId('list-item-text')).toBeInTheDocument();
    expect(getByText('Upload Media')).toBeInTheDocument();
  });
  it('renders Add, Edit, Remove Icons', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    expect(getByTestId('add-file')).toBeInTheDocument();
    expect(getByTestId('edit-file')).toBeInTheDocument();
    expect(getByTestId('remove-file')).toBeInTheDocument();
  });
  it('click add file', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    fireEvent.click(getByTestId('add-file'));
    expect(props.handlePick).toHaveBeenCalled();
  });
  it('click edit file', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    fireEvent.click(getByTestId('edit-file'));
    expect(props.handleEditFile).toHaveBeenCalled();
  });
  it('click remove file', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    fireEvent.click(getByTestId('remove-file'));
    expect(props.handleRemoveFile).toHaveBeenCalled();
  });
  it('renders a ListFileUpload component', () => {
    const { getByTestId } = render(<FileUpload {...props} />);
    expect(getByTestId('list-file')).toBeInTheDocument();
  });
});
