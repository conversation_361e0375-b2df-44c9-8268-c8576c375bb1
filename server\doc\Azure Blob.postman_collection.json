{"info": {"_postman_id": "ac98043a-f27d-414f-a606-dba41223d34a", "name": "Azure Blob", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "4480694"}, "item": [{"name": "Get Azure Storage Token", "event": [{"listen": "test", "script": {"exec": ["var res = JSON.parse(responseBody);", "pm.globals.set(\"oAuthBearerToken\", res.access_token);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "client_id", "value": "aaf1f70d-c139-4b99-9d53-9a20585f4958", "type": "text", "disabled": true}, {"key": "response_type", "value": "code", "type": "text", "disabled": true}, {"key": "response_mode", "value": "query", "type": "text", "disabled": true}, {"key": "scope", "value": "", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "aaf1f70d-c139-4b99-9d53-9a20585f4958", "type": "text"}, {"key": "client_secret", "value": "**********************************", "type": "text"}, {"key": "scope", "value": "https://vtstorcoredev.blob.core.usgovcloudapi.net/.default", "type": "text"}]}, "url": {"raw": "https://login.microsoftonline.us/2b6ba87b-e3cb-4547-bd91-e4160125db25/oauth2/v2.0/token", "protocol": "https", "host": ["login", "microsoftonline", "us"], "path": ["2b6ba87b-e3cb-4547-bd91-e4160125db25", "oauth2", "v2.0", "token"]}}, "response": []}, {"name": "Get Azure Blob Containers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-ms-date", "value": "Tue, 01 Aug 2023 14:46:00 GMT", "type": "text"}, {"key": "x-ms-version", "value": "2023-01-03", "type": "text"}, {"key": "x-ms-client-request-id", "value": "1234567890z", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "url": {"raw": "https://vtstorcoredev.blob.core.usgovcloudapi.net/?comp=list", "protocol": "https", "host": ["vtstorcoredev", "blob", "core", "usgov<PERSON>louda<PERSON>", "net"], "path": [""], "query": [{"key": "comp", "value": "list"}]}}, "response": []}, {"name": "Get Azure Blobs in Container", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-ms-date", "value": "Tue, 01 Aug 2023 15:11:00 GMT", "type": "text"}, {"key": "x-ms-version", "value": "2023-01-03", "type": "text"}, {"key": "x-ms-client-request-id", "value": "1234567890z", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "url": {"raw": "https://vtstorcoredev.blob.core.usgovcloudapi.net/powerbi-contact?restype=container&comp=list", "protocol": "https", "host": ["vtstorcoredev", "blob", "core", "usgov<PERSON>louda<PERSON>", "net"], "path": ["powerbi-contact"], "query": [{"key": "restype", "value": "container"}, {"key": "comp", "value": "list"}]}}, "response": []}, {"name": "Get Blob in Container", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-ms-date", "value": "Tue, 01 Aug 2023 20:53:00 GMT", "type": "text"}, {"key": "x-ms-version", "value": "2023-01-03", "type": "text"}, {"key": "x-ms-client-request-id", "value": "1234567890z", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "url": {"raw": "https://vtstorcoredev.blob.core.usgovcloudapi.net/powerbi-contact/PbixTemplates/TemplateReportDesktop.pbix", "protocol": "https", "host": ["vtstorcoredev", "blob", "core", "usgov<PERSON>louda<PERSON>", "net"], "path": ["powerbi-contact", "PbixTemplates", "TemplateReportDesktop.pbix"]}}, "response": []}, {"name": "Put Blob in Container", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-ms-date", "value": "Thu, 24 Aug 2023 15:55:15 GMT", "type": "text"}, {"key": "x-ms-version", "value": "2022-11-02", "type": "text"}, {"key": "x-ms-client-request-id", "value": "32433611", "type": "text"}, {"key": "x-ms-blob-type", "value": "BlockBlob", "type": "text"}, {"key": "X-Ms-Command-Name", "value": "StorageClient.PutBlob.UploadBlockBlobForSmallerBlobs", "type": "text", "disabled": true}, {"key": "X-Ms-Blob-Content-Type", "value": "image/png", "type": "text", "disabled": true}, {"key": "X-Ms-Effective-Locale", "value": "en.en-us", "type": "text", "disabled": true}], "body": {"mode": "file", "file": {"src": "/Users/<USER>/code/TemplateReportDesktopDemoTest.pbix"}}, "url": {"raw": "https://vtstorcoredev.blob.core.usgovcloudapi.net/powerbi-contact/TemplateReportDesktopDemoTest.pbix", "protocol": "https", "host": ["vtstorcoredev", "blob", "core", "usgov<PERSON>louda<PERSON>", "net"], "path": ["powerbi-contact", "TemplateReportDesktopDemoTest.pbix"]}}, "response": []}]}