import { SyntheticEvent, useEffect, useState } from 'react';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import CircularProgress from '@mui/material/CircularProgress';
import * as pbi from 'powerbi-client';
import { useSelector } from 'react-redux';
import getApiAuthToken from '../../helpers/getApiAuthToken';
import * as styles from './styles.scss';

const ContactAnalyticsPowerbi = () => {
  const token = useSelector(getApiAuthToken);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [mode, setMode] = useState(pbi.models.ViewMode.View);
  const [powerbiService, setPowerbiService] = useState<pbi.service.Service>();

  useEffect(() => {
    // const TOKEN_CHECK_INTERVAL = 20000;
    // const refreshTokenInterval = setInterval(() => {
    //   const embedData = JSON.parse(localStorage.getItem('pbi-token') || '{}');
    //   const now = new Date().getTime();
    //   const expiry = new Date(embedData.embedTokenExp).getTime();
    //   const diff = expiry - now;
    //   if (diff < TOKEN_CHECK_INTERVAL) {
    //     const reportContainer = document.getElementById('report-container');
    //     if (reportContainer) {
    //       powerbiService?.reset(reportContainer);
    //     }
    //     setPowerbiService(undefined);
    //     localStorage.removeItem('pbi-token');
    //   }
    // }, TOKEN_CHECK_INTERVAL);

    let host = window.location.protocol + '//' + window.location.host;
    if (host.includes('local.veritone')) {
      host = host.replace(/:[0-9][0-9][0-9][0-9]/, ':3002');
    }
    const applicationId = window.config?.veritoneAppId;

    const getToken = () => {
      return fetch(`${host}/api/v1/powerbi/generateEmbedToken`, {
        method: 'POST',
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'same-origin',
        headers: {
          Authorization: `Bearer ${token}`,
          'x-veritone-application': applicationId,
        },
      });
    };

    const doEmbedding = async () => {
      const reportContainer = document.getElementById('report-container');
      if (reportContainer) {
        // let embedData = JSON.parse(localStorage.getItem('pbi-token') || '{}');

        // if (isEmpty(embedData)) {
        const response = await getToken();
        const embedData = await response.json();
        // localStorage.setItem('pbi-token', JSON.stringify(embedData));
        // }

        if (embedData?.message === 'OrgHasNoTokenRegisteredError') {
          setError('Organization is not provisioned for PowerBI.');
          return;
        }
        if (embedData?.message) {
          setError('There was an error generating report.');
          return;
        }
        const reportLoadConfig = {
          type: 'report',
          tokenType: pbi.models.TokenType.Embed,
          permissions: pbi.models.Permissions.All,
          viewMode: mode,
          accessToken: embedData.embedToken,
          embedUrl: embedData.embedUrl,
          tokenExpiry: embedData.embedTokenExp,
          hostname: embedData.embedTokenHostname,
          settings: {
            panes: {
              filters: {
                visible: false,
              },
              pageNavigation: {
                visible: true,
              },
            },
          },
          // THIS DOESNT WORK
          // eventHooks: {
          //   accessTokenProvider: async () => {
          //     try {
          //       const response = await getToken();
          //       const date = await response.json();
          //       localStorage.setItem('pbi-token', JSON.stringify(date));
          //       return date.embedToken;
          //     } catch (e) {
          //       console.error(`There was a problem refreshing PBI token: ${e}`);
          //     }
          //   }
          // }
        };
        const powerbi = new pbi.service.Service(
          pbi.factories.hpmFactory,
          pbi.factories.wpmpFactory,
          pbi.factories.routerFactory
        );

        setPowerbiService(powerbi);

        powerbi.bootstrap(reportContainer, {
          type: 'report',
          hostname: reportLoadConfig.hostname,
        });

        const report = powerbi.embed(reportContainer, reportLoadConfig);

        report.off('loaded');
        report.on('loaded', function () {
          console.log('Report load successful');
        });

        report.off('rendered');
        report.on('rendered', function () {
          setIsLoading(false);
          console.log('Report render successful');
        });

        report.off('error');
        report.on('error', function (event: { detail: string }) {
          const errorMsg = event.detail;
          console.error(errorMsg);
        });

        report.on(
          'buttonClicked',
          async function (event: {
            detail: {
              bookmark: string;
              id: string;
              title: string;
              type: string;
            };
          }) {
            if (event.detail.type === 'WebUrl') {
              console.log('Export CSV button clicked');

              if (report instanceof pbi.Report) {
                const activePage = await report
                  .getPages()
                  .then((pages) => pages.find((page) => page.isActive));
                if (activePage) {
                  const csvTable = await activePage
                    .getVisuals()
                    .then((visuals) =>
                      visuals.find((visual) => visual.title === 'CSV Table')
                    );
                  if (csvTable) {
                    csvTable
                      .exportData()
                      .then((report) => downloadCSV(report.data))
                      .catch((error) => {
                        console.error('ExportDataFailed', error);
                        if (error.detailedMessage) {
                          console.error(
                            'Detailed Message:',
                            error.detailedMessage
                          );
                        }
                        if (error.technicalDetails) {
                          console.error(
                            'Technical Details:',
                            error.technicalDetails
                          );
                        }
                      });
                  }
                }
              } else {
                console.error('Report is not an instance of pbi.Report');
              }
            }
          }
        );
      }
    };
    if (!powerbiService) {
      doEmbedding();
    }
    // return () => {
    //   clearInterval(refreshTokenInterval);
    // };
  }, [token, mode, powerbiService]);

  function downloadCSV(csvData: string) {
    try {
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);

        // Get today's date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];
        link.setAttribute('download', `ContactAnalytics_${today}`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    }
  }

  const changeMode = (_e: SyntheticEvent, value: string) => {
    if (powerbiService) {
      const reportContainer = document.getElementById('report-container');
      if (reportContainer) {
        powerbiService.reset(reportContainer);
      }
      setPowerbiService(undefined);
    }
    setMode(parseInt(value, 10));
  };

  return (
    <div className={styles['contact-analytics-powerbi']}>
      <div className={styles['control-group']}>
        <FormControl>
          <RadioGroup
            row
            name="contact-analytics-powerbi-mode-group"
            onChange={changeMode}
            defaultValue={pbi.models.ViewMode.View}
          >
            <FormControlLabel
              value={pbi.models.ViewMode.View}
              control={<Radio />}
              label="View"
            />
            <FormControlLabel
              value={pbi.models.ViewMode.Edit}
              control={<Radio />}
              label="Edit"
            />
          </RadioGroup>
        </FormControl>
      </div>
      {error && (
        <div className={styles['error-message-container']}>{error}</div>
      )}
      {isLoading && !error && (
        <div className={styles['loading-container']}>
          <div className={styles['loading']}>
            <div>Generating Report...</div>
            <CircularProgress size={50} />
          </div>
        </div>
      )}
      <section
        id="report-container"
        className={styles['report-container']}
        style={{ visibility: isLoading ? 'hidden' : 'initial' }}
      />
    </div>
  );
};

export default ContactAnalyticsPowerbi;
