/* stylelint-disable */
$fontSize: normal;
$fontFamily: <PERSON><PERSON><PERSON>;
$spinner-width: 24px;
$spinner-color: #2196f3;
$spinner-background: #ccebff;

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.notificationContainer {
  background-color: #fff;
  position: fixed;
  right: 15px;
  top: 60px;
  width: 600px;
  height: auto;
  min-height: 100px;
  max-height: 500px;
  overflow: auto;
  border-width: 4px;
  border-radius: 4px;

  .notificationHeader {
    font-size: 14px;
    font-weight: 500;
    line-height: 48px;
    padding: 0 16px;
    background-color: #f5f5f5;
    align-items: center;
    display: flex;
    flex-direction: row;

    .notificationCountTitle {
      color: #5c6269;
      font-size: 16px;
      font-style: $fontSize;
      font-family: $fontFamily;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: -1.30385e-9px;
    }

    .notificationCount {
      font-size: 12px;
      line-height: 15px;
      padding: 2px 12px 0;
      border-radius: 8px;
      margin-left: 20px;
      background-color: #00bcd4;
      font-style: $fontSize;
      font-family: $fontFamily;
      color: #fff;
    }

    .notificationClose {
      margin-left: auto;
    }
  }

  .spinner::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    top: 32%;
    left: 3%;
    width: $spinner-width;
    height: $spinner-width;
    border-radius: 50%;
    border: 2px solid $spinner-color;
    border-top-color: $spinner-background;
    animation: spin 0.6s linear infinite;
  }
}

.notificationTitle {
  width: 210px;
  padding: 0;

  span {
    color: #2a323c;
    font-size: 14px;
    font-style: $fontSize;
    font-family: $fontFamily;
    font-weight: 300;
    line-height: 20px;
    letter-spacing: -1.30385e-9px;
  }
}

.notificationStatus span {
  color: #5c6269;
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
  font-style: $fontSize;
  font-family: $fontFamily;
  letter-spacing: -0.0000000013px;
  padding-left: 118px;
}

.removeNotification {
  color: rgba(0, 0, 0, 0.54);
  font-size: 20px;
}

.notificationContent {
  max-height: 352px;
  overflow: auto;
}

.notificationIcon {
  width: 24px;
}

.showBadge {
  display: block !important;
}

.notificationBtn {
  position: relative;
  display: block;
}

.notificationBadge {
  position: absolute;
  top: -10px;
  right: -5px;
  line-height: 20px;
  padding: 0 6px;
  background: #00bcd4;
  display: none;
  color: white;
  border-radius: 50%;
}
